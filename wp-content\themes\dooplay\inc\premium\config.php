<?php
/**
 * DeshiFlix Premium System - Configuration
 *
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Early return if already loaded
if (defined('DESHIFLIX_PREMIUM_LOADED')) {
    return;
}
define('DESHIFLIX_PREMIUM_LOADED', true);

// Enable output buffering if not already enabled
if (!ob_get_level()) {
    ob_start();
}

// Set error reporting for development (disable in production)
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Premium system configuration
define('DESHIFLIX_PREMIUM_DEBUG', defined('WP_DEBUG') && WP_DEBUG);
define('DESHIFLIX_PREMIUM_LOG_ERRORS', true);
define('DESHIFLIX_PREMIUM_CACHE_ENABLED', true);
define('DESHIFLIX_PREMIUM_CACHE_DURATION', 3600); // 1 hour

// Security configuration
define('DESHIFLIX_PREMIUM_SECURITY_ENABLED', true);
define('DESHIFLIX_PREMIUM_CONTENT_PROTECTION', true);
define('DESHIFLIX_PREMIUM_DOWNLOAD_PROTECTION', true);

// Payment configuration
define('DESHIFLIX_PREMIUM_PAYMENT_TIMEOUT', 300); // 5 minutes
define('DESHIFLIX_PREMIUM_PAYMENT_RETRY_ATTEMPTS', 3);

// Analytics configuration
define('DESHIFLIX_PREMIUM_ANALYTICS_ENABLED', true);
define('DESHIFLIX_PREMIUM_ANALYTICS_RETENTION_DAYS', 365);

/**
 * Clean output buffer and handle headers safely
 */
function deshiflix_premium_clean_output() {
    if (ob_get_level()) {
        ob_end_clean();
    }
}

/**
 * Start output buffering safely
 */
function deshiflix_premium_start_buffer() {
    if (!ob_get_level()) {
        ob_start();
    }
}

/**
 * Handle admin redirects safely
 */
function deshiflix_premium_safe_redirect($url) {
    if (!headers_sent()) {
        wp_redirect($url);
        exit;
    } else {
        // Fallback to JavaScript redirect
        echo '<script>window.location.href = "' . esc_url($url) . '";</script>';
        exit;
    }
}

/**
 * Log premium system errors
 */
function deshiflix_premium_log_error($message, $context = array()) {
    if (!DESHIFLIX_PREMIUM_LOG_ERRORS) {
        return;
    }
    
    $log_entry = array(
        'timestamp' => current_time('mysql'),
        'message' => $message,
        'context' => $context,
        'user_id' => get_current_user_id(),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    );
    
    error_log('[DeshiFlix Premium] ' . json_encode($log_entry));
}

/**
 * Get premium system status
 */
function deshiflix_premium_get_system_status() {
    return array(
        'version' => DESHIFLIX_PREMIUM_VERSION ?? '1.0.0',
        'debug_mode' => DESHIFLIX_PREMIUM_DEBUG,
        'cache_enabled' => DESHIFLIX_PREMIUM_CACHE_ENABLED,
        'security_enabled' => DESHIFLIX_PREMIUM_SECURITY_ENABLED,
        'analytics_enabled' => DESHIFLIX_PREMIUM_ANALYTICS_ENABLED,
        'output_buffering' => ob_get_level() > 0,
        'headers_sent' => headers_sent(),
        'memory_usage' => memory_get_usage(true),
        'memory_limit' => ini_get('memory_limit')
    );
}

// Include admin components (with error handling)
if (is_admin()) {
    // Include admin menu
    if (file_exists(DESHIFLIX_PREMIUM_PATH . 'admin/premium-admin-menu.php')) {
        try {
            require_once DESHIFLIX_PREMIUM_PATH . 'admin/premium-admin-menu.php';
        } catch (Exception $e) {
            error_log('Premium Admin Menu Error: ' . $e->getMessage());
        }
    }

    // Include DooPlay admin
    if (file_exists(DESHIFLIX_PREMIUM_PATH . 'admin/premium-dooplay-admin.php')) {
        try {
            require_once DESHIFLIX_PREMIUM_PATH . 'admin/premium-dooplay-admin.php';
        } catch (Exception $e) {
            error_log('Premium Admin Error: ' . $e->getMessage());
        }
    }
}

// Initialize output buffering
deshiflix_premium_start_buffer();

// Register shutdown function to clean up
register_shutdown_function('deshiflix_premium_clean_output');

// Include premium features handler (with error handling)
if (file_exists(DESHIFLIX_PREMIUM_PATH . 'premium-features.php')) {
    try {
        require_once DESHIFLIX_PREMIUM_PATH . 'premium-features.php';
    } catch (Exception $e) {
        error_log('Premium Features Error: ' . $e->getMessage());
    }
}

// Include download protection
if (file_exists(DESHIFLIX_PREMIUM_PATH . 'frontend/download-protection.php')) {
    try {
        require_once DESHIFLIX_PREMIUM_PATH . 'frontend/download-protection.php';
    } catch (Exception $e) {
        error_log('Download Protection Error: ' . $e->getMessage());
    }
}

// Include mobile API
if (file_exists(DESHIFLIX_PREMIUM_PATH . 'api/mobile-premium-api.php')) {
    try {
        require_once DESHIFLIX_PREMIUM_PATH . 'api/mobile-premium-api.php';
    } catch (Exception $e) {
        error_log('Mobile API Error: ' . $e->getMessage());
    }
}

// Include advanced features
if (file_exists(DESHIFLIX_PREMIUM_PATH . 'features/watchlist-premium.php')) {
    try {
        require_once DESHIFLIX_PREMIUM_PATH . 'features/watchlist-premium.php';
    } catch (Exception $e) {
        error_log('Premium Watchlist Error: ' . $e->getMessage());
    }
}

if (file_exists(DESHIFLIX_PREMIUM_PATH . 'features/notification-system.php')) {
    try {
        require_once DESHIFLIX_PREMIUM_PATH . 'features/notification-system.php';
    } catch (Exception $e) {
        error_log('Notification System Error: ' . $e->getMessage());
    }
}

if (file_exists(DESHIFLIX_PREMIUM_PATH . 'features/referral-system.php')) {
    try {
        require_once DESHIFLIX_PREMIUM_PATH . 'features/referral-system.php';
    } catch (Exception $e) {
        error_log('Referral System Error: ' . $e->getMessage());
    }
}

// Include test user creator (for development)
if (file_exists(DESHIFLIX_PREMIUM_PATH . 'test-premium-user.php')) {
    try {
        require_once DESHIFLIX_PREMIUM_PATH . 'test-premium-user.php';
    } catch (Exception $e) {
        error_log('Test User Error: ' . $e->getMessage());
    }
}

// Add payment modal to footer
add_action('wp_footer', 'deshiflix_add_payment_modal');
function deshiflix_add_payment_modal() {
    if (is_user_logged_in()) {
        include DESHIFLIX_PREMIUM_PATH . 'templates/payment-modal.php';
    }
}
?>
