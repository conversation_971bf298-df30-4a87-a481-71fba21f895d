/**
 * Android TV Optimized Video Player
 * Enhanced video player with TV-specific controls and features
 */

(function($) {
    'use strict';

    var TVPlayer = {
        player: null,
        isFullscreen: false,
        controlsVisible: true,
        controlsTimeout: null,
        currentQuality: 'auto',
        availableQualities: ['auto', '1080p', '720p', '480p'],
        
        init: function() {
            if (!window.AndroidTV || !window.AndroidTV.isAndroidTV) return;
            
            this.setupPlayer();
            this.bindEvents();
            this.initializeControls();
        },
        
        setupPlayer: function() {
            var videoElement = document.getElementById('video-element');
            if (!videoElement) return;
            
            this.player = videoElement;
            
            // Set TV-optimized attributes
            this.player.setAttribute('playsinline', '');
            this.player.setAttribute('webkit-playsinline', '');
            this.player.preload = 'metadata';
            
            // Apply TV settings
            if (window.TVSettings) {
                this.player.volume = window.TVSettings.settings.volume;
                this.player.autoplay = window.TVSettings.settings.autoplay;
            }
            
            // Setup HLS.js for better streaming support
            this.setupHLS();
        },
        
        setupHLS: function() {
            if (typeof Hls !== 'undefined' && Hls.isSupported()) {
                var hls = new Hls({
                    enableWorker: true,
                    lowLatencyMode: true,
                    backBufferLength: 90
                });
                
                hls.loadSource(this.player.src);
                hls.attachMedia(this.player);
                
                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    console.log('HLS manifest loaded');
                });
                
                hls.on(Hls.Events.ERROR, function(event, data) {
                    console.error('HLS error:', data);
                    if (data.fatal) {
                        TVPlayer.handleError('Stream error occurred');
                    }
                });
                
                this.hls = hls;
            }
        },
        
        initializeControls: function() {
            this.createCustomControls();
            this.setupControlsAutoHide();
        },
        
        createCustomControls: function() {
            var controlsHTML = `
                <div class="tv-player-controls" id="tv-player-controls">
                    <div class="controls-top">
                        <div class="channel-info">
                            <span class="channel-name">${this.getChannelName()}</span>
                            <span class="live-indicator">● LIVE</span>
                        </div>
                        <div class="player-status">
                            <span class="quality-indicator">${this.currentQuality}</span>
                            <span class="volume-indicator">🔊 ${Math.round(this.player.volume * 100)}%</span>
                        </div>
                    </div>
                    
                    <div class="controls-center">
                        <button class="control-btn-large tv-focusable" id="tv-play-pause">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    
                    <div class="controls-bottom">
                        <div class="control-row">
                            <button class="control-btn tv-focusable" id="tv-mute">
                                <i class="fas fa-volume-up"></i>
                            </button>
                            <div class="volume-bar">
                                <div class="volume-fill" style="width: ${this.player.volume * 100}%"></div>
                            </div>
                            <button class="control-btn tv-focusable" id="tv-quality">
                                <i class="fas fa-cog"></i>
                            </button>
                            <button class="control-btn tv-focusable" id="tv-fullscreen">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                        
                        <div class="control-hints">
                            <span>Space: Play/Pause</span>
                            <span>M: Mute</span>
                            <span>F: Fullscreen</span>
                            <span>↑↓: Volume</span>
                            <span>Back: Exit</span>
                        </div>
                    </div>
                </div>
            `;
            
            $('.video-player').append(controlsHTML);
        },
        
        setupControlsAutoHide: function() {
            var self = this;
            
            // Show controls on any interaction
            $(document).on('keydown mousemove', function() {
                self.showControls();
            });
            
            // Auto-hide after 5 seconds
            this.resetControlsTimeout();
        },
        
        showControls: function() {
            $('#tv-player-controls').addClass('visible');
            this.controlsVisible = true;
            this.resetControlsTimeout();
        },
        
        hideControls: function() {
            if (!this.isFullscreen) return; // Only hide in fullscreen
            
            $('#tv-player-controls').removeClass('visible');
            this.controlsVisible = false;
        },
        
        resetControlsTimeout: function() {
            var self = this;
            
            clearTimeout(this.controlsTimeout);
            this.controlsTimeout = setTimeout(function() {
                self.hideControls();
            }, 5000);
        },
        
        bindEvents: function() {
            var self = this;
            
            // Player events
            $(this.player).on('play', function() {
                $('#tv-play-pause i').removeClass('fa-play').addClass('fa-pause');
                self.updateStatus();
            });
            
            $(this.player).on('pause', function() {
                $('#tv-play-pause i').removeClass('fa-pause').addClass('fa-play');
                self.updateStatus();
            });
            
            $(this.player).on('volumechange', function() {
                self.updateVolumeDisplay();
            });
            
            $(this.player).on('error', function() {
                self.handleError('Video playback error');
            });
            
            $(this.player).on('loadstart', function() {
                self.showLoading();
            });
            
            $(this.player).on('canplay', function() {
                self.hideLoading();
            });
            
            // Control button events
            $(document).on('click', '#tv-play-pause', function() {
                self.togglePlayPause();
            });
            
            $(document).on('click', '#tv-mute', function() {
                self.toggleMute();
            });
            
            $(document).on('click', '#tv-quality', function() {
                self.showQualityMenu();
            });
            
            $(document).on('click', '#tv-fullscreen', function() {
                self.toggleFullscreen();
            });
            
            // Fullscreen events
            $(document).on('fullscreenchange webkitfullscreenchange mozfullscreenchange msfullscreenchange', function() {
                self.handleFullscreenChange();
            });
            
            // Focus events for TV navigation
            $(document).on('tv-focus', '.control-btn, .control-btn-large', function() {
                $(this).addClass('tv-control-focused');
                setTimeout(function() {
                    $(this).removeClass('tv-control-focused');
                }.bind(this), 200);
            });
        },
        
        togglePlayPause: function() {
            if (this.player.paused) {
                this.player.play();
                if (window.AndroidTV) {
                    window.AndroidTV.showNotification('Playing');
                }
            } else {
                this.player.pause();
                if (window.AndroidTV) {
                    window.AndroidTV.showNotification('Paused');
                }
            }
        },
        
        toggleMute: function() {
            this.player.muted = !this.player.muted;
            this.updateVolumeDisplay();
            
            if (window.AndroidTV) {
                window.AndroidTV.showNotification(this.player.muted ? 'Muted' : 'Unmuted');
            }
        },
        
        toggleFullscreen: function() {
            if (!this.isFullscreen) {
                this.enterFullscreen();
            } else {
                this.exitFullscreen();
            }
        },
        
        enterFullscreen: function() {
            var element = this.player.parentElement;
            
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        },
        
        exitFullscreen: function() {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        },
        
        handleFullscreenChange: function() {
            this.isFullscreen = !!(document.fullscreenElement || 
                                 document.webkitFullscreenElement || 
                                 document.mozFullScreenElement || 
                                 document.msFullscreenElement);
            
            if (this.isFullscreen) {
                $('body').addClass('tv-fullscreen-mode');
                $('#tv-fullscreen i').removeClass('fa-expand').addClass('fa-compress');
                this.showControls();
                
                if (window.AndroidTV) {
                    window.AndroidTV.showNotification('Fullscreen Mode');
                }
            } else {
                $('body').removeClass('tv-fullscreen-mode');
                $('#tv-fullscreen i').removeClass('fa-compress').addClass('fa-expand');
                this.showControls();
                
                if (window.AndroidTV) {
                    window.AndroidTV.showNotification('Windowed Mode');
                }
            }
        },
        
        showQualityMenu: function() {
            var menu = `
                <div class="quality-menu-overlay" id="quality-menu-overlay">
                    <div class="quality-menu">
                        <h3>Video Quality</h3>
                        ${this.availableQualities.map(quality => 
                            `<button class="quality-option tv-focusable ${quality === this.currentQuality ? 'active' : ''}" 
                                     data-quality="${quality}">${quality}</button>`
                        ).join('')}
                    </div>
                </div>
            `;
            
            $('body').append(menu);
            
            // Focus first option
            setTimeout(function() {
                $('.quality-option').first().addClass('tv-focused');
            }, 100);
            
            // Bind events
            $(document).on('click', '.quality-option', function() {
                var quality = $(this).data('quality');
                TVPlayer.setQuality(quality);
                $('#quality-menu-overlay').remove();
            });
            
            // Close on escape
            $(document).on('keydown.quality-menu', function(e) {
                if (e.keyCode === 27) {
                    $('#quality-menu-overlay').remove();
                    $(document).off('keydown.quality-menu');
                }
            });
        },
        
        setQuality: function(quality) {
            this.currentQuality = quality;
            $('.quality-indicator').text(quality);
            
            if (window.AndroidTV) {
                window.AndroidTV.showNotification('Quality: ' + quality);
            }
            
            // Here you would implement actual quality switching
            // This depends on your streaming setup (HLS, DASH, etc.)
        },
        
        updateVolumeDisplay: function() {
            var volume = Math.round(this.player.volume * 100);
            var isMuted = this.player.muted;
            
            $('.volume-indicator').text(isMuted ? '🔇 Muted' : `🔊 ${volume}%`);
            $('.volume-fill').css('width', isMuted ? '0%' : `${volume}%`);
            
            var muteIcon = $('#tv-mute i');
            if (isMuted) {
                muteIcon.removeClass('fa-volume-up fa-volume-down').addClass('fa-volume-mute');
            } else if (volume > 50) {
                muteIcon.removeClass('fa-volume-mute fa-volume-down').addClass('fa-volume-up');
            } else {
                muteIcon.removeClass('fa-volume-mute fa-volume-up').addClass('fa-volume-down');
            }
        },
        
        updateStatus: function() {
            // Update any status indicators
            if (this.player.paused) {
                $('.live-indicator').text('⏸ PAUSED');
            } else {
                $('.live-indicator').text('● LIVE');
            }
        },
        
        showLoading: function() {
            $('.player-loading').show();
        },
        
        hideLoading: function() {
            $('.player-loading').hide();
        },
        
        handleError: function(message) {
            console.error('TV Player Error:', message);
            $('.player-error').show();
            $('.player-loading').hide();
            
            if (window.AndroidTV) {
                window.AndroidTV.showNotification('Error: ' + message);
            }
        },
        
        getChannelName: function() {
            return $('[data-channel-name]').first().data('channel-name') || 'Live TV';
        },
        
        // Volume control methods for remote
        volumeUp: function() {
            this.player.volume = Math.min(1, this.player.volume + 0.1);
            this.updateVolumeDisplay();
        },
        
        volumeDown: function() {
            this.player.volume = Math.max(0, this.player.volume - 0.1);
            this.updateVolumeDisplay();
        },
        
        // Cleanup
        destroy: function() {
            if (this.hls) {
                this.hls.destroy();
            }
            
            clearTimeout(this.controlsTimeout);
            $(document).off('.tv-player');
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        TVPlayer.init();
    });

    // Make TVPlayer globally available
    window.TVPlayer = TVPlayer;

})(jQuery);
