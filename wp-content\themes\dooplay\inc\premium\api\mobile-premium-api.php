<?php
/**
 * Mobile Premium API for DeshiFlix App
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Mobile_Premium_API {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('rest_api_init', array($this, 'register_api_routes'));
    }
    
    /**
     * Register API routes
     */
    public function register_api_routes() {
        // Premium status endpoint
        register_rest_route('deshiflix/v1', '/premium/status', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_premium_status'),
            'permission_callback' => array($this, 'check_api_permissions'),
        ));
        
        // Premium plans endpoint
        register_rest_route('deshiflix/v1', '/premium/plans', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_premium_plans'),
            'permission_callback' => '__return_true',
        ));
        
        // Content access check
        register_rest_route('deshiflix/v1', '/premium/content/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'check_content_access'),
            'permission_callback' => array($this, 'check_api_permissions'),
        ));
        
        // Device registration
        register_rest_route('deshiflix/v1', '/premium/device', array(
            'methods' => 'POST',
            'callback' => array($this, 'register_device'),
            'permission_callback' => array($this, 'check_api_permissions'),
        ));
        
        // Download tracking
        register_rest_route('deshiflix/v1', '/premium/download', array(
            'methods' => 'POST',
            'callback' => array($this, 'track_mobile_download'),
            'permission_callback' => array($this, 'check_api_permissions'),
        ));
    }
    
    /**
     * Check API permissions
     */
    public function check_api_permissions($request) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return new WP_Error('unauthorized', 'User must be logged in', array('status' => 401));
        }
        
        // Verify API key if provided
        $api_key = $request->get_header('X-API-Key');
        if ($api_key && !$this->verify_api_key($api_key)) {
            return new WP_Error('invalid_api_key', 'Invalid API key', array('status' => 403));
        }
        
        return true;
    }
    
    /**
     * Verify API key
     */
    private function verify_api_key($api_key) {
        $valid_keys = get_option('deshiflix_mobile_api_keys', array());
        return in_array($api_key, $valid_keys);
    }
    
    /**
     * Get premium status
     */
    public function get_premium_status($request) {
        $user_id = get_current_user_id();
        
        if (!function_exists('deshiflix_premium') || !deshiflix_premium()) {
            return new WP_Error('premium_unavailable', 'Premium system not available', array('status' => 503));
        }
        
        $premium_core = deshiflix_premium();
        $is_premium = $premium_core->is_user_premium($user_id);
        
        $response = array(
            'user_id' => $user_id,
            'is_premium' => $is_premium,
            'status' => $is_premium ? 'active' : 'inactive'
        );
        
        if ($is_premium) {
            $user_details = $premium_core->get_user_premium_details($user_id);
            $response['plan'] = $user_details['plan'] ?? null;
            $response['expires_at'] = $user_details['expires_at'] ?? null;
            $response['features'] = $user_details['features'] ?? array();
        }
        
        return rest_ensure_response($response);
    }
    
    /**
     * Get premium plans
     */
    public function get_premium_plans($request) {
        global $wpdb;
        
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plans = $wpdb->get_results(
            "SELECT * FROM $table_plans WHERE status = 'active' ORDER BY sort_order ASC"
        );
        
        $formatted_plans = array();
        
        foreach ($plans as $plan) {
            $features = json_decode($plan->features, true) ?: array();
            
            $formatted_plans[] = array(
                'id' => intval($plan->id),
                'name' => $plan->name,
                'description' => $plan->description,
                'price' => floatval($plan->price),
                'original_price' => floatval($plan->original_price),
                'duration_days' => intval($plan->duration_days),
                'max_devices' => intval($plan->max_devices),
                'download_limit' => intval($plan->download_limit),
                'quality_limit' => $plan->quality_limit,
                'features' => $features,
                'discount_percentage' => $plan->original_price > 0 ? 
                    round((($plan->original_price - $plan->price) / $plan->original_price) * 100) : 0
            );
        }
        
        return rest_ensure_response($formatted_plans);
    }
    
    /**
     * Check content access
     */
    public function check_content_access($request) {
        $post_id = intval($request['id']);
        $user_id = get_current_user_id();
        
        if (!function_exists('deshiflix_premium') || !deshiflix_premium()) {
            return new WP_Error('premium_unavailable', 'Premium system not available', array('status' => 503));
        }
        
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        
        $response = array(
            'post_id' => $post_id,
            'is_premium_content' => $content_manager->is_premium_content($post_id),
            'has_access' => false,
            'premium_level' => null,
            'features' => array()
        );
        
        if ($response['is_premium_content']) {
            $response['has_access'] = $content_manager->user_has_content_access($post_id, $user_id);
            $response['premium_level'] = get_post_meta($post_id, '_premium_level', true);
            $response['features'] = get_post_meta($post_id, '_premium_features', true) ?: array();
        } else {
            $response['has_access'] = true; // Free content
        }
        
        return rest_ensure_response($response);
    }
    
    /**
     * Register device
     */
    public function register_device($request) {
        $user_id = get_current_user_id();
        $device_data = $request->get_json_params();
        
        if (!isset($device_data['device_id']) || !isset($device_data['device_name'])) {
            return new WP_Error('missing_data', 'Device ID and name are required', array('status' => 400));
        }
        
        global $wpdb;
        $table_devices = $wpdb->prefix . 'deshiflix_premium_devices';
        
        // Check if device already exists
        $existing_device = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_devices WHERE user_id = %d AND device_id = %s",
            $user_id,
            $device_data['device_id']
        ));
        
        $device_info = array(
            'user_id' => $user_id,
            'device_id' => sanitize_text_field($device_data['device_id']),
            'device_name' => sanitize_text_field($device_data['device_name']),
            'device_type' => sanitize_text_field($device_data['device_type'] ?? 'mobile'),
            'os' => sanitize_text_field($device_data['os'] ?? ''),
            'app_version' => sanitize_text_field($device_data['app_version'] ?? ''),
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'last_active' => current_time('mysql')
        );
        
        if ($existing_device) {
            // Update existing device
            $wpdb->update(
                $table_devices,
                $device_info,
                array('id' => $existing_device->id)
            );
            $device_id = $existing_device->id;
        } else {
            // Insert new device
            $device_info['created_at'] = current_time('mysql');
            $wpdb->insert($table_devices, $device_info);
            $device_id = $wpdb->insert_id;
        }
        
        return rest_ensure_response(array(
            'device_id' => $device_id,
            'status' => 'registered',
            'message' => 'Device registered successfully'
        ));
    }
    
    /**
     * Track mobile download
     */
    public function track_mobile_download($request) {
        $user_id = get_current_user_id();
        $download_data = $request->get_json_params();
        
        if (!isset($download_data['post_id'])) {
            return new WP_Error('missing_data', 'Post ID is required', array('status' => 400));
        }
        
        $post_id = intval($download_data['post_id']);
        
        // Verify user has access to this content
        if (!function_exists('deshiflix_premium') || !deshiflix_premium()) {
            return new WP_Error('premium_unavailable', 'Premium system not available', array('status' => 503));
        }
        
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        if (!$content_manager->user_has_content_access($post_id, $user_id)) {
            return new WP_Error('access_denied', 'Access denied to this content', array('status' => 403));
        }
        
        // Track download
        global $wpdb;
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        $wpdb->insert(
            $table_analytics,
            array(
                'user_id' => $user_id,
                'event_type' => 'mobile_download',
                'event_data' => json_encode(array(
                    'post_id' => $post_id,
                    'post_title' => get_the_title($post_id),
                    'device_id' => sanitize_text_field($download_data['device_id'] ?? ''),
                    'quality' => sanitize_text_field($download_data['quality'] ?? ''),
                    'file_size' => sanitize_text_field($download_data['file_size'] ?? ''),
                    'download_time' => current_time('mysql')
                )),
                'post_id' => $post_id,
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT']
            )
        );
        
        return rest_ensure_response(array(
            'status' => 'tracked',
            'message' => 'Download tracked successfully'
        ));
    }
}

// Initialize mobile API
DeshiFlix_Mobile_Premium_API::get_instance();
