/**
 * Stream Fix Utility
 * Automatically detects and fixes common streaming issues
 */

(function($) {
    'use strict';

    var StreamFix = {
        video: null,
        originalSrc: '',
        backupSrc: '',
        retryCount: 0,
        maxRetries: 3,
        fixAttempts: [],
        
        init: function() {
            this.video = document.getElementById('livetv-video');
            if (!this.video) return;
            
            this.originalSrc = this.video.getAttribute('data-stream-url');
            this.backupSrc = this.video.getAttribute('data-backup-url');
            
            this.bindEvents();
            this.startHealthCheck();
            
            console.log('Stream Fix utility initialized');
        },
        
        bindEvents: function() {
            var self = this;
            
            // Video error events
            this.video.addEventListener('error', function(e) {
                self.handleVideoError(e);
            });
            
            // Stalled/waiting events
            this.video.addEventListener('stalled', function() {
                self.handleStalled();
            });
            
            this.video.addEventListener('waiting', function() {
                self.handleWaiting();
            });
            
            // Success events
            this.video.addEventListener('canplay', function() {
                self.handleSuccess();
            });
            
            this.video.addEventListener('playing', function() {
                self.handlePlaying();
            });
        },
        
        handleVideoError: function(e) {
            console.error('Video error detected:', e);
            
            var error = this.video.error;
            if (error) {
                console.error('Error code:', error.code);
                console.error('Error message:', error.message);
                
                switch(error.code) {
                    case MediaError.MEDIA_ERR_ABORTED:
                        this.logFix('MEDIA_ERR_ABORTED', 'User aborted playback');
                        break;
                    case MediaError.MEDIA_ERR_NETWORK:
                        this.logFix('MEDIA_ERR_NETWORK', 'Network error');
                        this.tryNetworkFix();
                        break;
                    case MediaError.MEDIA_ERR_DECODE:
                        this.logFix('MEDIA_ERR_DECODE', 'Decode error');
                        this.tryDecodeFix();
                        break;
                    case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
                        this.logFix('MEDIA_ERR_SRC_NOT_SUPPORTED', 'Source not supported');
                        this.tryFormatFix();
                        break;
                    default:
                        this.logFix('UNKNOWN_ERROR', 'Unknown error');
                        this.tryGenericFix();
                        break;
                }
            }
        },
        
        handleStalled: function() {
            console.warn('Video stalled');
            this.logFix('STALLED', 'Video playback stalled');
            
            // Try to recover from stall
            setTimeout(() => {
                if (this.video.readyState < 3) { // HAVE_FUTURE_DATA
                    this.tryBufferFix();
                }
            }, 5000);
        },
        
        handleWaiting: function() {
            console.log('Video waiting for data');
            
            // If waiting too long, try to fix
            setTimeout(() => {
                if (this.video.readyState < 2) { // HAVE_CURRENT_DATA
                    this.tryBufferFix();
                }
            }, 10000);
        },
        
        handleSuccess: function() {
            console.log('Video can play - resetting retry count');
            this.retryCount = 0;
        },
        
        handlePlaying: function() {
            console.log('Video playing successfully');
            this.retryCount = 0;
        },
        
        tryNetworkFix: function() {
            if (this.retryCount >= this.maxRetries) {
                this.showFinalError('Network error - max retries reached');
                return;
            }
            
            this.retryCount++;
            this.logFix('NETWORK_FIX', `Attempting network fix (${this.retryCount}/${this.maxRetries})`);
            
            // Try reloading the video
            setTimeout(() => {
                this.video.load();
            }, 2000);
        },
        
        tryDecodeFix: function() {
            this.logFix('DECODE_FIX', 'Trying decode fix');
            
            // Try backup URL if available
            if (this.backupSrc && this.video.src !== this.backupSrc) {
                this.logFix('BACKUP_URL', 'Switching to backup URL');
                this.video.src = this.backupSrc;
                this.video.load();
                return;
            }
            
            // Try different format
            this.tryFormatFix();
        },
        
        tryFormatFix: function() {
            this.logFix('FORMAT_FIX', 'Trying format fix');
            
            var currentSrc = this.video.src;
            
            // If it's an HLS stream, try direct MP4
            if (currentSrc.includes('.m3u8')) {
                var mp4Src = currentSrc.replace('.m3u8', '.mp4');
                this.logFix('HLS_TO_MP4', 'Converting HLS to MP4');
                this.video.src = mp4Src;
                this.video.load();
                return;
            }
            
            // Try backup URL
            if (this.backupSrc) {
                this.logFix('BACKUP_URL', 'Using backup URL');
                this.video.src = this.backupSrc;
                this.video.load();
                return;
            }
            
            this.tryGenericFix();
        },
        
        tryBufferFix: function() {
            this.logFix('BUFFER_FIX', 'Trying buffer fix');
            
            // Try to seek to current time to force buffer refresh
            var currentTime = this.video.currentTime;
            this.video.currentTime = currentTime + 0.1;
            
            // If still not working, reload
            setTimeout(() => {
                if (this.video.readyState < 2) {
                    this.video.load();
                }
            }, 3000);
        },
        
        tryGenericFix: function() {
            if (this.retryCount >= this.maxRetries) {
                this.showFinalError('Generic error - max retries reached');
                return;
            }
            
            this.retryCount++;
            this.logFix('GENERIC_FIX', `Generic fix attempt (${this.retryCount}/${this.maxRetries})`);
            
            // Wait and reload
            setTimeout(() => {
                this.video.load();
            }, 3000);
        },
        
        startHealthCheck: function() {
            var self = this;
            
            // Check video health every 30 seconds
            setInterval(function() {
                self.performHealthCheck();
            }, 30000);
        },
        
        performHealthCheck: function() {
            if (!this.video) return;
            
            var currentTime = this.video.currentTime;
            var readyState = this.video.readyState;
            var networkState = this.video.networkState;
            
            // Check if video is stuck
            setTimeout(() => {
                if (this.video.currentTime === currentTime && 
                    !this.video.paused && 
                    !this.video.ended) {
                    this.logFix('HEALTH_CHECK', 'Video appears stuck');
                    this.tryBufferFix();
                }
            }, 5000);
            
            // Log health status
            console.log('Health check:', {
                currentTime: currentTime,
                readyState: readyState,
                networkState: networkState,
                paused: this.video.paused,
                ended: this.video.ended
            });
        },
        
        logFix: function(type, message) {
            var timestamp = new Date().toISOString();
            var logEntry = {
                timestamp: timestamp,
                type: type,
                message: message,
                videoSrc: this.video.src,
                readyState: this.video.readyState,
                networkState: this.video.networkState
            };
            
            this.fixAttempts.push(logEntry);
            console.log(`[StreamFix] ${type}: ${message}`, logEntry);
            
            // Keep only last 20 entries
            if (this.fixAttempts.length > 20) {
                this.fixAttempts = this.fixAttempts.slice(-20);
            }
            
            // Update UI if debug element exists
            this.updateDebugUI();
        },
        
        updateDebugUI: function() {
            var debugElement = document.getElementById('stream-debug');
            if (debugElement) {
                var statusElement = document.getElementById('stream-status');
                if (statusElement && this.fixAttempts.length > 0) {
                    var lastFix = this.fixAttempts[this.fixAttempts.length - 1];
                    statusElement.textContent = lastFix.message;
                }
            }
        },
        
        showFinalError: function(message) {
            this.logFix('FINAL_ERROR', message);
            
            // Show error overlay
            var errorOverlay = document.getElementById('error-message');
            if (errorOverlay) {
                errorOverlay.style.display = 'flex';
                var errorText = document.getElementById('error-text');
                if (errorText) {
                    errorText.textContent = message + '. Please try a different stream or contact support.';
                }
            }
            
            // Hide loading overlay
            var loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        },
        
        // Public methods for manual fixes
        forceReload: function() {
            this.logFix('MANUAL_RELOAD', 'Manual reload requested');
            this.retryCount = 0;
            this.video.src = this.originalSrc;
            this.video.load();
        },
        
        switchToBackup: function() {
            if (this.backupSrc) {
                this.logFix('MANUAL_BACKUP', 'Manual backup switch requested');
                this.video.src = this.backupSrc;
                this.video.load();
            }
        },
        
        getFixHistory: function() {
            return this.fixAttempts;
        },
        
        clearFixHistory: function() {
            this.fixAttempts = [];
            this.logFix('HISTORY_CLEARED', 'Fix history cleared');
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        StreamFix.init();
    });

    // Make StreamFix globally available
    window.StreamFix = StreamFix;

    // Add global retry function for buttons
    window.retryStreamWithFix = function() {
        if (window.StreamFix) {
            window.StreamFix.forceReload();
        }
    };

    // Add backup switch function
    window.switchToBackupStream = function() {
        if (window.StreamFix) {
            window.StreamFix.switchToBackup();
        }
    };

})(jQuery);
