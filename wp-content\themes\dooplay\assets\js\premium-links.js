jQuery(document).ready(function($) {
    
    // Override the original LinkPost function to include premium support
    $(document).off('click', '#dooplay_anchor_postlinks');
    $(document).on('click', '#dooplay_anchor_postlinks', function() {
        if (!$('#dooplay_lfield_urls').val()) {
            alert('Please add at least one link');
            return false;
        }
        
        $('#doolinks_response').addClass('onload');
        $('#publish').hide();
        
        $.ajax({
            url: (typeof ajaxurl !== 'undefined') ? ajaxurl : '/wp-admin/admin-ajax.php',
            type: 'post',
            data: {
                urls: $('#dooplay_lfield_urls').val(),
                type: $('#dooplay_lfield_type').val(),
                quality: $('#dooplay_lfield_qual').val(),
                language: $('#dooplay_lfield_lang').val(),
                size: $('#dooplay_lfield_size').val(),
                premium: $('#dooplay_lfield_premium').is(':checked') ? 1 : 0,
                postid: $('#post_ID').val(),
                action: 'doosave_links'
            },
            error: function(xhr) {
                console.log(xhr);
            },
            success: function(response) {
                $('#publish').show();
                $('#doolinks_response').removeClass('onload');
                $('#doolinks_response').html(response);
                $('#dooplay_lfield_urls').val('');
                $('#dooplay_lfield_size').val('');
                $('#dooplay_lfield_premium').prop('checked', false);
            }
        });
        
        return false;
    });
    
    // Add premium toggle functionality to existing links
    $(document).on('click', '.premium-toggle', function() {
        var linkId = $(this).data('link-id');
        var isPremium = $(this).hasClass('premium-active');
        var newStatus = isPremium ? 0 : 1;
        
        var $this = $(this);
        $this.prop('disabled', true);
        
        $.ajax({
            url: (typeof ajaxurl !== 'undefined') ? ajaxurl : '/wp-admin/admin-ajax.php',
            type: 'post',
            data: {
                action: 'toggle_link_premium',
                link_id: linkId,
                premium_status: newStatus,
                nonce: $('#_wpnonce').val()
            },
            success: function(response) {
                if (response.success) {
                    if (newStatus) {
                        $this.addClass('premium-active');
                        $this.html('👑');
                        $this.attr('title', 'Premium Link - Click to make free');
                    } else {
                        $this.removeClass('premium-active');
                        $this.html('-');
                        $this.attr('title', 'Free Link - Click to make premium');
                    }
                }
                $this.prop('disabled', false);
            },
            error: function() {
                $this.prop('disabled', false);
            }
        });
    });
    
    // Style premium links in the table
    function stylePremiumLinks() {
        $('.link-row-sortable').each(function() {
            var $row = $(this);
            var hasPremiumIcon = $row.find('td:nth-child(9)').text().includes('👑');
            
            if (hasPremiumIcon) {
                $row.addClass('premium-link-row');
            } else {
                $row.removeClass('premium-link-row');
            }
        });
    }
    
    // Apply styling on page load and after AJAX updates
    stylePremiumLinks();
    
    // Re-apply styling after link reload
    $(document).on('click', '#dooplay_anchor_reloadllist', function() {
        setTimeout(stylePremiumLinks, 1000);
    });
    
    // Add CSS for premium links
    if (!$('#premium-links-styles').length) {
        $('head').append(`
            <style id="premium-links-styles">
                .premium-link-row {
                    background: linear-gradient(90deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%);
                    border-left: 3px solid #FFD700;
                }
                
                .premium-toggle {
                    background: none;
                    border: none;
                    cursor: pointer;
                    font-size: 16px;
                    padding: 2px 6px;
                    border-radius: 3px;
                    transition: all 0.3s ease;
                }
                
                .premium-toggle:hover {
                    background: rgba(255, 215, 0, 0.2);
                }
                
                .premium-toggle.premium-active {
                    color: #FFD700;
                }
                
                .premium-toggle:not(.premium-active) {
                    color: #ccc;
                }
                
                #dooplay_lfield_premium {
                    margin-right: 5px;
                }
                
                .episode-premium-indicator {
                    color: #FFD700;
                    font-size: 12px;
                    margin-left: 5px;
                }
                
                /* Episode generator premium styling */
                input[name^="episode_premium_"]:checked + span {
                    color: #FFD700 !important;
                    text-shadow: 0 0 3px rgba(255, 215, 0, 0.5);
                }
                
                /* Premium badge in episode titles */
                .episode-premium-badge {
                    background: linear-gradient(45deg, #FFD700, #FFA500);
                    color: #000;
                    padding: 2px 6px;
                    border-radius: 8px;
                    font-size: 10px;
                    font-weight: bold;
                    margin-left: 5px;
                }
            </style>
        `);
    }
});

// Add premium indicator to episode titles in the links table
function addEpisodePremiumIndicators() {
    jQuery('.link-row-sortable').each(function() {
        var $row = jQuery(this);
        var $episodeBadge = $row.find('td:nth-child(2) span[style*="background: #673AB7"]');
        var hasPremiumIcon = $row.find('td:nth-child(9)').text().includes('👑');
        
        if ($episodeBadge.length && hasPremiumIcon) {
            if (!$episodeBadge.find('.episode-premium-badge').length) {
                $episodeBadge.append('<span class="episode-premium-badge">👑 PREMIUM</span>');
            }
        }
    });
}

// Call on page load and after updates
jQuery(document).ready(function() {
    setTimeout(addEpisodePremiumIndicators, 500);
});

jQuery(document).on('click', '#dooplay_anchor_reloadllist', function() {
    setTimeout(addEpisodePremiumIndicators, 1500);
});
