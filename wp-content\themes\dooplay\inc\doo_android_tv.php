<?php
/*
* Android TV Support for DooPlay Theme
* Adds comprehensive remote control support for the entire site
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class DooAndroidTV {
    
    public function __construct() {
        // Add hooks for Android TV support
        add_action('wp_footer', array($this, 'add_tv_support_script'));
        add_filter('body_class', array($this, 'add_tv_body_class'));
        
        // Add TV-focusable classes to various elements
        add_filter('nav_menu_link_attributes', array($this, 'add_tv_focus_to_menu'), 10, 4);
        add_filter('the_content', array($this, 'add_tv_focus_to_content'));
        add_action('wp_head', array($this, 'add_tv_meta_tags'));
    }
    
    /**
     * Add Android TV detection and body class
     */
    public function add_tv_body_class($classes) {
        // Add android-tv class if detected
        $classes[] = 'android-tv-ready';
        return $classes;
    }
    
    /**
     * Add TV-focusable class to navigation menu items
     */
    public function add_tv_focus_to_menu($atts, $item, $args, $depth) {
        $atts['class'] = isset($atts['class']) ? $atts['class'] . ' tv-focusable' : 'tv-focusable';
        $atts['tabindex'] = '0';
        return $atts;
    }
    
    /**
     * Add TV-focusable classes to content links and buttons
     */
    public function add_tv_focus_to_content($content) {
        // Add tv-focusable class to links and buttons in content
        $content = preg_replace('/<a\s+([^>]*?)>/i', '<a $1 class="tv-focusable" tabindex="0">', $content);
        $content = preg_replace('/<button\s+([^>]*?)>/i', '<button $1 class="tv-focusable" tabindex="0">', $content);
        return $content;
    }
    
    /**
     * Add meta tags for Android TV optimization
     */
    public function add_tv_meta_tags() {
        echo '<meta name="android-tv-compatible" content="true">' . "\n";
        echo '<meta name="tv-remote-control" content="enabled">' . "\n";
        echo '<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">' . "\n";
    }
    
    /**
     * Add JavaScript to automatically add TV-focusable classes
     */
    public function add_tv_support_script() {
        ?>
        <script>
        // Auto-add TV-focusable classes to existing elements
        document.addEventListener('DOMContentLoaded', function() {
            // Function to add TV-focusable class to elements
            function addTVFocusable() {
                // Movie/Series cards
                const items = document.querySelectorAll('.item:not(.tv-focusable)');
                items.forEach(item => {
                    item.classList.add('tv-focusable');
                    item.setAttribute('tabindex', '0');
                });
                
                // Poster links
                const posters = document.querySelectorAll('.poster a:not(.tv-focusable)');
                posters.forEach(poster => {
                    poster.classList.add('tv-focusable');
                    poster.setAttribute('tabindex', '0');
                });
                
                // Thumbnail links
                const thumbnails = document.querySelectorAll('.thumbnail a:not(.tv-focusable)');
                thumbnails.forEach(thumb => {
                    thumb.classList.add('tv-focusable');
                    thumb.setAttribute('tabindex', '0');
                });
                
                // Result items
                const results = document.querySelectorAll('.result-item:not(.tv-focusable)');
                results.forEach(result => {
                    result.classList.add('tv-focusable');
                    result.setAttribute('tabindex', '0');
                });
                
                // Navigation buttons
                const navBtns = document.querySelectorAll('.nav_items_module a:not(.tv-focusable)');
                navBtns.forEach(btn => {
                    btn.classList.add('tv-focusable');
                    btn.setAttribute('tabindex', '0');
                });
                
                // See all links
                const seeAllLinks = document.querySelectorAll('.see-all:not(.tv-focusable)');
                seeAllLinks.forEach(link => {
                    link.classList.add('tv-focusable');
                    link.setAttribute('tabindex', '0');
                });
                
                // Pagination links
                const pageLinks = document.querySelectorAll('.pagination a:not(.tv-focusable), .page-numbers:not(.tv-focusable)');
                pageLinks.forEach(link => {
                    link.classList.add('tv-focusable');
                    link.setAttribute('tabindex', '0');
                });
                
                // Form elements
                const inputs = document.querySelectorAll('input:not(.tv-focusable), select:not(.tv-focusable), textarea:not(.tv-focusable)');
                inputs.forEach(input => {
                    input.classList.add('tv-focusable');
                    if (!input.hasAttribute('tabindex')) {
                        input.setAttribute('tabindex', '0');
                    }
                });
                
                // Buttons
                const buttons = document.querySelectorAll('button:not(.tv-focusable), .btn:not(.tv-focusable), .button:not(.tv-focusable)');
                buttons.forEach(btn => {
                    btn.classList.add('tv-focusable');
                    btn.setAttribute('tabindex', '0');
                });
                
                // Player controls
                const videos = document.querySelectorAll('video:not(.tv-focusable)');
                videos.forEach(video => {
                    video.classList.add('tv-focusable');
                    video.setAttribute('tabindex', '0');
                });
                
                // Episode/Season lists
                const episodeLinks = document.querySelectorAll('.episode-list a:not(.tv-focusable), .season-list a:not(.tv-focusable)');
                episodeLinks.forEach(link => {
                    link.classList.add('tv-focusable');
                    link.setAttribute('tabindex', '0');
                });
                
                // Genre and filter links
                const filterLinks = document.querySelectorAll('.genre-list a:not(.tv-focusable), .year-filter a:not(.tv-focusable), .quality-filter a:not(.tv-focusable)');
                filterLinks.forEach(link => {
                    link.classList.add('tv-focusable');
                    link.setAttribute('tabindex', '0');
                });
                
                // Download and action buttons
                const actionBtns = document.querySelectorAll('.play-btn:not(.tv-focusable), .download-btn:not(.tv-focusable), .favorite-btn:not(.tv-focusable), .watchlist-btn:not(.tv-focusable)');
                actionBtns.forEach(btn => {
                    btn.classList.add('tv-focusable');
                    btn.setAttribute('tabindex', '0');
                });
            }
            
            // Run initially
            addTVFocusable();
            
            // Run again after AJAX content loads
            const observer = new MutationObserver(function(mutations) {
                let shouldUpdate = false;
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes.length > 0) {
                        shouldUpdate = true;
                    }
                });
                
                if (shouldUpdate) {
                    setTimeout(addTVFocusable, 100);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Handle AJAX events
            jQuery(document).ajaxComplete(function() {
                setTimeout(addTVFocusable, 200);
            });
        });
        </script>
        <?php
    }
    
    /**
     * Get TV-optimized image size
     */
    public static function get_tv_image_size($default_size = 'medium') {
        // Return larger image sizes for TV viewing
        $tv_sizes = array(
            'thumbnail' => 'medium',
            'medium' => 'large',
            'large' => 'full'
        );
        
        return isset($tv_sizes[$default_size]) ? $tv_sizes[$default_size] : $default_size;
    }
    
    /**
     * Check if current request is from Android TV
     */
    public static function is_android_tv() {
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
        
        return (
            stripos($user_agent, 'android') !== false &&
            (
                stripos($user_agent, 'tv') !== false ||
                stripos($user_agent, 'googletv') !== false ||
                stripos($user_agent, 'androidtv') !== false
            )
        );
    }
    
    /**
     * Add TV-specific attributes to elements
     */
    public static function tv_attributes($additional_classes = '') {
        $classes = 'tv-focusable ' . $additional_classes;
        return 'class="' . trim($classes) . '" tabindex="0"';
    }
}

// Initialize Android TV support
new DooAndroidTV();

// Helper functions for templates
function doo_tv_focusable($additional_classes = '') {
    return DooAndroidTV::tv_attributes($additional_classes);
}

function doo_is_android_tv() {
    return DooAndroidTV::is_android_tv();
}

function doo_tv_image_size($size = 'medium') {
    return DooAndroidTV::get_tv_image_size($size);
}
