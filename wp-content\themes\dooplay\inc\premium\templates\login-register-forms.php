<?php
/**
 * Premium Login & Register Forms
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Login_Register_Forms {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Add shortcodes
        add_shortcode('premium_login_form', array($this, 'login_form_shortcode'));
        add_shortcode('premium_register_form', array($this, 'register_form_shortcode'));
        add_shortcode('premium_account_page', array($this, 'account_page_shortcode'));
        
        // AJAX handlers
        add_action('wp_ajax_nopriv_premium_login', array($this, 'handle_login'));
        add_action('wp_ajax_nopriv_premium_register', array($this, 'handle_register'));
        add_action('wp_ajax_premium_update_account', array($this, 'handle_account_update'));
        
        // Enqueue scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Enqueue scripts
     */
    public function enqueue_scripts() {
        wp_enqueue_script('premium-auth-forms', get_template_directory_uri() . '/inc/premium/assets/js/auth-forms.js', array('jquery'), '1.0.0', true);
        
        wp_localize_script('premium-auth-forms', 'premiumAuth', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'login_nonce' => wp_create_nonce('premium_login'),
            'register_nonce' => wp_create_nonce('premium_register'),
            'account_nonce' => wp_create_nonce('premium_account_update'),
            'messages' => array(
                'login_success' => __('Login successful! Redirecting...', 'deshiflix'),
                'register_success' => __('Registration successful! Please check your email.', 'deshiflix'),
                'update_success' => __('Account updated successfully!', 'deshiflix'),
                'error' => __('An error occurred. Please try again.', 'deshiflix')
            )
        ));
    }
    
    /**
     * Login form shortcode
     */
    public function login_form_shortcode($atts) {
        if (is_user_logged_in()) {
            return '<p>' . __('You are already logged in.', 'deshiflix') . ' <a href="/premium-dashboard/">' . __('Go to Dashboard', 'deshiflix') . '</a></p>';
        }
        
        $atts = shortcode_atts(array(
            'redirect' => '',
            'show_register_link' => 'true'
        ), $atts);
        
        ob_start();
        ?>
        <div class="premium-login-form">
            <div class="form-container">
                <div class="form-header">
                    <h2><?php _e('Welcome Back!', 'deshiflix'); ?></h2>
                    <p><?php _e('Sign in to your account to continue', 'deshiflix'); ?></p>
                </div>
                
                <form id="premium-login-form" class="auth-form">
                    <div class="form-group">
                        <label for="login_username"><?php _e('Username or Email', 'deshiflix'); ?></label>
                        <input type="text" id="login_username" name="username" required>
                        <i class="fas fa-user form-icon"></i>
                    </div>
                    
                    <div class="form-group">
                        <label for="login_password"><?php _e('Password', 'deshiflix'); ?></label>
                        <input type="password" id="login_password" name="password" required>
                        <i class="fas fa-lock form-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('login_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="remember" value="1">
                            <span class="checkmark"></span>
                            <?php _e('Remember me', 'deshiflix'); ?>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-full">
                        <span class="btn-text"><?php _e('Sign In', 'deshiflix'); ?></span>
                        <span class="btn-loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i> <?php _e('Signing in...', 'deshiflix'); ?>
                        </span>
                    </button>
                    
                    <div class="form-links">
                        <a href="<?php echo wp_lostpassword_url(); ?>" class="forgot-password">
                            <?php _e('Forgot your password?', 'deshiflix'); ?>
                        </a>
                    </div>
                </form>
                
                <?php if ($atts['show_register_link'] === 'true'): ?>
                <div class="form-footer">
                    <p><?php _e("Don't have an account?", 'deshiflix'); ?> 
                       <a href="/register/" class="register-link"><?php _e('Sign up here', 'deshiflix'); ?></a>
                    </p>
                </div>
                <?php endif; ?>
                
                <div class="social-login">
                    <div class="divider">
                        <span><?php _e('Or continue with', 'deshiflix'); ?></span>
                    </div>
                    <div class="social-buttons">
                        <button class="btn btn-social btn-google">
                            <i class="fab fa-google"></i>
                            <?php _e('Google', 'deshiflix'); ?>
                        </button>
                        <button class="btn btn-social btn-facebook">
                            <i class="fab fa-facebook-f"></i>
                            <?php _e('Facebook', 'deshiflix'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Register form shortcode
     */
    public function register_form_shortcode($atts) {
        if (is_user_logged_in()) {
            return '<p>' . __('You are already registered and logged in.', 'deshiflix') . ' <a href="/premium-dashboard/">' . __('Go to Dashboard', 'deshiflix') . '</a></p>';
        }
        
        $atts = shortcode_atts(array(
            'show_login_link' => 'true'
        ), $atts);
        
        ob_start();
        ?>
        <div class="premium-register-form">
            <div class="form-container">
                <div class="form-header">
                    <h2><?php _e('Join DeshiFlix!', 'deshiflix'); ?></h2>
                    <p><?php _e('Create your account and start watching', 'deshiflix'); ?></p>
                </div>
                
                <form id="premium-register-form" class="auth-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="register_first_name"><?php _e('First Name', 'deshiflix'); ?></label>
                            <input type="text" id="register_first_name" name="first_name" required>
                            <i class="fas fa-user form-icon"></i>
                        </div>
                        
                        <div class="form-group">
                            <label for="register_last_name"><?php _e('Last Name', 'deshiflix'); ?></label>
                            <input type="text" id="register_last_name" name="last_name" required>
                            <i class="fas fa-user form-icon"></i>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="register_username"><?php _e('Username', 'deshiflix'); ?></label>
                        <input type="text" id="register_username" name="username" required>
                        <i class="fas fa-at form-icon"></i>
                        <small class="form-help"><?php _e('Choose a unique username', 'deshiflix'); ?></small>
                    </div>
                    
                    <div class="form-group">
                        <label for="register_email"><?php _e('Email Address', 'deshiflix'); ?></label>
                        <input type="email" id="register_email" name="email" required>
                        <i class="fas fa-envelope form-icon"></i>
                    </div>
                    
                    <div class="form-group">
                        <label for="register_password"><?php _e('Password', 'deshiflix'); ?></label>
                        <input type="password" id="register_password" name="password" required>
                        <i class="fas fa-lock form-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('register_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <div class="password-strength">
                            <div class="strength-meter">
                                <div class="strength-bar"></div>
                            </div>
                            <small class="strength-text"><?php _e('Password strength', 'deshiflix'); ?></small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="register_confirm_password"><?php _e('Confirm Password', 'deshiflix'); ?></label>
                        <input type="password" id="register_confirm_password" name="confirm_password" required>
                        <i class="fas fa-lock form-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('register_confirm_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    
                    <div class="form-group">
                        <label for="register_phone"><?php _e('Phone Number (Optional)', 'deshiflix'); ?></label>
                        <input type="tel" id="register_phone" name="phone">
                        <i class="fas fa-phone form-icon"></i>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="terms" value="1" required>
                            <span class="checkmark"></span>
                            <?php printf(__('I agree to the %s and %s', 'deshiflix'), 
                                '<a href="/terms-of-service/" target="_blank">Terms of Service</a>',
                                '<a href="/privacy-policy/" target="_blank">Privacy Policy</a>'
                            ); ?>
                        </label>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="newsletter" value="1">
                            <span class="checkmark"></span>
                            <?php _e('Subscribe to our newsletter for updates and offers', 'deshiflix'); ?>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-full">
                        <span class="btn-text"><?php _e('Create Account', 'deshiflix'); ?></span>
                        <span class="btn-loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i> <?php _e('Creating account...', 'deshiflix'); ?>
                        </span>
                    </button>
                </form>
                
                <?php if ($atts['show_login_link'] === 'true'): ?>
                <div class="form-footer">
                    <p><?php _e('Already have an account?', 'deshiflix'); ?> 
                       <a href="/login/" class="login-link"><?php _e('Sign in here', 'deshiflix'); ?></a>
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Account page shortcode
     */
    public function account_page_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please login to view your account.', 'deshiflix') . ' <a href="/login/">' . __('Login here', 'deshiflix') . '</a></p>';
        }
        
        $current_user = wp_get_current_user();
        
        ob_start();
        ?>
        <div class="premium-account-page">
            <div class="account-header">
                <h2><?php _e('My Account', 'deshiflix'); ?></h2>
                <p><?php _e('Manage your account settings and preferences', 'deshiflix'); ?></p>
            </div>
            
            <div class="account-tabs">
                <ul class="tab-nav">
                    <li class="active"><a href="#profile" data-tab="profile"><?php _e('Profile', 'deshiflix'); ?></a></li>
                    <li><a href="#security" data-tab="security"><?php _e('Security', 'deshiflix'); ?></a></li>
                    <li><a href="#preferences" data-tab="preferences"><?php _e('Preferences', 'deshiflix'); ?></a></li>
                </ul>
                
                <div class="tab-content">
                    <div id="profile" class="tab-pane active">
                        <form id="profile-update-form" class="account-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="first_name"><?php _e('First Name', 'deshiflix'); ?></label>
                                    <input type="text" id="first_name" name="first_name" 
                                           value="<?php echo esc_attr(get_user_meta($current_user->ID, 'first_name', true)); ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="last_name"><?php _e('Last Name', 'deshiflix'); ?></label>
                                    <input type="text" id="last_name" name="last_name" 
                                           value="<?php echo esc_attr(get_user_meta($current_user->ID, 'last_name', true)); ?>">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="display_name"><?php _e('Display Name', 'deshiflix'); ?></label>
                                <input type="text" id="display_name" name="display_name" 
                                       value="<?php echo esc_attr($current_user->display_name); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="user_email"><?php _e('Email Address', 'deshiflix'); ?></label>
                                <input type="email" id="user_email" name="user_email" 
                                       value="<?php echo esc_attr($current_user->user_email); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="description"><?php _e('Bio', 'deshiflix'); ?></label>
                                <textarea id="description" name="description" rows="4"><?php echo esc_textarea(get_user_meta($current_user->ID, 'description', true)); ?></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <?php _e('Update Profile', 'deshiflix'); ?>
                            </button>
                        </form>
                    </div>
                    
                    <div id="security" class="tab-pane">
                        <form id="password-change-form" class="account-form">
                            <div class="form-group">
                                <label for="current_password"><?php _e('Current Password', 'deshiflix'); ?></label>
                                <input type="password" id="current_password" name="current_password" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="new_password"><?php _e('New Password', 'deshiflix'); ?></label>
                                <input type="password" id="new_password" name="new_password" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_new_password"><?php _e('Confirm New Password', 'deshiflix'); ?></label>
                                <input type="password" id="confirm_new_password" name="confirm_new_password" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <?php _e('Change Password', 'deshiflix'); ?>
                            </button>
                        </form>
                    </div>
                    
                    <div id="preferences" class="tab-pane">
                        <form id="preferences-form" class="account-form">
                            <div class="form-group">
                                <label><?php _e('Email Notifications', 'deshiflix'); ?></label>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="email_new_content" value="1" checked>
                                        <span class="checkmark"></span>
                                        <?php _e('New content notifications', 'deshiflix'); ?>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="email_promotions" value="1">
                                        <span class="checkmark"></span>
                                        <?php _e('Promotional offers', 'deshiflix'); ?>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="preferred_language"><?php _e('Preferred Language', 'deshiflix'); ?></label>
                                <select id="preferred_language" name="preferred_language">
                                    <option value="bn">বাংলা</option>
                                    <option value="en">English</option>
                                    <option value="hi">हिन्दी</option>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <?php _e('Save Preferences', 'deshiflix'); ?>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Handle login
     */
    public function handle_login() {
        if (!wp_verify_nonce($_POST['nonce'], 'premium_login')) {
            wp_send_json_error('Security check failed');
        }
        
        $username = sanitize_text_field($_POST['username']);
        $password = $_POST['password'];
        $remember = isset($_POST['remember']) ? true : false;
        
        $user = wp_authenticate($username, $password);
        
        if (is_wp_error($user)) {
            wp_send_json_error($user->get_error_message());
        }
        
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID, $remember);
        
        wp_send_json_success(array(
            'message' => __('Login successful!', 'deshiflix'),
            'redirect' => '/premium-dashboard/'
        ));
    }
    
    /**
     * Handle registration
     */
    public function handle_register() {
        if (!wp_verify_nonce($_POST['nonce'], 'premium_register')) {
            wp_send_json_error('Security check failed');
        }
        
        $username = sanitize_text_field($_POST['username']);
        $email = sanitize_email($_POST['email']);
        $password = $_POST['password'];
        $first_name = sanitize_text_field($_POST['first_name']);
        $last_name = sanitize_text_field($_POST['last_name']);
        
        // Validate
        if (username_exists($username)) {
            wp_send_json_error(__('Username already exists', 'deshiflix'));
        }
        
        if (email_exists($email)) {
            wp_send_json_error(__('Email already registered', 'deshiflix'));
        }
        
        // Create user
        $user_id = wp_create_user($username, $password, $email);
        
        if (is_wp_error($user_id)) {
            wp_send_json_error($user_id->get_error_message());
        }
        
        // Update user meta
        update_user_meta($user_id, 'first_name', $first_name);
        update_user_meta($user_id, 'last_name', $last_name);
        
        // Process referral if exists
        if (isset($_POST['referral_code'])) {
            $referral_code = sanitize_text_field($_POST['referral_code']);
            // Handle referral logic here
        }
        
        wp_send_json_success(array(
            'message' => __('Registration successful!', 'deshiflix'),
            'redirect' => '/login/'
        ));
    }
    
    /**
     * Handle account update
     */
    public function handle_account_update() {
        if (!wp_verify_nonce($_POST['nonce'], 'premium_account_update')) {
            wp_send_json_error('Security check failed');
        }
        
        if (!is_user_logged_in()) {
            wp_send_json_error('User not logged in');
        }
        
        $user_id = get_current_user_id();
        
        // Update user data
        $user_data = array(
            'ID' => $user_id,
            'display_name' => sanitize_text_field($_POST['display_name']),
            'user_email' => sanitize_email($_POST['user_email'])
        );
        
        wp_update_user($user_data);
        
        // Update user meta
        update_user_meta($user_id, 'first_name', sanitize_text_field($_POST['first_name']));
        update_user_meta($user_id, 'last_name', sanitize_text_field($_POST['last_name']));
        update_user_meta($user_id, 'description', sanitize_textarea_field($_POST['description']));
        
        wp_send_json_success(array(
            'message' => __('Account updated successfully!', 'deshiflix')
        ));
    }
}

// Initialize login/register forms
DeshiFlix_Login_Register_Forms::get_instance();
