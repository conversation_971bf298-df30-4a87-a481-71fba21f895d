<?php
/*
* Android TV Optimized Live TV Player Template
* Enhanced for remote control navigation and TV viewing experience
*/

// Get channel data
$channel_slug = isset($_GET['channel']) ? sanitize_text_field($_GET['channel']) : '';
if (empty($channel_slug)) {
    wp_redirect(get_permalink(76));
    exit;
}

global $wpdb;
$table_channels = $wpdb->prefix . 'doo_livetv_channels';
$table_categories = $wpdb->prefix . 'doo_livetv_categories';

$channel = $wpdb->get_row($wpdb->prepare(
    "SELECT c.*, cat.name as category_name, cat.color as category_color
     FROM $table_channels c
     LEFT JOIN $table_categories cat ON c.category_id = cat.id
     WHERE c.slug = %s AND c.status = 'active'",
    $channel_slug
));

if (!$channel) {
    wp_redirect(get_permalink(76));
    exit;
}

// Update view count
$wpdb->query($wpdb->prepare(
    "UPDATE $table_channels SET views = views + 1 WHERE id = %d",
    $channel->id
));

// Get related channels
$related_channels = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM $table_channels 
     WHERE category_id = %d AND id != %d AND status = 'active' 
     ORDER BY views DESC LIMIT 6",
    $channel->category_id,
    $channel->id
));
?>

<!-- Android TV Optimized Player -->
<div class="live-tv-player-container android-tv-player">
    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb tv-safe-area">
        <a href="<?php echo get_permalink(76); ?>" class="tv-focusable">
            <i class="fas fa-tv"></i> Live TV
        </a>
        <span class="separator">›</span>
        <?php if ($channel->category_name): ?>
            <a href="<?php echo add_query_arg('category', $channel->category_id, get_permalink(76)); ?>" class="tv-focusable">
                <?php echo esc_html($channel->category_name); ?>
            </a>
            <span class="separator">›</span>
        <?php endif; ?>
        <span class="current"><?php echo esc_html($channel->name); ?></span>
    </div>

    <!-- Main Player Section -->
    <div class="player-section tv-safe-area">
        <div class="player-wrapper tv-focusable" tabindex="0">
            <div class="video-player tv-mode" id="tv-video-player">
                <!-- Loading State -->
                <div class="player-loading" id="player-loading">
                    <div class="loading-spinner"></div>
                    <p class="loading-text">Loading channel...</p>
                </div>

                <!-- Video Element -->
                <video id="video-element" 
                       class="tv-video" 
                       controls 
                       autoplay 
                       muted
                       data-channel-id="<?php echo $channel->id; ?>"
                       data-channel-name="<?php echo esc_attr($channel->name); ?>">
                    <source src="<?php echo esc_url($channel->stream_url); ?>" type="application/x-mpegURL">
                    <?php if ($channel->backup_url): ?>
                        <source src="<?php echo esc_url($channel->backup_url); ?>" type="application/x-mpegURL">
                    <?php endif; ?>
                    Your browser does not support the video tag.
                </video>

                <!-- TV-Optimized Controls Overlay -->
                <div class="player-controls-overlay tv-mode" id="tv-controls">
                    <div class="control-buttons">
                        <button class="control-btn tv-focusable" id="play-pause-btn" title="Play/Pause (Space)">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="control-btn tv-focusable" id="mute-btn" title="Mute (M)">
                            <i class="fas fa-volume-up"></i>
                        </button>
                        <button class="control-btn tv-focusable" id="fullscreen-btn" title="Fullscreen (F)">
                            <i class="fas fa-expand"></i>
                        </button>
                        <div class="quality-selector">
                            <button class="control-btn tv-focusable" id="quality-btn" title="Quality">
                                <i class="fas fa-cog"></i>
                            </button>
                            <div class="quality-menu" id="quality-menu">
                                <div class="quality-option active" data-quality="auto">Auto</div>
                                <div class="quality-option" data-quality="1080p">1080p</div>
                                <div class="quality-option" data-quality="720p">720p</div>
                                <div class="quality-option" data-quality="480p">480p</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- TV Remote Control Hints -->
                    <div class="tv-control-hints">
                        <span><i class="fas fa-play"></i> Space: Play/Pause</span>
                        <span><i class="fas fa-volume-up"></i> M: Mute</span>
                        <span><i class="fas fa-expand"></i> F: Fullscreen</span>
                        <span><i class="fas fa-arrow-left"></i> Back: Return</span>
                    </div>
                </div>

                <!-- Error State -->
                <div class="player-error" id="player-error" style="display: none;">
                    <div class="error-content">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Stream Error</h3>
                        <p>Unable to load the stream. Please try again.</p>
                        <button class="btn btn-primary tv-focusable" id="retry-btn">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                        <button class="btn btn-secondary tv-focusable" id="back-btn">
                            <i class="fas fa-arrow-left"></i> Go Back
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Channel Information Bar -->
        <div class="channel-info-bar tv-safe-area">
            <div class="channel-details">
                <?php if ($channel->logo_url): ?>
                    <img src="<?php echo esc_url($channel->logo_url); ?>" 
                         alt="<?php echo esc_attr($channel->name); ?>" 
                         class="channel-logo-small">
                <?php else: ?>
                    <div class="channel-logo-placeholder-small">
                        <i class="fas fa-tv"></i>
                    </div>
                <?php endif; ?>
                
                <div class="channel-meta">
                    <h1 class="channel-title"><?php echo esc_html($channel->name); ?></h1>
                    
                    <div class="channel-tags">
                        <?php if ($channel->category_name): ?>
                            <span class="tag category-tag" style="background-color: <?php echo esc_attr($channel->category_color ?: '#007cba'); ?>">
                                <i class="fas fa-tag"></i>
                                <?php echo esc_html($channel->category_name); ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($channel->quality): ?>
                            <span class="tag quality-tag quality-<?php echo strtolower($channel->quality); ?>">
                                <i class="fas fa-hd-video"></i>
                                <?php echo esc_html($channel->quality); ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($channel->country): ?>
                            <span class="tag country-tag">
                                <i class="fas fa-globe"></i>
                                <?php echo esc_html($channel->country); ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($channel->language): ?>
                            <span class="tag language-tag">
                                <i class="fas fa-language"></i>
                                <?php echo esc_html($channel->language); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($channel->description): ?>
                        <p class="channel-description"><?php echo esc_html($channel->description); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="channel-actions">
                <div class="viewer-stats">
                    <i class="fas fa-eye"></i>
                    <span><?php echo number_format($channel->views); ?> viewers</span>
                </div>
                
                <button class="btn btn-secondary tv-focusable favorite-btn" 
                        data-channel-id="<?php echo $channel->id; ?>"
                        title="Add to Favorites">
                    <i class="fas fa-heart"></i>
                    Favorite
                </button>
                
                <button class="btn btn-secondary tv-focusable share-btn" 
                        data-channel-name="<?php echo esc_attr($channel->name); ?>"
                        title="Share Channel">
                    <i class="fas fa-share"></i>
                    Share
                </button>
            </div>
        </div>
    </div>

    <!-- Related Channels Section -->
    <?php if (!empty($related_channels)): ?>
    <div class="related-channels tv-safe-area">
        <h3>Related Channels</h3>
        <div class="related-grid">
            <?php foreach ($related_channels as $related): ?>
                <div class="related-channel">
                    <a href="<?php echo esc_url(add_query_arg('channel', $related->slug, get_permalink(76))); ?>" 
                       class="related-link tv-focusable"
                       data-channel-name="<?php echo esc_attr($related->name); ?>">
                        <?php if ($related->logo_url): ?>
                            <img src="<?php echo esc_url($related->logo_url); ?>" 
                                 alt="<?php echo esc_attr($related->name); ?>" 
                                 class="related-logo">
                        <?php else: ?>
                            <div class="related-logo-placeholder">
                                <i class="fas fa-tv"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="related-info">
                            <h4><?php echo esc_html($related->name); ?></h4>
                            <?php if ($related->country): ?>
                                <span class="related-country"><?php echo esc_html($related->country); ?></span>
                            <?php endif; ?>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- TV-Specific JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize TV-optimized player
    if (window.AndroidTV && window.AndroidTV.isAndroidTV) {
        initTVPlayer();
    }
    
    function initTVPlayer() {
        var video = document.getElementById('video-element');
        var loading = document.getElementById('player-loading');
        var error = document.getElementById('player-error');
        
        // Auto-hide loading after timeout
        setTimeout(function() {
            if (loading.style.display !== 'none') {
                loading.style.display = 'none';
                if (video.readyState < 2) {
                    showError();
                }
            }
        }, 10000);
        
        // Video events
        video.addEventListener('loadstart', function() {
            loading.style.display = 'flex';
            error.style.display = 'none';
        });
        
        video.addEventListener('canplay', function() {
            loading.style.display = 'none';
        });
        
        video.addEventListener('error', function() {
            showError();
        });
        
        // Control button events
        document.getElementById('retry-btn').addEventListener('click', function() {
            video.load();
            error.style.display = 'none';
            loading.style.display = 'flex';
        });
        
        document.getElementById('back-btn').addEventListener('click', function() {
            window.history.back();
        });
        
        function showError() {
            loading.style.display = 'none';
            error.style.display = 'flex';
        }
    }
});
</script>
