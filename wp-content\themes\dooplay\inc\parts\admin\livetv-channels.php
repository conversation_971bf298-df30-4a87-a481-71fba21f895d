<?php
// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

global $wpdb;
$livetv = new DooLiveTV();

// Handle form submissions
if (isset($_POST['action'])) {
    if ($_POST['action'] === 'bulk_action' && isset($_POST['bulk_action']) && isset($_POST['channel_ids'])) {
        $action = sanitize_text_field($_POST['bulk_action']);
        $channel_ids = array_map('intval', $_POST['channel_ids']);
        
        if (!empty($channel_ids)) {
            $table_channels = $wpdb->prefix . 'doo_livetv_channels';
            $placeholders = implode(',', array_fill(0, count($channel_ids), '%d'));
            
            switch ($action) {
                case 'delete':
                    $wpdb->query($wpdb->prepare(
                        "DELETE FROM $table_channels WHERE id IN ($placeholders)",
                        $channel_ids
                    ));
                    echo '<div class="notice notice-success"><p>Selected channels deleted successfully.</p></div>';
                    break;
                    
                case 'activate':
                    $wpdb->query($wpdb->prepare(
                        "UPDATE $table_channels SET status = 'active' WHERE id IN ($placeholders)",
                        $channel_ids
                    ));
                    echo '<div class="notice notice-success"><p>Selected channels activated successfully.</p></div>';
                    break;
                    
                case 'deactivate':
                    $wpdb->query($wpdb->prepare(
                        "UPDATE $table_channels SET status = 'inactive' WHERE id IN ($placeholders)",
                        $channel_ids
                    ));
                    echo '<div class="notice notice-success"><p>Selected channels deactivated successfully.</p></div>';
                    break;
            }
        }
    }
}

// Get filter parameters
$category_filter = isset($_GET['category']) ? intval($_GET['category']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';

// Pagination
$per_page = 20;
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$offset = ($current_page - 1) * $per_page;

// Build query
$where_conditions = array();
$query_params = array();

if ($category_filter) {
    $where_conditions[] = "c.category_id = %d";
    $query_params[] = $category_filter;
}

if ($status_filter) {
    $where_conditions[] = "c.status = %s";
    $query_params[] = $status_filter;
}

if ($search) {
    $where_conditions[] = "c.name LIKE %s";
    $query_params[] = '%' . $wpdb->esc_like($search) . '%';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count
$count_sql = "SELECT COUNT(*) FROM {$wpdb->prefix}doo_livetv_channels c $where_clause";
$total_items = $wpdb->get_var(!empty($query_params) ? $wpdb->prepare($count_sql, $query_params) : $count_sql);

// Get channels
$sql = "SELECT c.*, cat.name as category_name, cat.color as category_color 
        FROM {$wpdb->prefix}doo_livetv_channels c 
        LEFT JOIN {$wpdb->prefix}doo_livetv_categories cat ON c.category_id = cat.id 
        $where_clause 
        ORDER BY c.sort_order ASC, c.name ASC 
        LIMIT %d OFFSET %d";

$query_params[] = $per_page;
$query_params[] = $offset;

$channels = $wpdb->get_results($wpdb->prepare($sql, $query_params));

// Get categories for filter
$categories = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}doo_livetv_categories WHERE status = 'active' ORDER BY name ASC");

// Pagination
$total_pages = ceil($total_items / $per_page);
?>

<div class="wrap">
    <h1 class="wp-heading-inline">Live TV Channels</h1>
    <a href="<?php echo admin_url('admin.php?page=doo-livetv-add'); ?>" class="page-title-action">Add New Channel</a>
    <a href="<?php echo admin_url('admin.php?page=doo-livetv-import'); ?>" class="page-title-action">Import Playlist</a>
    
    <hr class="wp-header-end">
    
    <!-- Filters -->
    <div class="tablenav top">
        <div class="alignleft actions">
            <form method="get" action="">
                <input type="hidden" name="page" value="doo-livetv">
                
                <select name="category">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category->id; ?>" <?php selected($category_filter, $category->id); ?>>
                            <?php echo esc_html($category->name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                
                <select name="status">
                    <option value="">All Statuses</option>
                    <option value="active" <?php selected($status_filter, 'active'); ?>>Active</option>
                    <option value="inactive" <?php selected($status_filter, 'inactive'); ?>>Inactive</option>
                </select>
                
                <input type="search" name="s" value="<?php echo esc_attr($search); ?>" placeholder="Search channels...">
                <input type="submit" class="button" value="Filter">
                
                <?php if ($category_filter || $status_filter || $search): ?>
                    <a href="<?php echo admin_url('admin.php?page=doo-livetv'); ?>" class="button">Clear Filters</a>
                <?php endif; ?>
            </form>
        </div>
        
        <div class="tablenav-pages">
            <?php if ($total_pages > 1): ?>
                <span class="displaying-num"><?php echo $total_items; ?> items</span>
                <?php
                $page_links = paginate_links(array(
                    'base' => add_query_arg('paged', '%#%'),
                    'format' => '',
                    'prev_text' => '&laquo;',
                    'next_text' => '&raquo;',
                    'total' => $total_pages,
                    'current' => $current_page,
                    'type' => 'plain'
                ));
                echo $page_links;
                ?>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Bulk Actions Form -->
    <form method="post" id="channels-form">
        <div class="tablenav top">
            <div class="alignleft actions bulkactions">
                <select name="bulk_action">
                    <option value="">Bulk Actions</option>
                    <option value="activate">Activate</option>
                    <option value="deactivate">Deactivate</option>
                    <option value="delete">Delete</option>
                </select>
                <input type="submit" class="button action" value="Apply">
            </div>
        </div>
        
        <input type="hidden" name="action" value="bulk_action">
        
        <!-- Channels Table -->
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <td class="manage-column column-cb check-column">
                        <input type="checkbox" id="cb-select-all">
                    </td>
                    <th class="manage-column">Logo</th>
                    <th class="manage-column">Channel Name</th>
                    <th class="manage-column">Category</th>
                    <th class="manage-column">Country</th>
                    <th class="manage-column">Quality</th>
                    <th class="manage-column">Views</th>
                    <th class="manage-column">Status</th>
                    <th class="manage-column">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($channels)): ?>
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 20px;">
                            <p>No channels found.</p>
                            <a href="<?php echo admin_url('admin.php?page=doo-livetv-add'); ?>" class="button button-primary">Add Your First Channel</a>
                            <a href="<?php echo admin_url('admin.php?page=doo-livetv-import'); ?>" class="button">Import from Playlist</a>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($channels as $channel): ?>
                        <tr>
                            <th class="check-column">
                                <input type="checkbox" name="channel_ids[]" value="<?php echo $channel->id; ?>">
                            </th>
                            <td>
                                <?php if ($channel->logo_url): ?>
                                    <img src="<?php echo esc_url($channel->logo_url); ?>" 
                                         alt="<?php echo esc_attr($channel->name); ?>" 
                                         style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                                <?php else: ?>
                                    <div style="width: 40px; height: 40px; background: #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                                        No Logo
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?php echo esc_html($channel->name); ?></strong>
                                <div class="row-actions">
                                    <span class="edit">
                                        <a href="<?php echo admin_url('admin.php?page=doo-livetv-add&edit=' . $channel->id); ?>">Edit</a> |
                                    </span>
                                    <span class="view">
                                        <a href="<?php echo home_url('/live-tv/?channel=' . $channel->slug); ?>" target="_blank">View</a> |
                                    </span>
                                    <span class="delete">
                                        <a href="#" class="delete-channel" data-id="<?php echo $channel->id; ?>" style="color: #a00;">Delete</a>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <?php if ($channel->category_name): ?>
                                    <span class="category-badge" style="background-color: <?php echo esc_attr($channel->category_color); ?>; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px;">
                                        <?php echo esc_html($channel->category_name); ?>
                                    </span>
                                <?php else: ?>
                                    <span style="color: #666;">Uncategorized</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html($channel->country ?: '—'); ?></td>
                            <td>
                                <span class="quality-badge <?php echo strtolower($channel->quality); ?>">
                                    <?php echo esc_html($channel->quality ?: 'SD'); ?>
                                </span>
                            </td>
                            <td><?php echo number_format($channel->views); ?></td>
                            <td>
                                <span class="status-<?php echo $channel->status; ?>">
                                    <?php echo ucfirst($channel->status); ?>
                                </span>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=doo-livetv-add&edit=' . $channel->id); ?>" class="button button-small">Edit</a>
                                <a href="#" class="button button-small delete-channel" data-id="<?php echo $channel->id; ?>">Delete</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </form>
    
    <!-- Bottom pagination -->
    <div class="tablenav bottom">
        <div class="tablenav-pages">
            <?php if ($total_pages > 1): ?>
                <span class="displaying-num"><?php echo $total_items; ?> items</span>
                <?php echo $page_links; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.category-badge {
    display: inline-block;
    font-weight: 500;
}

.quality-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

.quality-badge.hd {
    background-color: #28a745;
    color: white;
}

.quality-badge.sd {
    background-color: #ffc107;
    color: #212529;
}

.quality-badge.fhd {
    background-color: #007bff;
    color: white;
}

.status-active {
    color: #28a745;
    font-weight: 500;
}

.status-inactive {
    color: #dc3545;
    font-weight: 500;
}

.wp-list-table img {
    border: 1px solid #ddd;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Select all checkbox
    $('#cb-select-all').on('change', function() {
        $('input[name="channel_ids[]"]').prop('checked', this.checked);
    });
    
    // Delete channel
    $('.delete-channel').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm('Are you sure you want to delete this channel?')) {
            return;
        }
        
        var channelId = $(this).data('id');
        var row = $(this).closest('tr');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'doo_livetv_delete_channel',
                channel_id: channelId,
                nonce: '<?php echo wp_create_nonce('doo_livetv_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    row.fadeOut(function() {
                        $(this).remove();
                    });
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
            }
        });
    });
});
</script>
