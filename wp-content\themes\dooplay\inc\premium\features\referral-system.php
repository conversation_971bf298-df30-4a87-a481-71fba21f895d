<?php
/**
 * Premium Referral & Reward System
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Referral_System {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // User registration hooks
        add_action('user_register', array($this, 'process_referral_signup'));
        add_action('deshiflix_premium_activated', array($this, 'process_referral_reward'));
        
        // AJAX handlers
        add_action('wp_ajax_generate_referral_code', array($this, 'generate_referral_code'));
        add_action('wp_ajax_get_referral_stats', array($this, 'get_referral_stats'));
        
        // Shortcodes
        add_shortcode('referral_dashboard', array($this, 'referral_dashboard_shortcode'));
        add_shortcode('referral_link', array($this, 'referral_link_shortcode'));
        
        // URL parameter handling
        add_action('init', array($this, 'handle_referral_link'));
    }
    
    /**
     * Handle referral link
     */
    public function handle_referral_link() {
        if (isset($_GET['ref']) && !is_user_logged_in()) {
            $referral_code = sanitize_text_field($_GET['ref']);
            
            // Store referral code in session/cookie
            if (!session_id()) {
                session_start();
            }
            $_SESSION['referral_code'] = $referral_code;
            
            // Also store in cookie for 30 days
            setcookie('deshiflix_referral', $referral_code, time() + (30 * 24 * 60 * 60), '/');
        }
    }
    
    /**
     * Process referral signup
     */
    public function process_referral_signup($user_id) {
        $referral_code = null;
        
        // Check session first
        if (session_id() && isset($_SESSION['referral_code'])) {
            $referral_code = $_SESSION['referral_code'];
        }
        // Then check cookie
        elseif (isset($_COOKIE['deshiflix_referral'])) {
            $referral_code = $_COOKIE['deshiflix_referral'];
        }
        
        if ($referral_code) {
            $referrer_id = $this->get_user_by_referral_code($referral_code);
            
            if ($referrer_id && $referrer_id != $user_id) {
                // Store referral relationship
                update_user_meta($user_id, '_referred_by', $referrer_id);
                update_user_meta($user_id, '_referral_code_used', $referral_code);
                
                // Track referral
                $this->track_referral($referrer_id, $user_id, 'signup');
                
                // Give signup bonus to referrer
                $this->give_referral_bonus($referrer_id, 'signup', array(
                    'referred_user' => $user_id,
                    'bonus_days' => 3 // 3 days free
                ));
                
                // Clean up
                unset($_SESSION['referral_code']);
                setcookie('deshiflix_referral', '', time() - 3600, '/');
            }
        }
    }
    
    /**
     * Process referral reward when referred user buys premium
     */
    public function process_referral_reward($user_id) {
        $referrer_id = get_user_meta($user_id, '_referred_by', true);
        
        if ($referrer_id) {
            // Give premium purchase bonus to referrer
            $this->give_referral_bonus($referrer_id, 'premium_purchase', array(
                'referred_user' => $user_id,
                'bonus_days' => 7, // 7 days free
                'cash_reward' => 50 // 50 Taka credit
            ));
            
            // Track referral conversion
            $this->track_referral($referrer_id, $user_id, 'premium_purchase');
        }
    }
    
    /**
     * Generate referral code
     */
    public function generate_referral_code() {
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            wp_send_json_error('User not logged in');
        }
        
        // Check if user already has a referral code
        $existing_code = get_user_meta($user_id, '_referral_code', true);
        
        if (!$existing_code) {
            // Generate unique code
            $code = $this->create_unique_referral_code($user_id);
            update_user_meta($user_id, '_referral_code', $code);
        } else {
            $code = $existing_code;
        }
        
        $referral_link = home_url('/?ref=' . $code);
        
        wp_send_json_success(array(
            'code' => $code,
            'link' => $referral_link
        ));
    }
    
    /**
     * Get referral stats
     */
    public function get_referral_stats() {
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            wp_send_json_error('User not logged in');
        }
        
        global $wpdb;
        $table_referrals = $wpdb->prefix . 'deshiflix_referrals';
        
        // Create table if not exists
        $this->maybe_create_referrals_table();
        
        $stats = $wpdb->get_row($wpdb->prepare(
            "SELECT 
                COUNT(*) as total_referrals,
                SUM(CASE WHEN type = 'premium_purchase' THEN 1 ELSE 0 END) as premium_conversions,
                SUM(bonus_days) as total_bonus_days,
                SUM(cash_reward) as total_cash_rewards
             FROM $table_referrals 
             WHERE referrer_id = %d",
            $user_id
        ));
        
        $recent_referrals = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_referrals WHERE referrer_id = %d ORDER BY created_at DESC LIMIT 10",
            $user_id
        ));
        
        wp_send_json_success(array(
            'stats' => $stats,
            'recent_referrals' => $recent_referrals,
            'referral_code' => get_user_meta($user_id, '_referral_code', true)
        ));
    }
    
    /**
     * Create unique referral code
     */
    private function create_unique_referral_code($user_id) {
        $user = get_user_by('ID', $user_id);
        $base_code = strtoupper(substr($user->user_login, 0, 3) . substr(md5($user_id), 0, 5));
        
        // Ensure uniqueness
        $code = $base_code;
        $counter = 1;
        
        while ($this->get_user_by_referral_code($code)) {
            $code = $base_code . $counter;
            $counter++;
        }
        
        return $code;
    }
    
    /**
     * Get user by referral code
     */
    private function get_user_by_referral_code($code) {
        global $wpdb;
        
        $user_id = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM {$wpdb->usermeta} WHERE meta_key = '_referral_code' AND meta_value = %s",
            $code
        ));
        
        return $user_id ? intval($user_id) : false;
    }
    
    /**
     * Give referral bonus
     */
    private function give_referral_bonus($referrer_id, $type, $data) {
        // Extend premium subscription
        if (isset($data['bonus_days']) && $data['bonus_days'] > 0) {
            $this->extend_premium_subscription($referrer_id, $data['bonus_days']);
        }
        
        // Add cash reward
        if (isset($data['cash_reward']) && $data['cash_reward'] > 0) {
            $current_balance = get_user_meta($referrer_id, '_cash_balance', true) ?: 0;
            update_user_meta($referrer_id, '_cash_balance', $current_balance + $data['cash_reward']);
        }
        
        // Track the reward
        global $wpdb;
        $table_referrals = $wpdb->prefix . 'deshiflix_referrals';
        
        $wpdb->insert(
            $table_referrals,
            array(
                'referrer_id' => $referrer_id,
                'referred_user_id' => $data['referred_user'],
                'type' => $type,
                'bonus_days' => $data['bonus_days'] ?? 0,
                'cash_reward' => $data['cash_reward'] ?? 0,
                'status' => 'completed',
                'created_at' => current_time('mysql')
            )
        );
        
        // Send notification
        if (class_exists('DeshiFlix_Premium_Notifications')) {
            $notifications = DeshiFlix_Premium_Notifications::get_instance();
            $referred_user = get_user_by('ID', $data['referred_user']);
            
            $message = $type === 'signup' 
                ? "🎉 {$referred_user->display_name} আপনার referral link দিয়ে signup করেছে! আপনি {$data['bonus_days']} দিন free premium পেয়েছেন।"
                : "💰 {$referred_user->display_name} premium কিনেছে! আপনি {$data['bonus_days']} দিন free + {$data['cash_reward']}৳ পেয়েছেন।";
            
            $notifications->create_notification($referrer_id, array(
                'type' => 'referral_reward',
                'title' => 'Referral Bonus পেয়েছেন!',
                'message' => $message,
                'icon' => '🎁'
            ));
        }
    }
    
    /**
     * Extend premium subscription
     */
    private function extend_premium_subscription($user_id, $days) {
        if (!function_exists('deshiflix_premium')) {
            return;
        }
        
        global $wpdb;
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        
        // Check if user has active subscription
        $subscription = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_users WHERE user_id = %d AND status = 'active' ORDER BY end_date DESC LIMIT 1",
            $user_id
        ));
        
        if ($subscription) {
            // Extend existing subscription
            $new_end_date = date('Y-m-d H:i:s', strtotime($subscription->end_date . " +{$days} days"));
            
            $wpdb->update(
                $table_users,
                array('end_date' => $new_end_date),
                array('id' => $subscription->id)
            );
        } else {
            // Create new subscription
            $premium_user = DeshiFlix_Premium_User::get_instance();
            $premium_user->activate_premium_subscription($user_id, 1, $days, 'referral_bonus');
        }
    }
    
    /**
     * Track referral
     */
    private function track_referral($referrer_id, $referred_user_id, $type) {
        global $wpdb;
        
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        $wpdb->insert(
            $table_analytics,
            array(
                'user_id' => $referrer_id,
                'event_type' => 'referral_' . $type,
                'event_data' => json_encode(array(
                    'referred_user_id' => $referred_user_id,
                    'referred_user_name' => get_user_by('ID', $referred_user_id)->display_name,
                    'type' => $type,
                    'timestamp' => current_time('mysql')
                )),
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT']
            )
        );
    }
    
    /**
     * Referral dashboard shortcode
     */
    public function referral_dashboard_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>Please login to view referral dashboard.</p>';
        }
        
        ob_start();
        ?>
        <div id="referral-dashboard">
            <h3>🎁 Referral Dashboard</h3>
            
            <div class="referral-stats">
                <div class="stat-card">
                    <h4>Total Referrals</h4>
                    <span class="stat-number" id="total-referrals">-</span>
                </div>
                <div class="stat-card">
                    <h4>Premium Conversions</h4>
                    <span class="stat-number" id="premium-conversions">-</span>
                </div>
                <div class="stat-card">
                    <h4>Bonus Days Earned</h4>
                    <span class="stat-number" id="bonus-days">-</span>
                </div>
                <div class="stat-card">
                    <h4>Cash Rewards</h4>
                    <span class="stat-number" id="cash-rewards">-</span>৳
                </div>
            </div>
            
            <div class="referral-link-section">
                <h4>Your Referral Link</h4>
                <div class="referral-link-container">
                    <input type="text" id="referral-link" readonly>
                    <button id="copy-referral-link">Copy</button>
                    <button id="share-referral-link">Share</button>
                </div>
            </div>
            
            <div class="recent-referrals">
                <h4>Recent Referrals</h4>
                <div id="referrals-list"></div>
            </div>
        </div>
        
        <style>
        #referral-dashboard {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .referral-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
            margin-top: 10px;
        }
        
        .referral-link-container {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        
        #referral-link {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        
        button {
            padding: 10px 20px;
            background: #FFD700;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // Load referral stats
            loadReferralStats();
            
            // Generate referral code if not exists
            generateReferralCode();
            
            // Copy referral link
            $('#copy-referral-link').click(function() {
                $('#referral-link').select();
                document.execCommand('copy');
                alert('Referral link copied!');
            });
            
            // Share referral link
            $('#share-referral-link').click(function() {
                if (navigator.share) {
                    navigator.share({
                        title: 'Join DeshiFlix Premium',
                        text: 'Get premium access to unlimited movies and shows!',
                        url: $('#referral-link').val()
                    });
                } else {
                    // Fallback
                    $('#copy-referral-link').click();
                }
            });
            
            function loadReferralStats() {
                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: {
                        action: 'get_referral_stats'
                    },
                    success: function(response) {
                        if (response.success) {
                            var stats = response.data.stats;
                            $('#total-referrals').text(stats.total_referrals || 0);
                            $('#premium-conversions').text(stats.premium_conversions || 0);
                            $('#bonus-days').text(stats.total_bonus_days || 0);
                            $('#cash-rewards').text(stats.total_cash_rewards || 0);
                        }
                    }
                });
            }
            
            function generateReferralCode() {
                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: {
                        action: 'generate_referral_code'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#referral-link').val(response.data.link);
                        }
                    }
                });
            }
        });
        </script>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Referral link shortcode
     */
    public function referral_link_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '';
        }
        
        $user_id = get_current_user_id();
        $referral_code = get_user_meta($user_id, '_referral_code', true);
        
        if (!$referral_code) {
            $referral_code = $this->create_unique_referral_code($user_id);
            update_user_meta($user_id, '_referral_code', $referral_code);
        }
        
        return home_url('/?ref=' . $referral_code);
    }
    
    /**
     * Maybe create referrals table
     */
    private function maybe_create_referrals_table() {
        global $wpdb;
        
        $table_referrals = $wpdb->prefix . 'deshiflix_referrals';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_referrals (
            id int(11) NOT NULL AUTO_INCREMENT,
            referrer_id int(11) NOT NULL,
            referred_user_id int(11) NOT NULL,
            type enum('signup','premium_purchase') NOT NULL,
            bonus_days int(11) DEFAULT 0,
            cash_reward decimal(10,2) DEFAULT 0,
            status enum('pending','completed','cancelled') DEFAULT 'pending',
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY referrer_id (referrer_id),
            KEY referred_user_id (referred_user_id),
            KEY type (type),
            KEY status (status)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

// Initialize referral system
DeshiFlix_Referral_System::get_instance();
