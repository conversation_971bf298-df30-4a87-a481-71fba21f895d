<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Advanced Stream Player - Deepto TV</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .player-container {
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 30px;
            position: relative;
        }
        
        video, iframe {
            width: 100%;
            height: 500px;
            background: #000;
            display: block;
            border: none;
        }
        
        .player-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .tab-btn {
            background: #333;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .tab-btn.active {
            background: #007cba;
        }
        
        .tab-btn:hover {
            background: #555;
        }
        
        .tab-btn.active:hover {
            background: #005a87;
        }
        
        .player-method {
            display: none;
        }
        
        .player-method.active {
            display: block;
        }
        
        .info {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.loading {
            background: #ffc107;
            color: #000;
        }
        
        .status.success {
            background: #28a745;
            color: white;
        }
        
        .status.error {
            background: #dc3545;
            color: white;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 Advanced Stream Player</h1>
            <h2>Deepto TV - Multiple Player Methods</h2>
        </div>
        
        <div class="info">
            <h3>📺 Stream Information</h3>
            <p><strong>Channel:</strong> Deepto TV</p>
            <p><strong>Stream URL:</strong> <code>https://byphdgllyk.gpcdn.net/hls/deeptotv/index.m3u8</code></p>
            <p><strong>Type:</strong> HLS (.m3u8)</p>
            <p><strong>Content:</strong> Bangladeshi Entertainment</p>
        </div>
        
        <div class="player-tabs">
            <button class="tab-btn active" onclick="switchPlayer('hls')">HLS.js Player</button>
            <button class="tab-btn" onclick="switchPlayer('native')">Native Player</button>
            <button class="tab-btn" onclick="switchPlayer('iframe')">Iframe Player</button>
            <button class="tab-btn" onclick="switchPlayer('proxy')">Proxy Player</button>
            <button class="tab-btn" onclick="switchPlayer('embed')">Embed Player</button>
        </div>
        
        <div class="player-container">
            <!-- HLS.js Player -->
            <div id="hls-player" class="player-method active">
                <video id="hls-video" controls autoplay muted>
                    Your browser does not support the video tag.
                </video>
            </div>
            
            <!-- Native Player -->
            <div id="native-player" class="player-method">
                <video id="native-video" controls autoplay muted>
                    <source src="https://byphdgllyk.gpcdn.net/hls/deeptotv/index.m3u8" type="application/vnd.apple.mpegurl">
                    Your browser does not support the video tag.
                </video>
            </div>
            
            <!-- Iframe Player -->
            <div id="iframe-player" class="player-method">
                <iframe id="iframe-embed" allowfullscreen></iframe>
            </div>
            
            <!-- Proxy Player -->
            <div id="proxy-player" class="player-method">
                <video id="proxy-video" controls autoplay muted>
                    Your browser does not support the video tag.
                </video>
            </div>
            
            <!-- Embed Player -->
            <div id="embed-player" class="player-method">
                <iframe id="embed-iframe" allowfullscreen></iframe>
            </div>
        </div>
        
        <div id="status" class="status loading">
            🔄 Initializing HLS.js player...
        </div>
        
        <div class="controls">
            <button onclick="reloadCurrentPlayer()">🔄 Reload</button>
            <button onclick="testDirectUrl()">🔗 Test Direct</button>
            <button onclick="toggleMute()">🔊 Toggle Mute</button>
            <button onclick="tryAllMethods()">🎯 Try All Methods</button>
        </div>
        
        <div class="info">
            <h3>🔍 Player Methods</h3>
            <div id="method-results">
                <p><strong>HLS.js:</strong> Advanced HLS player with error recovery</p>
                <p><strong>Native:</strong> Browser's built-in HLS support</p>
                <p><strong>Iframe:</strong> Embedded player in iframe</p>
                <p><strong>Proxy:</strong> Stream through proxy to bypass CORS</p>
                <p><strong>Embed:</strong> External player embed</p>
            </div>
        </div>
        
        <div id="console-log" class="log">
            <div>Advanced Player Console:</div>
        </div>
    </div>

    <script>
        // Get stream URL from URL parameter or use default
        const urlParams = new URLSearchParams(window.location.search);
        const streamUrl = urlParams.get('stream') || 'https://byphdgllyk.gpcdn.net/hls/deeptotv/index.m3u8';
        const proxyUrl = 'stream-proxy.php?url=' + encodeURIComponent(streamUrl);

        // Update page title and info
        if (urlParams.get('stream')) {
            document.title = 'Advanced Player - Custom Stream';
            document.querySelector('.info p:nth-child(3)').innerHTML = '<strong>Stream URL:</strong> <code>' + streamUrl + '</code>';
        }
        
        let currentPlayer = 'hls';
        let hlsInstance = null;
        
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('console-log');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'loading') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function switchPlayer(method) {
            // Update tabs
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update players
            document.querySelectorAll('.player-method').forEach(player => player.classList.remove('active'));
            document.getElementById(method + '-player').classList.add('active');
            
            currentPlayer = method;
            log(`🔄 Switched to ${method} player`);
            
            // Initialize the selected player
            initializePlayer(method);
        }
        
        function initializePlayer(method) {
            updateStatus(`🔄 Initializing ${method} player...`, 'loading');
            
            switch(method) {
                case 'hls':
                    initHLSPlayer();
                    break;
                case 'native':
                    initNativePlayer();
                    break;
                case 'iframe':
                    initIframePlayer();
                    break;
                case 'proxy':
                    initProxyPlayer();
                    break;
                case 'embed':
                    initEmbedPlayer();
                    break;
            }
        }
        
        function initHLSPlayer() {
            const video = document.getElementById('hls-video');
            
            if (Hls.isSupported()) {
                if (hlsInstance) {
                    hlsInstance.destroy();
                }
                
                hlsInstance = new Hls({
                    enableWorker: false,
                    lowLatencyMode: false,
                    debug: true,
                    xhrSetup: function(xhr, url) {
                        xhr.setRequestHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
                        xhr.setRequestHeader('Referer', window.location.origin);
                    },
                    manifestLoadingTimeOut: 30000,
                    manifestLoadingMaxRetry: 5,
                    fragLoadingTimeOut: 30000,
                    fragLoadingMaxRetry: 6
                });
                
                hlsInstance.loadSource(streamUrl);
                hlsInstance.attachMedia(video);
                
                hlsInstance.on(Hls.Events.MANIFEST_PARSED, function() {
                    log('✅ HLS manifest parsed');
                    updateStatus('✅ HLS player ready', 'success');
                });
                
                hlsInstance.on(Hls.Events.ERROR, function(event, data) {
                    log('❌ HLS error: ' + JSON.stringify(data));
                    updateStatus('❌ HLS error: ' + data.type, 'error');
                });
                
            } else {
                log('❌ HLS.js not supported');
                updateStatus('❌ HLS.js not supported', 'error');
            }
        }
        
        function initNativePlayer() {
            const video = document.getElementById('native-video');
            video.load();
            log('🍎 Native player initialized');
            updateStatus('🍎 Native player ready', 'success');
        }
        
        function initIframePlayer() {
            const iframe = document.getElementById('iframe-embed');
            const embedUrl = `data:text/html,<video controls autoplay muted style="width:100%;height:100%"><source src="${streamUrl}" type="application/vnd.apple.mpegurl"></video>`;
            iframe.src = embedUrl;
            log('📺 Iframe player initialized');
            updateStatus('📺 Iframe player ready', 'success');
        }
        
        function initProxyPlayer() {
            const video = document.getElementById('proxy-video');
            video.src = proxyUrl;
            video.load();
            log('🔄 Proxy player initialized');
            updateStatus('🔄 Proxy player ready', 'success');
        }
        
        function initEmbedPlayer() {
            const iframe = document.getElementById('embed-iframe');
            const embedHtml = `
                <html>
                <head>
                    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
                </head>
                <body style="margin:0;background:#000;">
                    <video id="v" controls autoplay muted style="width:100%;height:100vh;"></video>
                    <script>
                        const video = document.getElementById('v');
                        const hls = new Hls();
                        hls.loadSource('${streamUrl}');
                        hls.attachMedia(video);
                    </script>
                </body>
                </html>
            `;
            iframe.srcdoc = embedHtml;
            log('🎬 Embed player initialized');
            updateStatus('🎬 Embed player ready', 'success');
        }
        
        function reloadCurrentPlayer() {
            log('🔄 Reloading current player');
            initializePlayer(currentPlayer);
        }
        
        function testDirectUrl() {
            window.open(streamUrl, '_blank');
        }
        
        function toggleMute() {
            const videos = document.querySelectorAll('video');
            videos.forEach(video => {
                video.muted = !video.muted;
            });
            log(videos[0]?.muted ? '🔇 Muted' : '🔊 Unmuted');
        }
        
        function tryAllMethods() {
            log('🎯 Testing all player methods...');
            const methods = ['hls', 'native', 'iframe', 'proxy', 'embed'];
            let index = 0;
            
            function tryNext() {
                if (index < methods.length) {
                    switchPlayer(methods[index]);
                    index++;
                    setTimeout(tryNext, 3000);
                }
            }
            
            tryNext();
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('🌐 Advanced player loaded');
            initializePlayer('hls');
        });
    </script>
</body>
</html>
