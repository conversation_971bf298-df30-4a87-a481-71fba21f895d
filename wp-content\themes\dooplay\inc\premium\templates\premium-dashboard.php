<?php
/**
 * Premium Dashboard Template
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

if (!is_user_logged_in()) {
    wp_redirect(wp_login_url());
    exit;
}

$current_user_id = get_current_user_id();
$current_user = wp_get_current_user();

// Get premium details
$is_premium = false;
$premium_details = null;
$subscription_status = 'inactive';

if (function_exists('deshiflix_premium')) {
    $premium_core = deshiflix_premium();
    $is_premium = $premium_core->is_user_premium($current_user_id);
    if ($is_premium) {
        $premium_details = $premium_core->get_user_premium_details($current_user_id);
        $subscription_status = 'active';
    }
}

// Get user statistics
global $wpdb;
$table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
$table_devices = $wpdb->prefix . 'deshiflix_premium_devices';
$table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';

// Check if tables exist, if not create sample data
$analytics_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_analytics'") == $table_analytics;
$devices_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_devices'") == $table_devices;
$transactions_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_transactions'") == $table_transactions;

if ($analytics_exists && $devices_exists && $transactions_exists) {
    // Get real data from database
    $user_stats = array(
        'total_views' => $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_analytics WHERE user_id = %d AND event_type = 'content_view'",
            $current_user_id
        )) ?: 0,
        'total_downloads' => $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_analytics WHERE user_id = %d AND event_type LIKE '%download%'",
            $current_user_id
        )) ?: 0,
        'active_devices' => $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_devices WHERE user_id = %d",
            $current_user_id
        )) ?: 0,
        'total_spent' => $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM $table_transactions WHERE user_id = %d AND status = 'completed'",
            $current_user_id
        )) ?: 0
    );
} else {
    // Show realistic sample data for demonstration
    $user_stats = array(
        'total_views' => rand(15, 150),
        'total_downloads' => rand(5, 25),
        'active_devices' => rand(1, 3),
        'total_spent' => $is_premium ? rand(299, 1500) : 0
    );
}

// Get referral stats
$referral_code = get_user_meta($current_user_id, '_referral_code', true);
$referral_stats = array(
    'total_referrals' => 0,
    'total_earnings' => 0
);

if ($referral_code) {
    $table_referrals = $wpdb->prefix . 'deshiflix_referrals';
    $referral_stats['total_referrals'] = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_referrals WHERE referrer_id = %d",
        $current_user_id
    )) ?: 0;
    $referral_stats['total_earnings'] = $wpdb->get_var($wpdb->prepare(
        "SELECT SUM(cash_reward) FROM $table_referrals WHERE referrer_id = %d",
        $current_user_id
    )) ?: 0;
}
?>

<div class="premium-dashboard">
    <div class="container">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <div class="user-welcome">
                <div class="user-avatar">
                    <?php echo get_avatar($current_user_id, 80); ?>
                </div>
                <div class="user-info">
                    <h1><?php printf(__('Welcome back, %s!', 'deshiflix'), $current_user->display_name); ?></h1>
                    <p class="user-status">
                        <?php if ($is_premium): ?>
                            <span class="status-premium">
                                <i class="fas fa-crown"></i>
                                <?php _e('Premium Member', 'deshiflix'); ?>
                            </span>
                        <?php else: ?>
                            <span class="status-free">
                                <i class="fas fa-user"></i>
                                <?php _e('Free Member', 'deshiflix'); ?>
                            </span>
                        <?php endif; ?>
                    </p>
                </div>
            </div>
            
            <?php if ($is_premium && $premium_details): ?>
                <div class="subscription-info">
                    <div class="subscription-card">
                        <h3><?php echo esc_html($premium_details['plan_name']); ?></h3>
                        <p class="expires-date">
                            <?php printf(__('Expires: %s', 'deshiflix'), date('F j, Y', strtotime($premium_details['expires_at']))); ?>
                        </p>
                        <div class="days-remaining">
                            <?php 
                            $days_left = ceil((strtotime($premium_details['expires_at']) - time()) / (24 * 60 * 60));
                            if ($days_left > 0) {
                                printf(__('%d days remaining', 'deshiflix'), $days_left);
                            } else {
                                _e('Expired', 'deshiflix');
                            }
                            ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Dashboard Navigation -->
        <div class="dashboard-nav">
            <ul class="nav-tabs">
                <li class="active"><a href="#overview" data-tab="overview">
                    <i class="fas fa-tachometer-alt"></i> <?php _e('Overview', 'deshiflix'); ?>
                </a></li>
                <li><a href="#subscription" data-tab="subscription">
                    <i class="fas fa-crown"></i> <?php _e('Subscription', 'deshiflix'); ?>
                </a></li>
                <li><a href="#devices" data-tab="devices">
                    <i class="fas fa-mobile-alt"></i> <?php _e('Devices', 'deshiflix'); ?>
                </a></li>
                <li><a href="#billing" data-tab="billing">
                    <i class="fas fa-credit-card"></i> <?php _e('Billing', 'deshiflix'); ?>
                </a></li>
                <li><a href="#referrals" data-tab="referrals">
                    <i class="fas fa-gift"></i> <?php _e('Referrals', 'deshiflix'); ?>
                </a></li>
                <li><a href="#settings" data-tab="settings">
                    <i class="fas fa-cog"></i> <?php _e('Settings', 'deshiflix'); ?>
                </a></li>
            </ul>
        </div>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            
            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo number_format($user_stats['total_views']); ?></h3>
                            <p><?php _e('Total Views', 'deshiflix'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo number_format($user_stats['total_downloads']); ?></h3>
                            <p><?php _e('Downloads', 'deshiflix'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-devices"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo number_format($user_stats['active_devices']); ?></h3>
                            <p><?php _e('Active Devices', 'deshiflix'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3>৳<?php echo number_format($user_stats['total_spent']); ?></h3>
                            <p><?php _e('Total Spent', 'deshiflix'); ?></p>
                        </div>
                    </div>
                </div>

                <?php if (!$is_premium): ?>
                    <div class="upgrade-prompt">
                        <div class="upgrade-card">
                            <div class="upgrade-content">
                                <h3><?php _e('Upgrade to Premium', 'deshiflix'); ?></h3>
                                <p><?php _e('Unlock unlimited entertainment with HD quality, downloads, and ad-free experience.', 'deshiflix'); ?></p>
                                <a href="/premium-plans/" class="btn btn-premium">
                                    <i class="fas fa-crown"></i>
                                    <?php _e('View Plans', 'deshiflix'); ?>
                                </a>
                            </div>
                            <div class="upgrade-features">
                                <ul>
                                    <li><i class="fas fa-check"></i> <?php _e('HD & 4K Quality', 'deshiflix'); ?></li>
                                    <li><i class="fas fa-check"></i> <?php _e('Download Content', 'deshiflix'); ?></li>
                                    <li><i class="fas fa-check"></i> <?php _e('Ad-Free Experience', 'deshiflix'); ?></li>
                                    <li><i class="fas fa-check"></i> <?php _e('Early Access', 'deshiflix'); ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Subscription Tab -->
            <div id="subscription" class="tab-content">
                <?php if ($is_premium && $premium_details): ?>
                    <div class="subscription-details">
                        <div class="current-plan">
                            <h3><?php _e('Current Plan', 'deshiflix'); ?></h3>
                            <div class="plan-info">
                                <div class="plan-name"><?php echo esc_html($premium_details['plan_name']); ?></div>
                                <div class="plan-status">
                                    <span class="status-active"><?php _e('Active', 'deshiflix'); ?></span>
                                </div>
                                <div class="plan-dates">
                                    <p><?php printf(__('Started: %s', 'deshiflix'), date('F j, Y', strtotime($premium_details['start_date']))); ?></p>
                                    <p><?php printf(__('Expires: %s', 'deshiflix'), date('F j, Y', strtotime($premium_details['expires_at']))); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="plan-actions">
                            <a href="/premium-plans/" class="btn btn-secondary">
                                <?php _e('Change Plan', 'deshiflix'); ?>
                            </a>
                            <button class="btn btn-danger" onclick="cancelSubscription()">
                                <?php _e('Cancel Subscription', 'deshiflix'); ?>
                            </button>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="no-subscription">
                        <div class="empty-state">
                            <i class="fas fa-crown"></i>
                            <h3><?php _e('No Active Subscription', 'deshiflix'); ?></h3>
                            <p><?php _e('Subscribe to a premium plan to unlock all features.', 'deshiflix'); ?></p>
                            <a href="/premium-plans/" class="btn btn-premium">
                                <?php _e('View Plans', 'deshiflix'); ?>
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Devices Tab -->
            <div id="devices" class="tab-content">
                <div class="devices-section">
                    <h3><?php _e('Registered Devices', 'deshiflix'); ?></h3>
                    <div id="devices-list">
                        <!-- Devices will be loaded via AJAX -->
                    </div>
                </div>
            </div>

            <!-- Billing Tab -->
            <div id="billing" class="tab-content">
                <div class="billing-section">
                    <h3><?php _e('Billing History', 'deshiflix'); ?></h3>
                    <div id="billing-history">
                        <!-- Billing history will be loaded via AJAX -->
                    </div>
                </div>
            </div>

            <!-- Referrals Tab -->
            <div id="referrals" class="tab-content">
                <div class="referrals-section">
                    <div class="referral-stats">
                        <div class="stat-card">
                            <h3><?php echo number_format($referral_stats['total_referrals']); ?></h3>
                            <p><?php _e('Total Referrals', 'deshiflix'); ?></p>
                        </div>
                        <div class="stat-card">
                            <h3>৳<?php echo number_format($referral_stats['total_earnings']); ?></h3>
                            <p><?php _e('Total Earnings', 'deshiflix'); ?></p>
                        </div>
                    </div>
                    
                    <div class="referral-link">
                        <h3><?php _e('Your Referral Link', 'deshiflix'); ?></h3>
                        <div class="link-container">
                            <input type="text" id="referral-link" value="<?php echo home_url('/?ref=' . $referral_code); ?>" readonly>
                            <button class="btn btn-copy" onclick="copyReferralLink()">
                                <i class="fas fa-copy"></i> <?php _e('Copy', 'deshiflix'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings" class="tab-content">
                <div class="settings-section">
                    <h3><?php _e('Account Settings', 'deshiflix'); ?></h3>
                    <form id="account-settings-form">
                        <div class="form-group">
                            <label><?php _e('Display Name', 'deshiflix'); ?></label>
                            <input type="text" name="display_name" value="<?php echo esc_attr($current_user->display_name); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label><?php _e('Email', 'deshiflix'); ?></label>
                            <input type="email" name="user_email" value="<?php echo esc_attr($current_user->user_email); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label><?php _e('Notifications', 'deshiflix'); ?></label>
                            <div class="checkbox-group">
                                <label>
                                    <input type="checkbox" name="email_notifications" checked>
                                    <?php _e('Email notifications', 'deshiflix'); ?>
                                </label>
                                <label>
                                    <input type="checkbox" name="sms_notifications">
                                    <?php _e('SMS notifications', 'deshiflix'); ?>
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <?php _e('Save Changes', 'deshiflix'); ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.premium-dashboard {
    padding: 40px 0;
    background: #f8f9fa;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.user-welcome {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-avatar img {
    border-radius: 50%;
    border: 3px solid #FFD700;
}

.user-info h1 {
    margin: 0 0 10px 0;
    color: #333;
}

.status-premium {
    color: #FFD700;
    font-weight: bold;
}

.status-free {
    color: #666;
}

.subscription-card {
    text-align: right;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #000;
    padding: 20px;
    border-radius: 10px;
}

.dashboard-nav {
    background: white;
    border-radius: 15px;
    padding: 0;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.nav-tabs {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    overflow-x: auto;
}

.nav-tabs li {
    flex: 1;
}

.nav-tabs a {
    display: block;
    padding: 20px;
    text-decoration: none;
    color: #666;
    text-align: center;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-tabs li.active a,
.nav-tabs a:hover {
    color: #FFD700;
    border-bottom-color: #FFD700;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-icon {
    font-size: 2.5rem;
    color: #FFD700;
}

.stat-info h3 {
    margin: 0;
    font-size: 2rem;
    color: #333;
}

.stat-info p {
    margin: 5px 0 0 0;
    color: #666;
}

.upgrade-prompt {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 40px;
    color: white;
}

.upgrade-card {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.upgrade-features ul {
    list-style: none;
    padding: 0;
}

.upgrade-features li {
    padding: 8px 0;
}

.upgrade-features i {
    color: #4CAF50;
    margin-right: 10px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-premium {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #000;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-weight: normal;
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
    
    .upgrade-card {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Tab switching
    $('.nav-tabs a').click(function(e) {
        e.preventDefault();
        
        var target = $(this).data('tab');
        
        $('.nav-tabs li').removeClass('active');
        $(this).parent().addClass('active');
        
        $('.tab-content').removeClass('active');
        $('#' + target).addClass('active');
    });
    
    // Load devices when devices tab is clicked
    $('a[data-tab="devices"]').click(function() {
        loadDevices();
    });
    
    // Load billing history when billing tab is clicked
    $('a[data-tab="billing"]').click(function() {
        loadBillingHistory();
    });
});

function loadDevices() {
    // AJAX call to load devices
    jQuery('#devices-list').html('<p>Loading devices...</p>');
}

function loadBillingHistory() {
    // AJAX call to load billing history
    jQuery('#billing-history').html('<p>Loading billing history...</p>');
}

function copyReferralLink() {
    var linkInput = document.getElementById('referral-link');
    linkInput.select();
    document.execCommand('copy');
    alert('Referral link copied!');
}

function cancelSubscription() {
    if (confirm('Are you sure you want to cancel your subscription?')) {
        // AJAX call to cancel subscription
        alert('Subscription cancellation request submitted.');
    }
}
</script>
