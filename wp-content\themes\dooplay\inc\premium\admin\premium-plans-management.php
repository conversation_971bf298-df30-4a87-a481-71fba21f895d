<?php
/**
 * Premium Plans Management
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Premium_Plans_Management {
    
    /**
     * Render plans management page
     */
    public static function render_plans_page() {
        // Handle form submissions
        if (isset($_POST['action']) && wp_verify_nonce($_POST['plans_nonce'], 'premium_plans_action')) {
            self::handle_plan_actions();
        }
        
        ?>
        <div class="wrap">
            <h1><?php _e('Premium Plans Management', 'deshiflix'); ?></h1>
            
            <!-- Plans Overview -->
            <div class="premium-plans-overview" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                
                <div class="plan-stat" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 24px;"><?php echo self::get_total_plans(); ?></h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;"><?php _e('Total Plans', 'deshiflix'); ?></p>
                </div>
                
                <div class="plan-stat" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 24px;"><?php echo self::get_active_plans(); ?></h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;"><?php _e('Active Plans', 'deshiflix'); ?></p>
                </div>
                
                <div class="plan-stat" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 24px;"><?php echo self::get_popular_plan(); ?></h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;"><?php _e('Most Popular', 'deshiflix'); ?></p>
                </div>
                
            </div>
            
            <!-- Add New Plan -->
            <div class="card" style="margin-bottom: 20px;">
                <h2><?php _e('Add New Premium Plan', 'deshiflix'); ?></h2>
                <form method="post" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <?php wp_nonce_field('premium_plans_action', 'plans_nonce'); ?>
                    <input type="hidden" name="action" value="add_plan">
                    
                    <div>
                        <label><strong><?php _e('Plan Name', 'deshiflix'); ?></strong></label>
                        <input type="text" name="plan_name" placeholder="e.g., Monthly Premium" required style="width: 100%;">
                    </div>
                    
                    <div>
                        <label><strong><?php _e('Price (৳)', 'deshiflix'); ?></strong></label>
                        <input type="number" name="plan_price" placeholder="199" required style="width: 100%;">
                    </div>
                    
                    <div>
                        <label><strong><?php _e('Duration (Days)', 'deshiflix'); ?></strong></label>
                        <input type="number" name="plan_duration" placeholder="30" required style="width: 100%;">
                    </div>
                    
                    <div>
                        <label><strong><?php _e('Description', 'deshiflix'); ?></strong></label>
                        <input type="text" name="plan_description" placeholder="Plan description" style="width: 100%;">
                    </div>
                    
                    <div style="grid-column: 1 / -1;">
                        <button type="submit" class="button button-primary">
                            <?php _e('Add Plan', 'deshiflix'); ?>
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Existing Plans -->
            <div class="card">
                <h2><?php _e('Existing Premium Plans', 'deshiflix'); ?></h2>
                
                <div class="plans-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                    <?php self::render_existing_plans(); ?>
                </div>
                
                <!-- Plans Table -->
                <table class="widefat" style="margin-top: 30px;">
                    <thead>
                        <tr>
                            <th><?php _e('Plan Name', 'deshiflix'); ?></th>
                            <th><?php _e('Price', 'deshiflix'); ?></th>
                            <th><?php _e('Duration', 'deshiflix'); ?></th>
                            <th><?php _e('Subscribers', 'deshiflix'); ?></th>
                            <th><?php _e('Status', 'deshiflix'); ?></th>
                            <th><?php _e('Actions', 'deshiflix'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php self::render_plans_table(); ?>
                    </tbody>
                </table>
            </div>
            
        </div>
        
        <script>
        function togglePlan(planId, status) {
            const action = status === 'active' ? 'deactivate' : 'activate';
            if (confirm(`${action.charAt(0).toUpperCase() + action.slice(1)} this plan?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_plan">
                    <input type="hidden" name="plan_id" value="${planId}">
                    <input type="hidden" name="new_status" value="${status === 'active' ? 'inactive' : 'active'}">
                    <input type="hidden" name="plans_nonce" value="<?php echo wp_create_nonce('premium_plans_action'); ?>">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function deletePlan(planId) {
            if (confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_plan">
                    <input type="hidden" name="plan_id" value="${planId}">
                    <input type="hidden" name="plans_nonce" value="<?php echo wp_create_nonce('premium_plans_action'); ?>">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        </script>
        <?php
    }
    
    /**
     * Handle plan actions
     */
    private static function handle_plan_actions() {
        $action = $_POST['action'];
        
        switch ($action) {
            case 'add_plan':
                self::add_plan();
                break;
            case 'toggle_plan':
                self::toggle_plan();
                break;
            case 'delete_plan':
                self::delete_plan();
                break;
        }
    }
    
    /**
     * Add new plan
     */
    private static function add_plan() {
        $name = sanitize_text_field($_POST['plan_name']);
        $price = intval($_POST['plan_price']);
        $duration = intval($_POST['plan_duration']);
        $description = sanitize_text_field($_POST['plan_description']);
        
        // In real implementation, save to database
        // For now, show success message
        echo '<div class="notice notice-success"><p>' . sprintf(__('Plan "%s" added successfully!', 'deshiflix'), $name) . '</p></div>';
    }
    
    /**
     * Get total plans count
     */
    private static function get_total_plans() {
        return 4; // Demo count
    }
    
    /**
     * Get active plans count
     */
    private static function get_active_plans() {
        return 3; // Demo count
    }
    
    /**
     * Get popular plan
     */
    private static function get_popular_plan() {
        return 'Monthly'; // Demo popular plan
    }
    
    /**
     * Render existing plans as cards
     */
    private static function render_existing_plans() {
        $plans = array(
            array(
                'id' => 1,
                'name' => 'Weekly Premium',
                'price' => 99,
                'duration' => 7,
                'description' => 'Perfect for trying premium features',
                'subscribers' => 25,
                'status' => 'active'
            ),
            array(
                'id' => 2,
                'name' => 'Monthly Premium',
                'price' => 199,
                'duration' => 30,
                'description' => 'Most popular plan for regular users',
                'subscribers' => 150,
                'status' => 'active'
            ),
            array(
                'id' => 3,
                'name' => 'Quarterly Premium',
                'price' => 499,
                'duration' => 90,
                'description' => 'Best value for 3 months',
                'subscribers' => 75,
                'status' => 'active'
            ),
            array(
                'id' => 4,
                'name' => 'Yearly Premium',
                'price' => 1499,
                'duration' => 365,
                'description' => 'Maximum savings for a full year',
                'subscribers' => 45,
                'status' => 'inactive'
            )
        );
        
        foreach ($plans as $plan) {
            $status_color = $plan['status'] === 'active' ? '#46b450' : '#dc3232';
            $status_text = $plan['status'] === 'active' ? 'Active' : 'Inactive';
            
            echo '<div class="plan-card" style="border: 1px solid #ddd; border-radius: 10px; padding: 20px; background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">';
            echo '<div style="display: flex; justify-content: between; align-items: center; margin-bottom: 15px;">';
            echo '<h3 style="margin: 0; color: #333;">' . esc_html($plan['name']) . '</h3>';
            echo '<span style="background: ' . $status_color . '; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">' . $status_text . '</span>';
            echo '</div>';
            
            echo '<div style="font-size: 24px; font-weight: bold; color: #667eea; margin-bottom: 10px;">৳' . number_format($plan['price']) . '</div>';
            echo '<div style="color: #666; margin-bottom: 10px;">' . $plan['duration'] . ' days</div>';
            echo '<div style="color: #888; font-size: 14px; margin-bottom: 15px;">' . esc_html($plan['description']) . '</div>';
            echo '<div style="color: #666; font-size: 14px; margin-bottom: 15px;"><strong>' . $plan['subscribers'] . '</strong> subscribers</div>';
            
            echo '<div style="display: flex; gap: 10px;">';
            echo '<button type="button" class="button button-small" onclick="togglePlan(' . $plan['id'] . ', \'' . $plan['status'] . '\')">';
            echo $plan['status'] === 'active' ? 'Deactivate' : 'Activate';
            echo '</button>';
            echo '<button type="button" class="button button-small" onclick="deletePlan(' . $plan['id'] . ')">Delete</button>';
            echo '</div>';
            
            echo '</div>';
        }
    }
    
    /**
     * Render plans table
     */
    private static function render_plans_table() {
        $plans = array(
            array('name' => 'Weekly Premium', 'price' => '৳99', 'duration' => '7 days', 'subscribers' => 25, 'status' => 'active'),
            array('name' => 'Monthly Premium', 'price' => '৳199', 'duration' => '30 days', 'subscribers' => 150, 'status' => 'active'),
            array('name' => 'Quarterly Premium', 'price' => '৳499', 'duration' => '90 days', 'subscribers' => 75, 'status' => 'active'),
            array('name' => 'Yearly Premium', 'price' => '৳1499', 'duration' => '365 days', 'subscribers' => 45, 'status' => 'inactive')
        );
        
        foreach ($plans as $index => $plan) {
            $status_color = $plan['status'] === 'active' ? '#46b450' : '#dc3232';
            
            echo '<tr>';
            echo '<td><strong>' . esc_html($plan['name']) . '</strong></td>';
            echo '<td>' . esc_html($plan['price']) . '</td>';
            echo '<td>' . esc_html($plan['duration']) . '</td>';
            echo '<td>' . $plan['subscribers'] . '</td>';
            echo '<td><span style="color: ' . $status_color . '; font-weight: bold;">' . ucfirst($plan['status']) . '</span></td>';
            echo '<td>';
            echo '<button type="button" class="button button-small" onclick="togglePlan(' . ($index + 1) . ', \'' . $plan['status'] . '\')">';
            echo $plan['status'] === 'active' ? 'Deactivate' : 'Activate';
            echo '</button> ';
            echo '<button type="button" class="button button-small" onclick="deletePlan(' . ($index + 1) . ')">Delete</button>';
            echo '</td>';
            echo '</tr>';
        }
    }
    
    /**
     * Toggle plan status
     */
    private static function toggle_plan() {
        echo '<div class="notice notice-success"><p>' . __('Plan status updated successfully!', 'deshiflix') . '</p></div>';
    }
    
    /**
     * Delete plan
     */
    private static function delete_plan() {
        echo '<div class="notice notice-warning"><p>' . __('Plan deleted successfully!', 'deshiflix') . '</p></div>';
    }
}
