<?php
/**
 * Template Name: Login Page
 * 
 * @package DooPlay
 * @subpackage DeshiFlix Premium
 */

get_header(); ?>

<div id="single">
    <div class="content">
        <div class="module">
            <div class="content">
                <div class="login-page-wrapper">
                    <?php echo do_shortcode('[premium_login_form]'); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.login-page-wrapper {
    max-width: 500px;
    margin: 40px auto;
    padding: 0 20px;
}

.premium-login-form .form-container {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header h2 {
    color: #333;
    margin-bottom: 10px;
}

.form-header p {
    color: #666;
}

.form-group {
    position: relative;
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px 40px 12px 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #FFD700;
}

.form-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #999;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.btn-primary {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #000;
}

.btn-full {
    width: 100%;
}

.form-links {
    text-align: center;
    margin: 20px 0;
}

.form-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.social-login {
    margin-top: 30px;
}

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #ddd;
}

.divider span {
    background: white;
    padding: 0 15px;
    color: #666;
}

.social-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.btn-social {
    background: white;
    border: 2px solid #ddd;
    color: #333;
}

.btn-google:hover {
    border-color: #db4437;
    color: #db4437;
}

.btn-facebook:hover {
    border-color: #3b5998;
    color: #3b5998;
}
</style>

<?php get_footer(); ?>
