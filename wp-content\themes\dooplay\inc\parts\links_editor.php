<div class="dooplay_links">
    <!-- Episode Links Generator Section -->
    <div class="episode-links-generator" style="background: #f9f9f9; padding: 15px; margin-bottom: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h4 style="margin-top: 0;"><?php _d('Episode Links Generator'); ?></h4>
        <table style="width: 100%;">
            <tbody>
                <tr>
                    <td style="width: 15%;">
                        <label for="episode_count"><?php _d('Episodes'); ?>:</label>
                        <select id="episode_count" name="episode_count" style="width: 100%;">
                            <option value=""><?php _d('Select Episodes'); ?></option>
                            <?php for($i = 1; $i <= 50; $i++): ?>
                                <option value="<?php echo $i; ?>"><?php echo $i; ?> <?php _d('Episodes'); ?></option>
                            <?php endfor; ?>
                        </select>
                    </td>
                    <td style="width: 15%;">
                        <label for="episode_type"><?php _d('Type'); ?>:</label>
                        <select id='episode_type' name='episode_type' style="width: 100%;">
                            <?php foreach( $this->types() as $type) { echo "<option>{$type}</option>"; } ?>
                        </select>
                    </td>
                    <td style="width: 15%;">
                        <label for="episode_lang"><?php _d('Language'); ?>:</label>
                        <select id='episode_lang' name='episode_lang' style="width: 100%;">
                            <?php foreach( $this->langs() as $type) { echo "<option>{$type}</option>"; } ?>
                        </select>
                    </td>
                    <td style="width: 15%;">
                        <label for="episode_quality"><?php _d('Quality'); ?>:</label>
                        <select id='episode_quality' name='episode_quality' style="width: 100%;">
                            <?php foreach( $this->resolutions() as $type) { echo "<option>{$type}</option>"; } ?>
                        </select>
                    </td>
                    <td style="width: 15%;">
                        <label for="episode_size"><?php _d('File size'); ?>:</label>
                        <input type="text" id="episode_size" name="episode_size" placeholder="<?php _d('File size'); ?>" style="width: 100%;"/>
                    </td>
                    <td style="width: 25%;">
                        <label>&nbsp;</label><br>
                        <button type="button" id="generate_episode_fields" class="button button-primary"><?php _d('Generate Fields'); ?></button>
                        <button type="button" id="clear_episode_fields" class="button button-secondary"><?php _d('Clear All'); ?></button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Generated Episode Fields Container -->
    <div id="episode_fields_container" style="margin-bottom: 20px;"></div>

    <!-- Original Single Link Form -->
    <div class="dform">
        <h4><?php _d('Add Single Link'); ?></h4>
        <fieldset>
            <table>
                <tbody>
                    <tr>
                        <td>
                            <select id='dooplay_lfield_type' name='dooplay_lfield_type'>
                                <?php foreach( $this->types() as $type) { echo "<option>{$type}</option>"; } ?>
                            </select>
                        </td>
                        <td>
                            <select id='dooplay_lfield_lang' name='dooplay_lfield_lang'>
                                <?php foreach( $this->langs() as $type) { echo "<option>{$type}</option>"; } ?>
                            </select>
                        </td>
                        <td>
                            <select id='dooplay_lfield_qual' name='dooplay_lfield_qual'>
                                <?php foreach( $this->resolutions() as $type) { echo "<option>{$type}</option>"; } ?>
                            </select>
                        </td>
                        <td>
                            <input type="text" id="dooplay_lfield_size" name="dooplay_lfield_size" placeholder="<?php _d('File size'); ?>"/>
                        </td>
                    </tr>
                </tbody>
            </table>
        </fieldset>
        <fieldset>
            <textarea id="dooplay_lfield_urls" name="dooplay_lfield_urls" rows="3" placeholder="<?php _d('Add a link per line'); ?>"></textarea>
        </fieldset>
        <fieldset>
            <a href="#" id="dooplay_anchor_hideform" class="button button-decundary"><?php _d('Cancel'); ?></a>
            <a href="#" id="dooplay_anchor_postlinks" class="button button-primary right"><?php _d('Add Links'); ?></a>
        </fieldset>
    </div>
    <div class="dpre">
        <a href="#" id="dooplay_anchor_showform" class="button button-primary"><?php _d('Add Links'); ?></a>
        <a href="#" id="dooplay_anchor_reloadllist" class="button button-secundary right" data-id="<?php echo $post->ID; ?>"><?php _d('Reload List'); ?></a>
    </div>


    <table id="links-sortable-table">
        <thead>
            <tr>
                <th><?php _d('Type'); ?></th>
                <th><?php _d('Server'); ?></th>
                <th><?php _d('Language'); ?></th>
                <th><?php _d('Quality'); ?></th>
                <th><?php _d('Size'); ?></th>
                <th><?php _d('Clicks'); ?></th>
                <th><?php _d('User'); ?></th>
                <th><?php _d('Added'); ?></th>
                <th style="width: 80px;"><?php _d('Order'); ?></th>
                <th colspan="2"><?php _d('Manage'); ?></th>
            </tr>
        </thead>
        <tbody id="doolinks_response">
            <?php $this->tablelist_admin($post->ID); ?>
        </tbody>
    </table>
</div>

<script>
jQuery(document).ready(function($) {
    // File size format helper
    function formatFileSize(bytes) {
        if (bytes >= 1073741824) {
            return (bytes / 1073741824).toFixed(2) + ' GB';
        } else if (bytes >= 1048576) {
            return (bytes / 1048576).toFixed(2) + ' MB';
        } else if (bytes >= 1024) {
            return (bytes / 1024).toFixed(2) + ' KB';
        } else if (bytes > 1) {
            return bytes + ' bytes';
        } else if (bytes == 1) {
            return '1 byte';
        } else {
            return '0 bytes';
        }
    }
    // File size detection function
    function detectFileSizeForSingle(url) {
        if (!url) return;
        // HEAD request
        $.ajax({
            url: url,
            type: 'HEAD',
            timeout: 10000,
            success: function(data, status, xhr) {
                var contentLength = xhr.getResponseHeader('Content-Length');
                if (contentLength) {
                    var sizeInBytes = parseInt(contentLength);
                    var formattedSize = formatFileSize(sizeInBytes);
                    $('#dooplay_lfield_size').val(formattedSize).css('background', '#d4edda');
                } else {
                    extractSizeFromUrlForSingle(url);
                }
            },
            error: function() {
                extractSizeFromUrlForSingle(url);
            }
        });
    }
    // Extract size from URL patterns (fallback)
    function extractSizeFromUrlForSingle(url) {
        var sizePatterns = [
            /(\d+(?:\.\d+)?)\s*GB/i,
            /(\d+(?:\.\d+)?)\s*MB/i,
            /(\d+(?:\.\d+)?)\s*KB/i,
            /(\d+(?:\.\d+)?)\s*TB/i,
            /(\d+(?:\.\d+)?)\s*g/i,
            /(\d+(?:\.\d+)?)\s*m/i
        ];
        var detectedSize = null;
        for (var i = 0; i < sizePatterns.length; i++) {
            var match = url.match(sizePatterns[i]);
            if (match) {
                var size = parseFloat(match[1]);
                var unit = match[0].replace(match[1], '').trim().toUpperCase();
                if (unit.includes('G') || unit === 'G') {
                    detectedSize = size + ' GB';
                } else if (unit.includes('M') || unit === 'M') {
                    detectedSize = size + ' MB';
                } else if (unit.includes('K')) {
                    detectedSize = size + ' KB';
                } else if (unit.includes('T')) {
                    detectedSize = size + ' TB';
                }
                break;
            }
        }
        if (detectedSize) {
            $('#dooplay_lfield_size').val(detectedSize).css('background', '#fff3cd');
        } else {
            $('#dooplay_lfield_size').val('Unknown').css('background', '#f8d7da');
        }
    }
    // Event: যখন লিংক ইনপুটে পেস্ট বা টাইপ হবে
    $('#dooplay_lfield_urls').on('input paste', function() {
        var val = $(this).val().trim();
        // একাধিক লাইন থাকলে প্রথম লাইন নেবে
        var firstUrl = val.split('\n')[0].trim();
        if (firstUrl && firstUrl.match(/^https?:\/\//i)) {
            $('#dooplay_lfield_size').val('Auto-detecting...').css('background', '#e3e3e3');
            detectFileSizeForSingle(firstUrl);
        }
    });
    // যদি ইউজার টাইপ করতে শুরু করে এবং বক্সে 'Unknown' লেখা থাকে, তাহলে বক্স খালি করে দাও
    $('#dooplay_lfield_size').on('focus input', function() {
        if ($(this).val() === 'Unknown') {
            $(this).val('').css('background', '#fff');
        }
    });

    // Episode Generator Functionality
    $('#generate_episode_fields').on('click', function() {
        var episodeCount = $('#episode_count').val();
        var episodeType = $('#episode_type').val();
        var episodeLang = $('#episode_lang').val();
        var episodeQuality = $('#episode_quality').val();
        var episodeSize = $('#episode_size').val();

        if (!episodeCount) {
            alert('Please select number of episodes');
            return;
        }

        var container = $('#episode_fields_container');
        container.empty();

        for (var i = 1; i <= episodeCount; i++) {
            var episodeHtml = `
                <div class="episode-field" style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; border-radius: 5px;">
                    <h5>Episode ${i}</h5>
                    <table style="width: 100%;">
                        <tr>
                            <td style="width: 15%;">
                                <select name="episode_${i}_type" style="width: 100%;">
                                    <?php foreach( $this->types() as $type) { echo "<option>{$type}</option>"; } ?>
                                </select>
                            </td>
                            <td style="width: 15%;">
                                <select name="episode_${i}_lang" style="width: 100%;">
                                    <?php foreach( $this->langs() as $type) { echo "<option>{$type}</option>"; } ?>
                                </select>
                            </td>
                            <td style="width: 15%;">
                                <select name="episode_${i}_quality" style="width: 100%;">
                                    <?php foreach( $this->resolutions() as $type) { echo "<option>{$type}</option>"; } ?>
                                </select>
                            </td>
                            <td style="width: 15%;">
                                <input type="text" name="episode_${i}_size" placeholder="File size" value="${episodeSize}" style="width: 100%;"/>
                            </td>
                            <td style="width: 40%;">
                                <textarea name="episode_${i}_urls" rows="2" placeholder="Add episode ${i} links (one per line)" style="width: 100%;"></textarea>
                            </td>
                        </tr>
                    </table>
                </div>
            `;
            container.append(episodeHtml);
        }

        // Set default values
        container.find('select[name*="_type"]').val(episodeType);
        container.find('select[name*="_lang"]').val(episodeLang);
        container.find('select[name*="_quality"]').val(episodeQuality);

        // Add bulk action buttons
        container.append(`
            <div style="text-align: center; margin-top: 15px;">
                <button type="button" id="add_all_episodes" class="button button-primary">Add All Episodes</button>
                <button type="button" id="clear_episode_fields" class="button button-secondary">Clear All</button>
            </div>
        `);
    });

    // Clear episode fields
    $(document).on('click', '#clear_episode_fields', function() {
        $('#episode_fields_container').empty();
        $('#episode_count').val('');
        $('#episode_size').val('');
    });

    // Add all episodes
    $(document).on('click', '#add_all_episodes', function() {
        var episodeFields = $('.episode-field');
        var addedCount = 0;

        episodeFields.each(function(index) {
            var episodeNum = index + 1;
            var type = $(this).find('select[name*="_type"]').val();
            var lang = $(this).find('select[name*="_lang"]').val();
            var quality = $(this).find('select[name*="_quality"]').val();
            var size = $(this).find('input[name*="_size"]').val();
            var urls = $(this).find('textarea[name*="_urls"]').val().trim();

            if (urls) {
                var urlList = urls.split('\n').filter(url => url.trim());

                urlList.forEach(function(url) {
                    url = url.trim();
                    if (url) {
                        // Add episode prefix to title
                        var episodeTitle = 'EP' + episodeNum;

                        // AJAX call to add link
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'dooplay_add_link',
                                post_id: '<?php echo $post->ID; ?>',
                                link_url: url,
                                link_type: type,
                                link_lang: lang,
                                link_quality: quality,
                                link_size: size,
                                episode_title: episodeTitle,
                                nonce: '<?php echo wp_create_nonce("dooplay_add_link"); ?>'
                            },
                            success: function(response) {
                                addedCount++;
                                if (addedCount === 1) {
                                    // Reload the links table after first success
                                    $('#dooplay_anchor_reloadllist').trigger('click');
                                }
                            }
                        });
                    }
                });
            }
        });

        if (addedCount > 0) {
            alert('Episodes added successfully!');
            $('#episode_fields_container').empty();
        } else {
            alert('Please add URLs for at least one episode');
        }
    });
});
</script>
