<?php
/**
 * DeshiFlix Premium System - Payment Management Class
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Premium_Payment {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Supported payment gateways
     */
    private $gateways = array();
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize payment system
     */
    private function init() {
        $this->init_gateways();
        $this->init_hooks();
    }
    
    /**
     * Initialize payment gateways
     */
    private function init_gateways() {
        $this->gateways = array(
            'bkash' => array(
                'name' => 'bKash',
                'title' => __('bKash Mobile Banking', 'deshiflix'),
                'description' => __('Pay securely using bKash mobile banking', 'deshiflix'),
                'icon' => DESHIFLIX_PREMIUM_ASSETS_URL . 'images/bkash-logo.png',
                'enabled' => true,
                'test_mode' => true,
                'api_key' => '',
                'api_secret' => '',
                'username' => '',
                'password' => ''
            ),
            'nagad' => array(
                'name' => 'nagad',
                'title' => __('Nagad Mobile Banking', 'deshiflix'),
                'description' => __('Pay securely using Nagad mobile banking', 'deshiflix'),
                'icon' => DESHIFLIX_PREMIUM_ASSETS_URL . 'images/nagad-logo.png',
                'enabled' => true,
                'test_mode' => true,
                'merchant_id' => '',
                'merchant_key' => ''
            ),
            'rocket' => array(
                'name' => 'rocket',
                'title' => __('Rocket Mobile Banking', 'deshiflix'),
                'description' => __('Pay securely using Rocket mobile banking', 'deshiflix'),
                'icon' => DESHIFLIX_PREMIUM_ASSETS_URL . 'images/rocket-logo.png',
                'enabled' => true,
                'test_mode' => true,
                'merchant_id' => '',
                'merchant_key' => ''
            ),
            'upay' => array(
                'name' => 'upay',
                'title' => __('Upay Mobile Banking', 'deshiflix'),
                'description' => __('Pay securely using Upay mobile banking', 'deshiflix'),
                'icon' => DESHIFLIX_PREMIUM_ASSETS_URL . 'images/upay-logo.png',
                'enabled' => true,
                'test_mode' => true,
                'merchant_id' => '',
                'merchant_key' => ''
            ),
            'sslcommerz' => array(
                'name' => 'sslcommerz',
                'title' => __('SSLCommerz Payment Gateway', 'deshiflix'),
                'description' => __('Pay using cards, mobile banking, and internet banking', 'deshiflix'),
                'icon' => DESHIFLIX_PREMIUM_ASSETS_URL . 'images/sslcommerz-logo.png',
                'enabled' => true,
                'test_mode' => true,
                'store_id' => '',
                'store_password' => ''
            ),
            'aamarpay' => array(
                'name' => 'aamarpay',
                'title' => __('aamarPay Payment Gateway', 'deshiflix'),
                'description' => __('Pay using various payment methods', 'deshiflix'),
                'icon' => DESHIFLIX_PREMIUM_ASSETS_URL . 'images/aamarpay-logo.png',
                'enabled' => true,
                'test_mode' => true,
                'store_id' => '',
                'signature_key' => ''
            )
        );
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Payment processing hooks
        add_action('init', array($this, 'handle_payment_callback'));
        add_action('wp_ajax_process_premium_payment', array($this, 'ajax_process_payment'));
        add_action('wp_ajax_nopriv_process_premium_payment', array($this, 'ajax_process_payment'));
        
        // Payment verification hooks
        add_action('wp_ajax_verify_payment', array($this, 'ajax_verify_payment'));
        add_action('wp_ajax_nopriv_verify_payment', array($this, 'ajax_verify_payment'));
        
        // Admin hooks
        add_action('admin_menu', array($this, 'add_payment_admin_menu'));
        add_action('admin_init', array($this, 'save_payment_settings'));
        
        // Shortcodes
        add_shortcode('premium_payment_form', array($this, 'payment_form_shortcode'));
        add_shortcode('payment_methods', array($this, 'payment_methods_shortcode'));
    }
    
    /**
     * Get enabled payment gateways
     */
    public function get_enabled_gateways() {
        $enabled_gateways = array();
        
        foreach ($this->gateways as $gateway_id => $gateway) {
            if ($gateway['enabled']) {
                $enabled_gateways[$gateway_id] = $gateway;
            }
        }
        
        return $enabled_gateways;
    }
    
    /**
     * Process payment
     */
    public function process_payment($gateway, $user_id, $plan_id, $amount) {
        $transaction_id = $this->generate_transaction_id();
        
        // Save transaction to database
        $this->save_transaction($transaction_id, $user_id, $plan_id, $amount, $gateway, 'pending');
        
        switch ($gateway) {
            case 'bkash':
                return $this->process_bkash_payment($transaction_id, $amount);
            case 'nagad':
                return $this->process_nagad_payment($transaction_id, $amount);
            case 'rocket':
                return $this->process_rocket_payment($transaction_id, $amount);
            case 'upay':
                return $this->process_upay_payment($transaction_id, $amount);
            case 'sslcommerz':
                return $this->process_sslcommerz_payment($transaction_id, $amount);
            case 'aamarpay':
                return $this->process_aamarpay_payment($transaction_id, $amount);
            default:
                return array('success' => false, 'message' => __('Invalid payment gateway', 'deshiflix'));
        }
    }
    
    /**
     * Process bKash payment
     */
    private function process_bkash_payment($transaction_id, $amount) {
        $gateway_config = $this->gateways['bkash'];
        
        if ($gateway_config['test_mode']) {
            // Test mode - simulate payment
            return array(
                'success' => true,
                'payment_url' => $this->get_test_payment_url('bkash', $transaction_id),
                'transaction_id' => $transaction_id
            );
        }
        
        // Production bKash API integration
        $api_url = 'https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized/checkout/create';
        
        $headers = array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->get_bkash_token(),
            'X-APP-Key' => $gateway_config['api_key']
        );
        
        $body = array(
            'mode' => '0011',
            'payerReference' => $transaction_id,
            'callbackURL' => home_url('/premium-payment-callback/bkash/'),
            'amount' => $amount,
            'currency' => 'BDT',
            'intent' => 'sale',
            'merchantInvoiceNumber' => $transaction_id
        );
        
        $response = wp_remote_post($api_url, array(
            'headers' => $headers,
            'body' => json_encode($body),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (isset($response_body['bkashURL'])) {
            return array(
                'success' => true,
                'payment_url' => $response_body['bkashURL'],
                'transaction_id' => $transaction_id
            );
        }
        
        return array('success' => false, 'message' => __('bKash payment initialization failed', 'deshiflix'));
    }
    
    /**
     * Process Nagad payment
     */
    private function process_nagad_payment($transaction_id, $amount) {
        $gateway_config = $this->gateways['nagad'];
        
        if ($gateway_config['test_mode']) {
            return array(
                'success' => true,
                'payment_url' => $this->get_test_payment_url('nagad', $transaction_id),
                'transaction_id' => $transaction_id
            );
        }
        
        // Production Nagad API integration
        $api_url = 'https://api.mynagad.com/api/dfs/check-out/initialize/' . $gateway_config['merchant_id'] . '/' . $transaction_id;
        
        $timestamp = time();
        $random = rand(1000000, 9999999);
        
        $headers = array(
            'Content-Type' => 'application/json',
            'X-KM-Api-Version' => 'v-0.2.0',
            'X-KM-IP-V4' => $_SERVER['REMOTE_ADDR'],
            'X-KM-Client-Type' => 'PC_WEB'
        );
        
        $body = array(
            'accountNumber' => '', // Customer phone number
            'dateTime' => date('YmdHis'),
            'sensitiveData' => $this->encrypt_nagad_data(array(
                'merchantId' => $gateway_config['merchant_id'],
                'datetime' => $timestamp,
                'orderId' => $transaction_id,
                'challenge' => $random
            )),
            'signature' => $this->generate_nagad_signature($transaction_id, $amount, $timestamp, $random)
        );
        
        $response = wp_remote_post($api_url, array(
            'headers' => $headers,
            'body' => json_encode($body),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (isset($response_body['callBackUrl'])) {
            return array(
                'success' => true,
                'payment_url' => $response_body['callBackUrl'],
                'transaction_id' => $transaction_id
            );
        }
        
        return array('success' => false, 'message' => __('Nagad payment initialization failed', 'deshiflix'));
    }
    
    /**
     * Process SSLCommerz payment
     */
    private function process_sslcommerz_payment($transaction_id, $amount) {
        $gateway_config = $this->gateways['sslcommerz'];
        
        if ($gateway_config['test_mode']) {
            $api_url = 'https://sandbox.sslcommerz.com/gwprocess/v4/api.php';
        } else {
            $api_url = 'https://securepay.sslcommerz.com/gwprocess/v4/api.php';
        }
        
        $user = wp_get_current_user();
        
        $post_data = array(
            'store_id' => $gateway_config['store_id'],
            'store_passwd' => $gateway_config['store_password'],
            'total_amount' => $amount,
            'currency' => 'BDT',
            'tran_id' => $transaction_id,
            'success_url' => home_url('/premium-payment-success/'),
            'fail_url' => home_url('/premium-payment-failed/'),
            'cancel_url' => home_url('/premium-payment-cancelled/'),
            'ipn_url' => home_url('/premium-payment-ipn/'),
            'product_name' => 'DeshiFlix Premium Subscription',
            'product_category' => 'Digital Service',
            'product_profile' => 'digital-goods',
            'cus_name' => $user->display_name,
            'cus_email' => $user->user_email,
            'cus_add1' => 'Dhaka',
            'cus_city' => 'Dhaka',
            'cus_country' => 'Bangladesh',
            'cus_phone' => '',
            'shipping_method' => 'NO',
            'multi_card_name' => 'mastercard,visacard,amexcard,internetbank,mobilebank',
            'value_a' => $transaction_id,
            'value_b' => get_current_user_id(),
            'value_c' => '',
            'value_d' => ''
        );
        
        $response = wp_remote_post($api_url, array(
            'body' => $post_data,
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (isset($response_body['GatewayPageURL'])) {
            return array(
                'success' => true,
                'payment_url' => $response_body['GatewayPageURL'],
                'transaction_id' => $transaction_id
            );
        }
        
        return array('success' => false, 'message' => __('SSLCommerz payment initialization failed', 'deshiflix'));
    }
    
    /**
     * Process aamarPay payment
     */
    private function process_aamarpay_payment($transaction_id, $amount) {
        $gateway_config = $this->gateways['aamarpay'];
        
        if ($gateway_config['test_mode']) {
            $api_url = 'https://sandbox.aamarpay.com/jsonpost.php';
        } else {
            $api_url = 'https://secure.aamarpay.com/jsonpost.php';
        }
        
        $user = wp_get_current_user();
        
        $post_data = array(
            'store_id' => $gateway_config['store_id'],
            'signature_key' => $gateway_config['signature_key'],
            'tran_id' => $transaction_id,
            'amount' => $amount,
            'currency' => 'BDT',
            'desc' => 'DeshiFlix Premium Subscription',
            'cus_name' => $user->display_name,
            'cus_email' => $user->user_email,
            'cus_phone' => '',
            'cus_add1' => 'Dhaka',
            'cus_city' => 'Dhaka',
            'cus_country' => 'Bangladesh',
            'success_url' => home_url('/premium-payment-success/'),
            'fail_url' => home_url('/premium-payment-failed/'),
            'cancel_url' => home_url('/premium-payment-cancelled/'),
            'type' => 'json'
        );
        
        $response = wp_remote_post($api_url, array(
            'body' => $post_data,
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (isset($response_body['payment_url'])) {
            return array(
                'success' => true,
                'payment_url' => $response_body['payment_url'],
                'transaction_id' => $transaction_id
            );
        }
        
        return array('success' => false, 'message' => __('aamarPay payment initialization failed', 'deshiflix'));
    }
    
    /**
     * Get test payment URL
     */
    private function get_test_payment_url($gateway, $transaction_id) {
        return add_query_arg(array(
            'action' => 'test_payment',
            'gateway' => $gateway,
            'transaction_id' => $transaction_id,
            'nonce' => wp_create_nonce('test_payment_' . $transaction_id)
        ), home_url('/premium-test-payment/'));
    }
    
    /**
     * Generate transaction ID
     */
    private function generate_transaction_id() {
        return 'DFLIX_' . time() . '_' . rand(1000, 9999);
    }
    
    /**
     * Save transaction to database
     */
    private function save_transaction($transaction_id, $user_id, $plan_id, $amount, $gateway, $status) {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        return $wpdb->insert($table_transactions, array(
            'user_id' => $user_id,
            'plan_id' => $plan_id,
            'amount' => $amount,
            'currency' => 'BDT',
            'payment_method' => $gateway,
            'transaction_id' => $transaction_id,
            'status' => $status
        ));
    }
    
    /**
     * Update transaction status
     */
    public function update_transaction_status($transaction_id, $status, $gateway_transaction_id = '') {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        $update_data = array('status' => $status);
        
        if ($gateway_transaction_id) {
            $update_data['gateway_transaction_id'] = $gateway_transaction_id;
        }
        
        return $wpdb->update(
            $table_transactions,
            $update_data,
            array('transaction_id' => $transaction_id)
        );
    }
    
    /**
     * Get transaction details
     */
    public function get_transaction($transaction_id) {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_transactions WHERE transaction_id = %s",
            $transaction_id
        ));
    }
    
    /**
     * Handle payment callback
     */
    public function handle_payment_callback() {
        if (isset($_GET['premium_payment_callback'])) {
            $gateway = sanitize_text_field($_GET['gateway']);
            $transaction_id = sanitize_text_field($_GET['transaction_id']);
            
            switch ($gateway) {
                case 'bkash':
                    $this->handle_bkash_callback();
                    break;
                case 'nagad':
                    $this->handle_nagad_callback();
                    break;
                case 'sslcommerz':
                    $this->handle_sslcommerz_callback();
                    break;
                case 'aamarpay':
                    $this->handle_aamarpay_callback();
                    break;
            }
        }
    }
    
    /**
     * Handle successful payment
     */
    public function handle_successful_payment($transaction_id, $gateway_transaction_id = '') {
        $transaction = $this->get_transaction($transaction_id);
        
        if (!$transaction || $transaction->status !== 'pending') {
            return false;
        }
        
        // Update transaction status
        $this->update_transaction_status($transaction_id, 'completed', $gateway_transaction_id);
        
        // Get plan details
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_plans WHERE id = %d",
            $transaction->plan_id
        ));
        
        if ($plan) {
            // Activate premium subscription
            $premium_user = DeshiFlix_Premium_User::get_instance();
            $premium_user->activate_premium_subscription(
                $transaction->user_id,
                $transaction->plan_id,
                $plan->duration_days,
                $transaction->payment_method
            );
            
            // Send success notification
            $this->send_payment_success_notification($transaction->user_id, $transaction_id);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * AJAX process payment
     */
    public function ajax_process_payment() {
        check_ajax_referer('deshiflix_premium_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        $plan_id = intval($_POST['plan_id']);
        $gateway = sanitize_text_field($_POST['gateway']);
        
        if (!$user_id) {
            wp_send_json_error(array('message' => __('Please login first', 'deshiflix')));
        }
        
        // Get plan details
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_plans WHERE id = %d AND status = 'active'",
            $plan_id
        ));
        
        if (!$plan) {
            wp_send_json_error(array('message' => __('Invalid plan selected', 'deshiflix')));
        }
        
        // Process payment
        $result = $this->process_payment($gateway, $user_id, $plan_id, $plan->price);
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    /**
     * Payment form shortcode
     */
    public function payment_form_shortcode($atts) {
        $atts = shortcode_atts(array(
            'plan_id' => 0
        ), $atts);
        
        if (!is_user_logged_in()) {
            return '<p>' . __('Please login to purchase premium subscription', 'deshiflix') . '</p>';
        }
        
        ob_start();
        include DESHIFLIX_PREMIUM_PATH . 'templates/payment-form.php';
        return ob_get_clean();
    }
    
    /**
     * Payment methods shortcode
     */
    public function payment_methods_shortcode($atts) {
        $enabled_gateways = $this->get_enabled_gateways();
        
        ob_start();
        ?>
        <div class="payment-methods">
            <h4><?php _e('Supported Payment Methods', 'deshiflix'); ?></h4>
            <div class="payment-gateway-list">
                <?php foreach ($enabled_gateways as $gateway_id => $gateway): ?>
                    <div class="payment-gateway-item">
                        <img src="<?php echo esc_url($gateway['icon']); ?>" alt="<?php echo esc_attr($gateway['title']); ?>">
                        <span><?php echo esc_html($gateway['title']); ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Send payment success notification
     */
    private function send_payment_success_notification($user_id, $transaction_id) {
        $user = get_user_by('ID', $user_id);
        if (!$user) return;

        $subject = __('Payment Successful - DeshiFlix Premium', 'deshiflix');
        $message = sprintf(
            __('Your payment has been processed successfully. Transaction ID: %s. Your premium subscription is now active.', 'deshiflix'),
            $transaction_id
        );

        wp_mail($user->user_email, $subject, $message);
    }

    /**
     * Handle bKash callback
     */
    private function handle_bkash_callback() {
        $payment_id = sanitize_text_field($_GET['paymentID'] ?? '');
        $status = sanitize_text_field($_GET['status'] ?? '');

        if ($status === 'success' && $payment_id) {
            // Verify payment with bKash API
            $verification_result = $this->verify_bkash_payment($payment_id);

            if ($verification_result['success']) {
                $transaction_id = $verification_result['transaction_id'];
                $this->handle_successful_payment($transaction_id, $payment_id);
                wp_redirect(home_url('/premium-payment-success/?transaction_id=' . $transaction_id));
            } else {
                wp_redirect(home_url('/premium-payment-failed/'));
            }
        } else {
            wp_redirect(home_url('/premium-payment-cancelled/'));
        }
        exit;
    }

    /**
     * Handle Nagad callback
     */
    private function handle_nagad_callback() {
        $payment_ref_id = sanitize_text_field($_GET['payment_ref_id'] ?? '');
        $status = sanitize_text_field($_GET['status'] ?? '');

        if ($status === 'Success' && $payment_ref_id) {
            // Verify payment with Nagad API
            $verification_result = $this->verify_nagad_payment($payment_ref_id);

            if ($verification_result['success']) {
                $transaction_id = $verification_result['transaction_id'];
                $this->handle_successful_payment($transaction_id, $payment_ref_id);
                wp_redirect(home_url('/premium-payment-success/?transaction_id=' . $transaction_id));
            } else {
                wp_redirect(home_url('/premium-payment-failed/'));
            }
        } else {
            wp_redirect(home_url('/premium-payment-cancelled/'));
        }
        exit;
    }

    /**
     * Handle SSLCommerz callback
     */
    private function handle_sslcommerz_callback() {
        $val_id = sanitize_text_field($_POST['val_id'] ?? '');
        $status = sanitize_text_field($_POST['status'] ?? '');
        $tran_id = sanitize_text_field($_POST['tran_id'] ?? '');

        if ($status === 'VALID' && $val_id && $tran_id) {
            // Verify payment with SSLCommerz API
            $verification_result = $this->verify_sslcommerz_payment($val_id, $tran_id);

            if ($verification_result['success']) {
                $this->handle_successful_payment($tran_id, $val_id);
                wp_redirect(home_url('/premium-payment-success/?transaction_id=' . $tran_id));
            } else {
                wp_redirect(home_url('/premium-payment-failed/'));
            }
        } else {
            wp_redirect(home_url('/premium-payment-failed/'));
        }
        exit;
    }

    /**
     * Handle aamarPay callback
     */
    private function handle_aamarpay_callback() {
        $mer_txnid = sanitize_text_field($_POST['mer_txnid'] ?? '');
        $pay_status = sanitize_text_field($_POST['pay_status'] ?? '');

        if ($pay_status === 'Successful' && $mer_txnid) {
            // Verify payment with aamarPay API
            $verification_result = $this->verify_aamarpay_payment($mer_txnid);

            if ($verification_result['success']) {
                $this->handle_successful_payment($mer_txnid, $mer_txnid);
                wp_redirect(home_url('/premium-payment-success/?transaction_id=' . $mer_txnid));
            } else {
                wp_redirect(home_url('/premium-payment-failed/'));
            }
        } else {
            wp_redirect(home_url('/premium-payment-failed/'));
        }
        exit;
    }

    /**
     * Verify bKash payment
     */
    private function verify_bkash_payment($payment_id) {
        $gateway_config = $this->gateways['bkash'];

        if ($gateway_config['test_mode']) {
            // Test mode verification
            return array(
                'success' => true,
                'transaction_id' => 'TEST_' . $payment_id
            );
        }

        $api_url = 'https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized/checkout/execute';

        $headers = array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->get_bkash_token(),
            'X-APP-Key' => $gateway_config['api_key']
        );

        $body = array('paymentID' => $payment_id);

        $response = wp_remote_post($api_url, array(
            'headers' => $headers,
            'body' => json_encode($body),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }

        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if (isset($response_body['transactionStatus']) && $response_body['transactionStatus'] === 'Completed') {
            return array(
                'success' => true,
                'transaction_id' => $response_body['merchantInvoiceNumber']
            );
        }

        return array('success' => false, 'message' => 'Payment verification failed');
    }

    /**
     * Verify Nagad payment
     */
    private function verify_nagad_payment($payment_ref_id) {
        $gateway_config = $this->gateways['nagad'];

        if ($gateway_config['test_mode']) {
            return array(
                'success' => true,
                'transaction_id' => 'TEST_' . $payment_ref_id
            );
        }

        // Nagad verification API call
        $api_url = 'https://api.mynagad.com/api/dfs/verify/payment/' . $payment_ref_id;

        $headers = array(
            'Content-Type' => 'application/json',
            'X-KM-Api-Version' => 'v-0.2.0'
        );

        $response = wp_remote_get($api_url, array(
            'headers' => $headers,
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }

        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if (isset($response_body['status']) && $response_body['status'] === 'Success') {
            return array(
                'success' => true,
                'transaction_id' => $response_body['orderId']
            );
        }

        return array('success' => false, 'message' => 'Payment verification failed');
    }

    /**
     * Verify SSLCommerz payment
     */
    private function verify_sslcommerz_payment($val_id, $tran_id) {
        $gateway_config = $this->gateways['sslcommerz'];

        if ($gateway_config['test_mode']) {
            $api_url = 'https://sandbox.sslcommerz.com/validator/api/validationserverAPI.php';
        } else {
            $api_url = 'https://securepay.sslcommerz.com/validator/api/validationserverAPI.php';
        }

        $post_data = array(
            'val_id' => $val_id,
            'store_id' => $gateway_config['store_id'],
            'store_passwd' => $gateway_config['store_password'],
            'format' => 'json'
        );

        $response = wp_remote_post($api_url, array(
            'body' => $post_data,
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }

        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if (isset($response_body['status']) && $response_body['status'] === 'VALID' && $response_body['tran_id'] === $tran_id) {
            return array('success' => true, 'transaction_id' => $tran_id);
        }

        return array('success' => false, 'message' => 'Payment verification failed');
    }

    /**
     * Verify aamarPay payment
     */
    private function verify_aamarpay_payment($mer_txnid) {
        $gateway_config = $this->gateways['aamarpay'];

        if ($gateway_config['test_mode']) {
            return array(
                'success' => true,
                'transaction_id' => $mer_txnid
            );
        }

        // aamarPay verification would be implemented here
        // For now, return success for demo
        return array('success' => true, 'transaction_id' => $mer_txnid);
    }

    /**
     * Get bKash token
     */
    private function get_bkash_token() {
        $gateway_config = $this->gateways['bkash'];

        // Check if token is cached
        $cached_token = get_transient('bkash_access_token');
        if ($cached_token) {
            return $cached_token;
        }

        $api_url = 'https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized/checkout/token/grant';

        $headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'username' => $gateway_config['username'],
            'password' => $gateway_config['password']
        );

        $body = array(
            'app_key' => $gateway_config['api_key'],
            'app_secret' => $gateway_config['api_secret']
        );

        $response = wp_remote_post($api_url, array(
            'headers' => $headers,
            'body' => json_encode($body),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if (isset($response_body['id_token'])) {
            $token = $response_body['id_token'];
            // Cache token for 1 hour
            set_transient('bkash_access_token', $token, 3600);
            return $token;
        }

        return false;
    }

    /**
     * Encrypt Nagad data
     */
    private function encrypt_nagad_data($data) {
        // Nagad encryption implementation
        return base64_encode(json_encode($data));
    }

    /**
     * Generate Nagad signature
     */
    private function generate_nagad_signature($order_id, $amount, $timestamp, $random) {
        $gateway_config = $this->gateways['nagad'];

        $data = $gateway_config['merchant_id'] . $order_id . $amount . 'BDT' . $timestamp . $random;
        return hash_hmac('sha256', $data, $gateway_config['merchant_key']);
    }

    /**
     * Process Rocket payment
     */
    private function process_rocket_payment($transaction_id, $amount) {
        $gateway_config = $this->gateways['rocket'];

        if ($gateway_config['test_mode']) {
            return array(
                'success' => true,
                'payment_url' => $this->get_test_payment_url('rocket', $transaction_id),
                'transaction_id' => $transaction_id
            );
        }

        // Rocket API integration would be implemented here
        // For now, return test URL
        return array(
            'success' => true,
            'payment_url' => $this->get_test_payment_url('rocket', $transaction_id),
            'transaction_id' => $transaction_id
        );
    }

    /**
     * Process Upay payment
     */
    private function process_upay_payment($transaction_id, $amount) {
        $gateway_config = $this->gateways['upay'];

        if ($gateway_config['test_mode']) {
            return array(
                'success' => true,
                'payment_url' => $this->get_test_payment_url('upay', $transaction_id),
                'transaction_id' => $transaction_id
            );
        }

        // Upay API integration would be implemented here
        return array(
            'success' => true,
            'payment_url' => $this->get_test_payment_url('upay', $transaction_id),
            'transaction_id' => $transaction_id
        );
    }

    /**
     * Add payment admin menu
     */
    public function add_payment_admin_menu() {
        add_submenu_page(
            'deshiflix-premium',
            __('Payment Settings', 'deshiflix'),
            __('Payment Settings', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-payment-settings',
            array($this, 'payment_settings_page')
        );
    }

    /**
     * Payment settings page
     */
    public function payment_settings_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Payment Gateway Settings', 'deshiflix'); ?></h1>

            <div class="payment-gateways-status">
                <h2><?php _e('Gateway Status', 'deshiflix'); ?></h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Gateway', 'deshiflix'); ?></th>
                            <th><?php _e('Status', 'deshiflix'); ?></th>
                            <th><?php _e('Mode', 'deshiflix'); ?></th>
                            <th><?php _e('Actions', 'deshiflix'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($this->gateways as $gateway_id => $gateway): ?>
                        <tr>
                            <td>
                                <strong><?php echo esc_html($gateway['title']); ?></strong><br>
                                <small><?php echo esc_html($gateway['description']); ?></small>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $gateway['enabled'] ? 'enabled' : 'disabled'; ?>">
                                    <?php echo $gateway['enabled'] ? __('Enabled', 'deshiflix') : __('Disabled', 'deshiflix'); ?>
                                </span>
                            </td>
                            <td>
                                <span class="mode-badge <?php echo $gateway['test_mode'] ? 'test' : 'live'; ?>">
                                    <?php echo $gateway['test_mode'] ? __('Test', 'deshiflix') : __('Live', 'deshiflix'); ?>
                                </span>
                            </td>
                            <td>
                                <button class="button test-gateway" data-gateway="<?php echo esc_attr($gateway_id); ?>">
                                    <?php _e('Test', 'deshiflix'); ?>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="payment-statistics">
                <h2><?php _e('Payment Statistics', 'deshiflix'); ?></h2>
                <?php $this->render_payment_statistics(); ?>
            </div>
        </div>

        <style>
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-badge.enabled {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.disabled {
            background: #f8d7da;
            color: #721c24;
        }

        .mode-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .mode-badge.test {
            background: #fff3cd;
            color: #856404;
        }

        .mode-badge.live {
            background: #d1ecf1;
            color: #0c5460;
        }
        </style>
        <?php
    }

    /**
     * Render payment statistics
     */
    private function render_payment_statistics() {
        global $wpdb;

        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';

        // Get payment method statistics
        $payment_stats = $wpdb->get_results(
            "SELECT payment_method, COUNT(*) as count, SUM(amount) as total_amount
             FROM $table_transactions
             WHERE status = 'completed'
             GROUP BY payment_method
             ORDER BY total_amount DESC"
        );

        if (empty($payment_stats)) {
            echo '<p>' . __('No payment statistics available.', 'deshiflix') . '</p>';
            return;
        }

        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>' . __('Payment Method', 'deshiflix') . '</th>';
        echo '<th>' . __('Transactions', 'deshiflix') . '</th>';
        echo '<th>' . __('Total Amount', 'deshiflix') . '</th>';
        echo '<th>' . __('Average', 'deshiflix') . '</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($payment_stats as $stat) {
            $average = $stat->count > 0 ? $stat->total_amount / $stat->count : 0;
            echo '<tr>';
            echo '<td>' . ucfirst($stat->payment_method) . '</td>';
            echo '<td>' . number_format($stat->count) . '</td>';
            echo '<td>৳' . number_format($stat->total_amount, 2) . '</td>';
            echo '<td>৳' . number_format($average, 2) . '</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
    }

    /**
     * Save payment settings
     */
    public function save_payment_settings() {
        if (isset($_POST['save_payment_settings']) && wp_verify_nonce($_POST['payment_settings_nonce'], 'save_payment_settings')) {
            // This would save payment gateway settings
            // Implementation would be similar to premium settings
        }
    }
}
