<?php
/*
* Simple Live TV Player Template
* Clean and lightweight player for live streaming
*/

// Get channel data
$channel_slug = isset($_GET['channel']) ? sanitize_text_field($_GET['channel']) : '';
if (empty($channel_slug)) {
    wp_redirect(get_permalink(76));
    exit;
}

global $wpdb;
$table_channels = $wpdb->prefix . 'doo_livetv_channels';
$table_categories = $wpdb->prefix . 'doo_livetv_categories';

$channel = $wpdb->get_row($wpdb->prepare(
    "SELECT c.*, cat.name as category_name, cat.color as category_color
     FROM $table_channels c
     LEFT JOIN $table_categories cat ON c.category_id = cat.id
     WHERE c.slug = %s AND c.status = 'active'",
    $channel_slug
));

if (!$channel) {
    wp_redirect(get_permalink(76));
    exit;
}

// Update view count
$wpdb->query($wpdb->prepare(
    "UPDATE $table_channels SET views = views + 1 WHERE id = %d",
    $channel->id
));
?>

<div class="live-tv-content">
    <!-- Breadcrumb -->
    <div class="breadcrumb" style="margin-bottom: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
        <a href="<?php echo get_permalink(76); ?>" style="color: #007cba; text-decoration: none;">
            <i class="fas fa-tv"></i> Live TV
        </a>
        <span style="margin: 0 10px; color: #ccc;">›</span>
        <?php if ($channel->category_name): ?>
            <span style="color: #ccc;"><?php echo esc_html($channel->category_name); ?></span>
            <span style="margin: 0 10px; color: #ccc;">›</span>
        <?php endif; ?>
        <span style="color: #fff; font-weight: 600;"><?php echo esc_html($channel->name); ?></span>
    </div>

    <!-- Simple Video Player -->
    <div class="simple-player-container" style="background: #000; border-radius: 12px; overflow: hidden; margin-bottom: 30px; box-shadow: 0 8px 32px rgba(0,0,0,0.3);">
        <?php if ($channel->stream_url): ?>
            <!-- Video Element -->
            <video
                id="simple-video-player"
                class="tv-focusable"
                tabindex="0"
                controls
                autoplay
                muted
                playsinline
                webkit-playsinline
                style="width: 100%; height: 500px; background: #000; display: block;">
                <source src="<?php echo esc_url($channel->stream_url); ?>" type="application/vnd.apple.mpegurl">
                <source src="<?php echo esc_url($channel->stream_url); ?>" type="video/mp4">
                <?php if ($channel->backup_url): ?>
                    <source src="<?php echo esc_url($channel->backup_url); ?>" type="application/vnd.apple.mpegurl">
                    <source src="<?php echo esc_url($channel->backup_url); ?>" type="video/mp4">
                <?php endif; ?>
                Your browser does not support the video tag.
            </video>

            <!-- Error Message -->
            <div id="error-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.95); display: none; align-items: center; justify-content: center; z-index: 10;">
                <div style="text-align: center; color: white; padding: 30px; max-width: 500px;">
                    <div style="font-size: 4rem; margin-bottom: 20px;">📺</div>
                    <h3 style="margin-bottom: 15px;">Stream Not Available</h3>
                    <p style="margin-bottom: 20px; color: #ccc;">Unable to load the stream. This might be due to:</p>
                    <ul style="text-align: left; margin: 20px 0; color: #ccc; font-size: 14px;">
                        <li>Stream server is offline</li>
                        <li>CORS (Cross-Origin) restrictions</li>
                        <li>Network connectivity issues</li>
                        <li>Invalid stream URL</li>
                    </ul>
                    <div style="margin-top: 25px;">
                        <button onclick="retryStream()" style="background: #007cba; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px; margin: 5px;">
                            🔄 Retry
                        </button>
                        <button onclick="openDirect()" style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px; margin: 5px;">
                            🔗 Open Direct
                        </button>
                        <button onclick="tryAdvancedPlayer()" style="background: #ff9800; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px; margin: 5px;">
                            🎬 Advanced Player
                        </button>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div style="width: 100%; height: 500px; background: #000; display: flex; align-items: center; justify-content: center; color: white;">
                <div style="text-align: center;">
                    <div style="font-size: 4rem; margin-bottom: 20px;">📺</div>
                    <h3>No Stream Available</h3>
                    <p style="color: #ccc;">Stream URL not configured for this channel</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Channel Information -->
    <div class="channel-info" style="display: flex; gap: 30px; margin-bottom: 30px; flex-wrap: wrap;">
        <div class="channel-details" style="flex: 1; min-width: 300px;">
            <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
                <?php if ($channel->logo_url): ?>
                    <img src="<?php echo esc_url($channel->logo_url); ?>" 
                         alt="<?php echo esc_attr($channel->name); ?>" 
                         style="width: 80px; height: 80px; object-fit: cover; border-radius: 12px; border: 3px solid rgba(255,255,255,0.2);">
                <?php else: ?>
                    <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: rgba(255,255,255,0.6); font-size: 2rem; border: 3px solid rgba(255,255,255,0.2);">
                        <i class="fas fa-tv"></i>
                    </div>
                <?php endif; ?>
                
                <div>
                    <h1 style="font-size: 2rem; font-weight: 700; margin-bottom: 10px; color: #ffffff;"><?php echo esc_html($channel->name); ?></h1>
                    
                    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                        <?php if ($channel->category_name): ?>
                            <span style="background: <?php echo esc_attr($channel->category_color ?: '#007cba'); ?>; color: white; padding: 6px 12px; border-radius: 15px; font-size: 14px; font-weight: 600;">
                                <i class="fas fa-tag"></i> <?php echo esc_html($channel->category_name); ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($channel->quality): ?>
                            <span style="background: #28a745; color: white; padding: 6px 12px; border-radius: 15px; font-size: 14px; font-weight: 600;">
                                <i class="fas fa-hd-video"></i> <?php echo esc_html($channel->quality); ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($channel->country): ?>
                            <span style="background: rgba(255,255,255,0.2); color: white; padding: 6px 12px; border-radius: 15px; font-size: 14px; font-weight: 600;">
                                <i class="fas fa-globe"></i> <?php echo esc_html($channel->country); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <?php if ($channel->description): ?>
                <p style="color: rgba(255,255,255,0.9); line-height: 1.6; margin-bottom: 20px;"><?php echo esc_html($channel->description); ?></p>
            <?php endif; ?>
        </div>
        
        <div class="channel-stats" style="text-align: center; min-width: 200px;">
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 12px; border: 2px solid rgba(255,255,255,0.2);">
                <div style="font-size: 2rem; color: #007cba; margin-bottom: 10px;">
                    <i class="fas fa-eye"></i>
                </div>
                <div style="font-size: 1.5rem; font-weight: 700; color: #ffffff; margin-bottom: 5px;">
                    <?php echo number_format($channel->views); ?>
                </div>
                <div style="color: rgba(255,255,255,0.7); font-size: 14px;">
                    Total Views
                </div>
            </div>
        </div>
    </div>

    <!-- Related Channels Section -->
    <?php
    // Get related channels from same category
    $livetv = new DooLiveTV();
    $related_channels = $livetv->get_channels(array(
        'category_id' => $channel->category_id,
        'limit' => 12,
        'status' => 'active',
        'exclude_id' => $channel->id
    ));

    if (!empty($related_channels)): ?>
    <div class="related-channels-section" style="margin-top: 40px;">
        <h3 style="color: #fff; font-size: 1.5rem; margin-bottom: 20px; text-align: center;">
            📺 Related Channels
        </h3>

        <div class="related-channels-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
            <?php foreach ($related_channels as $related): ?>
                <div class="related-channel-card tv-focusable" style="background: rgba(255,255,255,0.05); border-radius: 12px; overflow: hidden; transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.1); cursor: pointer;"
                     onclick="window.location.href='<?php echo add_query_arg('channel', $related->slug, get_permalink(76)); ?>'">

                    <div class="related-channel-poster" style="position: relative; height: 120px; background: linear-gradient(135deg, #2c2c54 0%, #40407a 100%);">
                        <?php if ($related->logo_url): ?>
                            <img src="<?php echo esc_url($related->logo_url); ?>"
                                 alt="<?php echo esc_attr($related->name); ?>"
                                 style="width: 100%; height: 100%; object-fit: cover;"
                                 loading="lazy">
                        <?php else: ?>
                            <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column; color: rgba(255,255,255,0.8);">
                                <i class="fas fa-tv" style="font-size: 1.5rem; margin-bottom: 8px; opacity: 0.8;"></i>
                                <span style="font-size: 0.75rem; font-weight: bold; text-transform: uppercase; letter-spacing: 0.5px; text-align: center; padding: 0 5px; line-height: 1.2;"><?php echo esc_html($related->name); ?></span>
                            </div>
                        <?php endif; ?>

                        <!-- Quality badges -->
                        <div style="position: absolute; top: 8px; left: 8px; display: flex; gap: 5px;">
                            <span style="background: #e74c3c; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.7rem; font-weight: bold;">HD</span>
                            <span style="background: #27ae60; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.7rem; font-weight: bold; animation: pulse 2s infinite;">LIVE</span>
                        </div>

                        <!-- Play button overlay -->
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.7); border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; opacity: 0; transition: opacity 0.3s ease;" class="play-overlay">
                            <i class="fas fa-play" style="color: white; font-size: 1rem; margin-left: 2px;"></i>
                        </div>
                    </div>

                    <div style="padding: 12px;">
                        <h4 style="color: #fff; font-size: 0.9rem; margin: 0 0 5px 0; font-weight: 600; line-height: 1.3; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            <?php echo esc_html($related->name); ?>
                        </h4>
                        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.75rem; color: rgba(255,255,255,0.7);">
                            <span><?php echo esc_html($related->category_name ?: 'General'); ?></span>
                            <span><?php echo number_format($related->views ?: 0); ?> views</span>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Back to Channels -->
    <div style="text-align: center; margin-top: 40px;">
        <a href="<?php echo get_permalink(76); ?>" class="tv-focusable" tabindex="0" style="background: #007cba; color: white; padding: 15px 30px; border-radius: 8px; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 10px;">
            <i class="fas fa-arrow-left"></i>
            Back to Channels
        </a>
    </div>
</div>

<style>
.simple-player-container {
    position: relative;
}

#simple-video-player {
    outline: none;
}

#simple-video-player::-webkit-media-controls-panel {
    background-color: rgba(0,0,0,0.8);
}

/* Fix hover dark overlay issue */
#simple-video-player:hover {
    filter: none !important;
    opacity: 1 !important;
}

.simple-player-container:hover {
    filter: none !important;
    opacity: 1 !important;
}

/* Remove any dark overlays on hover */
.simple-player-container:hover::before,
.simple-player-container:hover::after {
    display: none !important;
}

/* Related Channels Styling */
.related-channel-card:hover {
    transform: translateY(-5px) !important;
    border-color: #007cba !important;
    box-shadow: 0 10px 30px rgba(0, 124, 186, 0.3) !important;
}

.related-channel-card:hover .play-overlay {
    opacity: 1 !important;
}

.related-channels-grid {
    scrollbar-width: thin;
    scrollbar-color: #007cba transparent;
}

.related-channels-grid::-webkit-scrollbar {
    height: 8px;
}

.related-channels-grid::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 4px;
}

.related-channels-grid::-webkit-scrollbar-thumb {
    background: #007cba;
    border-radius: 4px;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Responsive */
@media (max-width: 768px) {
    #simple-video-player {
        height: 250px !important;
    }

    .channel-info {
        flex-direction: column !important;
    }

    .channel-details div:first-child {
        flex-direction: column !important;
        text-align: center !important;
    }

    .related-channels-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
        gap: 10px !important;
    }

    .related-channel-poster {
        height: 100px !important;
    }
}

@media (max-width: 480px) {
    .related-channels-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple Live TV player loaded');
    
    var video = document.getElementById('simple-video-player');
    var errorOverlay = document.getElementById('error-overlay');
    
    if (!video) return;
    
    // Get stream URL from first source
    var sources = video.querySelectorAll('source');
    var streamUrl = sources.length > 0 ? sources[0].src : '';
    
    console.log('Stream URL:', streamUrl);
    
    // Initialize HLS if needed
    if (streamUrl.includes('.m3u8') && typeof Hls !== 'undefined' && Hls.isSupported()) {
        console.log('Initializing HLS.js');
        var hls = new Hls({
            enableWorker: false,
            lowLatencyMode: false,
            debug: true,
            xhrSetup: function(xhr, url) {
                // Add headers to bypass CORS issues
                xhr.setRequestHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
                xhr.setRequestHeader('Referer', window.location.origin);
            },
            fetchSetup: function(context, initParams) {
                // Setup fetch with proper headers
                initParams.headers = initParams.headers || {};
                initParams.headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
                initParams.headers['Referer'] = window.location.origin;
                initParams.mode = 'cors';
                initParams.credentials = 'omit';
                return new Request(context.url, initParams);
            },
            manifestLoadingTimeOut: 30000,
            manifestLoadingMaxRetry: 3,
            manifestLoadingRetryDelay: 2000,
            levelLoadingTimeOut: 30000,
            levelLoadingMaxRetry: 4,
            fragLoadingTimeOut: 30000,
            fragLoadingMaxRetry: 6,
            fragLoadingRetryDelay: 1000
        });

        hls.loadSource(streamUrl);
        hls.attachMedia(video);

        hls.on(Hls.Events.MANIFEST_PARSED, function() {
            console.log('HLS manifest loaded successfully');
            hideError();
        });

        hls.on(Hls.Events.ERROR, function(event, data) {
            console.error('HLS error:', data);

            if (data.fatal) {
                switch(data.type) {
                    case Hls.ErrorTypes.NETWORK_ERROR:
                        console.log('Network error, trying to recover...');
                        setTimeout(function() {
                            hls.startLoad();
                        }, 1000);
                        break;
                    case Hls.ErrorTypes.MEDIA_ERROR:
                        console.log('Media error, trying to recover...');
                        setTimeout(function() {
                            hls.recoverMediaError();
                        }, 1000);
                        break;
                    default:
                        console.log('Fatal error, showing error message');
                        showError();
                        break;
                }
            }
        });

        hls.on(Hls.Events.FRAG_LOADED, function() {
            console.log('Fragment loaded successfully');
        });

        // Store hls instance globally for debugging
        window.hlsInstance = hls;
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        console.log('Using native HLS support');
        video.src = streamUrl;
        video.load();
    }
    
    // Video events
    video.addEventListener('error', function(e) {
        console.error('Video error:', e);
        showError();
    });
    
    video.addEventListener('canplay', function() {
        console.log('Video can play');
        hideError();
    });
    
    function showError() {
        if (errorOverlay) {
            errorOverlay.style.display = 'flex';
        }
    }
    
    function hideError() {
        if (errorOverlay) {
            errorOverlay.style.display = 'none';
        }
    }
    
    // Global functions
    window.retryStream = function() {
        console.log('Retrying stream');
        hideError();
        video.load();
        video.play().catch(function(e) {
            console.log('Play failed:', e);
            showError();
        });
    };
    
    window.openDirect = function() {
        if (streamUrl) {
            window.open(streamUrl, '_blank');
        }
    };

    window.tryAdvancedPlayer = function() {
        var advancedPlayerUrl = '<?php echo get_template_directory_uri(); ?>/advanced-player.php?stream=' + encodeURIComponent(streamUrl);
        window.open(advancedPlayerUrl, '_blank');
    };
});
</script>
