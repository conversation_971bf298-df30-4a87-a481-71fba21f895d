/**
 * DeshiFlix Premium Frontend JavaScript
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

(function($) {
    'use strict';
    
    // Premium system object
    var DeshiFlixPremium = {
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.checkPremiumStatus();
            this.initContentProtection();
            this.initPaymentSystem();
        },
        
        // Bind events
        bindEvents: function() {
            $(document).on('click', '.btn-premium-upgrade', this.handleUpgradeClick);
            $(document).on('click', '.btn-cancel-premium', this.handleCancelClick);
            $(document).on('click', '.premium-content-unlock', this.handleContentUnlock);
            $(document).on('click', '.download-premium-content', this.handlePremiumDownload);
            $(document).on('submit', '.premium-payment-form', this.handlePaymentSubmit);
        },
        
        // Check premium status
        checkPremiumStatus: function() {
            if (typeof deshiflix_premium === 'undefined') {
                return;
            }
            
            $.ajax({
                url: deshiflix_premium.ajax_url,
                type: 'POST',
                data: {
                    action: 'premium_check_status',
                    nonce: deshiflix_premium.nonce
                },
                success: function(response) {
                    if (response.success) {
                        DeshiFlixPremium.updatePremiumUI(response.data);
                    }
                }
            });
        },
        
        // Update premium UI
        updatePremiumUI: function(data) {
            if (data.is_premium) {
                $('body').addClass('user-premium');
                $('.premium-upgrade-prompt').hide();
                $('.premium-user-badge').show();
            } else {
                $('body').addClass('user-free');
                $('.premium-upgrade-prompt').show();
                $('.premium-user-badge').hide();
            }
        },
        
        // Initialize content protection
        initContentProtection: function() {
            this.protectPremiumContent();
            this.addPremiumIndicators();
            this.initVideoProtection();
        },
        
        // Protect premium content
        protectPremiumContent: function() {
            $('.premium-content-lock').each(function() {
                var $container = $(this);
                var postId = $container.data('post-id');
                
                // Check access for this content
                DeshiFlixPremium.checkContentAccess(postId, function(hasAccess) {
                    if (!hasAccess) {
                        $container.find('.content-player').hide();
                        $container.find('.download-links').hide();
                        $container.find('.premium-lock-overlay').show();
                    }
                });
            });
        },
        
        // Check content access
        checkContentAccess: function(postId, callback) {
            $.ajax({
                url: deshiflix_premium.ajax_url,
                type: 'POST',
                data: {
                    action: 'check_content_access',
                    nonce: deshiflix_premium.nonce,
                    post_id: postId
                },
                success: function(response) {
                    if (response.success) {
                        callback(response.data.has_access);
                    } else {
                        callback(false);
                    }
                },
                error: function() {
                    callback(false);
                }
            });
        },
        
        // Add premium indicators
        addPremiumIndicators: function() {
            $('.post-item[data-premium="true"]').each(function() {
                var $item = $(this);
                var premiumLevel = $item.data('premium-level') || 'premium';
                
                var indicator = '<div class="premium-indicator">' +
                               '<span class="premium-badge level-' + premiumLevel + '">' +
                               '💎 Premium' +
                               '</span>' +
                               '</div>';
                
                $item.find('.poster').append(indicator);
            });
        },
        
        // Initialize video protection
        initVideoProtection: function() {
            // Disable right-click on premium videos
            $('.premium-video-player').on('contextmenu', function(e) {
                e.preventDefault();
                DeshiFlixPremium.showProtectionMessage();
                return false;
            });
            
            // Disable video download
            $('.premium-video-player video').attr('controlsList', 'nodownload');
            
            // Add watermark to premium videos
            this.addVideoWatermark();
        },
        
        // Add video watermark
        addVideoWatermark: function() {
            $('.premium-video-player').each(function() {
                var $player = $(this);
                var watermark = '<div class="video-watermark">DeshiFlix Premium</div>';
                $player.append(watermark);
            });
        },
        
        // Show protection message
        showProtectionMessage: function() {
            var message = deshiflix_premium.messages.content_protected || 'This content is protected';
            this.showNotification(message, 'warning');
        },
        
        // Handle upgrade click
        handleUpgradeClick: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var planId = $btn.data('plan-id');
            var planName = $btn.data('plan-name');
            var planPrice = $btn.data('plan-price');
            
            DeshiFlixPremium.showUpgradeModal(planId, planName, planPrice);
        },
        
        // Show upgrade modal
        showUpgradeModal: function(planId, planName, planPrice) {
            var modalHtml = this.getUpgradeModalHtml(planId, planName, planPrice);
            
            // Remove existing modal
            $('.premium-upgrade-modal').remove();
            
            // Add modal to body
            $('body').append(modalHtml);
            
            // Show modal
            $('.premium-upgrade-modal').fadeIn();
            
            // Bind modal events
            this.bindModalEvents();
        },
        
        // Get upgrade modal HTML
        getUpgradeModalHtml: function(planId, planName, planPrice) {
            return '<div class="premium-upgrade-modal">' +
                   '<div class="modal-overlay"></div>' +
                   '<div class="modal-content">' +
                   '<div class="modal-header">' +
                   '<h3>Upgrade to Premium</h3>' +
                   '<button class="modal-close">&times;</button>' +
                   '</div>' +
                   '<div class="modal-body">' +
                   '<div class="selected-plan">' +
                   '<h4>' + planName + '</h4>' +
                   '<div class="plan-price">৳' + planPrice + '</div>' +
                   '</div>' +
                   '<div class="payment-methods">' +
                   this.getPaymentMethodsHtml() +
                   '</div>' +
                   '</div>' +
                   '<div class="modal-footer">' +
                   '<button class="btn btn-cancel">Cancel</button>' +
                   '<button class="btn btn-upgrade" data-plan-id="' + planId + '">Proceed to Payment</button>' +
                   '</div>' +
                   '</div>' +
                   '</div>';
        },
        
        // Get payment methods HTML
        getPaymentMethodsHtml: function() {
            return '<div class="payment-methods-list">' +
                   '<div class="payment-method" data-method="bkash">' +
                   '<img src="' + deshiflix_premium.assets_url + 'images/bkash-logo.png" alt="bKash">' +
                   '<span>bKash</span>' +
                   '</div>' +
                   '<div class="payment-method" data-method="nagad">' +
                   '<img src="' + deshiflix_premium.assets_url + 'images/nagad-logo.png" alt="Nagad">' +
                   '<span>Nagad</span>' +
                   '</div>' +
                   '<div class="payment-method" data-method="rocket">' +
                   '<img src="' + deshiflix_premium.assets_url + 'images/rocket-logo.png" alt="Rocket">' +
                   '<span>Rocket</span>' +
                   '</div>' +
                   '</div>';
        },
        
        // Bind modal events
        bindModalEvents: function() {
            var self = this;
            
            // Close modal
            $(document).on('click', '.modal-close, .modal-overlay, .btn-cancel', function() {
                $('.premium-upgrade-modal').fadeOut(function() {
                    $(this).remove();
                });
            });
            
            // Select payment method
            $(document).on('click', '.payment-method', function() {
                $('.payment-method').removeClass('selected');
                $(this).addClass('selected');
            });
            
            // Proceed to payment
            $(document).on('click', '.btn-upgrade', function() {
                var planId = $(this).data('plan-id');
                var selectedMethod = $('.payment-method.selected').data('method');
                
                if (!selectedMethod) {
                    self.showNotification('Please select a payment method', 'error');
                    return;
                }
                
                self.processPayment(planId, selectedMethod);
            });
        },
        
        // Process payment
        processPayment: function(planId, paymentMethod) {
            var $btn = $('.btn-upgrade');
            $btn.prop('disabled', true).text('Processing...');
            
            $.ajax({
                url: deshiflix_premium.ajax_url,
                type: 'POST',
                data: {
                    action: 'process_premium_payment',
                    nonce: deshiflix_premium.nonce,
                    plan_id: planId,
                    gateway: paymentMethod
                },
                success: function(response) {
                    if (response.success) {
                        // Redirect to payment gateway
                        window.location.href = response.data.payment_url;
                    } else {
                        DeshiFlixPremium.showNotification(response.data.message || 'Payment failed', 'error');
                        $btn.prop('disabled', false).text('Proceed to Payment');
                    }
                },
                error: function() {
                    DeshiFlixPremium.showNotification('An error occurred', 'error');
                    $btn.prop('disabled', false).text('Proceed to Payment');
                }
            });
        },
        
        // Handle cancel click
        handleCancelClick: function(e) {
            e.preventDefault();
            
            if (!confirm(deshiflix_premium.messages.confirm_cancel)) {
                return;
            }
            
            var $btn = $(this);
            $btn.prop('disabled', true).text('Cancelling...');
            
            $.ajax({
                url: deshiflix_premium.ajax_url,
                type: 'POST',
                data: {
                    action: 'cancel_premium',
                    nonce: deshiflix_premium.nonce
                },
                success: function(response) {
                    if (response.success) {
                        DeshiFlixPremium.showNotification('Subscription cancelled successfully', 'success');
                        location.reload();
                    } else {
                        DeshiFlixPremium.showNotification(response.data.message || 'Cancellation failed', 'error');
                        $btn.prop('disabled', false).text('Cancel Subscription');
                    }
                },
                error: function() {
                    DeshiFlixPremium.showNotification('An error occurred', 'error');
                    $btn.prop('disabled', false).text('Cancel Subscription');
                }
            });
        },
        
        // Handle content unlock
        handleContentUnlock: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var postId = $btn.data('post-id');
            
            // Check if user is logged in
            if (!deshiflix_premium.is_user_premium) {
                DeshiFlixPremium.showLoginPrompt();
                return;
            }
            
            // Show upgrade prompt
            DeshiFlixPremium.showUpgradeModal();
        },
        
        // Handle premium download
        handlePremiumDownload: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var downloadUrl = $btn.attr('href');
            var postId = $btn.data('post-id');
            
            // Check download limits
            DeshiFlixPremium.checkDownloadLimits(function(allowed, remaining) {
                if (allowed) {
                    // Track download and proceed
                    DeshiFlixPremium.trackDownload(postId);
                    window.open(downloadUrl, '_blank');
                    
                    if (remaining !== 'unlimited') {
                        DeshiFlixPremium.showNotification(
                            'Download started. ' + remaining + ' downloads remaining this month.',
                            'info'
                        );
                    }
                } else {
                    DeshiFlixPremium.showNotification(
                        'Download limit reached. Upgrade your plan for more downloads.',
                        'warning'
                    );
                }
            });
        },
        
        // Check download limits
        checkDownloadLimits: function(callback) {
            $.ajax({
                url: deshiflix_premium.ajax_url,
                type: 'POST',
                data: {
                    action: 'check_download_limits',
                    nonce: deshiflix_premium.nonce
                },
                success: function(response) {
                    if (response.success) {
                        callback(response.data.allowed, response.data.remaining);
                    } else {
                        callback(false, 0);
                    }
                },
                error: function() {
                    callback(false, 0);
                }
            });
        },
        
        // Track download
        trackDownload: function(postId) {
            $.ajax({
                url: deshiflix_premium.ajax_url,
                type: 'POST',
                data: {
                    action: 'track_premium_download',
                    nonce: deshiflix_premium.nonce,
                    post_id: postId
                }
            });
        },
        
        // Initialize payment system
        initPaymentSystem: function() {
            this.bindPaymentEvents();
        },
        
        // Bind payment events
        bindPaymentEvents: function() {
            $(document).on('submit', '.premium-payment-form', this.handlePaymentSubmit);
        },
        
        // Handle payment submit
        handlePaymentSubmit: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var formData = $form.serialize();
            
            $form.find('.btn-submit').prop('disabled', true).text('Processing...');
            
            $.ajax({
                url: deshiflix_premium.ajax_url,
                type: 'POST',
                data: formData + '&action=process_premium_payment&nonce=' + deshiflix_premium.nonce,
                success: function(response) {
                    if (response.success) {
                        window.location.href = response.data.payment_url;
                    } else {
                        DeshiFlixPremium.showNotification(response.data.message || 'Payment failed', 'error');
                        $form.find('.btn-submit').prop('disabled', false).text('Complete Payment');
                    }
                },
                error: function() {
                    DeshiFlixPremium.showNotification('An error occurred', 'error');
                    $form.find('.btn-submit').prop('disabled', false).text('Complete Payment');
                }
            });
        },
        
        // Show login prompt
        showLoginPrompt: function() {
            var loginUrl = deshiflix_premium.login_url || '/wp-login.php';
            var message = 'Please login to access premium content';
            
            if (confirm(message + '. Redirect to login page?')) {
                window.location.href = loginUrl;
            }
        },
        
        // Show notification
        showNotification: function(message, type) {
            type = type || 'info';
            
            var notification = '<div class="premium-notification notification-' + type + '">' +
                              '<span class="notification-message">' + message + '</span>' +
                              '<button class="notification-close">&times;</button>' +
                              '</div>';
            
            // Remove existing notifications
            $('.premium-notification').remove();
            
            // Add notification
            $('body').append(notification);
            
            // Auto hide after 5 seconds
            setTimeout(function() {
                $('.premium-notification').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
            
            // Manual close
            $(document).on('click', '.notification-close', function() {
                $(this).parent().fadeOut(function() {
                    $(this).remove();
                });
            });
        },
        
        // Utility functions
        utils: {
            // Format currency
            formatCurrency: function(amount) {
                return '৳' + parseFloat(amount).toLocaleString('en-BD', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2
                });
            },
            
            // Format date
            formatDate: function(dateString) {
                var date = new Date(dateString);
                return date.toLocaleDateString('en-BD', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            },
            
            // Get time remaining
            getTimeRemaining: function(endDate) {
                var now = new Date().getTime();
                var end = new Date(endDate).getTime();
                var distance = end - now;
                
                if (distance < 0) {
                    return 'Expired';
                }
                
                var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                
                if (days > 0) {
                    return days + ' days remaining';
                } else if (hours > 0) {
                    return hours + ' hours remaining';
                } else {
                    return 'Expires soon';
                }
            }
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        DeshiFlixPremium.init();
    });
    
    // Make available globally
    window.DeshiFlixPremium = DeshiFlixPremium;
    
})(jQuery);
