<?php
/**
 * Payment Gateway Configuration
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Payment_Gateway_Config {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_menu', array($this, 'add_config_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('wp_ajax_test_payment_gateway', array($this, 'test_gateway_connection'));
    }
    
    /**
     * Add configuration menu
     */
    public function add_config_menu() {
        add_submenu_page(
            'deshiflix-premium',
            'Payment Gateways',
            'Payment Gateways',
            'manage_options',
            'premium-payment-config',
            array($this, 'config_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('deshiflix_payment_settings', 'deshiflix_payment_gateways');
    }
    
    /**
     * Configuration page
     */
    public function config_page() {
        if (isset($_POST['save_settings'])) {
            $this->save_gateway_settings();
            echo '<div class="notice notice-success"><p>Payment gateway settings saved!</p></div>';
        }
        
        $settings = get_option('deshiflix_payment_gateways', $this->get_default_settings());
        ?>
        <div class="wrap">
            <h1>💳 Payment Gateway Configuration</h1>
            
            <div class="payment-config-dashboard">
                <div class="config-tabs">
                    <ul class="tab-nav">
                        <li class="active"><a href="#bkash" data-tab="bkash">bKash</a></li>
                        <li><a href="#nagad" data-tab="nagad">Nagad</a></li>
                        <li><a href="#rocket" data-tab="rocket">Rocket</a></li>
                        <li><a href="#sslcommerz" data-tab="sslcommerz">SSLCommerz</a></li>
                        <li><a href="#aamarpay" data-tab="aamarpay">aamarPay</a></li>
                        <li><a href="#general" data-tab="general">General</a></li>
                    </ul>
                </div>
                
                <form method="post" class="payment-config-form">
                    
                    <!-- bKash Configuration -->
                    <div id="bkash" class="tab-content active">
                        <div class="gateway-header">
                            <h2>🟡 bKash Configuration</h2>
                            <p>Configure bKash payment gateway for mobile banking</p>
                        </div>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">Enable bKash</th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="gateways[bkash][enabled]" value="1" 
                                               <?php checked($settings['bkash']['enabled'] ?? false); ?>>
                                        Enable bKash payments
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Environment</th>
                                <td>
                                    <select name="gateways[bkash][environment]">
                                        <option value="sandbox" <?php selected($settings['bkash']['environment'] ?? '', 'sandbox'); ?>>
                                            Sandbox (Testing)
                                        </option>
                                        <option value="live" <?php selected($settings['bkash']['environment'] ?? '', 'live'); ?>>
                                            Live (Production)
                                        </option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">App Key</th>
                                <td>
                                    <input type="text" name="gateways[bkash][app_key]" 
                                           value="<?php echo esc_attr($settings['bkash']['app_key'] ?? ''); ?>" 
                                           class="regular-text" placeholder="Your bKash App Key">
                                    <p class="description">Get this from your bKash merchant dashboard</p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">App Secret</th>
                                <td>
                                    <input type="password" name="gateways[bkash][app_secret]" 
                                           value="<?php echo esc_attr($settings['bkash']['app_secret'] ?? ''); ?>" 
                                           class="regular-text" placeholder="Your bKash App Secret">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Username</th>
                                <td>
                                    <input type="text" name="gateways[bkash][username]" 
                                           value="<?php echo esc_attr($settings['bkash']['username'] ?? ''); ?>" 
                                           class="regular-text" placeholder="bKash Username">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Password</th>
                                <td>
                                    <input type="password" name="gateways[bkash][password]" 
                                           value="<?php echo esc_attr($settings['bkash']['password'] ?? ''); ?>" 
                                           class="regular-text" placeholder="bKash Password">
                                </td>
                            </tr>
                        </table>
                        
                        <div class="gateway-test">
                            <button type="button" class="button button-secondary" onclick="testGateway('bkash')">
                                Test bKash Connection
                            </button>
                            <div id="bkash-test-result" class="test-result"></div>
                        </div>
                    </div>
                    
                    <!-- Nagad Configuration -->
                    <div id="nagad" class="tab-content">
                        <div class="gateway-header">
                            <h2>🟠 Nagad Configuration</h2>
                            <p>Configure Nagad payment gateway</p>
                        </div>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">Enable Nagad</th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="gateways[nagad][enabled]" value="1" 
                                               <?php checked($settings['nagad']['enabled'] ?? false); ?>>
                                        Enable Nagad payments
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Merchant ID</th>
                                <td>
                                    <input type="text" name="gateways[nagad][merchant_id]" 
                                           value="<?php echo esc_attr($settings['nagad']['merchant_id'] ?? ''); ?>" 
                                           class="regular-text" placeholder="Nagad Merchant ID">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Merchant Key</th>
                                <td>
                                    <input type="password" name="gateways[nagad][merchant_key]" 
                                           value="<?php echo esc_attr($settings['nagad']['merchant_key'] ?? ''); ?>" 
                                           class="regular-text" placeholder="Nagad Merchant Key">
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- Rocket Configuration -->
                    <div id="rocket" class="tab-content">
                        <div class="gateway-header">
                            <h2>🚀 Rocket Configuration</h2>
                            <p>Configure Rocket payment gateway</p>
                        </div>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">Enable Rocket</th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="gateways[rocket][enabled]" value="1" 
                                               <?php checked($settings['rocket']['enabled'] ?? false); ?>>
                                        Enable Rocket payments
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Merchant Number</th>
                                <td>
                                    <input type="text" name="gateways[rocket][merchant_number]" 
                                           value="<?php echo esc_attr($settings['rocket']['merchant_number'] ?? ''); ?>" 
                                           class="regular-text" placeholder="Rocket Merchant Number">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">API Key</th>
                                <td>
                                    <input type="password" name="gateways[rocket][api_key]" 
                                           value="<?php echo esc_attr($settings['rocket']['api_key'] ?? ''); ?>" 
                                           class="regular-text" placeholder="Rocket API Key">
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- SSLCommerz Configuration -->
                    <div id="sslcommerz" class="tab-content">
                        <div class="gateway-header">
                            <h2>🔒 SSLCommerz Configuration</h2>
                            <p>Configure SSLCommerz for card payments</p>
                        </div>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">Enable SSLCommerz</th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="gateways[sslcommerz][enabled]" value="1" 
                                               <?php checked($settings['sslcommerz']['enabled'] ?? false); ?>>
                                        Enable SSLCommerz payments
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Store ID</th>
                                <td>
                                    <input type="text" name="gateways[sslcommerz][store_id]" 
                                           value="<?php echo esc_attr($settings['sslcommerz']['store_id'] ?? ''); ?>" 
                                           class="regular-text" placeholder="SSLCommerz Store ID">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Store Password</th>
                                <td>
                                    <input type="password" name="gateways[sslcommerz][store_password]" 
                                           value="<?php echo esc_attr($settings['sslcommerz']['store_password'] ?? ''); ?>" 
                                           class="regular-text" placeholder="SSLCommerz Store Password">
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- aamarPay Configuration -->
                    <div id="aamarpay" class="tab-content">
                        <div class="gateway-header">
                            <h2>💳 aamarPay Configuration</h2>
                            <p>Configure aamarPay payment gateway</p>
                        </div>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">Enable aamarPay</th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="gateways[aamarpay][enabled]" value="1" 
                                               <?php checked($settings['aamarpay']['enabled'] ?? false); ?>>
                                        Enable aamarPay payments
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Store ID</th>
                                <td>
                                    <input type="text" name="gateways[aamarpay][store_id]" 
                                           value="<?php echo esc_attr($settings['aamarpay']['store_id'] ?? ''); ?>" 
                                           class="regular-text" placeholder="aamarPay Store ID">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Signature Key</th>
                                <td>
                                    <input type="password" name="gateways[aamarpay][signature_key]" 
                                           value="<?php echo esc_attr($settings['aamarpay']['signature_key'] ?? ''); ?>" 
                                           class="regular-text" placeholder="aamarPay Signature Key">
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- General Settings -->
                    <div id="general" class="tab-content">
                        <div class="gateway-header">
                            <h2>⚙️ General Settings</h2>
                            <p>General payment configuration</p>
                        </div>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">Default Currency</th>
                                <td>
                                    <select name="gateways[general][currency]">
                                        <option value="BDT" <?php selected($settings['general']['currency'] ?? '', 'BDT'); ?>>
                                            BDT (Bangladeshi Taka)
                                        </option>
                                        <option value="USD" <?php selected($settings['general']['currency'] ?? '', 'USD'); ?>>
                                            USD (US Dollar)
                                        </option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Payment Timeout</th>
                                <td>
                                    <input type="number" name="gateways[general][timeout]" 
                                           value="<?php echo esc_attr($settings['general']['timeout'] ?? '30'); ?>" 
                                           min="10" max="300" class="small-text"> minutes
                                    <p class="description">How long to wait for payment completion</p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Success URL</th>
                                <td>
                                    <input type="url" name="gateways[general][success_url]" 
                                           value="<?php echo esc_attr($settings['general']['success_url'] ?? home_url('/premium-payment-success/')); ?>" 
                                           class="regular-text">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Failed URL</th>
                                <td>
                                    <input type="url" name="gateways[general][failed_url]" 
                                           value="<?php echo esc_attr($settings['general']['failed_url'] ?? home_url('/premium-payment-failed/')); ?>" 
                                           class="regular-text">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">Cancel URL</th>
                                <td>
                                    <input type="url" name="gateways[general][cancel_url]" 
                                           value="<?php echo esc_attr($settings['general']['cancel_url'] ?? home_url('/premium-payment-cancelled/')); ?>" 
                                           class="regular-text">
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <p class="submit">
                        <input type="submit" name="save_settings" class="button-primary" value="Save Payment Settings">
                        <button type="button" class="button button-secondary" onclick="testAllGateways()">
                            Test All Gateways
                        </button>
                    </p>
                </form>
            </div>
        </div>
        
        <style>
        .payment-config-dashboard {
            max-width: 1000px;
            margin: 20px 0;
        }
        
        .config-tabs {
            background: white;
            border-radius: 8px 8px 0 0;
            border: 1px solid #ddd;
            border-bottom: none;
        }
        
        .tab-nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .tab-nav li {
            flex: 1;
        }
        
        .tab-nav a {
            display: block;
            padding: 15px;
            text-decoration: none;
            color: #666;
            text-align: center;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab-nav li.active a,
        .tab-nav a:hover {
            color: #0073aa;
            border-bottom-color: #0073aa;
        }
        
        .payment-config-form {
            background: white;
            border: 1px solid #ddd;
            border-radius: 0 0 8px 8px;
            padding: 20px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .gateway-header {
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .gateway-header h2 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .gateway-test {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        
        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // Tab switching
            $('.tab-nav a').click(function(e) {
                e.preventDefault();
                
                var target = $(this).attr('href').substring(1);
                
                $('.tab-nav li').removeClass('active');
                $(this).parent().addClass('active');
                
                $('.tab-content').removeClass('active');
                $('#' + target).addClass('active');
            });
        });
        
        function testGateway(gateway) {
            var resultDiv = document.getElementById(gateway + '-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result';
            resultDiv.innerHTML = 'Testing ' + gateway + ' connection...';
            
            jQuery.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'test_payment_gateway',
                    gateway: gateway,
                    nonce: '<?php echo wp_create_nonce('test_payment_gateway'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        resultDiv.className = 'test-result success';
                        resultDiv.innerHTML = '✅ ' + response.data.message;
                    } else {
                        resultDiv.className = 'test-result error';
                        resultDiv.innerHTML = '❌ ' + response.data;
                    }
                },
                error: function() {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ Network error occurred';
                }
            });
        }
        
        function testAllGateways() {
            var gateways = ['bkash', 'nagad', 'rocket', 'sslcommerz', 'aamarpay'];
            gateways.forEach(function(gateway) {
                setTimeout(function() {
                    testGateway(gateway);
                }, Math.random() * 2000);
            });
        }
        </script>
        <?php
    }
    
    /**
     * Save gateway settings
     */
    private function save_gateway_settings() {
        if (isset($_POST['gateways'])) {
            $gateways = $_POST['gateways'];
            
            // Sanitize settings
            foreach ($gateways as $gateway => &$settings) {
                if (is_array($settings)) {
                    foreach ($settings as $key => &$value) {
                        if (in_array($key, ['password', 'app_secret', 'merchant_key', 'api_key', 'store_password', 'signature_key'])) {
                            $value = sanitize_text_field($value);
                        } else {
                            $value = sanitize_text_field($value);
                        }
                    }
                }
            }
            
            update_option('deshiflix_payment_gateways', $gateways);
        }
    }
    
    /**
     * Test gateway connection
     */
    public function test_gateway_connection() {
        if (!wp_verify_nonce($_POST['nonce'], 'test_payment_gateway')) {
            wp_send_json_error('Security check failed');
        }
        
        $gateway = sanitize_text_field($_POST['gateway']);
        $settings = get_option('deshiflix_payment_gateways', array());
        
        switch ($gateway) {
            case 'bkash':
                $result = $this->test_bkash_connection($settings['bkash'] ?? array());
                break;
            case 'nagad':
                $result = $this->test_nagad_connection($settings['nagad'] ?? array());
                break;
            case 'rocket':
                $result = $this->test_rocket_connection($settings['rocket'] ?? array());
                break;
            case 'sslcommerz':
                $result = $this->test_sslcommerz_connection($settings['sslcommerz'] ?? array());
                break;
            case 'aamarpay':
                $result = $this->test_aamarpay_connection($settings['aamarpay'] ?? array());
                break;
            default:
                wp_send_json_error('Invalid gateway');
        }
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    /**
     * Test bKash connection
     */
    private function test_bkash_connection($settings) {
        if (empty($settings['app_key']) || empty($settings['app_secret'])) {
            return array('success' => false, 'message' => 'App Key and App Secret are required');
        }
        
        // Test API connection
        $api_url = ($settings['environment'] === 'live') ? 
            'https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized/checkout/token/grant' :
            'https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/token/grant';
        
        $response = wp_remote_post($api_url, array(
            'timeout' => 10,
            'headers' => array(
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'username' => $settings['username'] ?? '',
                'password' => $settings['password'] ?? ''
            ),
            'body' => json_encode(array(
                'app_key' => $settings['app_key'],
                'app_secret' => $settings['app_secret']
            ))
        ));
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => 'Connection failed: ' . $response->get_error_message());
        }
        
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (isset($response_body['id_token'])) {
            return array('success' => true, 'message' => 'bKash connection successful!');
        } else {
            return array('success' => false, 'message' => 'Authentication failed: ' . ($response_body['errorMessage'] ?? 'Unknown error'));
        }
    }
    
    /**
     * Test other gateway connections (simplified)
     */
    private function test_nagad_connection($settings) {
        return array('success' => true, 'message' => 'Nagad configuration saved (test connection not implemented)');
    }
    
    private function test_rocket_connection($settings) {
        return array('success' => true, 'message' => 'Rocket configuration saved (test connection not implemented)');
    }
    
    private function test_sslcommerz_connection($settings) {
        return array('success' => true, 'message' => 'SSLCommerz configuration saved (test connection not implemented)');
    }
    
    private function test_aamarpay_connection($settings) {
        return array('success' => true, 'message' => 'aamarPay configuration saved (test connection not implemented)');
    }
    
    /**
     * Get default settings
     */
    private function get_default_settings() {
        return array(
            'bkash' => array(
                'enabled' => false,
                'environment' => 'sandbox',
                'app_key' => '',
                'app_secret' => '',
                'username' => '',
                'password' => ''
            ),
            'nagad' => array(
                'enabled' => false,
                'merchant_id' => '',
                'merchant_key' => ''
            ),
            'rocket' => array(
                'enabled' => false,
                'merchant_number' => '',
                'api_key' => ''
            ),
            'sslcommerz' => array(
                'enabled' => false,
                'store_id' => '',
                'store_password' => ''
            ),
            'aamarpay' => array(
                'enabled' => false,
                'store_id' => '',
                'signature_key' => ''
            ),
            'general' => array(
                'currency' => 'BDT',
                'timeout' => 30,
                'success_url' => home_url('/premium-payment-success/'),
                'failed_url' => home_url('/premium-payment-failed/'),
                'cancel_url' => home_url('/premium-payment-cancelled/')
            )
        );
    }
}

// Initialize payment gateway configuration
DeshiFlix_Payment_Gateway_Config::get_instance();
