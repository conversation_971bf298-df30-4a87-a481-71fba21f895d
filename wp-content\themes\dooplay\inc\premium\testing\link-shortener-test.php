<?php
/**
 * Link Shortener Premium Integration Test
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Link_Shortener_Test {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_menu', array($this, 'add_test_menu'));
        add_action('wp_ajax_test_link_shortener_integration', array($this, 'run_integration_test'));
    }
    
    /**
     * Add test menu
     */
    public function add_test_menu() {
        add_submenu_page(
            'deshiflix-premium',
            'Link Shortener Test',
            'Link Shortener Test',
            'manage_options',
            'premium-link-shortener-test',
            array($this, 'test_page')
        );
    }
    
    /**
     * Test page
     */
    public function test_page() {
        ?>
        <div class="wrap">
            <h1>🔗 Link Shortener Premium Integration Test</h1>
            
            <div class="link-test-dashboard">
                <div class="test-overview">
                    <h2>Integration Status Check</h2>
                    <div class="status-cards">
                        <div class="status-card">
                            <h3>Link Shortener System</h3>
                            <div class="status-indicator <?php echo $this->check_shortener_system() ? 'active' : 'inactive'; ?>"></div>
                            <p><?php echo $this->check_shortener_system() ? 'Active' : 'Inactive'; ?></p>
                        </div>
                        
                        <div class="status-card">
                            <h3>Premium Integration</h3>
                            <div class="status-indicator <?php echo $this->check_premium_integration() ? 'active' : 'inactive'; ?>"></div>
                            <p><?php echo $this->check_premium_integration() ? 'Integrated' : 'Not Integrated'; ?></p>
                        </div>
                        
                        <div class="status-card">
                            <h3>Wait Time Filter</h3>
                            <div class="status-indicator <?php echo $this->check_wait_time_filter() ? 'active' : 'inactive'; ?>"></div>
                            <p><?php echo $this->check_wait_time_filter() ? 'Working' : 'Not Working'; ?></p>
                        </div>
                        
                        <div class="status-card">
                            <h3>Premium Link Access</h3>
                            <div class="status-indicator <?php echo $this->check_premium_link_access() ? 'active' : 'inactive'; ?>"></div>
                            <p><?php echo $this->check_premium_link_access() ? 'Protected' : 'Not Protected'; ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="test-scenarios">
                    <h2>Test Scenarios</h2>
                    <div class="scenario-grid">
                        <div class="scenario-card">
                            <h3>🆓 Free User</h3>
                            <ul>
                                <li>✅ Normal links: Wait time applies</li>
                                <li>✅ Premium links: Redirected to upgrade</li>
                                <li>✅ Countdown timer: Shows full wait time</li>
                                <li>✅ Ads: Visible during wait</li>
                            </ul>
                        </div>
                        
                        <div class="scenario-card">
                            <h3>👑 Premium User</h3>
                            <ul>
                                <li>✅ Normal links: Instant download (0 seconds)</li>
                                <li>✅ Premium links: Instant download</li>
                                <li>✅ Countdown timer: Bypassed</li>
                                <li>✅ Ads: Hidden (if enabled)</li>
                            </ul>
                        </div>
                        
                        <div class="scenario-card">
                            <h3>🔗 Link Types</h3>
                            <ul>
                                <li>✅ Episode links: Premium support</li>
                                <li>✅ Single links: Premium support</li>
                                <li>✅ Torrent links: Premium support</li>
                                <li>✅ External links: Shortener integration</li>
                            </ul>
                        </div>
                        
                        <div class="scenario-card">
                            <h3>⚙️ Admin Settings</h3>
                            <ul>
                                <li>✅ Instant download toggle</li>
                                <li>✅ Wait time configuration</li>
                                <li>✅ Premium link marking</li>
                                <li>✅ Ad-free experience</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="test-controls">
                    <h2>Live Testing</h2>
                    <button id="run-integration-test" class="button button-primary button-hero">
                        🧪 Run Complete Integration Test
                    </button>
                    <button id="simulate-user-scenarios" class="button button-secondary">
                        👥 Simulate User Scenarios
                    </button>
                </div>
                
                <div id="test-results" class="test-results-container"></div>
                
                <div class="configuration-check">
                    <h2>Current Configuration</h2>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th>Setting</th>
                                <th>Value</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Default Wait Time</td>
                                <td><?php echo dooplay_get_option('linktimewait', '30'); ?> seconds</td>
                                <td><span class="status-ok">✅ Configured</span></td>
                            </tr>
                            <tr>
                                <td>Instant Download (Premium)</td>
                                <td><?php 
                                    $settings = get_option('deshiflix_premium_settings', array());
                                    echo isset($settings['enable_instant_download']) && $settings['enable_instant_download'] ? 'Enabled' : 'Disabled';
                                ?></td>
                                <td><span class="status-<?php echo isset($settings['enable_instant_download']) && $settings['enable_instant_download'] ? 'ok' : 'warning'; ?>">
                                    <?php echo isset($settings['enable_instant_download']) && $settings['enable_instant_download'] ? '✅ Active' : '⚠️ Inactive'; ?>
                                </span></td>
                            </tr>
                            <tr>
                                <td>Ad-Free Experience</td>
                                <td><?php echo isset($settings['enable_ad_free']) && $settings['enable_ad_free'] ? 'Enabled' : 'Disabled'; ?></td>
                                <td><span class="status-<?php echo isset($settings['enable_ad_free']) && $settings['enable_ad_free'] ? 'ok' : 'info'; ?>">
                                    <?php echo isset($settings['enable_ad_free']) && $settings['enable_ad_free'] ? '✅ Active' : 'ℹ️ Optional'; ?>
                                </span></td>
                            </tr>
                            <tr>
                                <td>Link Output Type</td>
                                <td><?php echo dooplay_get_option('linkoutputtype', 'btn'); ?></td>
                                <td><span class="status-ok">✅ Configured</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <style>
        .link-test-dashboard {
            max-width: 1200px;
            margin: 20px 0;
        }
        
        .test-overview {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .status-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            position: relative;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        
        .status-indicator.active {
            background: #4CAF50;
        }
        
        .status-indicator.inactive {
            background: #f44336;
        }
        
        .test-scenarios {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .scenario-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .scenario-card {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        
        .scenario-card h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .scenario-card ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .scenario-card li {
            padding: 5px 0;
            font-size: 14px;
        }
        
        .test-controls {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-results-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            display: none;
            margin-bottom: 30px;
        }
        
        .test-results-container.active {
            display: block;
        }
        
        .configuration-check {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status-ok {
            color: #4CAF50;
        }
        
        .status-warning {
            color: #ff9800;
        }
        
        .status-info {
            color: #2196F3;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            $('#run-integration-test').click(function() {
                var $btn = $(this);
                var $results = $('#test-results');
                
                $btn.prop('disabled', true).text('Running Tests...');
                $results.addClass('active').text('Initializing link shortener integration tests...\n\n');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_link_shortener_integration',
                        nonce: '<?php echo wp_create_nonce('link_shortener_test'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $results.text(response.data.results);
                        } else {
                            $results.text('Test Failed: ' + response.data);
                        }
                        $btn.prop('disabled', false).text('🧪 Run Complete Integration Test');
                    },
                    error: function() {
                        $results.text('Network error occurred during testing');
                        $btn.prop('disabled', false).text('🧪 Run Complete Integration Test');
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * Check shortener system
     */
    private function check_shortener_system() {
        $wait_time = dooplay_get_option('linktimewait');
        return !empty($wait_time) && is_numeric($wait_time);
    }
    
    /**
     * Check premium integration
     */
    private function check_premium_integration() {
        return has_filter('dooplay_download_wait_time');
    }
    
    /**
     * Check wait time filter
     */
    private function check_wait_time_filter() {
        return function_exists('deshiflix_premium') && 
               class_exists('DeshiFlix_Premium_Features');
    }
    
    /**
     * Check premium link access
     */
    private function check_premium_link_access() {
        return has_action('template_redirect', 'DeshiFlix_Premium_Features::check_premium_link_access');
    }
    
    /**
     * Run integration test
     */
    public function run_integration_test() {
        if (!wp_verify_nonce($_POST['nonce'], 'link_shortener_test')) {
            wp_send_json_error('Security check failed');
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $results = $this->execute_integration_tests();
        
        wp_send_json_success(array('results' => $results));
    }
    
    /**
     * Execute integration tests
     */
    private function execute_integration_tests() {
        $results = "🔗 LINK SHORTENER PREMIUM INTEGRATION TEST\n";
        $results .= "=" . str_repeat("=", 50) . "\n\n";
        
        $start_time = microtime(true);
        
        try {
            // Test 1: Basic Shortener System
            $results .= $this->test_basic_shortener_system();
            
            // Test 2: Premium Integration
            $results .= $this->test_premium_integration();
            
            // Test 3: Wait Time Modification
            $results .= $this->test_wait_time_modification();
            
            // Test 4: Premium Link Access Control
            $results .= $this->test_premium_link_access_control();
            
            // Test 5: User Scenarios
            $results .= $this->test_user_scenarios();
            
        } catch (Exception $e) {
            $results .= "\n❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
        }
        
        $end_time = microtime(true);
        $execution_time = round($end_time - $start_time, 2);
        
        $results .= "\n" . str_repeat("=", 50) . "\n";
        $results .= "🏁 TESTS COMPLETED IN {$execution_time} SECONDS\n";
        $results .= "📊 OVERALL STATUS: " . $this->get_overall_integration_status() . "\n";
        
        return $results;
    }
    
    /**
     * Test basic shortener system
     */
    private function test_basic_shortener_system() {
        $result = "🔧 TEST 1: BASIC SHORTENER SYSTEM\n";
        $result .= str_repeat("-", 30) . "\n";
        
        // Check wait time setting
        $wait_time = dooplay_get_option('linktimewait');
        if ($wait_time !== false) {
            $result .= "✅ Wait time setting: {$wait_time} seconds\n";
        } else {
            $result .= "❌ Wait time setting: Not configured\n";
        }
        
        // Check output type
        $output_type = dooplay_get_option('linkoutputtype', 'btn');
        $result .= "✅ Output type: {$output_type}\n";
        
        // Check if shortener template exists
        $template_path = get_template_directory() . '/single-dt_links.php';
        if (file_exists($template_path)) {
            $result .= "✅ Shortener template: EXISTS\n";
        } else {
            $result .= "❌ Shortener template: MISSING\n";
        }
        
        // Check JavaScript countdown
        $js_path = get_template_directory() . '/assets/js/front.links.min.js';
        if (file_exists($js_path)) {
            $result .= "✅ Countdown JavaScript: EXISTS\n";
        } else {
            $result .= "❌ Countdown JavaScript: MISSING\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test premium integration
     */
    private function test_premium_integration() {
        $result = "👑 TEST 2: PREMIUM INTEGRATION\n";
        $result .= str_repeat("-", 30) . "\n";
        
        // Check filter hook
        if (has_filter('dooplay_download_wait_time')) {
            $result .= "✅ Wait time filter: REGISTERED\n";
        } else {
            $result .= "❌ Wait time filter: NOT REGISTERED\n";
        }
        
        // Check premium features class
        if (class_exists('DeshiFlix_Premium_Features')) {
            $result .= "✅ Premium features class: LOADED\n";
        } else {
            $result .= "❌ Premium features class: NOT LOADED\n";
        }
        
        // Check premium settings
        $settings = get_option('deshiflix_premium_settings', array());
        if (isset($settings['enable_instant_download'])) {
            $status = $settings['enable_instant_download'] ? 'ENABLED' : 'DISABLED';
            $result .= "✅ Instant download setting: {$status}\n";
        } else {
            $result .= "⚠️ Instant download setting: NOT CONFIGURED\n";
        }
        
        // Check template redirect hook
        if (has_action('template_redirect')) {
            $result .= "✅ Template redirect hook: REGISTERED\n";
        } else {
            $result .= "❌ Template redirect hook: NOT REGISTERED\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test wait time modification
     */
    private function test_wait_time_modification() {
        $result = "⏱️ TEST 3: WAIT TIME MODIFICATION\n";
        $result .= str_repeat("-", 30) . "\n";
        
        $original_wait_time = 30;
        
        // Test for non-premium user
        $free_user_wait_time = apply_filters('dooplay_download_wait_time', $original_wait_time, 0);
        $result .= "✅ Free user wait time: {$free_user_wait_time} seconds\n";
        
        // Test for premium user (simulate)
        if (function_exists('deshiflix_premium')) {
            $result .= "✅ Premium function: AVAILABLE\n";
        } else {
            $result .= "❌ Premium function: NOT AVAILABLE\n";
        }
        
        // Check if filter modifies wait time correctly
        if ($free_user_wait_time == $original_wait_time) {
            $result .= "✅ Wait time preservation: WORKING\n";
        } else {
            $result .= "⚠️ Wait time modification: UNEXPECTED BEHAVIOR\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test premium link access control
     */
    private function test_premium_link_access_control() {
        $result = "🔒 TEST 4: PREMIUM LINK ACCESS CONTROL\n";
        $result .= str_repeat("-", 30) . "\n";
        
        // Check if premium meta field is supported
        global $wpdb;
        $premium_links = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->postmeta} WHERE meta_key = '_dool_premium'");
        $result .= "✅ Premium links in database: {$premium_links}\n";
        
        // Check premium plans page
        $premium_page = get_page_by_path('premium-plans');
        if ($premium_page) {
            $result .= "✅ Premium plans page: EXISTS\n";
        } else {
            $result .= "❌ Premium plans page: MISSING\n";
        }
        
        // Check access control function
        if (method_exists('DeshiFlix_Premium_Features', 'check_premium_link_access')) {
            $result .= "✅ Access control method: EXISTS\n";
        } else {
            $result .= "❌ Access control method: MISSING\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test user scenarios
     */
    private function test_user_scenarios() {
        $result = "👥 TEST 5: USER SCENARIOS\n";
        $result .= str_repeat("-", 30) . "\n";
        
        $result .= "📋 Scenario Testing:\n";
        $result .= "  🆓 Free User + Normal Link: Wait time applies\n";
        $result .= "  🆓 Free User + Premium Link: Redirected to upgrade\n";
        $result .= "  👑 Premium User + Normal Link: Instant download\n";
        $result .= "  👑 Premium User + Premium Link: Instant download\n";
        
        // Check if all components are ready for scenarios
        $components_ready = 
            $this->check_shortener_system() &&
            $this->check_premium_integration() &&
            $this->check_wait_time_filter() &&
            $this->check_premium_link_access();
        
        if ($components_ready) {
            $result .= "✅ All scenarios: READY FOR TESTING\n";
        } else {
            $result .= "❌ Some scenarios: NOT READY\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Get overall integration status
     */
    private function get_overall_integration_status() {
        $all_checks = array(
            $this->check_shortener_system(),
            $this->check_premium_integration(),
            $this->check_wait_time_filter(),
            $this->check_premium_link_access()
        );
        
        $passed = array_filter($all_checks);
        $total = count($all_checks);
        $passed_count = count($passed);
        
        if ($passed_count === $total) {
            return "✅ FULLY INTEGRATED ({$passed_count}/{$total})";
        } elseif ($passed_count >= $total * 0.75) {
            return "⚠️ MOSTLY INTEGRATED ({$passed_count}/{$total})";
        } else {
            return "❌ NEEDS ATTENTION ({$passed_count}/{$total})";
        }
    }
}

// Initialize link shortener test
DeshiFlix_Link_Shortener_Test::get_instance();
