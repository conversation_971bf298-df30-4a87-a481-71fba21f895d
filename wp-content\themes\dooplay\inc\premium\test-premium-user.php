<?php
/**
 * Test Premium User Creator
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Create test premium user for testing features
 */
function deshiflix_create_test_premium_user() {
    // Check if we're in admin and user has permission
    if (!is_admin() || !current_user_can('manage_options')) {
        return;
    }
    
    // Check if test user already exists
    $test_user = get_user_by('login', 'premium_test_user');
    if ($test_user) {
        return $test_user->ID;
    }
    
    // Create test user
    $user_id = wp_create_user(
        'premium_test_user',
        'premium123',
        '<EMAIL>'
    );
    
    if (is_wp_error($user_id)) {
        return false;
    }
    
    // Update user meta
    wp_update_user(array(
        'ID' => $user_id,
        'display_name' => 'Premium Test User',
        'first_name' => 'Premium',
        'last_name' => 'User',
        'role' => 'subscriber'
    ));
    
    // Create premium subscription for test user
    global $wpdb;
    $table_users = $wpdb->prefix . 'deshiflix_premium_users';
    $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
    
    // Check if tables exist, if not create demo entry
    $users_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_users'");
    $plans_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_plans'");
    
    if ($users_table_exists && $plans_table_exists) {
        // Check if test plan exists
        $test_plan = $wpdb->get_row("SELECT * FROM $table_plans WHERE name = 'Test Premium Plan' LIMIT 1");
        
        if (!$test_plan) {
            // Create test plan
            $wpdb->insert(
                $table_plans,
                array(
                    'name' => 'Test Premium Plan',
                    'description' => 'Test plan for premium features',
                    'price' => 199,
                    'duration' => 30,
                    'features' => json_encode(array(
                        'ad_free' => true,
                        'instant_download' => true,
                        'hd_quality' => true,
                        'download_protection' => true,
                        'early_access' => true
                    )),
                    'status' => 'active',
                    'created_at' => current_time('mysql')
                ),
                array('%s', '%s', '%d', '%d', '%s', '%s', '%s')
            );
            $plan_id = $wpdb->insert_id;
        } else {
            $plan_id = $test_plan->id;
        }
        
        // Check if user already has premium subscription
        $existing_subscription = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_users WHERE user_id = %d",
            $user_id
        ));
        
        if (!$existing_subscription) {
            // Create premium subscription
            $wpdb->insert(
                $table_users,
                array(
                    'user_id' => $user_id,
                    'plan_id' => $plan_id,
                    'status' => 'active',
                    'created_at' => current_time('mysql'),
                    'expires_at' => date('Y-m-d H:i:s', strtotime('+30 days'))
                ),
                array('%d', '%d', '%s', '%s', '%s')
            );
        }
    }
    
    return $user_id;
}

/**
 * Add admin notice for test user
 */
function deshiflix_premium_test_user_notice() {
    if (!is_admin() || !current_user_can('manage_options')) {
        return;
    }
    
    $test_user = get_user_by('login', 'premium_test_user');
    if ($test_user) {
        ?>
        <div class="notice notice-info">
            <p>
                <strong>🌟 Premium Test User Available:</strong> 
                Login as <code>premium_test_user</code> / <code>premium123</code> to test premium features 
                (instant downloads & ad-free experience).
                <a href="<?php echo wp_logout_url(wp_login_url()); ?>" class="button button-small" style="margin-left: 10px;">
                    Switch to Test User
                </a>
            </p>
        </div>
        <?php
    }
}

// Create test user on admin init
add_action('admin_init', 'deshiflix_create_test_premium_user');

// Show admin notice
add_action('admin_notices', 'deshiflix_premium_test_user_notice');

/**
 * Add quick login link for test user
 */
function deshiflix_add_test_user_login_link() {
    if (!is_admin() || !current_user_can('manage_options')) {
        return;
    }
    
    // Add to admin bar
    global $wp_admin_bar;
    
    $test_user = get_user_by('login', 'premium_test_user');
    if ($test_user) {
        $wp_admin_bar->add_node(array(
            'id' => 'premium-test-user',
            'title' => '🌟 Test Premium Features',
            'href' => wp_logout_url(wp_login_url()),
            'meta' => array(
                'title' => 'Login as premium test user to test features'
            )
        ));
    }
}
add_action('admin_bar_menu', 'deshiflix_add_test_user_login_link', 100);

/**
 * Auto-login test user function (for development only)
 */
function deshiflix_auto_login_test_user() {
    // Only in development - check for specific parameter
    if (isset($_GET['test_premium']) && $_GET['test_premium'] === 'login' && !is_user_logged_in()) {
        $test_user = get_user_by('login', 'premium_test_user');
        if ($test_user) {
            wp_set_current_user($test_user->ID);
            wp_set_auth_cookie($test_user->ID);
            wp_redirect(home_url());
            exit;
        }
    }
}
add_action('init', 'deshiflix_auto_login_test_user');

/**
 * Display premium status in admin
 */
function deshiflix_display_premium_status() {
    if (!is_admin()) {
        return;
    }
    
    $current_user_id = get_current_user_id();

    if (class_exists('DeshiFlix_Premium_Features')) {
        $premium_features = DeshiFlix_Premium_Features::get_instance();
        $is_premium = $premium_features->is_user_premium($current_user_id);
    } else {
        $is_premium = false;
    }

    if ($is_premium) {
        ?>
        <div class="notice notice-success">
            <p>
                <strong>🌟 Premium Status:</strong> 
                Current user has premium access. Premium features are active:
                <span style="color: #46b450;">✓ Instant Downloads</span> | 
                <span style="color: #46b450;">✓ Ad-Free Experience</span>
            </p>
        </div>
        <?php
    }
}
add_action('admin_notices', 'deshiflix_display_premium_status');
