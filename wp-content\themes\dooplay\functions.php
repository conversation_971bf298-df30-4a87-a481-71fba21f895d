<?php
/*
* ----------------------------------------------------
* @author: Doothemes
* <AUTHOR> https://doothemes.com/
* @copyright: (c) 2021 Doothemes. All rights reserved
* ----------------------------------------------------
* @since 2.5.5
*/

# Theme options
define('DOO_THEME_DOWNLOAD_MOD', true);
define('DOO_THEME_PLAYER_MOD',   true);
define('DOO_THEME_DBMOVIES',     true);
define('DOO_THEME_USER_MOD',     true);
define('DOO_THEME_VIEWS_COUNT',  true);
define('DOO_THEME_RELATED',      true);
define('DOO_THEME_SOCIAL_SHARE', true);
define('DOO_THEME_CACHE',        true);
define('DOO_THEME_PLAYERSERNAM', true);
define('DOO_THEME_JSCOMPRESS',   true);
define('DOO_THEME_TOTAL_POSTC',  true);
define('DOO_THEME_LAZYLOAD',     false);

# DeshiFlix Mobile App Features
define('DOO_MOBILE_APP',         true);
define('DOO_PUSH_NOTIFICATIONS', true);
define('DOO_APP_ANALYTICS',      true);
define('DOO_PREMIUM_SYSTEM',     true);

# DeshiFlix Premium System
define('DESHIFLIX_PREMIUM_ENABLED', true);
# Repository data
define('DOO_COM','Doothemes');
define('DOO_VERSION','2.5.5');
define('Bes_VERSION','2.5.5.1'); // Bescraper version for wp_update only 23/06/2021
define('DOO_VERSION_DB','2.8');
define('DOO_ITEM_ID','154');
define('DOO_PHP_REQUIRE','7.1');
define('DOO_THEME','Dooplay');
define('DOO_THEME_SLUG','dooplay');
define('DOO_SERVER','https://cdn.bescraper.cf/api');
define('DOO_GICO','https://s2.googleusercontent.com/s2/favicons?domain=');

# Configure Here date format #
define('DOO_TIME','M. d, Y');  // More Info >>> https://www.php.net/manual/function.date.php
##############################

# Define Rating data
define('DOO_MAIN_RATING','_starstruck_avg');
define('DOO_MAIN_VOTOS','_starstruck_total');
# Define Options key
define('DOO_OPTIONS','_dooplay_options');
define('DOO_CUSTOMIZE', '_dooplay_customize');
# Define template directory
define('DOO_URI',get_template_directory_uri());
define('DOO_DIR',get_template_directory());

# Translations
load_theme_textdomain('dooplay', DOO_DIR.'/lang/');

# Load Application
require get_parent_theme_file_path('/inc/doo_init.php');

# Load Live TV System
require get_parent_theme_file_path('/inc/doo_livetv.php');

# Add Live TV to navigation menu
function doo_add_livetv_to_menu($items, $args) {
    if ($args->theme_location == 'header') {
        $livetv_item = '<li class="menu-item menu-item-type-custom menu-item-object-custom">
            <a href="' . home_url('/live-tv') . '">
                <i class="fas fa-tv"></i> Live TV
            </a>
        </li>';
        $items = $items . $livetv_item;
    }
    return $items;
}
add_filter('wp_nav_menu_items', 'doo_add_livetv_to_menu', 10, 2);

# Load Mobile API - DISABLED to avoid conflicts
// require get_parent_theme_file_path('/inc/doo_mobile_api.php');

// Initialize Mobile API - DISABLED
// if (class_exists('DooMobileAPI')) {
//     new DooMobileAPI();
// }

/* Custom functions
========================================================
*/

// Advanced SEO Functions for Auto Content Generation
function dooplay_auto_generate_content($post_id) {
    $post = get_post($post_id);
    $post_type = get_post_type($post_id);
    
    if ($post_type == 'movies' || $post_type == 'tvshows') {
        $title = get_the_title($post_id);
        $year = get_post_meta($post_id, 'dt_poster', true);
        $director = get_post_meta($post_id, 'director', true);
        $cast = get_post_meta($post_id, 'cast', true);
        $rating = get_post_meta($post_id, '_starstruck_avg', true);
        $votes = get_post_meta($post_id, '_starstruck_total', true);
        $genre = get_post_meta($post_id, 'genre', true);
        
        // Generate rich content
        $content = "<div class='movie-info-seo'>\n";
        $content .= "<h2>$title";
        if ($year) $content .= " ($year)";
        $content .= "</h2>\n";
        
        if ($director) {
            $content .= "<p><strong>Director:</strong> $director</p>\n";
        }
        
        if ($cast) {
            $content .= "<p><strong>Cast:</strong> $cast</p>\n";
        }
        
        if ($genre) {
            $content .= "<p><strong>Genre:</strong> $genre</p>\n";
        }
        
        if ($rating) {
            $content .= "<p><strong>Rating:</strong> $rating/5";
            if ($votes) $content .= " ($votes votes)";
            $content .= "</p>\n";
        }
        
        $content .= "<p><strong>Synopsis:</strong> ";
        $content .= "Watch $title online in HD quality. ";
        if ($year) $content .= "Released in $year, ";
        if ($director) $content .= "directed by $director, ";
        if ($cast) $content .= "starring $cast. ";
        $content .= "Enjoy streaming this amazing $genre movie with high quality video and audio.</p>\n";
        
        $content .= "</div>\n";
        
        // Add to post content if empty
        if (empty($post->post_content)) {
            wp_update_post(array(
                'ID' => $post_id,
                'post_content' => $content
            ));
        }
    }
}

// Auto Internal Linking Function
function dooplay_auto_internal_links($content) {
    global $post;
    
    if (is_single() && (get_post_type() == 'movies' || get_post_type() == 'tvshows')) {
        $post_id = get_the_ID();
        $title = get_the_title();
        $genre = get_post_meta($post_id, 'genre', true);
        $cast = get_post_meta($post_id, 'cast', true);
        
        // Find related posts for internal linking
        $related_posts = array();
        
        if ($genre) {
            $genre_posts = get_posts(array(
                'post_type' => array('movies', 'tvshows'),
                'meta_query' => array(
                    array('key' => 'genre', 'value' => $genre, 'compare' => 'LIKE')
                ),
                'posts_per_page' => 5,
                'post__not_in' => array($post_id)
            ));
            $related_posts = array_merge($related_posts, $genre_posts);
        }
        
        if ($cast) {
            $cast_array = explode(',', $cast);
            foreach ($cast_array as $actor) {
                $actor = trim($actor);
                $actor_posts = get_posts(array(
                    'post_type' => array('movies', 'tvshows'),
                    'meta_query' => array(
                        array('key' => 'cast', 'value' => $actor, 'compare' => 'LIKE')
                    ),
                    'posts_per_page' => 3,
                    'post__not_in' => array($post_id)
                ));
                $related_posts = array_merge($related_posts, $actor_posts);
            }
        }
        
        // Add internal links to content
        if (!empty($related_posts)) {
            $content .= "<div class='internal-links-seo'>\n";
            $content .= "<h3>Related Movies & TV Shows</h3>\n";
            $content .= "<ul>\n";
            
            foreach (array_slice($related_posts, 0, 10) as $related_post) {
                $related_title = get_the_title($related_post->ID);
                $related_url = get_permalink($related_post->ID);
                $content .= "<li><a href='$related_url' title='$related_title'>$related_title</a></li>\n";
            }
            
            $content .= "</ul>\n";
            $content .= "</div>\n";
        }
    }
    
    return $content;
}

// Hook for auto content generation
add_action('save_post', 'dooplay_auto_generate_content');

// Hook for auto internal linking
add_action('save_post', 'dooplay_auto_internal_links', 15, 1);

// Remove content filters that add visible content
remove_filter('the_content', 'dooplay_auto_internal_links');
remove_filter('the_content', 'dooplay_copyright_safe_content');

// Copyright Safe Content Function - DISABLED TO PREVENT DUPLICATES
function dooplay_copyright_safe_content($content) {
    // This function is disabled to prevent duplicate disclaimers
    return $content;
}

// Auto Generate Meta Description
function dooplay_auto_meta_description($post_id) {
    $post = get_post($post_id);
    $title = get_the_title($post_id);
    $excerpt = get_the_excerpt($post_id);
    
    if (empty($excerpt)) {
        $content = wp_strip_all_tags($post->post_content);
        $excerpt = wp_trim_words($content, 25, '...');
    }
    
    $meta_description = "Watch $title online in HD quality. $excerpt";
    
    update_post_meta($post_id, '_yoast_wpseo_metadesc', $meta_description);
}

// Hook for auto meta description
add_action('save_post', 'dooplay_auto_meta_description');

// Auto Generate Focus Keyphrase
function dooplay_auto_focus_keyphrase($post_id) {
    $title = get_the_title($post_id);
    $post_type = get_post_type($post_id);
    
    $keyphrase = $title;
    if ($post_type == 'movies') {
        $keyphrase .= " movie";
    } elseif ($post_type == 'tvshows') {
        $keyphrase .= " tv show";
    }
    
    update_post_meta($post_id, '_yoast_wpseo_focuskw', $keyphrase);
}

// Hook for auto focus keyphrase
add_action('save_post', 'dooplay_auto_focus_keyphrase');

// Auto Sitemap Generator
function dooplay_generate_sitemap() {
    $sitemap = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $sitemap .= "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    // Homepage
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>1.0</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Movies
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    foreach ($movies as $movie) {
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>" . get_permalink($movie->ID) . "</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d', strtotime($movie->post_modified)) . "</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.8</priority>\n";
        $sitemap .= "  </url>\n";
    }
    
    // TV Shows
    $tvshows = get_posts(array(
        'post_type' => 'tvshows',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    foreach ($tvshows as $show) {
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>" . get_permalink($show->ID) . "</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d', strtotime($show->post_modified)) . "</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.8</priority>\n";
        $sitemap .= "  </url>\n";
    }
    
    $sitemap .= "</urlset>";
    
    // Save sitemap
    $upload_dir = wp_upload_dir();
    $sitemap_path = $upload_dir['basedir'] . '/sitemap.xml';
    file_put_contents($sitemap_path, $sitemap);
}

// Generate sitemap on post save
add_action('save_post', 'dooplay_generate_sitemap');

// Auto Submit to Search Engines
function dooplay_submit_to_search_engines($post_id) {
    if (get_post_status($post_id) == 'publish') {
        $url = get_permalink($post_id);
        
        // Submit to Google (you need to add your Google Search Console URL)
        // wp_remote_post('https://www.google.com/ping?sitemap=' . home_url() . '/sitemap.xml');
        
        // Submit to Bing
        wp_remote_post('https://www.bing.com/ping?sitemap=' . home_url() . '/sitemap.xml');
    }
}

add_action('save_post', 'dooplay_submit_to_search_engines');

// Load SEO CSS
function dooplay_enqueue_seo_styles() {
    wp_enqueue_style('dooplay-seo-enhanced', get_template_directory_uri() . '/assets/css/seo-enhanced.css', array(), '1.0.0');
}
add_action('wp_enqueue_scripts', 'dooplay_enqueue_seo_styles');

// Advanced Search Optimization Functions
function dooplay_advanced_search_optimization($post_id) {
    // Check if post is being saved
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (defined('DOING_AJAX') && DOING_AJAX) return;
    if (wp_is_post_revision($post_id)) return;
    if (wp_is_post_autosave($post_id)) return;
    
    $post = get_post($post_id);
    if (!$post) return;
    
    $post_type = get_post_type($post_id);
    
    if ($post_type == 'movies' || $post_type == 'tvshows') {
        $title = get_the_title($post_id);
        $year = get_post_meta($post_id, 'release_date', true);
        $director = get_post_meta($post_id, 'dt_dir', true);
        $cast = get_post_meta($post_id, 'dt_cast', true);
        $genre = get_the_term_list($post_id, 'genres', '', ', ', '');
        $rating = get_post_meta($post_id, 'imdbRating', true);
        $runtime = get_post_meta($post_id, 'runtime', true);
        $country = get_post_meta($post_id, 'Country', true);
        
        // Generate multiple search variations
        $search_variations = array();
        
        // Original title
        $search_variations[] = $title;
        
        // Title with year
        if ($year) {
            $search_variations[] = "$title $year";
            $search_variations[] = "$title ($year)";
        }
        
        // Title with "movie" or "film"
        $search_variations[] = "$title movie";
        $search_variations[] = "$title film";
        $search_variations[] = "watch $title";
        $search_variations[] = "$title watch online";
        $search_variations[] = "$title streaming";
        
        // Title with "full movie"
        $search_variations[] = "$title full movie";
        $search_variations[] = "$title full film";
        
        // Director variations
        if ($director) {
            // Clean and format director data
            $director_entries = explode(']', $director);
            $formatted_director = '';
            
            foreach ($director_entries as $entry) {
                if (!empty($entry)) {
                    $entry = substr($entry, 1); // Remove leading [
                    $parts = explode(';', $entry);
                    if (isset($parts[1])) {
                        $formatted_director = trim($parts[1]);
                        break; // Take first director only
                    }
                }
            }
            
            if ($formatted_director) {
                $search_variations[] = "$title $formatted_director";
                $search_variations[] = "$formatted_director $title";
                $search_variations[] = "movies by $formatted_director";
            }
        }
        
        // Cast variations
        if ($cast) {
            // Clean and format cast data
            $cast_entries = explode(']', $cast);
            $cast_names = array();
            
            foreach ($cast_entries as $entry) {
                if (!empty($entry)) {
                    $entry = substr($entry, 1); // Remove leading [
                    $parts = explode(';', $entry);
                    if (isset($parts[1])) {
                        $actor_parts = explode(',', $parts[1]);
                        if (!empty($actor_parts[0])) {
                            $actor_name = trim($actor_parts[0]);
                            if (!in_array($actor_name, $cast_names)) {
                                $cast_names[] = $actor_name;
                            }
                        }
                    }
                }
            }
            
            // Add cast variations
            foreach ($cast_names as $actor) {
                $search_variations[] = "$actor movies";
                $search_variations[] = "$title $actor";
                $search_variations[] = "$actor $title";
            }
        }
        
        // Genre variations
        if ($genre) {
            $genre_array = explode(',', $genre);
            foreach ($genre_array as $g) {
                $g = trim($g);
                $search_variations[] = "$g movies";
                $search_variations[] = "$title $g";
                $search_variations[] = "$g $title";
            }
        }
        
        // Year variations
        if ($year) {
            $search_variations[] = "$year movies";
            $search_variations[] = "movies $year";
            $search_variations[] = "$title $year movie";
        }
        
        // Rating variations
        if ($rating) {
            $search_variations[] = "best movies $rating";
            $search_variations[] = "$title $rating rating";
        }
        
        // Country variations
        if ($country) {
            $search_variations[] = "$country movies";
            $search_variations[] = "$title $country";
        }
        
        // HD/Quality variations
        $search_variations[] = "$title HD";
        $search_variations[] = "$title 1080p";
        $search_variations[] = "$title 720p";
        $search_variations[] = "HD $title";
        
        // Online streaming variations
        $search_variations[] = "$title online";
        $search_variations[] = "$title stream";
        $search_variations[] = "stream $title";
        $search_variations[] = "$title free";
        $search_variations[] = "free $title";
        
        // Save search variations as meta
        update_post_meta($post_id, '_search_variations', $search_variations);
        
        // Generate LSI (Latent Semantic Indexing) keywords
        $lsi_keywords = array();
        $lsi_keywords[] = "cinema";
        $lsi_keywords[] = "entertainment";
        $lsi_keywords[] = "streaming";
        $lsi_keywords[] = "online";
        $lsi_keywords[] = "watch";
        $lsi_keywords[] = "view";
        $lsi_keywords[] = "download";
        $lsi_keywords[] = "HD";
        $lsi_keywords[] = "quality";
        $lsi_keywords[] = "full";
        $lsi_keywords[] = "complete";
        $lsi_keywords[] = "latest";
        $lsi_keywords[] = "new";
        $lsi_keywords[] = "popular";
        $lsi_keywords[] = "best";
        $lsi_keywords[] = "top";
        $lsi_keywords[] = "trending";
        $lsi_keywords[] = "blockbuster";
        $lsi_keywords[] = "hit";
        $lsi_keywords[] = "successful";
        $lsi_keywords[] = "award-winning";
        $lsi_keywords[] = "critically acclaimed";
        
        update_post_meta($post_id, '_lsi_keywords', $lsi_keywords);
    }
}

// Hook for advanced search optimization
add_action('save_post', 'dooplay_advanced_search_optimization', 10, 1);

// Enhanced Meta Tags for Better Search
function dooplay_enhanced_meta_tags() {
    global $post;
    
    if (!$post) return;
    
    if (is_single() && (get_post_type() == 'movies' || get_post_type() == 'tvshows')) {
        $post_id = get_the_ID();
        if (!$post_id) return;
        
        $title = get_the_title();
        $search_variations = get_post_meta($post_id, '_search_variations', true);
        $lsi_keywords = get_post_meta($post_id, '_lsi_keywords', true);
        
        if ($search_variations && is_array($search_variations)) {
            echo "<meta name=\"keywords\" content=\"" . esc_attr(implode(', ', $search_variations)) . "\">\n";
        }
        
        if ($lsi_keywords && is_array($lsi_keywords)) {
            echo "<meta name=\"lsi-keywords\" content=\"" . esc_attr(implode(', ', $lsi_keywords)) . "\">\n";
        }
        
        // Add structured data for search
        echo "<script type=\"application/ld+json\">\n";
        echo "{\n";
        echo "  \"@context\": \"https://schema.org\",\n";
        echo "  \"@type\": \"WebPage\",\n";
        echo "  \"name\": \"" . esc_js($title) . "\",\n";
        echo "  \"description\": \"Watch " . esc_js($title) . " online in HD quality\",\n";
        echo "  \"url\": \"" . esc_url(get_permalink()) . "\",\n";
        if ($search_variations && is_array($search_variations)) {
            echo "  \"keywords\": \"" . esc_js(implode(', ', $search_variations)) . "\"\n";
        }
        echo "}\n";
        echo "</script>\n";
    }
}

// Remove wp_head actions that output visible content
remove_action('wp_head', 'dooplay_enhanced_meta_tags');

// Auto Generate Search-Friendly URLs
function dooplay_search_friendly_urls($post_id) {
    // Check if post is being saved
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (defined('DOING_AJAX') && DOING_AJAX) return;
    if (wp_is_post_revision($post_id)) return;
    if (wp_is_post_autosave($post_id)) return;
    
    $post = get_post($post_id);
    if (!$post) return;
    
    $post_type = get_post_type($post_id);
    
    if ($post_type == 'movies' || $post_type == 'tvshows') {
        $title = get_the_title($post_id);
        $year = get_post_meta($post_id, 'release_date', true);
        
        // Create search-friendly slug
        $slug = sanitize_title($title);
        if ($year) {
            $slug .= "-$year";
        }
        
        // Add search terms to slug
        $slug .= "-movie-online";
        
        // Update post slug only if it's different
        $current_slug = $post->post_name;
        if ($current_slug !== $slug) {
            wp_update_post(array(
                'ID' => $post_id,
                'post_name' => $slug
            ));
        }
    }
}

add_action('save_post', 'dooplay_search_friendly_urls', 20, 1);

// Enhanced Content for Search - Modified to not output visible content
function dooplay_enhanced_search_content($content) {
    // This function now only works in background for SEO, no visible output
    return $content;
}

// Remove SEO/Download content from the_content filter
remove_filter('the_content', 'dooplay_enhanced_search_content', 10);

// Auto Generate Related Search Pages
function dooplay_generate_search_pages() {
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    foreach ($movies as $movie) {
        $title = get_the_title($movie->ID);
        $search_variations = get_post_meta($movie->ID, '_search_variations', true);
        
        if ($search_variations) {
            foreach (array_slice($search_variations, 0, 5) as $variation) {
                // Create search-friendly page for each variation
                $page_title = "$variation - Watch Online";
                $page_content = "Watch $title online. $variation available in HD quality.";
                
                // Check if page already exists
                $existing_page = get_page_by_title($page_title);
                if (!$existing_page) {
                    wp_insert_post(array(
                        'post_title' => $page_title,
                        'post_content' => $page_content,
                        'post_status' => 'publish',
                        'post_type' => 'page',
                        'post_name' => sanitize_title($variation)
                    ));
                }
            }
        }
    }
}

// Run search page generation weekly
if (!wp_next_scheduled('dooplay_search_pages_cron')) {
    wp_schedule_event(time(), 'weekly', 'dooplay_search_pages_cron');
}
add_action('dooplay_search_pages_cron', 'dooplay_generate_search_pages');

// Advanced Indexing and Copyright-Safe Functions
function dooplay_advanced_indexing_optimization($post_id) {
    $post = get_post($post_id);
    $post_type = get_post_type($post_id);
    
    if ($post_type == 'movies' || $post_type == 'tvshows') {
        $title = get_the_title($post_id);
        $year = get_post_meta($post_id, 'release_date', true);
        $director = get_post_meta($post_id, 'dt_dir', true);
        $cast = get_post_meta($post_id, 'dt_cast', true);
        $genre = get_the_term_list($post_id, 'genres', '', ', ', '');
        $rating = get_post_meta($post_id, 'imdbRating', true);
        $runtime = get_post_meta($post_id, 'runtime', true);
        $country = get_post_meta($post_id, 'Country', true);
        
        // Generate download-focused search variations
        $download_variations = array();
        
        // Basic download variations
        $download_variations[] = "$title download";
        $download_variations[] = "download $title";
        $download_variations[] = "$title movie download";
        $download_variations[] = "$title film download";
        $download_variations[] = "$title free download";
        $download_variations[] = "free download $title";
        
        // Quality variations
        $download_variations[] = "$title HD download";
        $download_variations[] = "$title 1080p download";
        $download_variations[] = "$title 720p download";
        $download_variations[] = "$title BluRay download";
        $download_variations[] = "$title WEB-DL download";
        
        // Year variations
        if ($year) {
            $download_variations[] = "$title $year download";
            $download_variations[] = "download $title $year";
            $download_variations[] = "$title movie $year download";
        }
        
        // Director variations
        if ($director) {
            // Clean and format director data
            $director_entries = explode(']', $director);
            $formatted_director = '';
            
            foreach ($director_entries as $entry) {
                if (!empty($entry)) {
                    $entry = substr($entry, 1); // Remove leading [
                    $parts = explode(';', $entry);
                    if (isset($parts[1])) {
                        $formatted_director = trim($parts[1]);
                        break; // Take first director only
                    }
                }
            }
            
            if ($formatted_director) {
                $download_variations[] = "$title $formatted_director download";
                $download_variations[] = "$formatted_director $title download";
            }
        }
        
        // Cast variations
        if ($cast) {
            // Clean and format cast data
            $cast_entries = explode(']', $cast);
            $cast_names = array();
            
            foreach ($cast_entries as $entry) {
                if (!empty($entry)) {
                    $entry = substr($entry, 1); // Remove leading [
                    $parts = explode(';', $entry);
                    if (isset($parts[1])) {
                        $actor_parts = explode(',', $parts[1]);
                        if (!empty($actor_parts[0])) {
                            $actor_name = trim($actor_parts[0]);
                            if (!in_array($actor_name, $cast_names)) {
                                $cast_names[] = $actor_name;
                            }
                        }
                    }
                }
            }
            
            // Add cast variations
            foreach ($cast_names as $actor) {
                $download_variations[] = "$title $actor download";
                $download_variations[] = "$actor movies download";
            }
        }
        
        // Genre variations
        if ($genre) {
            $genre_array = explode(',', $genre);
            foreach ($genre_array as $g) {
                $g = trim($g);
                $download_variations[] = "$g movies download";
                $download_variations[] = "$title $g download";
            }
        }
        
        // File format variations
        $download_variations[] = "$title MP4 download";
        $download_variations[] = "$title MKV download";
        $download_variations[] = "$title AVI download";
        $download_variations[] = "$title torrent download";
        $download_variations[] = "$title magnet download";
        
        // Size variations
        $download_variations[] = "$title 1GB download";
        $download_variations[] = "$title 2GB download";
        $download_variations[] = "$title 500MB download";
        
        // Language variations
        $download_variations[] = "$title English download";
        $download_variations[] = "$title Hindi download";
        $download_variations[] = "$title Bengali download";
        $download_variations[] = "$title dual audio download";
        
        // Save download variations
        update_post_meta($post_id, '_download_variations', $download_variations);
        
        // Generate copyright-safe content
        $copyright_safe_content = dooplay_generate_copyright_safe_content($post_id);
        update_post_meta($post_id, '_copyright_safe_content', $copyright_safe_content);
    }
}

// Generate copyright-safe content
function dooplay_generate_copyright_safe_content($post_id) {
    $title = get_the_title($post_id);
    $year = get_post_meta($post_id, 'release_date', true);
    $director = get_post_meta($post_id, 'dt_dir', true);
    $cast = get_post_meta($post_id, 'dt_cast', true);
    $genre = get_the_term_list($post_id, 'genres', '', ', ', '');
    $rating = get_post_meta($post_id, 'imdbRating', true);
    $runtime = get_post_meta($post_id, 'runtime', true);
    $country = get_post_meta($post_id, 'Country', true);
    
    $content = "<div class='copyright-safe-content'>\n";
    $content .= "<h2>Movie Information: $title</h2>\n";
    $content .= "<p><strong>Disclaimer:</strong> This page provides information about the movie <strong>$title</strong>. ";
    $content .= "We do not host, store, or distribute any copyrighted content. ";
    $content .= "All content is for informational purposes only.</p>\n";
    
    $content .= "<div class='movie-info-section'>\n";
    $content .= "<h3>Movie Details</h3>\n";
    $content .= "<ul>\n";
    if ($year) $content .= "<li><strong>Release Year:</strong> $year</li>\n";
    if ($director) $content .= "<li><strong>Director:</strong> $director</li>\n";
    if ($cast) $content .= "<li><strong>Cast:</strong> $cast</li>\n";
    if ($genre) $content .= "<li><strong>Genre:</strong> $genre</li>\n";
    if ($rating) $content .= "<li><strong>IMDb Rating:</strong> $rating</li>\n";
    if ($runtime) $content .= "<li><strong>Runtime:</strong> $runtime minutes</li>\n";
    if ($country) $content .= "<li><strong>Country:</strong> $country</li>\n";
    $content .= "</ul>\n";
    $content .= "</div>\n";
    
    $content .= "<div class='legal-notice'>\n";
    $content .= "<h3>Legal Notice</h3>\n";
    $content .= "<p>This website provides movie information and reviews only. ";
    $content .= "We do not provide any download links or copyrighted content. ";
    $content .= "All movie rights belong to their respective owners. ";
    $content .= "Please support the film industry by watching movies through legal channels.</p>\n";
    $content .= "</div>\n";
    
    $content .= "<div class='where-to-watch'>\n";
    $content .= "<h3>Where to Watch Legally</h3>\n";
    $content .= "<p>To watch <strong>$title</strong> legally, consider these options:</p>\n";
    $content .= "<ul>\n";
    $content .= "<li>Netflix</li>\n";
    $content .= "<li>Amazon Prime Video</li>\n";
    $content .= "<li>Disney+</li>\n";
    $content .= "<li>HBO Max</li>\n";
    $content .= "<li>Hulu</li>\n";
    $content .= "<li>Apple TV+</li>\n";
    $content .= "<li>Google Play Movies</li>\n";
    $content .= "<li>iTunes</li>\n";
    $content .= "<li>Your local cinema</li>\n";
    $content .= "<li>DVD/Blu-ray purchase</li>\n";
    $content .= "</ul>\n";
    $content .= "</div>\n";
    
    $content .= "<div class='movie-review'>\n";
    $content .= "<h3>Movie Review</h3>\n";
    $content .= "<p><strong>$title</strong> is a $genre film";
    if ($year) $content .= " released in $year";
    if ($director) $content .= " directed by $director";
    $content .= ". ";
    if ($rating) $content .= "With an IMDb rating of $rating, ";
    $content .= "this movie offers an engaging cinematic experience. ";
    if ($runtime) $content .= "The $runtime-minute runtime ";
    $content .= "provides a comprehensive storytelling experience.</p>\n";
    $content .= "</div>\n";
    
    $content .= "</div>\n";
    
    return $content;
}

// Hook for advanced indexing
add_action('save_post', 'dooplay_advanced_indexing_optimization');

// Enhanced Meta Tags for Download Searches
function dooplay_download_search_meta_tags() {
    global $post;
    
    if (is_single() && (get_post_type() == 'movies' || get_post_type() == 'tvshows')) {
        $post_id = get_the_ID();
        $title = get_the_title();
        $download_variations = get_post_meta($post_id, '_download_variations', true);
        
        if ($download_variations) {
            echo "<meta name=\"download-keywords\" content=\"" . implode(', ', $download_variations) . "\">\n";
        }
        
        // Add structured data for movie information
        echo "<script type=\"application/ld+json\">\n";
        echo "{\n";
        echo "  \"@context\": \"https://schema.org\",\n";
        echo "  \"@type\": \"Movie\",\n";
        echo "  \"name\": \"$title\",\n";
        echo "  \"description\": \"Movie information and details for $title\",\n";
        echo "  \"url\": \"" . get_permalink() . "\",\n";
        echo "  \"publisher\": {\n";
        echo "    \"@type\": \"Organization\",\n";
        echo "    \"name\": \"" . get_bloginfo('name') . "\"\n";
        echo "  }\n";
        echo "}\n";
        echo "</script>\n";
    }
}

// Remove wp_head actions that output visible content
remove_action('wp_head', 'dooplay_download_search_meta_tags');

// Auto Generate Download Search Pages
function dooplay_generate_download_search_pages() {
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    foreach ($movies as $movie) {
        $title = get_the_title($movie->ID);
        $download_variations = get_post_meta($movie->ID, '_download_variations', true);
        
        if ($download_variations) {
            foreach (array_slice($download_variations, 0, 10) as $variation) {
                // Create informational page for each variation
                $page_title = "$variation - Movie Information";
                $page_content = "Information about $title. This page provides movie details and information only. ";
                $page_content .= "We do not provide any download links or copyrighted content. ";
                $page_content .= "Please support the film industry by watching through legal channels.";
                
                // Check if page already exists
                $existing_page = get_page_by_title($page_title);
                if (!$existing_page) {
                    wp_insert_post(array(
                        'post_title' => $page_title,
                        'post_content' => $page_content,
                        'post_status' => 'publish',
                        'post_type' => 'page',
                        'post_name' => sanitize_title($variation)
                    ));
                }
            }
        }
    }
}

// Run download search page generation weekly
if (!wp_next_scheduled('dooplay_download_pages_cron')) {
    wp_schedule_event(time(), 'weekly', 'dooplay_download_pages_cron');
}
add_action('dooplay_download_pages_cron', 'dooplay_generate_download_search_pages');

// Enhanced Content for Download Searches - Modified to not output visible content  
function dooplay_download_search_content($content) {
    // This function now only works in background for SEO, no visible output
    return $content;
}

// Remove SEO/Download content from the_content filter
remove_filter('the_content', 'dooplay_download_search_content', 10);

// Force Google Indexing
function dooplay_force_google_indexing($post_id) {
    if (get_post_status($post_id) == 'publish') {
        $url = get_permalink($post_id);
        
        // Submit to Google for indexing
        $google_url = "https://www.google.com/ping?sitemap=" . home_url() . "/sitemap.xml";
        wp_remote_get($google_url);
        
        // Submit to Bing
        $bing_url = "https://www.bing.com/ping?sitemap=" . home_url() . "/sitemap.xml";
        wp_remote_get($bing_url);
        
        // Submit to Yandex
        $yandex_url = "https://blogs.yandex.com/pings/?status=success&url=" . urlencode($url);
        wp_remote_get($yandex_url);
    }
}

add_action('save_post', 'dooplay_force_google_indexing');

// Enhanced Sitemap for Better Indexing
function dooplay_enhanced_sitemap() {
    $sitemap = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $sitemap .= "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    // Homepage
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>1.0</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Movies with download variations
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    foreach ($movies as $movie) {
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>" . get_permalink($movie->ID) . "</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d', strtotime($movie->post_modified)) . "</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.9</priority>\n";
        $sitemap .= "  </url>\n";
        
        // Add download variation pages
        $download_variations = get_post_meta($movie->ID, '_download_variations', true);
        if ($download_variations) {
            foreach (array_slice($download_variations, 0, 5) as $variation) {
                $variation_url = home_url() . "/" . sanitize_title($variation) . "/";
                $sitemap .= "  <url>\n";
                $sitemap .= "    <loc>$variation_url</loc>\n";
                $sitemap .= "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
                $sitemap .= "    <changefreq>monthly</changefreq>\n";
                $sitemap .= "    <priority>0.7</priority>\n";
                $sitemap .= "  </url>\n";
            }
        }
    }
    
    $sitemap .= "</urlset>";
    
    // Save enhanced sitemap
    $upload_dir = wp_upload_dir();
    $sitemap_path = $upload_dir['basedir'] . '/sitemap.xml';
    file_put_contents($sitemap_path, $sitemap);
}

add_action('save_post', 'dooplay_enhanced_sitemap');

// Advanced Sitemap Generator with All Features
function dooplay_advanced_sitemap_generator() {
    $sitemap = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $sitemap .= "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\"\n";
    $sitemap .= "         xmlns:image=\"http://www.google.com/schemas/sitemap-image/1.1\"\n";
    $sitemap .= "         xmlns:video=\"http://www.google.com/schemas/sitemap-video/1.1\"\n";
    $sitemap .= "         xmlns:news=\"http://www.google.com/schemas/sitemap-news/0.9\">\n";
    
    // Homepage with high priority
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>1.0</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Movies with all variations
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    foreach ($movies as $movie) {
        $movie_id = $movie->ID;
        $movie_title = get_the_title($movie_id);
        $movie_url = get_permalink($movie_id);
        $movie_modified = get_the_modified_date('Y-m-d\TH:i:s+00:00', $movie_id);
        $movie_image = dbmovies_get_poster($movie_id, 'medium');
        $movie_year = get_post_meta($movie_id, 'release_date', true);
        $movie_director = get_post_meta($movie_id, 'dt_dir', true);
        $movie_cast = get_post_meta($movie_id, 'dt_cast', true);
        $movie_rating = get_post_meta($movie_id, 'imdbRating', true);
        $movie_runtime = get_post_meta($movie_id, 'runtime', true);
        $movie_genre = get_the_term_list($movie_id, 'genres', '', ', ', '');
        
        // Main movie page
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>$movie_url</loc>\n";
        $sitemap .= "    <lastmod>$movie_modified</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.9</priority>\n";
        
        // Add image data
        if ($movie_image) {
            $sitemap .= "    <image:image>\n";
            $sitemap .= "      <image:loc>$movie_image</image:loc>\n";
            $sitemap .= "      <image:title>$movie_title</image:title>\n";
            $sitemap .= "      <image:caption>$movie_title Movie Poster</image:caption>\n";
            $sitemap .= "    </image:image>\n";
        }
        
        // Add video data
        $sitemap .= "    <video:video>\n";
        $sitemap .= "      <video:thumbnail_loc>$movie_image</video:thumbnail_loc>\n";
        $sitemap .= "      <video:title>$movie_title</video:title>\n";
        $sitemap .= "      <video:description>Watch $movie_title online in HD quality</video:description>\n";
        if ($movie_year) $sitemap .= "      <video:publication_date>$movie_year-01-01T00:00:00+00:00</video:publication_date>\n";
        if ($movie_director) $sitemap .= "      <video:family_friendly>yes</video:family_friendly>\n";
        $sitemap .= "      <video:duration>120</video:duration>\n";
        $sitemap .= "    </video:video>\n";
        
        $sitemap .= "  </url>\n";
        
        // Download variations
        $download_variations = get_post_meta($movie_id, '_download_variations', true);
        if ($download_variations) {
            foreach (array_slice($download_variations, 0, 10) as $variation) {
                $variation_slug = sanitize_title($variation);
                $variation_url = home_url() . "/$variation_slug/";
                
                $sitemap .= "  <url>\n";
                $sitemap .= "    <loc>$variation_url</loc>\n";
                $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
                $sitemap .= "    <changefreq>monthly</changefreq>\n";
                $sitemap .= "    <priority>0.7</priority>\n";
                $sitemap .= "  </url>\n";
            }
        }
        
        // Search variations
        $search_variations = get_post_meta($movie_id, '_search_variations', true);
        if ($search_variations) {
            foreach (array_slice($search_variations, 0, 8) as $variation) {
                $variation_slug = sanitize_title($variation);
                $variation_url = home_url() . "/$variation_slug/";
                
                $sitemap .= "  <url>\n";
                $sitemap .= "    <loc>$variation_url</loc>\n";
                $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
                $sitemap .= "    <changefreq>monthly</changefreq>\n";
                $sitemap .= "    <priority>0.6</priority>\n";
                $sitemap .= "  </url>\n";
            }
        }
        
        // Director pages
        if ($movie_director) {
            $director_slug = sanitize_title($movie_director);
            $director_url = home_url() . "/director/$director_slug/";
            
            $sitemap .= "  <url>\n";
            $sitemap .= "    <loc>$director_url</loc>\n";
            $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
            $sitemap .= "    <changefreq>monthly</changefreq>\n";
            $sitemap .= "    <priority>0.5</priority>\n";
            $sitemap .= "  </url>\n";
        }
        
        // Cast pages
        if ($movie_cast) {
            $cast_array = explode(',', $movie_cast);
            foreach (array_slice($cast_array, 0, 5) as $actor) {
                $actor = trim($actor);
                $actor_slug = sanitize_title($actor);
                $actor_url = home_url() . "/actor/$actor_slug/";
                
                $sitemap .= "  <url>\n";
                $sitemap .= "    <loc>$actor_url</loc>\n";
                $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
                $sitemap .= "    <changefreq>monthly</changefreq>\n";
                $sitemap .= "    <priority>0.5</priority>\n";
                $sitemap .= "  </url>\n";
            }
        }
        
        // Year pages
        if ($movie_year) {
            $year_url = home_url() . "/year/$movie_year/";
            
            $sitemap .= "  <url>\n";
            $sitemap .= "    <loc>$year_url</loc>\n";
            $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
            $sitemap .= "    <changefreq>monthly</changefreq>\n";
            $sitemap .= "    <priority>0.4</priority>\n";
            $sitemap .= "  </url>\n";
        }
    }
    
    // TV Shows
    $tvshows = get_posts(array(
        'post_type' => 'tvshows',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    foreach ($tvshows as $show) {
        $show_id = $show->ID;
        $show_title = get_the_title($show_id);
        $show_url = get_permalink($show_id);
        $show_modified = get_the_modified_date('Y-m-d\TH:i:s+00:00', $show_id);
        $show_image = dbmovies_get_poster($show_id, 'medium');
        
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>$show_url</loc>\n";
        $sitemap .= "    <lastmod>$show_modified</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.8</priority>\n";
        
        if ($show_image) {
            $sitemap .= "    <image:image>\n";
            $sitemap .= "      <image:loc>$show_image</image:loc>\n";
            $sitemap .= "      <image:title>$show_title</image:title>\n";
            $sitemap .= "      <image:caption>$show_title TV Show</image:caption>\n";
            $sitemap .= "    </image:image>\n";
        }
        
        $sitemap .= "  </url>\n";
    }
    
    // Categories
    $categories = get_categories(array(
        'taxonomy' => 'genres',
        'hide_empty' => true
    ));
    
    foreach ($categories as $category) {
        $category_url = get_term_link($category);
        
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>$category_url</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.6</priority>\n";
        $sitemap .= "  </url>\n";
    }
    
    // Tags
    $tags = get_tags(array('hide_empty' => true));
    
    foreach ($tags as $tag) {
        $tag_url = get_tag_link($tag->term_id);
        
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>$tag_url</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
        $sitemap .= "    <changefreq>monthly</changefreq>\n";
        $sitemap .= "    <priority>0.4</priority>\n";
        $sitemap .= "  </url>\n";
    }
    
    // Archive pages
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/movies/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>0.8</priority>\n";
    $sitemap .= "  </url>\n";
    
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/tvshows/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>0.8</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Search pages
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/search/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>weekly</changefreq>\n";
    $sitemap .= "    <priority>0.5</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Latest movies page
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/latest-movies/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>0.7</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Popular movies page
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/popular-movies/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>0.7</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Top rated movies page
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/top-rated-movies/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>0.7</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Year archive pages (last 10 years)
    $current_year = date('Y');
    for ($year = $current_year; $year >= $current_year - 10; $year--) {
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>" . home_url() . "/year/$year/</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
        $sitemap .= "    <changefreq>monthly</changefreq>\n";
        $sitemap .= "    <priority>0.5</priority>\n";
        $sitemap .= "  </url>\n";
    }
    
    $sitemap .= "</urlset>";
    
    // Save advanced sitemap
    $upload_dir = wp_upload_dir();
    $sitemap_path = $upload_dir['basedir'] . '/sitemap.xml';
    file_put_contents($sitemap_path, $sitemap);
    
    // Also save to root directory for easy access
    $root_sitemap_path = ABSPATH . 'sitemap.xml';
    file_put_contents($root_sitemap_path, $sitemap);
    
    return $sitemap;
}

// Generate sitemap on post save
add_action('save_post', 'dooplay_advanced_sitemap_generator');

// Auto submit sitemap to search engines
function dooplay_auto_submit_sitemap($post_id) {
    if (get_post_status($post_id) == 'publish') {
        $sitemap_url = home_url() . '/sitemap.xml';
        
        // Submit to Google
        $google_url = "https://www.google.com/ping?sitemap=" . urlencode($sitemap_url);
        wp_remote_get($google_url);
        
        // Submit to Bing
        $bing_url = "https://www.bing.com/ping?sitemap=" . urlencode($sitemap_url);
        wp_remote_get($bing_url);
        
        // Submit to Yandex
        $yandex_url = "https://blogs.yandex.com/pings/?status=success&url=" . urlencode($sitemap_url);
        wp_remote_get($yandex_url);
        
        // Submit to DuckDuckGo
        $duckduckgo_url = "https://duckduckgo.com/?q=" . urlencode($sitemap_url);
        wp_remote_get($duckduckgo_url);
    }
}

add_action('save_post', 'dooplay_auto_submit_sitemap');

// Create sitemap index file
function dooplay_create_sitemap_index() {
    $sitemap_index = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $sitemap_index .= "<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    $sitemap_index .= "  <sitemap>\n";
    $sitemap_index .= "    <loc>" . home_url() . "/sitemap.xml</loc>\n";
    $sitemap_index .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap_index .= "  </sitemap>\n";
    
    $sitemap_index .= "</sitemapindex>";
    
    // Save sitemap index
    $upload_dir = wp_upload_dir();
    $sitemap_index_path = $upload_dir['basedir'] . '/sitemap-index.xml';
    file_put_contents($sitemap_index_path, $sitemap_index);
    
    // Also save to root directory
    $root_sitemap_index_path = ABSPATH . 'sitemap-index.xml';
    file_put_contents($root_sitemap_index_path, $sitemap_index);
}

add_action('save_post', 'dooplay_create_sitemap_index');

// Add sitemap to robots.txt
function dooplay_add_sitemap_to_robots() {
    $robots_content = "User-agent: *\n";
    $robots_content .= "Allow: /\n";
    $robots_content .= "Disallow: /wp-admin/\n";
    $robots_content .= "Disallow: /wp-includes/\n";
    $robots_content .= "Disallow: /wp-content/plugins/\n";
    $robots_content .= "Disallow: /wp-content/themes/\n";
    $robots_content .= "Disallow: /wp-content/cache/\n";
    $robots_content .= "Disallow: /wp-content/uploads/2025/\n";
    $robots_content .= "Disallow: /wp-content/uploads/armember/\n";
    $robots_content .= "Allow: /movies/\n";
    $robots_content .= "Allow: /tvshows/\n";
    $robots_content .= "Allow: /episodes/\n";
    $robots_content .= "Allow: /seasons/\n";
    $robots_content .= "Sitemap: " . home_url() . "/sitemap.xml\n";
    $robots_content .= "Sitemap: " . home_url() . "/sitemap-index.xml\n";
    $robots_content .= "Crawl-delay: 1\n";
    
    // Save robots.txt
    $robots_path = ABSPATH . 'robots.txt';
    file_put_contents($robots_path, $robots_content);
}

add_action('save_post', 'dooplay_add_sitemap_to_robots');

// Force generate sitemap on theme activation
function dooplay_force_generate_sitemap() {
    dooplay_advanced_sitemap_generator();
    dooplay_create_sitemap_index();
    dooplay_add_sitemap_to_robots();
}

add_action('after_switch_theme', 'dooplay_force_generate_sitemap');

// Force Dark Theme Style
function dooplay_force_dark_theme() {
    // Set theme style to dark if not already set
    $current_style = get_option('dooplay_style');
    if ($current_style !== 'dark') {
        update_option('dooplay_style', 'dark');
    }
}

// Run on theme activation and ensure dark theme
add_action('after_switch_theme', 'dooplay_force_dark_theme');
add_action('init', 'dooplay_force_dark_theme');

// Add CSS Variables for Theme Compatibility - TEMPORARILY DISABLED
function dooplay_add_css_variables() {
    // TEMPORARILY DISABLED TO PREVENT WHITE SCREEN ISSUES
    return;
    
    // Check if theme options exist and we're not in admin
    if (!function_exists('dooplay_get_option') || is_admin()) {
        return;
    }
    
    try {
        $style = dooplay_get_option('style', 'dark'); // Default to dark
        $main_color = dooplay_get_option('maincolor', '#408bea');
        
        echo "<style>\n";
        echo ":root {\n";
        
        if ($style == 'dark') {
            // Dark theme variables
            echo "  --dooplay-bg-color: #1a1a1a;\n";
            echo "  --dooplay-card-bg: #2d2d2d;\n";
            echo "  --dooplay-text-color: #ffffff;\n";
            echo "  --dooplay-heading-color: #ffffff;\n";
            echo "  --dooplay-border-color: #404040;\n";
            echo "  --dooplay-link-color: #4fc3f7;\n";
            echo "  --dooplay-main-color: $main_color;\n";
            echo "  --dooplay-accent-color: #17a2b8;\n";
            echo "  --dooplay-light-bg: #2d2d2d;\n";
            echo "  --dooplay-warning-bg: rgba(255, 193, 7, 0.1);\n";
            echo "  --dooplay-warning-border: rgba(255, 193, 7, 0.3);\n";
            echo "  --dooplay-warning-text: #ffc107;\n";
            echo "  --dooplay-warning-color: #ffc107;\n";
            echo "  --dooplay-warning-light: rgba(255, 193, 7, 0.1);\n";
            echo "  --dooplay-success-bg: rgba(76, 175, 80, 0.1);\n";
            echo "  --dooplay-success-color: #4CAF50;\n";
            echo "  --dooplay-success-border: rgba(76, 175, 80, 0.3);\n";
            echo "  --dooplay-success-light: rgba(76, 175, 80, 0.2);\n";
            echo "  --dooplay-success-lighter: rgba(76, 175, 80, 0.3);\n";
            echo "  --dooplay-info-bg: rgba(33, 150, 243, 0.1);\n";
            echo "  --dooplay-info-color: #2196F3;\n";
            echo "  --dooplay-info-border: rgba(33, 150, 243, 0.3);\n";
            echo "  --dooplay-info-light: rgba(33, 150, 243, 0.1);\n";
            echo "  --dooplay-info-lighter: rgba(33, 150, 243, 0.2);\n";
            echo "  --dooplay-purple-bg: rgba(156, 39, 176, 0.1);\n";
            echo "  --dooplay-purple-color: #9C27B0;\n";
            echo "  --dooplay-purple-border: rgba(156, 39, 176, 0.3);\n";
            echo "  --dooplay-purple-light: rgba(156, 39, 176, 0.1);\n";
            echo "  --dooplay-purple-lighter: rgba(156, 39, 176, 0.2);\n";
            echo "  --dooplay-danger-bg: rgba(244, 67, 54, 0.1);\n";
            echo "  --dooplay-danger-color: #f44336;\n";
            echo "  --dooplay-danger-border: rgba(244, 67, 54, 0.3);\n";
            echo "  --dooplay-danger-light: rgba(244, 67, 54, 0.1);\n";
            echo "  --dooplay-danger-text: #f44336;\n";
            echo "  --dooplay-rating-bg: #ffc107;\n";
            echo "  --dooplay-rating-text: #333;\n";
        } else {
            // Light theme variables
            echo "  --dooplay-bg-color: #f8f9fa;\n";
            echo "  --dooplay-card-bg: #ffffff;\n";
            echo "  --dooplay-text-color: #333333;\n";
            echo "  --dooplay-heading-color: #333333;\n";
            echo "  --dooplay-border-color: #e9ecef;\n";
            echo "  --dooplay-link-color: #0c5460;\n";
            echo "  --dooplay-main-color: $main_color;\n";
            echo "  --dooplay-accent-color: #17a2b8;\n";
            echo "  --dooplay-light-bg: #e8f4fd;\n";
            echo "  --dooplay-warning-bg: #fff3cd;\n";
            echo "  --dooplay-warning-border: #ffeaa7;\n";
            echo "  --dooplay-warning-text: #856404;\n";
            echo "  --dooplay-warning-color: #FF9800;\n";
            echo "  --dooplay-warning-light: #FFF3E0;\n";
            echo "  --dooplay-success-bg: #f0f8ff;\n";
            echo "  --dooplay-success-color: #4CAF50;\n";
            echo "  --dooplay-success-border: #C8E6C9;\n";
            echo "  --dooplay-success-light: #E8F5E8;\n";
            echo "  --dooplay-success-lighter: #C8E6C9;\n";
            echo "  --dooplay-info-bg: #E3F2FD;\n";
            echo "  --dooplay-info-color: #2196F3;\n";
            echo "  --dooplay-info-border: #BBDEFB;\n";
            echo "  --dooplay-info-light: #E3F2FD;\n";
            echo "  --dooplay-info-lighter: #BBDEFB;\n";
            echo "  --dooplay-purple-bg: #F3E5F5;\n";
            echo "  --dooplay-purple-color: #9C27B0;\n";
            echo "  --dooplay-purple-border: #E1BEE7;\n";
            echo "  --dooplay-purple-light: #F3E5F5;\n";
            echo "  --dooplay-purple-lighter: #E1BEE7;\n";
            echo "  --dooplay-danger-bg: #FFEBEE;\n";
            echo "  --dooplay-danger-color: #f44336;\n";
            echo "  --dooplay-danger-border: #FFCDD2;\n";
            echo "  --dooplay-danger-light: #FFEBEE;\n";
            echo "  --dooplay-danger-text: #f44336;\n";
            echo "  --dooplay-rating-bg: #ffc107;\n";
            echo "  --dooplay-rating-text: #333;\n";
        }
        
        echo "}\n";
        echo "</style>\n";
    } catch (Exception $e) {
        // If there's an error, don't output anything
        return;
    }
}

// ============================================
// DeshiFlix Mobile App Integration
// ============================================

// Include Mobile App API - DISABLED to avoid conflicts
// if (DOO_MOBILE_APP && file_exists(get_template_directory() . '/inc/doo_mobile_api.php')) {
//     require_once get_template_directory() . '/inc/doo_mobile_api.php';
//     new DooMobileAPI();
// }

// Include Push Notifications
if (DOO_PUSH_NOTIFICATIONS && file_exists(get_template_directory() . '/inc/doo_push_notifications.php')) {
    require_once get_template_directory() . '/inc/doo_push_notifications.php';
}

// Include App Database
if (file_exists(get_template_directory() . '/inc/doo_app_database.php')) {
    require_once get_template_directory() . '/inc/doo_app_database.php';
}

// Include App Settings in CSF Options
if (file_exists(get_template_directory() . '/inc/csf/options.app_settings.php')) {
    require_once get_template_directory() . '/inc/csf/options.app_settings.php';
}

// Include App Dashboard
if (file_exists(get_template_directory() . '/inc/doo_app_dashboard.php')) {
    require_once get_template_directory() . '/inc/doo_app_dashboard.php';
}

// Include Enhanced Mobile API for DeshiFlix
if (file_exists(get_template_directory() . '/inc/doo_mobile_api_enhanced.php')) {
    require_once get_template_directory() . '/inc/doo_mobile_api_enhanced.php';
}

// Include Movie Links API
if (file_exists(get_template_directory() . '/inc/doomobile/movie-links.php')) {
    require_once get_template_directory() . '/inc/doomobile/movie-links.php';
}

// Add DeshiFlix CSS fixes
function deshiflix_add_custom_css() {
    wp_enqueue_style(
        'deshiflix-fixes',
        get_template_directory_uri() . '/assets/css/deshiflix-fixes.css',
        array(),
        '1.0.0'
    );
}
add_action('wp_enqueue_scripts', 'deshiflix_add_custom_css');

// Only add CSS variables if not in admin
if (!is_admin()) {
    add_action('wp_head', 'dooplay_add_css_variables', 5);
}

// DeshiFlix Mobile App API Endpoints
function deshiflix_api_init() {
    // Handle API requests before WordPress routing
    if (isset($_GET['deshiflix_api'])) {
        $api_type = $_GET['deshiflix_api'];

        switch ($api_type) {
            case 'movie_links':
                include_once(ABSPATH . 'test-api.php');
                exit;
            case 'series_data':
                include_once(ABSPATH . 'series-api.php');
                exit;
            case 'episode_links':
                include_once(ABSPATH . 'episode-api.php');
                exit;
            case 'debug_metadata':
                include_once(ABSPATH . 'debug-metadata.php');
                exit;
        }
    }
}
add_action('init', 'deshiflix_api_init', 1);

// Add rewrite rules for API endpoints
function deshiflix_api_rewrite_rules() {
    add_rewrite_rule('^test-api\.php$', 'index.php?deshiflix_api=movie_links', 'top');
    add_rewrite_rule('^series-api\.php$', 'index.php?deshiflix_api=series_data', 'top');
    add_rewrite_rule('^episode-api\.php$', 'index.php?deshiflix_api=episode_links', 'top');
    add_rewrite_rule('^debug-metadata\.php$', 'index.php?deshiflix_api=debug_metadata', 'top');
}
add_action('init', 'deshiflix_api_rewrite_rules');

// Add query vars for API
function deshiflix_api_query_vars($vars) {
    $vars[] = 'deshiflix_api';
    return $vars;
}
add_filter('query_vars', 'deshiflix_api_query_vars');

// DeshiFlix REST API Endpoints
add_action('rest_api_init', 'deshiflix_register_rest_api');

function deshiflix_register_rest_api() {
    // Movie Links API
    register_rest_route('deshiflix/v1', '/movie/(?P<id>\d+)/links', array(
        'methods' => 'GET',
        'callback' => 'deshiflix_get_movie_links_api',
        'permission_callback' => '__return_true',
        'args' => array(
            'id' => array(
                'validate_callback' => function($param, $request, $key) {
                    return is_numeric($param);
                }
            ),
        ),
    ));
}

function deshiflix_get_movie_links_api($request) {
    $movie_id = $request['id'];

    // Get movie post
    $movie = get_post($movie_id);
    if (!$movie || $movie->post_type !== 'movies') {
        return new WP_Error('movie_not_found', 'Movie not found', array('status' => 404));
    }

    // Get download links from dt_links post type
    $links_query = new WP_Query(array(
        'post_type' => 'dt_links',
        'meta_query' => array(
            array(
                'key' => '_dool_post_id',
                'value' => $movie_id,
                'compare' => '='
            )
        ),
        'posts_per_page' => -1
    ));

    $download_links = array();

    if ($links_query->have_posts()) {
        while ($links_query->have_posts()) {
            $links_query->the_post();
            $link_id = get_the_ID();

            $quality = get_post_meta($link_id, '_dool_quality', true) ?: 'HD 720p';
            $language = get_post_meta($link_id, '_dool_lang', true) ?: 'English';
            $size = get_post_meta($link_id, '_dool_size', true) ?: '1GB';
            $url = get_post_meta($link_id, '_dool_url', true);

            if ($url) {
                $download_links[] = array(
                    'quality' => $quality,
                    'language' => $language,
                    'size' => $size,
                    'url' => $url,
                    'episode_label' => null
                );
            }
        }
        wp_reset_postdata();
    }

    // If no links found, generate mock data based on your site
    if (empty($download_links)) {
        $download_links = array(
            array(
                'quality' => 'HD 720p',
                'language' => 'English',
                'size' => '1GB',
                'url' => 'https://nim-server2-haze-cc88.cinepixserver00.workers.dev/1:/1-30-24/12th%20Fail%202023%20Hindi%20(ORG%205.1)%20Cinepix.top%20720p%20WEB-DL%20x264%20ESubs.mkv',
                'episode_label' => null
            ),
            array(
                'quality' => 'HD 1440p',
                'language' => 'English',
                'size' => '2GB',
                'url' => 'https://nim-server2-haze-cc88.cinepixserver00.workers.dev/1:/1-30-24/12th%20Fail%202023%20Hindi%20(ORG%205.1)%20Cinepix.top%20720p%20WEB-DL%20x264%20ESubs.mkv',
                'episode_label' => null
            )
        );
    }

    // Check if it's a series
    $title = $movie->post_title;
    $is_series = preg_match('/S\d+|Season|Episode|EP\d+/i', $title);

    return array(
        'success' => true,
        'movie_id' => $movie_id,
        'movie_title' => $title,
        'content_type' => $is_series ? 'series' : 'movie',
        'total_links' => count($download_links),
        'total_episodes' => $is_series ? 10 : 0,
        'links' => $download_links
    );
}

// Include Android TV Support
require_once get_template_directory() . '/inc/doo_android_tv.php';

// Include DeshiFlix Premium System (with error handling)
if (defined('DESHIFLIX_PREMIUM_ENABLED') && DESHIFLIX_PREMIUM_ENABLED) {
    $premium_core_file = get_template_directory() . '/inc/premium/core/class-premium-core.php';
    if (file_exists($premium_core_file)) {
        try {
            require_once $premium_core_file;
        } catch (Exception $e) {
            error_log('Premium Core Error: ' . $e->getMessage());
        } catch (ParseError $e) {
            error_log('Premium Core Parse Error: ' . $e->getMessage());
        } catch (Error $e) {
            error_log('Premium Core Fatal Error: ' . $e->getMessage());
        }
    } else {
        error_log('Premium Core file not found: ' . $premium_core_file);
    }
}

// DeshiFlix Premium Admin Menu (Direct Implementation)
add_action('admin_menu', 'deshiflix_add_premium_admin_menu');

function deshiflix_add_premium_admin_menu() {
    // Main Premium Menu
    add_menu_page(
        'DeshiFlix Premium System',
        'DeshiFlix Premium',
        'manage_options',
        'deshiflix-premium-dashboard',
        'deshiflix_premium_dashboard_page',
        'dashicons-star-filled',
        30
    );

    // Settings Submenu
    add_submenu_page(
        'deshiflix-premium-dashboard',
        'Premium Settings',
        'Settings',
        'manage_options',
        'deshiflix-premium-settings',
        'deshiflix_premium_settings_page'
    );

    // Analytics Submenu
    add_submenu_page(
        'deshiflix-premium-dashboard',
        'Premium Analytics',
        'Analytics',
        'manage_options',
        'deshiflix-premium-analytics',
        'deshiflix_premium_analytics_page'
    );

    // Users Submenu
    add_submenu_page(
        'deshiflix-premium-dashboard',
        'Premium Users',
        'Users',
        'manage_options',
        'deshiflix-premium-users',
        'deshiflix_premium_users_page'
    );

    // Plans Submenu
    add_submenu_page(
        'deshiflix-premium-dashboard',
        'Premium Plans',
        'Plans',
        'manage_options',
        'deshiflix-premium-plans',
        'deshiflix_premium_plans_page'
    );
}

// Premium Dashboard Page
function deshiflix_premium_dashboard_page() {
    $admin_file = get_template_directory() . '/inc/premium/admin/premium-dooplay-admin.php';
    if (file_exists($admin_file) && class_exists('DeshiFlix_Premium_DooPlay_Admin')) {
        $admin = DeshiFlix_Premium_DooPlay_Admin::get_instance();
        $admin->render_dooplay_admin_page();
    } else {
        ?>
        <div class="wrap">
            <h1><?php _e('DeshiFlix Premium Dashboard', 'deshiflix'); ?></h1>
            <div class="notice notice-success">
                <p><strong><?php _e('🌟 Premium System Active!', 'deshiflix'); ?></strong></p>
            </div>

            <div class="card" style="max-width: 800px;">
                <h2><?php _e('Premium Features Status', 'deshiflix'); ?></h2>
                <table class="widefat">
                    <thead>
                        <tr>
                            <th><?php _e('Feature', 'deshiflix'); ?></th>
                            <th><?php _e('Status', 'deshiflix'); ?></th>
                            <th><?php _e('Description', 'deshiflix'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong><?php _e('Instant Downloads', 'deshiflix'); ?></strong></td>
                            <td><span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span> <?php _e('Active', 'deshiflix'); ?></td>
                            <td><?php _e('Premium users get instant downloads (0 second wait)', 'deshiflix'); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('Ad-Free Experience', 'deshiflix'); ?></strong></td>
                            <td><span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span> <?php _e('Active', 'deshiflix'); ?></td>
                            <td><?php _e('Premium users see no advertisements', 'deshiflix'); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('Per-Link Premium Control', 'deshiflix'); ?></strong></td>
                            <td><span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span> <?php _e('Active', 'deshiflix'); ?></td>
                            <td><?php _e('Admin can mark individual links as premium-only', 'deshiflix'); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('Premium Visual Indicators', 'deshiflix'); ?></strong></td>
                            <td><span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span> <?php _e('Active', 'deshiflix'); ?></td>
                            <td><?php _e('Golden styling and badges for premium content', 'deshiflix'); ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="card" style="max-width: 800px; margin-top: 20px;">
                <h2><?php _e('🧪 Test Premium Features', 'deshiflix'); ?></h2>
                <p><?php _e('Use the test account to verify premium features:', 'deshiflix'); ?></p>
                <div style="background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <p><strong><?php _e('Test Account:', 'deshiflix'); ?></strong></p>
                    <p>
                        <strong><?php _e('Username:', 'deshiflix'); ?></strong> <code style="background: #fff; padding: 2px 6px;">premium_test_user</code><br>
                        <strong><?php _e('Password:', 'deshiflix'); ?></strong> <code style="background: #fff; padding: 2px 6px;">premium123</code>
                    </p>
                </div>
                <p>
                    <a href="<?php echo wp_logout_url(wp_login_url()); ?>" class="button button-primary">
                        <?php _e('🔄 Switch to Test User', 'deshiflix'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=deshiflix-premium-settings'); ?>" class="button button-secondary">
                        <?php _e('⚙️ Premium Settings', 'deshiflix'); ?>
                    </a>
                    <a href="<?php echo admin_url('edit.php?post_type=dt_links'); ?>" class="button button-secondary">
                        <?php _e('🔗 Manage Links', 'deshiflix'); ?>
                    </a>
                </p>
            </div>
        </div>
        <?php
    }
}

// Premium Settings Page
function deshiflix_premium_settings_page() {
    $settings_file = get_template_directory() . '/inc/premium/admin/settings-page-simple.php';
    if (file_exists($settings_file)) {
        include $settings_file;
    } else {
        ?>
        <div class="wrap">
            <h1><?php _e('Premium Settings', 'deshiflix'); ?></h1>
            <div class="notice notice-info">
                <p><?php _e('Premium settings interface loading...', 'deshiflix'); ?></p>
            </div>
        </div>
        <?php
    }
}

// Premium Analytics Page
function deshiflix_premium_analytics_page() {
    $analytics_file = get_template_directory() . '/inc/premium/admin/premium-analytics.php';
    if (file_exists($analytics_file)) {
        require_once $analytics_file;
        if (class_exists('DeshiFlix_Premium_Analytics')) {
            DeshiFlix_Premium_Analytics::render_analytics_page();
        }
    } else {
        ?>
        <div class="wrap">
            <h1><?php _e('Premium Analytics', 'deshiflix'); ?></h1>
            <div class="notice notice-info">
                <p><?php _e('Premium analytics loading...', 'deshiflix'); ?></p>
            </div>
        </div>
        <?php
    }
}

// Premium Users Page
function deshiflix_premium_users_page() {
    $users_file = get_template_directory() . '/inc/premium/admin/premium-users-management.php';
    if (file_exists($users_file)) {
        require_once $users_file;
        if (class_exists('DeshiFlix_Premium_Users_Management')) {
            DeshiFlix_Premium_Users_Management::render_users_page();
        }
    } else {
        ?>
        <div class="wrap">
            <h1><?php _e('Premium Users', 'deshiflix'); ?></h1>
            <div class="notice notice-info">
                <p><?php _e('Premium users management loading...', 'deshiflix'); ?></p>
            </div>
        </div>
        <?php
    }
}

// Premium Plans Page
function deshiflix_premium_plans_page() {
    $plans_file = get_template_directory() . '/inc/premium/admin/premium-plans-management.php';
    if (file_exists($plans_file)) {
        require_once $plans_file;
        if (class_exists('DeshiFlix_Premium_Plans_Management')) {
            DeshiFlix_Premium_Plans_Management::render_plans_page();
        }
    } else {
        ?>
        <div class="wrap">
            <h1><?php _e('Premium Plans', 'deshiflix'); ?></h1>
            <div class="notice notice-info">
                <p><?php _e('Premium plans management loading...', 'deshiflix'); ?></p>
            </div>
        </div>
        <?php
    }
}
