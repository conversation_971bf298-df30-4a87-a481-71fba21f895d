<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Stream Test - PTV Sports</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .player-container {
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 30px;
            position: relative;
        }
        
        video {
            width: 100%;
            height: 500px;
            background: #000;
            display: block;
        }
        
        .info {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.loading {
            background: #ffc107;
            color: #000;
        }
        
        .status.success {
            background: #28a745;
            color: white;
        }
        
        .status.error {
            background: #dc3545;
            color: white;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 Live Stream Test</h1>
            <h2>PTV Sports - Cricket Channel</h2>
        </div>
        
        <div class="info">
            <h3>📺 Stream Information</h3>
            <p><strong>Channel:</strong> Deepto TV</p>
            <p><strong>Stream URL:</strong> <code>https://byphdgllyk.gpcdn.net/hls/deeptotv/index.m3u8</code></p>
            <p><strong>Type:</strong> HLS (.m3u8)</p>
            <p><strong>Expected Content:</strong> Bangladeshi Entertainment</p>
        </div>

        <div class="player-container">
            <video id="test-video" controls autoplay muted>
                <source src="https://byphdgllyk.gpcdn.net/hls/deeptotv/index.m3u8" type="application/vnd.apple.mpegurl">
                Your browser does not support the video tag.
            </video>
        </div>
        
        <div id="status" class="status loading">
            🔄 Initializing stream test...
        </div>
        
        <div class="controls">
            <button onclick="reloadStream()">🔄 Reload Stream</button>
            <button onclick="testDirectUrl()">🔗 Test Direct URL</button>
            <button onclick="toggleMute()">🔊 Toggle Mute</button>
            <button onclick="goFullscreen()">⛶ Fullscreen</button>
        </div>
        
        <div class="info">
            <h3>🔍 Test Results</h3>
            <div id="test-results">
                <p>Testing stream compatibility...</p>
            </div>
        </div>
        
        <div id="console-log" class="log">
            <div>Stream Test Console:</div>
        </div>
    </div>

    <script>
        const video = document.getElementById('test-video');
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('test-results');
        const logDiv = document.getElementById('console-log');
        
        const streamUrl = 'https://byphdgllyk.gpcdn.net/hls/deeptotv/index.m3u8';
        let hls = null;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'loading') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function updateResults(html) {
            resultsDiv.innerHTML = html;
        }
        
        // Initialize player
        function initializePlayer() {
            log('🚀 Starting stream test...');
            log('📡 Stream URL: ' + streamUrl);
            
            // Check HLS support
            if (Hls.isSupported()) {
                log('✅ HLS.js is supported');
                initHLS();
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                log('✅ Native HLS support detected');
                initNativeHLS();
            } else {
                log('❌ HLS not supported, trying direct playback');
                initDirect();
            }
        }
        
        function initHLS() {
            try {
                hls = new Hls({
                    enableWorker: false,
                    lowLatencyMode: false,
                    debug: false
                });
                
                hls.loadSource(streamUrl);
                hls.attachMedia(video);
                
                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    log('✅ HLS manifest parsed successfully');
                    updateStatus('✅ Stream loaded successfully', 'success');
                    updateResults(`
                        <p>✅ <strong>Stream Status:</strong> Successfully loaded</p>
                        <p>🎯 <strong>Player Type:</strong> HLS.js</p>
                        <p>📊 <strong>Manifest:</strong> Parsed successfully</p>
                        <p>🎬 <strong>Ready to play:</strong> Yes</p>
                    `);
                });
                
                hls.on(Hls.Events.ERROR, function(event, data) {
                    log('❌ HLS error: ' + JSON.stringify(data));
                    updateStatus('❌ Stream error: ' + data.type, 'error');
                    updateResults(`
                        <p>❌ <strong>Stream Status:</strong> Error</p>
                        <p>🚫 <strong>Error Type:</strong> ${data.type}</p>
                        <p>📝 <strong>Details:</strong> ${data.details}</p>
                        <p>💡 <strong>Suggestion:</strong> Try reloading or check stream URL</p>
                    `);
                });
                
                hls.on(Hls.Events.FRAG_LOADED, function() {
                    log('📦 Fragment loaded');
                });
                
            } catch (error) {
                log('❌ HLS initialization error: ' + error.message);
                updateStatus('❌ HLS initialization failed', 'error');
            }
        }
        
        function initNativeHLS() {
            video.src = streamUrl;
            video.load();
            log('🍎 Using native HLS support');
        }
        
        function initDirect() {
            video.src = streamUrl;
            video.load();
            log('🎯 Using direct video playback');
        }
        
        // Video event listeners
        video.addEventListener('loadstart', function() {
            log('📥 Video loading started');
            updateStatus('📥 Loading stream...', 'loading');
        });
        
        video.addEventListener('canplay', function() {
            log('✅ Video can play');
            updateStatus('✅ Stream ready to play', 'success');
        });
        
        video.addEventListener('playing', function() {
            log('▶️ Video is playing');
            updateStatus('▶️ Stream is playing', 'success');
        });
        
        video.addEventListener('error', function(e) {
            log('❌ Video error: ' + e.message);
            updateStatus('❌ Video playback error', 'error');
            updateResults(`
                <p>❌ <strong>Stream Status:</strong> Playback Error</p>
                <p>🚫 <strong>Error:</strong> ${e.message || 'Unknown error'}</p>
                <p>💡 <strong>Possible causes:</strong></p>
                <ul>
                    <li>Stream is offline or unavailable</li>
                    <li>CORS (Cross-Origin) restrictions</li>
                    <li>Invalid stream format</li>
                    <li>Network connectivity issues</li>
                </ul>
            `);
        });
        
        video.addEventListener('waiting', function() {
            log('⏳ Video buffering...');
            updateStatus('⏳ Buffering...', 'loading');
        });
        
        video.addEventListener('pause', function() {
            log('⏸️ Video paused');
        });
        
        // Control functions
        function reloadStream() {
            log('🔄 Reloading stream...');
            if (hls) {
                hls.destroy();
                hls = null;
            }
            video.load();
            setTimeout(initializePlayer, 500);
        }
        
        function testDirectUrl() {
            window.open(streamUrl, '_blank');
        }
        
        function toggleMute() {
            video.muted = !video.muted;
            log(video.muted ? '🔇 Muted' : '🔊 Unmuted');
        }
        
        function goFullscreen() {
            if (video.requestFullscreen) {
                video.requestFullscreen();
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('🌐 Page loaded, initializing player...');
            initializePlayer();
        });
    </script>
</body>
</html>
