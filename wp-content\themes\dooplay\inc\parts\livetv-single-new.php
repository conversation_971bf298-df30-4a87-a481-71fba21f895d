<?php
/*
* Live TV Single Channel Template for Dooplay Theme
* Following Dooplay's single page structure
*/

// Get channel data (passed from parent template)
if (!isset($channel) || !$channel) {
    echo '<div class="no-results"><h3>Channel Not Found</h3><p>The requested channel could not be found.</p></div>';
    return;
}

// Get settings
$settings = get_option('doo_livetv_settings', array());
$player_type = isset($settings['player_type']) ? $settings['player_type'] : 'html5';

// Get related channels
$livetv = new DooLiveTV();
$related_channels = $livetv->get_channels(array(
    'category_id' => $channel->category_id,
    'limit' => 6,
    'status' => 'active'
));
?>

<!-- Single Channel Page - Following Dooplay Single Structure -->
<div class="sheader">
    <div class="data">
        <h1><?php echo esc_html($channel->name); ?></h1>
        <div class="extra">
            <?php if ($channel->category_name): ?>
                <span class="category" style="color: <?php echo esc_attr($channel->category_color); ?>">
                    <?php echo esc_html($channel->category_name); ?>
                </span>
            <?php endif; ?>
            
            <?php if ($channel->country): ?>
                <span class="country">
                    <i class="fas fa-globe"></i> <?php echo esc_html($channel->country); ?>
                </span>
            <?php endif; ?>
            
            <?php if ($channel->language): ?>
                <span class="language">
                    <i class="fas fa-language"></i> <?php echo esc_html($channel->language); ?>
                </span>
            <?php endif; ?>
            
            <?php if ($channel->quality): ?>
                <span class="quality">
                    <i class="fas fa-hd-video"></i> <?php echo esc_html($channel->quality); ?>
                </span>
            <?php endif; ?>
            
            <span class="views">
                <i class="fas fa-eye"></i> <?php echo number_format($channel->views); ?> views
            </span>
        </div>
        
        <?php if ($channel->description): ?>
            <div class="description">
                <p><?php echo esc_html($channel->description); ?></p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Simple Player Section -->
<div style="background: #000; margin: 20px 0; border-radius: 8px; overflow: hidden;">
    <!-- Player Header -->
    <div style="background: #333; padding: 15px; color: white;">
        <h3 style="margin: 0; display: inline-block;"><?php echo esc_html($channel->name); ?></h3>
        <span style="background: red; color: white; padding: 4px 8px; border-radius: 4px; margin-left: 15px; font-size: 12px;">● LIVE</span>
        <span style="background: #007cba; color: white; padding: 4px 8px; border-radius: 4px; margin-left: 10px; font-size: 12px;"><?php echo esc_html($channel->quality ?: 'HD'); ?></span>
        <a href="<?php echo get_permalink(76); ?>" style="float: right; background: #007cba; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;">← Back</a>
    </div>

    <!-- Simple Video Player -->
    <div id="player-container" style="position: relative; background: #000; border-radius: 8px; overflow: hidden;">
        <?php if ($channel->stream_url): ?>
            <!-- Simple Video Element -->
            <video
                id="simple-video"
                controls
                autoplay
                muted
                playsinline
                webkit-playsinline
                style="width: 100%; height: 500px; background: #000; display: block;">
                <source src="<?php echo esc_url($channel->stream_url); ?>" type="application/vnd.apple.mpegurl">
                <source src="<?php echo esc_url($channel->stream_url); ?>" type="video/mp4">
                <?php if ($channel->backup_url): ?>
                    <source src="<?php echo esc_url($channel->backup_url); ?>" type="application/vnd.apple.mpegurl">
                    <source src="<?php echo esc_url($channel->backup_url); ?>" type="video/mp4">
                <?php endif; ?>
                Your browser does not support the video tag.
            </video>

            <!-- Simple Error Message -->
            <div id="simple-error" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.9); display: none; align-items: center; justify-content: center; z-index: 10;">
                <div style="text-align: center; color: white; padding: 20px;">
                    <div style="font-size: 4rem; margin-bottom: 20px;">⚠️</div>
                    <h3>Stream Not Available</h3>
                    <p>Unable to load the stream</p>
                    <button onclick="reloadStream()" style="background: #007cba; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px; margin: 10px;">
                        🔄 Retry
                    </button>
                    <button onclick="openStreamDirect()" style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px; margin: 10px;">
                        🔗 Open Direct
                    </button>
                </div>
            </div>
        <?php else: ?>
            <div style="width: 100%; height: 500px; background: #000; display: flex; align-items: center; justify-content: center; color: white;">
                <div style="text-align: center;">
                    <div style="font-size: 4rem; margin-bottom: 20px;">📺</div>
                    <h3>No Stream Available</h3>
                    <p>Stream URL not configured for this channel</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Player Info -->
    <div style="background: #222; padding: 10px 15px; color: #ccc; font-size: 14px;">
        <span>👁 <?php echo number_format($channel->views); ?> views</span>
        <?php if ($channel->country): ?>
            <span style="margin-left: 20px;">🌍 <?php echo esc_html($channel->country); ?></span>
        <?php endif; ?>
        <?php if ($channel->language): ?>
            <span style="margin-left: 20px;">🗣 <?php echo esc_html($channel->language); ?></span>
        <?php endif; ?>
        <span style="float: right; color: #4caf50;">🟢 Online</span>
    </div>
</div>

<!-- Channel Information Tabs -->
<div class="channel-info-section">
    <div class="info-tabs" style="display: flex; border-bottom: 1px solid rgba(255,255,255,0.1); margin-bottom: 20px;">
        <div class="tab active" data-tab="info" style="padding: 15px 20px; cursor: pointer; border-bottom: 2px solid var(--dt-primary-color, #007cba); color: var(--dt-primary-color, #007cba);">
            <i class="fas fa-info-circle"></i> Information
        </div>
        <div class="tab" data-tab="related" style="padding: 15px 20px; cursor: pointer; color: rgba(255,255,255,0.7);">
            <i class="fas fa-list"></i> Related Channels
        </div>
    </div>
    
    <div class="tab-content">
        <div id="info-tab" class="tab-panel active">
            <div class="channel-details">
                <h3 style="margin-bottom: 20px;">Channel Details</h3>
                <div class="details-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div class="detail-item" style="padding: 15px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                        <strong style="color: rgba(255,255,255,0.8);">Name:</strong>
                        <span style="display: block; margin-top: 5px;"><?php echo esc_html($channel->name); ?></span>
                    </div>
                    
                    <?php if ($channel->category_name): ?>
                    <div class="detail-item" style="padding: 15px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                        <strong style="color: rgba(255,255,255,0.8);">Category:</strong>
                        <span style="display: block; margin-top: 5px; color: <?php echo esc_attr($channel->category_color); ?>">
                            <?php echo esc_html($channel->category_name); ?>
                        </span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($channel->country): ?>
                    <div class="detail-item" style="padding: 15px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                        <strong style="color: rgba(255,255,255,0.8);">Country:</strong>
                        <span style="display: block; margin-top: 5px;"><?php echo esc_html($channel->country); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($channel->language): ?>
                    <div class="detail-item" style="padding: 15px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                        <strong style="color: rgba(255,255,255,0.8);">Language:</strong>
                        <span style="display: block; margin-top: 5px;"><?php echo esc_html($channel->language); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($channel->quality): ?>
                    <div class="detail-item" style="padding: 15px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                        <strong style="color: rgba(255,255,255,0.8);">Quality:</strong>
                        <span style="display: block; margin-top: 5px;"><?php echo esc_html($channel->quality); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="detail-item" style="padding: 15px; background: rgba(255,255,255,0.05); border-radius: 6px;">
                        <strong style="color: rgba(255,255,255,0.8);">Views:</strong>
                        <span style="display: block; margin-top: 5px;"><?php echo number_format($channel->views); ?></span>
                    </div>
                </div>
                
                <?php if ($channel->description): ?>
                <div class="description-full" style="margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.05); border-radius: 8px;">
                    <h4 style="margin-bottom: 15px;">Description</h4>
                    <p style="line-height: 1.6; color: rgba(255,255,255,0.8);"><?php echo nl2br(esc_html($channel->description)); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div id="related-tab" class="tab-panel" style="display: none;">
            <?php if (!empty($related_channels)): ?>
                <div class="related-channels">
                    <h3 style="margin-bottom: 20px;">Related Channels</h3>
                    <div class="items normal">
                        <?php foreach ($related_channels as $related): ?>
                            <?php if ($related->id != $channel->id): ?>
                                <article class="item livetv">
                                    <div class="poster">
                                        <?php if ($related->logo_url): ?>
                                            <img src="<?php echo esc_url($related->logo_url); ?>" alt="<?php echo esc_attr($related->name); ?>" loading="lazy">
                                        <?php else: ?>
                                            <div class="no-logo">
                                                <i class="fas fa-tv"></i>
                                                <span><?php echo esc_html(substr($related->name, 0, 2)); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($related->quality): ?>
                                            <div class="mepo">
                                                <span class="quality"><?php echo esc_html($related->quality); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <a href="<?php echo esc_url(add_query_arg('channel', $related->slug, get_permalink(76))); ?>">
                                            <div class="see play1"></div>
                                        </a>
                                    </div>

                                    <div class="data">
                                        <h3><a href="<?php echo esc_url(add_query_arg('channel', $related->slug, get_permalink(76))); ?>"><?php echo esc_html($related->name); ?></a></h3>
                                        <span>
                                            <?php if ($related->category_name): ?>
                                                <?php echo esc_html($related->category_name); ?>
                                            <?php endif; ?>
                                            <?php if ($related->country): ?>
                                                <?php if ($related->category_name): ?> • <?php endif; ?>
                                                <?php echo esc_html($related->country); ?>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </article>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="no-related" style="text-align: center; padding: 40px; color: rgba(255,255,255,0.6);">
                    <i class="fas fa-tv" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                    <p>No related channels found.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.tab {
    transition: all 0.3s ease;
}

.tab:hover {
    color: var(--dt-primary-color, #007cba) !important;
}

.tab.active {
    color: var(--dt-primary-color, #007cba) !important;
    border-bottom-color: var(--dt-primary-color, #007cba) !important;
}

/* Player Enhancements */
#player-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

#livetv-video {
    outline: none;
}

#livetv-video::-webkit-media-controls-panel {
    background-color: rgba(0,0,0,0.8);
}

.loading-spinner {
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Error message styling */
#error-message button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* Stream debug info */
#stream-debug {
    font-family: monospace;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* Force cursor visibility on player page */
#player-container,
#player-container *,
#livetv-video,
button,
.control-btn {
    cursor: pointer !important;
}

body {
    cursor: auto !important;
}

/* Responsive player */
@media (max-width: 768px) {
    #livetv-video {
        height: 250px !important;
    }

    #error-message div {
        padding: 15px !important;
    }

    #error-message button {
        display: block;
        width: 100%;
        margin: 5px 0 !important;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple Live TV player loaded');

    var video = document.getElementById('simple-video');
    var errorDiv = document.getElementById('simple-error');

    if (!video) {
        console.log('No video element found');
        return;
    }

    // Get stream URLs
    var sources = video.querySelectorAll('source');
    var streamUrl = sources.length > 0 ? sources[0].src : '';

    console.log('Stream URL:', streamUrl);

    // Initialize HLS if needed
    if (streamUrl.includes('.m3u8') && typeof Hls !== 'undefined' && Hls.isSupported()) {
        console.log('Initializing HLS.js');
        var hls = new Hls({
            enableWorker: false,
            lowLatencyMode: false,
            debug: false
        });

        hls.loadSource(streamUrl);
        hls.attachMedia(video);

        hls.on(Hls.Events.MANIFEST_PARSED, function() {
            console.log('HLS manifest loaded');
        });

        hls.on(Hls.Events.ERROR, function(event, data) {
            console.error('HLS error:', data);
            if (data.fatal) {
                showSimpleError();
            }
        });
    }

    // Video event listeners
    video.addEventListener('error', function(e) {
        console.error('Video error:', e);
        showSimpleError();
    });

    video.addEventListener('loadstart', function() {
        console.log('Video loading started');
    });

    video.addEventListener('canplay', function() {
        console.log('Video can play');
        hideSimpleError();
    });

    video.addEventListener('playing', function() {
        console.log('Video is playing');
        hideSimpleError();
    });

    function showSimpleError() {
        if (errorDiv) {
            errorDiv.style.display = 'flex';
        }
    }

    function hideSimpleError() {
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    // Simple global functions
    window.reloadStream = function() {
        console.log('Reloading stream');
        var video = document.getElementById('simple-video');
        if (video) {
            video.load();
            video.play().catch(function(e) {
                console.log('Play failed:', e);
            });
        }

        var errorDiv = document.getElementById('simple-error');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    };

    window.openStreamDirect = function() {
        var video = document.getElementById('simple-video');
        if (video) {
            var sources = video.querySelectorAll('source');
            if (sources.length > 0) {
                window.open(sources[0].src, '_blank');
            }
        }
    };

    function initNativeHLS() {
        console.log('Initializing native HLS');

        // Clear any existing sources
        video.innerHTML = '';

        // Add HLS source
        var source = document.createElement('source');
        source.src = streamUrl;
        source.type = 'application/vnd.apple.mpegurl';
        video.appendChild(source);

        video.addEventListener('loadstart', function() {
            updateStatus('Loading stream...');
        });

        video.addEventListener('canplay', function() {
            updateStatus('Ready to play');
            hideLoading();
        });

        video.addEventListener('error', function(e) {
            console.error('Native HLS error:', e);
            initFallback();
        });

        video.load();
    }

    function initDirectVideo() {
        console.log('Initializing direct video');

        // Clear any existing sources
        video.innerHTML = '';

        // Add multiple source formats
        var streamType = detectStreamType(streamUrl);

        if (streamType === 'mp4') {
            addVideoSource(streamUrl, 'video/mp4');
        } else if (streamType === 'webm') {
            addVideoSource(streamUrl, 'video/webm');
        } else {
            // Try multiple formats
            addVideoSource(streamUrl, 'video/mp4');
            addVideoSource(streamUrl, 'application/vnd.apple.mpegurl');
            addVideoSource(streamUrl, 'video/webm');
        }

        video.addEventListener('loadstart', function() {
            updateStatus('Loading video...');
        });

        video.addEventListener('canplay', function() {
            updateStatus('Ready to play');
            hideLoading();
        });

        video.addEventListener('error', function(e) {
            console.error('Direct video error:', e);
            initFallback();
        });

        video.load();
    }

    function addVideoSource(src, type) {
        var source = document.createElement('source');
        source.src = src;
        source.type = type;
        video.appendChild(source);
    }

    function initFallback() {
        console.log('Initializing fallback player');

        // Clear any existing sources
        video.innerHTML = '';

        // Try the most compatible approach
        video.src = streamUrl;
        video.preload = 'metadata';

        video.addEventListener('loadstart', function() {
            updateStatus('Loading (fallback)...');
        });

        video.addEventListener('canplay', function() {
            updateStatus('Ready (fallback mode)');
            hideLoading();
        });

        video.addEventListener('error', function(e) {
            console.error('Fallback error:', e);
            tryBackupUrl();
        });

        // Add a timeout for loading
        setTimeout(function() {
            if (video.readyState < 2) { // HAVE_CURRENT_DATA
                console.log('Fallback timeout, trying backup');
                tryBackupUrl();
            }
        }, 15000);

        video.load();
    }

    function tryBackupUrl() {
        if (backupUrl && retryCount < maxRetries) {
            retryCount++;
            console.log('Trying backup URL, attempt:', retryCount);
            updateStatus('Trying backup stream...');

            if (hls) {
                hls.destroy();
                hls = null;
            }

            streamUrl = backupUrl;
            setTimeout(function() {
                initializePlayer();
            }, 1000);
        } else {
            showError('All stream sources failed to load. Please try again later.');
        }
    }

    // Video event listeners
    video.addEventListener('play', function() {
        updateStatus('Playing');
    });

    video.addEventListener('pause', function() {
        updateStatus('Paused');
    });

    video.addEventListener('waiting', function() {
        updateStatus('Buffering...');
    });

    video.addEventListener('playing', function() {
        updateStatus('Playing');
    });

    // Initialize player
    initializePlayer();

    // Global functions for buttons
    window.retryStream = function() {
        console.log('Retrying stream...');
        retryCount = 0;

        if (hls) {
            hls.destroy();
            hls = null;
        }

        if (errorMessage) {
            errorMessage.style.display = 'none';
        }
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }

        // Reset to original URL
        streamUrl = video.getAttribute('data-stream-url');

        setTimeout(function() {
            initializePlayer();
        }, 500);
    };

    window.testStream = function() {
        var url = video.getAttribute('data-stream-url');
        if (url) {
            window.open(url, '_blank');
        }
    };

    window.showFixHistory = function() {
        if (window.StreamFix) {
            var history = window.StreamFix.getFixHistory();
            var historyText = 'Stream Fix History:\n\n';

            if (history.length === 0) {
                historyText += 'No fix attempts recorded.';
            } else {
                history.forEach(function(entry, index) {
                    historyText += `${index + 1}. [${entry.timestamp}] ${entry.type}: ${entry.message}\n`;
                });
            }

            alert(historyText);
        } else {
            alert('Stream Fix utility not available.');
        }
    };

    // Alternative player functions
    window.tryIframePlayer = function() {
        var video = document.getElementById('livetv-video');
        var iframe = document.getElementById('iframe-player');
        var streamUrl = video.getAttribute('data-stream-url');

        if (iframe && streamUrl) {
            video.style.display = 'none';
            iframe.style.display = 'block';
            iframe.src = streamUrl;
        }
    };

    window.openInNewTab = function() {
        var video = document.getElementById('livetv-video');
        var streamUrl = video.getAttribute('data-stream-url');
        if (streamUrl) {
            window.open(streamUrl, '_blank');
        }
    };

    window.tryIframePlayer = function() {
        var video = document.getElementById('livetv-video');
        var iframe = document.getElementById('iframe-player');
        var streamUrl = video.getAttribute('data-stream-url');

        if (iframe && streamUrl) {
            console.log('Trying iframe player');

            // Hide video player
            video.style.display = 'none';

            // Show iframe player
            iframe.style.display = 'block';

            // Create a simple HTML5 player page
            var playerHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <title>Stream Player</title>
                    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
                    <style>
                        body { margin: 0; padding: 0; background: #000; }
                        video { width: 100%; height: 100vh; background: #000; }
                    </style>
                </head>
                <body>
                    <video id="video" controls autoplay muted>
                        <source src="${streamUrl}" type="application/vnd.apple.mpegurl">
                        <source src="${streamUrl}" type="video/mp4">
                    </video>
                    <script>
                        var video = document.getElementById('video');
                        var streamUrl = '${streamUrl}';

                        if (Hls.isSupported()) {
                            var hls = new Hls();
                            hls.loadSource(streamUrl);
                            hls.attachMedia(video);
                        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                            video.src = streamUrl;
                        }

                        video.play().catch(function(e) {
                            console.log('Autoplay failed:', e);
                        });
                    </script>
                </body>
                </html>
            `;

            // Set iframe content
            iframe.srcdoc = playerHTML;
        }
    };

    window.openInNewTab = function() {
        var video = document.getElementById('livetv-video');
        var streamUrl = video.getAttribute('data-stream-url');

        if (streamUrl) {
            window.open(streamUrl, '_blank');
        }
    };

    window.switchBackToVideo = function() {
        var video = document.getElementById('livetv-video');
        var iframe = document.getElementById('iframe-player');

        if (video && iframe) {
            iframe.style.display = 'none';
            video.style.display = 'block';
        }
    };

    // Auto-hide debug info after 10 seconds
    setTimeout(function() {
        if (streamDebug && streamStatus.textContent === 'Playing') {
            streamDebug.style.display = 'none';
        }
    }, 10000);

    // Tab switching
    document.querySelectorAll('.tab').forEach(function(tab) {
        tab.addEventListener('click', function() {
            var tabId = this.getAttribute('data-tab');

            // Remove active class from all tabs and panels
            document.querySelectorAll('.tab').forEach(function(t) {
                t.classList.remove('active');
                t.style.color = 'rgba(255,255,255,0.7)';
                t.style.borderBottomColor = 'transparent';
            });
            document.querySelectorAll('.tab-panel').forEach(function(p) {
                p.classList.remove('active');
                p.style.display = 'none';
            });

            // Add active class to clicked tab and corresponding panel
            this.classList.add('active');
            this.style.color = 'var(--dt-primary-color, #007cba)';
            this.style.borderBottomColor = 'var(--dt-primary-color, #007cba)';
            document.getElementById(tabId + '-tab').style.display = 'block';
            document.getElementById(tabId + '-tab').classList.add('active');
        });
    });
});

// Retry stream function
function retryStream() {
    var video = document.getElementById('livetv-video');
    var loadingOverlay = document.getElementById('loading-overlay');
    var errorMessage = document.getElementById('error-message');
    var streamUrl = video ? video.getAttribute('data-stream-url') : null;

    if (video && loadingOverlay && errorMessage && streamUrl) {
        console.log('Retrying stream...');
        errorMessage.style.display = 'none';
        loadingOverlay.style.display = 'flex';

        // Reload the page to reinitialize everything
        window.location.reload();
    }
}
</script>
