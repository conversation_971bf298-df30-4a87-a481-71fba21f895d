/* Live TV Styles for Dooplay Theme */

/* Live TV Menu Styles */
.live-tv-menu {
    display: inline-block;
    margin: 0;
    padding: 0;
}

.live-tv-menu .menu-item {
    display: inline-block;
    margin: 0;
    padding: 0;
}

.live-tv-menu .menu-item a {
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.live-tv-menu .menu-item a i {
    margin-right: 5px;
}

/* Live TV Item Styles - Following Dooplay Item Structure */
.item.livetv {
    /* Inherits all Dooplay item styles */
}

.item.livetv .poster .no-logo {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2c2c54 0%, #40407a 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: rgba(255,255,255,0.7);
    position: absolute;
    top: 0;
    left: 0;
}

.item.livetv .poster .no-logo i {
    font-size: 2rem;
    margin-bottom: 5px;
}

.item.livetv .poster .no-logo span {
    font-size: 1.2rem;
    font-weight: bold;
    text-transform: uppercase;
}

/* Search Form Styles */
.search-form {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255,255,255,0.05);
    border-radius: 8px;
    border: 1px solid rgba(255,255,255,0.1);
}

.search-form form {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.search-form input[type="text"] {
    flex: 1;
    min-width: 200px;
    padding: 12px 15px;
    border: 1px solid rgba(255,255,255,0.2);
    background: rgba(255,255,255,0.1);
    color: #fff;
    border-radius: 6px;
    font-size: 14px;
}

.search-form input[type="text"]::placeholder {
    color: rgba(255,255,255,0.6);
}

.search-form select {
    padding: 12px 15px;
    border: 1px solid rgba(255,255,255,0.2);
    background: rgba(255,255,255,0.1);
    color: #fff;
    border-radius: 6px;
    font-size: 14px;
    min-width: 150px;
}

.search-form button {
    padding: 12px 20px;
    background: var(--dt-primary-color, #007cba);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-form button:hover {
    background: var(--dt-primary-hover, #005a87);
}

.search-form .clear-filters {
    padding: 12px 20px;
    background: #dc3545;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-form .clear-filters:hover {
    background: #c82333;
}

/* No Results Styles */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: rgba(255,255,255,0.7);
}

.no-results i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: rgba(255,255,255,0.3);
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #ffffff;
}

.no-results p {
    margin-bottom: 20px;
    font-size: 16px;
}

.no-results .button {
    padding: 12px 24px;
    background: var(--dt-primary-color, #007cba);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.no-results .button:hover {
    background: var(--dt-primary-hover, #005a87);
}
.live-tv-container {
    width: 100%;
    float: left;
    margin: 0;
    padding: 0;
}

.live-tv-module {
    float: left;
    width: 100%;
    min-height: 812px;
    margin-top: 0;
    padding: 0;
    position: relative;
}

.live-tv-content {
    width: 100%;
    padding: 20px 40px;
    float: left;
}

/* Live TV Header - Following Dooplay Module Header Style */
.live-tv-header {
    float: left;
    width: 100%;
    padding: 15px 10px;
    line-height: 20px;
    border-bottom: solid 1px;
    margin-bottom: 25px;
}

.live-tv-header h1 {
    float: left;
    font-size: 20px;
    font-weight: 500;
    padding-left: 10px;
    margin: 0;
}

.live-tv-header .header-actions {
    float: right;
    font-weight: 500;
}

.live-tv-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.live-tv-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Filters and Search */
.live-tv-filters {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.filter-form {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 10px 45px 10px 20px;
    color: #ffffff;
    font-size: 14px;
    width: 250px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #007cba;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.2);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-btn {
    position: absolute;
    right: 5px;
    background: #007cba;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    color: white;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-btn:hover {
    background: #005a87;
}

.category-filter select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 10px 15px;
    color: #ffffff;
    font-size: 14px;
    min-width: 150px;
}

.clear-filters {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid #dc3545;
    border-radius: 20px;
    padding: 8px 15px;
    color: #dc3545;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.clear-filters:hover {
    background: #dc3545;
    color: white;
}

/* Category Tabs */
.category-tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.category-tab {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 8px 16px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.category-tab:hover,
.category-tab.active {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    transform: translateY(-2px);
}

/* Live TV Items - Following Dooplay Items Structure */
.live-tv-items {
    width: 100%;
    float: left;
    margin-bottom: 25px;
    border-bottom: solid 1px;
}

.live-tv-items:last-child {
    border-bottom: 0;
}

/* Channel Grid - Following Dooplay Item Structure */
.live-tv-items .item {
    width: calc(100% / 5);
    float: left;
    margin: 0;
    padding: 10px;
    position: relative;
}

.live-tv-items.full .item {
    width: calc(100% / 6);
}

/* Channel Poster - Following Dooplay Poster Structure */
.live-tv-items .item .poster {
    width: 100%;
    height: 100%;
    float: left;
    position: relative;
    overflow: hidden;
    margin: 0;
    padding-top: 140%;
}

.live-tv-items .item .poster a .see {
    background: url(../img/play1.svg) 50% 50% no-repeat;
    background-size: 40% 40%;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    -webkit-transition: all .65s ease-in-out;
    transition: all .65s ease-in-out;
    opacity: 0;
}

.live-tv-items .item .poster a:hover > .see {
    -webkit-transform: scale(.75);
    transform: scale(.75);
    opacity: 1;
}

/* Channel Image */
.live-tv-items .item .poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    transition: all 0.3s ease;
}

/* Channel Data - Following Dooplay Data Structure */
.live-tv-items .item .data {
    width: 100%;
    float: left;
    padding: 10px 0 0 0;
    text-align: center;
}

.live-tv-items .item .data h3 {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 5px 0;
    line-height: 18px;
}

.live-tv-items .item .data h3 a {
    text-decoration: none;
    transition: all 0.3s ease;
}

.live-tv-items .item .data .metadata {
    font-size: 12px;
    opacity: 0.7;
    margin: 0;
}

/* Quality Badge */
.live-tv-items .item .poster .quality {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    border-radius: 3px;
    text-transform: uppercase;
}

.live-tv-items .item .poster .quality.hd {
    background: #28a745;
    color: white;
}

.live-tv-items .item .poster .quality.sd {
    background: #ffc107;
    color: #212529;
}

.live-tv-items .item .poster .quality.fhd {
    background: #007bff;
    color: white;
}

.live-tv-items .item .poster .quality.uhd {
    background: #6f42c1;
    color: white;
}

/* Responsive Design - Following Dooplay Responsive Structure */
@media (max-width: 1200px) {
    .live-tv-items .item {
        width: calc(100% / 4);
    }

    .live-tv-items.full .item {
        width: calc(100% / 5);
    }
}

@media (max-width: 992px) {
    .live-tv-items .item {
        width: calc(100% / 3);
    }

    .live-tv-items.full .item {
        width: calc(100% / 4);
    }

    .live-tv-content {
        padding: 20px;
    }
}

@media (max-width: 768px) {
    .live-tv-items .item {
        width: calc(100% / 2);
    }

    .live-tv-items.full .item {
        width: calc(100% / 3);
    }

    .live-tv-header h1 {
        font-size: 18px;
    }

    .live-tv-header .header-actions {
        float: left;
        width: 100%;
        margin-top: 10px;
    }
}

@media (max-width: 480px) {
    .live-tv-items .item {
        width: calc(100% / 2);
    }

    .live-tv-items.full .item {
        width: calc(100% / 2);
    }

    .live-tv-content {
        padding: 15px;
    }
}

.channel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border-color: #007cba;
}

.channel-link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.channel-poster {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.channel-logo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.channel-card:hover .channel-logo {
    transform: scale(1.05);
}

.channel-logo-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #2c2c54 0%, #40407a 100%);
    color: rgba(255, 255, 255, 0.7);
}

.channel-logo-placeholder i {
    font-size: 2rem;
    margin-bottom: 5px;
}

.channel-logo-placeholder span {
    font-size: 1.2rem;
    font-weight: bold;
}

.channel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.channel-card:hover .channel-overlay {
    opacity: 1;
}

.play-button {
    background: #007cba;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: #005a87;
    transform: scale(1.1);
}

.quality-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.quality-badge.quality-hd {
    background: #28a745;
    color: white;
}

.quality-badge.quality-sd {
    background: #ffc107;
    color: #212529;
}

.quality-badge.quality-fhd {
    background: #007bff;
    color: white;
}

.quality-badge.quality-4k {
    background: #6f42c1;
    color: white;
}

.viewer-count {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    color: white;
    display: flex;
    align-items: center;
    gap: 4px;
}

.channel-info {
    padding: 15px;
}

.channel-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #ffffff;
    line-height: 1.3;
}

.channel-category,
.channel-country,
.channel-language {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.channel-category {
    font-weight: 500;
}

.favorite-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.favorite-btn:hover,
.favorite-btn.favorited {
    background: #dc3545;
    transform: scale(1.1);
}

/* No Channels State */
.no-channels {
    text-align: center;
    padding: 60px 20px;
    color: rgba(255, 255, 255, 0.7);
}

.no-channels-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.3);
}

.no-channels h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #ffffff;
}

.no-channels p {
    font-size: 1rem;
    margin-bottom: 20px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Pagination */
.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 20px;
    flex-wrap: wrap;
    gap: 20px;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 5px;
}

.page-link {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    min-width: 40px;
    text-align: center;
}

.page-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

.page-link.current {
    background: #007cba;
    border-color: #007cba;
    color: white;
}

.page-dots {
    color: rgba(255, 255, 255, 0.5);
    padding: 0 5px;
}

.pagination-info {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #007cba;
    color: white;
}

.btn-primary:hover {
    background: #005a87;
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .live-tv-title {
        font-size: 2rem;
    }
    
    .live-tv-filters {
        flex-direction: column;
        gap: 15px;
    }
    
    .filter-form {
        flex-direction: column;
        width: 100%;
    }
    
    .search-input {
        width: 100%;
        max-width: 300px;
    }
    
    .category-tabs {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
    }
    
    .channels-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        padding: 30px 0;
    }
    
    .pagination-wrapper {
        flex-direction: column;
        text-align: center;
    }
    
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .channels-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .channel-card {
        max-width: 400px;
        margin: 0 auto;
    }
}

/* Single Channel Page Styles */
#live-tv-single .breadcrumb {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: #007cba;
}

.breadcrumb .separator {
    margin: 0 10px;
    color: rgba(255, 255, 255, 0.5);
}

.breadcrumb .current {
    color: #ffffff;
    font-weight: 500;
}

.live-tv-player-section {
    padding: 30px 0;
}

.player-wrapper {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.video-player {
    position: relative;
    aspect-ratio: 16/9;
    background: #000;
    overflow: hidden;
}

.player-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#video-element {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.player-controls-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-player:hover .player-controls-overlay {
    opacity: 1;
}

.control-buttons {
    display: flex;
    align-items: center;
    gap: 15px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.quality-selector {
    position: relative;
}

.quality-menu {
    position: absolute;
    bottom: 50px;
    left: 0;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 6px;
    padding: 10px;
    min-width: 150px;
    display: none;
}

.quality-option {
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: background 0.3s ease;
    font-size: 14px;
}

.quality-option:hover,
.quality-option.active {
    background: rgba(255, 255, 255, 0.2);
}

.player-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
}

.error-content {
    text-align: center;
    color: white;
    max-width: 400px;
    padding: 20px;
}

.error-content i {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 20px;
}

.error-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.error-content p {
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.8);
}

.channel-info-bar {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
    flex-wrap: wrap;
}

.channel-details {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    flex: 1;
}

.channel-logo-small {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.channel-meta {
    flex: 1;
}

.channel-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #ffffff;
}

.channel-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.tag {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.category-tag {
    color: white;
}

.quality-tag {
    color: white;
}

.country-tag,
.language-tag {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}

.channel-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    margin: 0;
}

.channel-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

.viewer-stats {
    display: flex;
    align-items: center;
    gap: 5px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.related-channels {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.related-channels h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #ffffff;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.related-channel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.related-channel:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.related-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    text-decoration: none;
    color: inherit;
}

.related-logo {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
}

.related-logo-placeholder {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.6);
}

.related-info h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #ffffff;
}

.related-country {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

/* Single Page Responsive */
@media (max-width: 768px) {
    .channel-info-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .channel-details {
        flex-direction: column;
        text-align: center;
    }

    .channel-actions {
        align-items: center;
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
    }

    .related-grid {
        grid-template-columns: 1fr;
    }

    .control-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* Notifications */
.livetv-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.livetv-notification.show {
    transform: translateX(0);
}

.livetv-notification-success {
    border-left: 4px solid #28a745;
}

.livetv-notification-error {
    border-left: 4px solid #dc3545;
}

.livetv-notification-info {
    border-left: 4px solid #007cba;
}

.livetv-notification-warning {
    border-left: 4px solid #ffc107;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9998;
}

.loading-overlay .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

.justify-content-center {
    justify-content: center;
}

.align-items-center {
    align-items: center;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: 0.5rem !important;
}

.mb-2 {
    margin-bottom: 1rem !important;
}

.mb-3 {
    margin-bottom: 1.5rem !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.mt-1 {
    margin-top: 0.5rem !important;
}

.mt-2 {
    margin-top: 1rem !important;
}

.mt-3 {
    margin-top: 1.5rem !important;
}

/* Android TV Specific Styles */
/* Focus Management for Android TV Remote Control */
.tv-focused {
    outline: 3px solid #007cba !important;
    outline-offset: 2px;
    box-shadow: 0 0 0 5px rgba(0, 124, 186, 0.3) !important;
    transform: scale(1.05) !important;
    transition: all 0.2s ease !important;
    z-index: 100 !important;
    position: relative !important;
}

/* TV-Optimized Item Focus */
.item.tv-focused {
    transform: scale(1.08) !important;
    box-shadow: 0 8px 25px rgba(0, 124, 186, 0.4) !important;
}

.item.tv-focused .poster {
    border: 3px solid #007cba !important;
    box-shadow: 0 0 20px rgba(0, 124, 186, 0.6) !important;
}

/* TV-Optimized Button Focus */
.btn.tv-focused,
.search-form button.tv-focused,
.page-link.tv-focused {
    background: #007cba !important;
    color: white !important;
    transform: scale(1.1) !important;
    box-shadow: 0 4px 15px rgba(0, 124, 186, 0.5) !important;
}

/* TV-Optimized Input Focus */
.search-form input.tv-focused,
.search-form select.tv-focused {
    border-color: #007cba !important;
    background: rgba(0, 124, 186, 0.2) !important;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.3) !important;
}

/* Android TV Detection and Optimization */
@media (pointer: coarse) and (hover: none) {
    /* Detected Android TV or similar device */

    /* Larger touch targets for remote control */
    .item {
        padding: 15px !important;
    }

    .search-form input,
    .search-form select,
    .search-form button {
        padding: 15px 20px !important;
        font-size: 16px !important;
        min-height: 50px !important;
    }

    .page-link {
        padding: 12px 16px !important;
        font-size: 16px !important;
        min-width: 50px !important;
        min-height: 50px !important;
    }

    .btn {
        padding: 15px 25px !important;
        font-size: 16px !important;
        min-height: 50px !important;
    }

    /* Better contrast for TV viewing */
    .item .data h3 {
        font-size: 16px !important;
        font-weight: 600 !important;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
    }

    .item .data span {
        font-size: 14px !important;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
    }

    /* Simplified hover effects for TV */
    .item:hover {
        transform: scale(1.05) !important;
    }

    .item:hover .poster {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4) !important;
    }
}

/* TV-Specific Layout Adjustments */
@media screen and (min-width: 1920px) {
    /* 4K TV optimization */
    .live-tv-content {
        padding: 40px 60px !important;
    }

    .item {
        padding: 20px !important;
    }

    .item .data h3 {
        font-size: 18px !important;
    }

    .item .data span {
        font-size: 16px !important;
    }

    .search-form input,
    .search-form select,
    .search-form button {
        font-size: 18px !important;
        padding: 18px 25px !important;
    }
}

/* Android TV Remote Control Hints */
.tv-controls-hint {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 1000;
    display: none;
}

.tv-controls-hint.show {
    display: block;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* TV-Optimized Video Player */
.video-player.tv-mode {
    border: 3px solid transparent;
    transition: border-color 0.3s ease;
}

.video-player.tv-mode.tv-focused {
    border-color: #007cba;
    box-shadow: 0 0 20px rgba(0, 124, 186, 0.5);
}

.video-player .player-controls-overlay.tv-mode {
    opacity: 1;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
}

.control-btn.tv-focused {
    background: #007cba !important;
    transform: scale(1.2) !important;
    box-shadow: 0 4px 15px rgba(0, 124, 186, 0.5) !important;
}

/* TV-Optimized Grid Layout */
@media (min-width: 1200px) {
    .live-tv-items .item {
        width: calc(100% / 6) !important; /* More items per row on TV */
    }

    .live-tv-items.full .item {
        width: calc(100% / 8) !important;
    }
}

/* TV-Safe Area (for older TVs with overscan) */
@media screen and (min-width: 1920px) {
    .live-tv-content {
        margin: 2% 5% !important; /* TV-safe margins */
    }
}

/* High Contrast Mode for Better TV Visibility */
.tv-high-contrast {
    filter: contrast(1.2) brightness(1.1);
}

.tv-high-contrast .item .poster {
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.tv-high-contrast .item .data h3 {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
}

/* Loading States for TV */
.tv-loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    z-index: 9999;
}

.tv-loading .loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.tv-loading-text {
    font-size: 18px;
    font-weight: 500;
}

/* TV Notification System */
.tv-notification {
    position: fixed;
    top: 30px;
    right: 30px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 25px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    border: 2px solid #007cba;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.tv-notification.show {
    transform: translateX(0);
}

/* Android TV Body Modifications */
.android-tv-mode {
    /* Only hide cursor on actual Android TV devices, not desktop */
}

/* Show cursor on desktop/laptop */
@media (pointer: fine) {
    .android-tv-mode {
        cursor: auto !important;
    }

    .android-tv-mode * {
        cursor: auto !important;
    }
}

/* Hide cursor only on actual TV devices */
@media (pointer: coarse) and (hover: none) {
    .android-tv-mode {
        cursor: none;
    }
}

.android-tv-mode * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* TV-Optimized Scrollbar */
.android-tv-mode ::-webkit-scrollbar {
    width: 12px;
}

.android-tv-mode ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.android-tv-mode ::-webkit-scrollbar-thumb {
    background: #007cba;
    border-radius: 6px;
}

.android-tv-mode ::-webkit-scrollbar-thumb:hover {
    background: #005a87;
}

/* TV-Specific Animation Optimizations */
.android-tv-mode .item {
    will-change: transform;
    backface-visibility: hidden;
}

.android-tv-mode .tv-focused {
    will-change: transform, box-shadow;
}

/* Reduce motion for better TV performance */
@media (prefers-reduced-motion: reduce) {
    .android-tv-mode * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* TV-Safe Colors (better contrast) */
.android-tv-mode .item .data h3 a {
    color: #ffffff !important;
}

.android-tv-mode .item .data span {
    color: rgba(255, 255, 255, 0.9) !important;
}

.android-tv-mode .search-form input::placeholder {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* TV Remote Control Visual Feedback */
.tv-button-press {
    animation: buttonPress 0.1s ease;
}

@keyframes buttonPress {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* TV Loading Overlay */
.android-tv-mode .tv-loading {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* TV-Optimized Pagination */
.android-tv-mode .pagination {
    gap: 10px;
}

.android-tv-mode .page-link {
    min-width: 60px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* TV-Optimized Search Form */
.android-tv-mode .search-form {
    padding: 30px;
    border-radius: 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.android-tv-mode .search-form form {
    gap: 20px;
}

/* TV-Optimized Category Tabs */
.android-tv-mode .category-tab {
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 500;
    min-height: 50px;
    display: flex;
    align-items: center;
}

/* TV-Optimized Quality Badges */
.android-tv-mode .quality-badge {
    font-size: 14px;
    padding: 6px 12px;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* TV-Optimized Viewer Count */
.android-tv-mode .viewer-count {
    font-size: 14px;
    padding: 6px 12px;
    font-weight: 600;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* TV Error States */
.android-tv-mode .no-results {
    padding: 80px 40px;
}

.android-tv-mode .no-results i {
    font-size: 5rem;
}

.android-tv-mode .no-results h3 {
    font-size: 2rem;
    margin-bottom: 20px;
}

.android-tv-mode .no-results p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* TV-Optimized Header */
.android-tv-mode header h2 {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
}

.android-tv-mode header span {
    font-size: 1.2rem !important;
    font-weight: 500 !important;
}

/* TV Focus Ring Animation */
@keyframes focusRing {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 124, 186, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 124, 186, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 124, 186, 0);
    }
}

.android-tv-mode .tv-focused {
    animation: focusRing 2s infinite;
}

/* TV-Optimized Player Styles */
.android-tv-player {
    padding: 0;
    margin: 0;
}

.tv-safe-area {
    margin: 2% 5%; /* TV-safe margins for older TVs */
}

.player-section {
    margin-bottom: 40px;
}

.player-wrapper.tv-focusable:focus,
.player-wrapper.tv-focused {
    outline: 4px solid #007cba;
    outline-offset: 4px;
}

.tv-video {
    width: 100%;
    height: 100%;
    background: #000;
}

.channel-logo-small {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 12px;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.channel-logo-placeholder-small {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 2rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.tv-control-hints {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    background: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.android-tv-mode .tv-control-hints {
    opacity: 1;
}

.tv-control-hints span {
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.tv-control-hints i {
    width: 16px;
    text-align: center;
}

/* TV-Optimized Related Channels */
.android-tv-mode .related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.android-tv-mode .related-channel {
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.android-tv-mode .related-channel.tv-focused,
.android-tv-mode .related-link.tv-focused {
    border-color: #007cba;
    background: rgba(0, 124, 186, 0.2);
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 124, 186, 0.4);
}

.android-tv-mode .related-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.android-tv-mode .related-logo {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.android-tv-mode .related-logo-placeholder {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.android-tv-mode .related-info h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 6px;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.android-tv-mode .related-country {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* TV-Optimized Breadcrumb */
.android-tv-mode .breadcrumb {
    background: rgba(255, 255, 255, 0.08);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    font-size: 16px;
}

.android-tv-mode .breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.android-tv-mode .breadcrumb a.tv-focused {
    background: #007cba;
    color: white;
    transform: scale(1.05);
}

.android-tv-mode .breadcrumb .separator {
    margin: 0 15px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 18px;
}

.android-tv-mode .breadcrumb .current {
    color: #ffffff;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* TV-Optimized Channel Actions */
.android-tv-mode .channel-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 15px;
}

.android-tv-mode .channel-actions .btn {
    min-width: 140px;
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.android-tv-mode .channel-actions .btn.tv-focused {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 124, 186, 0.5);
}

.android-tv-mode .viewer-stats {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 20px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

/* TV-Optimized Channel Tags */
.android-tv-mode .channel-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 15px;
}

.android-tv-mode .tag {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.android-tv-mode .category-tag {
    color: white;
    background: linear-gradient(135deg, #007cba, #005a87);
}

.android-tv-mode .quality-tag {
    color: white;
}

.android-tv-mode .quality-tag.quality-hd {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.android-tv-mode .quality-tag.quality-fhd {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.android-tv-mode .quality-tag.quality-4k {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
}

.android-tv-mode .country-tag,
.android-tv-mode .language-tag {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.4);
}

/* TV-Optimized Channel Title */
.android-tv-mode .channel-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    line-height: 1.2;
}

.android-tv-mode .channel-description {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0;
    font-size: 16px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    max-width: 600px;
}

/* TV-Optimized Channel Info Bar */
.android-tv-mode .channel-info-bar {
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 30px;
    flex-wrap: wrap;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 40px;
}

.android-tv-mode .channel-details {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    flex: 1;
}

/* TV-Optimized Related Channels Header */
.android-tv-mode .related-channels h3 {
    font-size: 1.8rem;
    margin-bottom: 25px;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    font-weight: 700;
}

/* TV Performance Optimizations */
.android-tv-mode * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.android-tv-mode .tv-focused {
    will-change: transform, box-shadow;
    transform-origin: center;
}

/* TV-Specific Media Queries */
@media screen and (min-width: 1920px) {
    .android-tv-mode .channel-title {
        font-size: 2.5rem;
    }

    .android-tv-mode .related-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 25px;
    }

    .android-tv-mode .channel-info-bar {
        padding: 40px;
    }

    .android-tv-mode .tv-safe-area {
        margin: 3% 8%;
    }
}

/* TV Settings Panel */
.tv-settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.tv-settings-panel {
    background: rgba(20, 20, 20, 0.95);
    border: 3px solid #007cba;
    border-radius: 20px;
    padding: 40px;
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
}

.tv-settings-panel h2 {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.tv-settings-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.setting-group {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.setting-group h3 {
    color: #007cba;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.setting-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    padding: 8px 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.setting-toggle.enabled {
    background: #007cba;
    border-color: #007cba;
    color: white;
}

.setting-toggle.tv-focused {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 124, 186, 0.5);
}

.volume-slider {
    display: flex;
    align-items: center;
    gap: 15px;
}

.volume-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.volume-btn.tv-focused {
    background: #007cba;
    border-color: #007cba;
    color: white;
    transform: scale(1.2);
}

.volume-display {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    min-width: 50px;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.speed-selector {
    display: flex;
    gap: 10px;
}

.speed-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 8px 16px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.speed-btn.active,
.speed-btn.tv-focused {
    background: #007cba;
    border-color: #007cba;
    color: white;
    transform: scale(1.05);
}

.settings-footer {
    margin-top: 30px;
    text-align: center;
    padding-top: 20px;
    border-top: 2px solid rgba(255, 255, 255, 0.1);
}

.settings-footer .btn {
    min-width: 150px;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
}

/* TV Large Text Mode */
.tv-large-text {
    font-size: 120% !important;
}

.tv-large-text .item .data h3 {
    font-size: 18px !important;
}

.tv-large-text .item .data span {
    font-size: 16px !important;
}

.tv-large-text .search-form input,
.tv-large-text .search-form select,
.tv-large-text .search-form button {
    font-size: 18px !important;
    padding: 18px 25px !important;
}

.tv-large-text .page-link {
    font-size: 18px !important;
    padding: 15px 20px !important;
}

/* TV Reduced Motion Mode */
.tv-reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}

.tv-reduced-motion .tv-focused {
    animation: none !important;
}

/* TV Navigation Speed Modes */
.tv-nav-slow .tv-focused {
    transition-duration: 0.5s !important;
}

.tv-nav-normal .tv-focused {
    transition-duration: 0.3s !important;
}

.tv-nav-fast .tv-focused {
    transition-duration: 0.1s !important;
}

/* TV Settings Responsive */
@media screen and (max-width: 768px) {
    .tv-settings-panel {
        padding: 20px;
        width: 95%;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .volume-slider,
    .speed-selector {
        width: 100%;
        justify-content: space-between;
    }
}

/* TV Player Controls */
.tv-player-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.8) 0%,
        transparent 20%,
        transparent 80%,
        rgba(0, 0, 0, 0.9) 100%
    );
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 30px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.tv-player-controls.visible {
    opacity: 1;
    pointer-events: all;
}

.tv-fullscreen-mode .tv-player-controls {
    opacity: 1;
}

.controls-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.channel-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.channel-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.live-indicator {
    font-size: 14px;
    font-weight: 600;
    color: #ff4444;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.player-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

.quality-indicator,
.volume-indicator {
    background: rgba(0, 0, 0, 0.7);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.controls-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn-large {
    background: rgba(0, 0, 0, 0.8);
    border: 3px solid #007cba;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    color: white;
    font-size: 2.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

.control-btn-large:hover,
.control-btn-large.tv-focused {
    background: #007cba;
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(0, 124, 186, 0.6);
}

.control-btn-large.tv-control-focused {
    animation: controlPulse 0.2s ease;
}

@keyframes controlPulse {
    0% { transform: scale(1.1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1.1); }
}

.controls-bottom {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.control-row {
    display: flex;
    align-items: center;
    gap: 20px;
    justify-content: center;
}

.control-btn {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover,
.control-btn.tv-focused {
    background: #007cba;
    border-color: #007cba;
    transform: scale(1.15);
    box-shadow: 0 6px 20px rgba(0, 124, 186, 0.5);
}

.control-btn.tv-control-focused {
    animation: controlPulse 0.2s ease;
}

.volume-bar {
    width: 150px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.volume-fill {
    height: 100%;
    background: linear-gradient(90deg, #007cba, #00a8e6);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.control-hints {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.control-hints span {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    background: rgba(0, 0, 0, 0.6);
    padding: 5px 12px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* Quality Menu */
.quality-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.quality-menu {
    background: rgba(20, 20, 20, 0.95);
    border: 3px solid #007cba;
    border-radius: 15px;
    padding: 30px;
    min-width: 300px;
    text-align: center;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.7);
}

.quality-menu h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.quality-option {
    display: block;
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 12px 20px;
    margin-bottom: 10px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.quality-option:last-child {
    margin-bottom: 0;
}

.quality-option.active,
.quality-option.tv-focused {
    background: #007cba;
    border-color: #007cba;
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 124, 186, 0.5);
}

/* Fullscreen Mode Styles */
.tv-fullscreen-mode {
    overflow: hidden;
}

.tv-fullscreen-mode .video-player {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: #000 !important;
}

.tv-fullscreen-mode .tv-video {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
}

/* TV Player Responsive */
@media screen and (max-width: 768px) {
    .tv-player-controls {
        padding: 20px;
    }

    .control-btn-large {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .control-row {
        gap: 15px;
    }

    .control-hints {
        gap: 15px;
    }

    .control-hints span {
        font-size: 12px;
        padding: 4px 8px;
    }

    .channel-name {
        font-size: 1.2rem;
    }

    .volume-bar {
        width: 100px;
    }
}

/* TV Player Loading States */
.tv-player-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    text-align: center;
    z-index: 100;
}

.tv-player-loading .loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.tv-player-loading-text {
    font-size: 18px;
    font-weight: 500;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* TV Performance Optimizations */
.android-tv-mode {
    /* Enable hardware acceleration */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: auto;
}

.android-tv-mode * {
    /* Optimize rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeSpeed;
}

.android-tv-mode .item {
    /* Contain layout and paint for better performance */
    contain: layout style paint;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

.android-tv-mode .tv-focused {
    /* Optimize focus animations */
    will-change: transform, box-shadow, opacity;
    transform-origin: center;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.android-tv-mode .tv-disabled {
    /* Disable animations for off-screen elements */
    animation: none !important;
    transition: none !important;
    will-change: auto !important;
}

/* Image Loading Optimizations */
.android-tv-mode img {
    /* Optimize image rendering */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

.android-tv-mode img.loading {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.android-tv-mode img.loaded {
    opacity: 1;
}

.android-tv-mode img.error {
    opacity: 0.5;
    filter: grayscale(100%);
}

/* Lazy Loading Placeholder */
.android-tv-mode .lazy-placeholder {
    background: linear-gradient(135deg, #2c2c54 0%, #40407a 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.3);
    font-size: 2rem;
    min-height: 200px;
}

.android-tv-mode .lazy-placeholder::before {
    content: "📺";
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

/* Memory Optimization */
.android-tv-mode .out-of-viewport {
    /* Reduce memory usage for off-screen elements */
    visibility: hidden;
    pointer-events: none;
}

.android-tv-mode .in-viewport {
    visibility: visible;
    pointer-events: auto;
}

/* GPU Acceleration for Smooth Scrolling */
.android-tv-mode .items {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    -webkit-overflow-scrolling: touch;
}

/* Optimize Transitions for TV Hardware */
.android-tv-mode .tv-focused {
    transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.2s ease;
}

/* Reduce Motion for Performance */
@media (prefers-reduced-motion: reduce) {
    .android-tv-mode * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .android-tv-mode .tv-focused {
        animation: none !important;
    }
}

/* Low-End Device Optimizations */
@media (max-device-memory: 2GB) {
    .android-tv-mode .item {
        /* Reduce visual effects on low-memory devices */
        box-shadow: none !important;
    }

    .android-tv-mode .tv-focused {
        /* Simpler focus effect */
        outline: 3px solid #007cba !important;
        box-shadow: none !important;
        transform: none !important;
    }

    .android-tv-mode img {
        /* Reduce image quality */
        image-rendering: pixelated;
    }
}

/* High-End Device Enhancements */
@media (min-device-memory: 8GB) {
    .android-tv-mode .tv-focused {
        /* Enhanced effects for high-end devices */
        filter: brightness(1.1) contrast(1.05);
    }

    .android-tv-mode .item:hover {
        /* Smooth hover effects */
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
}

/* Network-Aware Loading */
@media (prefers-reduced-data: reduce) {
    .android-tv-mode img {
        /* Reduce image quality for slow connections */
        image-rendering: pixelated;
    }

    .android-tv-mode .lazy-placeholder {
        /* Show placeholder longer on slow connections */
        min-height: 150px;
    }
}

/* TV-Specific Viewport Optimizations */
@media screen and (min-width: 1920px) {
    .android-tv-mode {
        /* 4K TV optimizations */
        image-rendering: -webkit-optimize-contrast;
    }

    .android-tv-mode .item {
        /* Better spacing for large screens */
        margin: 0.5%;
    }
}

/* Focus Ring Performance */
.android-tv-mode .tv-focused::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 3px solid #007cba;
    border-radius: inherit;
    pointer-events: none;
    z-index: 1;
    animation: focusRingOptimized 2s infinite;
}

@keyframes focusRingOptimized {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 124, 186, 0.7);
        transform: scale(1);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 124, 186, 0);
        transform: scale(1.02);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 124, 186, 0);
        transform: scale(1);
    }
}

/* Scroll Performance */
.android-tv-mode .items {
    /* Optimize scrolling performance */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
}

/* Text Rendering Optimization */
.android-tv-mode {
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: subpixel-antialiased;
    -moz-osx-font-smoothing: auto;
}

/* Critical Resource Hints */
.android-tv-mode img[data-src] {
    /* Prepare for lazy loading */
    content-visibility: auto;
    contain-intrinsic-size: 300px 200px;
}

/* Intersection Observer Optimizations */
.android-tv-mode .item {
    /* Optimize intersection calculations */
    contain: layout size;
}

/* Memory-Efficient Animations */
.android-tv-mode .tv-focused {
    /* Use transform instead of changing layout properties */
    transform: scale(1.05) translateZ(0);
    -webkit-transform: scale(1.05) translateZ(0);
}

/* Efficient Shadow Effects */
.android-tv-mode .tv-focused {
    /* Use box-shadow instead of filter for better performance */
    box-shadow: 0 8px 25px rgba(0, 124, 186, 0.4),
                0 0 0 3px rgba(0, 124, 186, 0.3);
}

/* Reduce Paint Complexity */
.android-tv-mode .item .poster {
    /* Simplify complex backgrounds */
    background-attachment: local;
    background-size: cover;
    background-repeat: no-repeat;
}
