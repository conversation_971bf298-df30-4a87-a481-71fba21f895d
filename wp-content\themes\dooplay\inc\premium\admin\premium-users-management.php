<?php
/**
 * Premium Users Management
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Premium_Users_Management {
    
    /**
     * Render users management page
     */
    public static function render_users_page() {
        // Handle actions
        if (isset($_POST['action']) && wp_verify_nonce($_POST['users_nonce'], 'premium_users_action')) {
            self::handle_user_actions();
        }
        
        ?>
        <div class="wrap">
            <h1><?php _e('Premium Users Management', 'deshiflix'); ?></h1>
            
            <!-- Users Stats -->
            <div class="premium-users-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                
                <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 24px;"><?php echo self::get_active_users_count(); ?></h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;"><?php _e('Active Users', 'deshiflix'); ?></p>
                </div>
                
                <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 24px;"><?php echo self::get_expired_users_count(); ?></h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;"><?php _e('Expired Users', 'deshiflix'); ?></p>
                </div>
                
                <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 24px;"><?php echo self::get_trial_users_count(); ?></h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;"><?php _e('Trial Users', 'deshiflix'); ?></p>
                </div>
                
                <div class="stat-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <h3 style="margin: 0; font-size: 24px;">৳<?php echo number_format(self::get_total_revenue()); ?></h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;"><?php _e('Total Revenue', 'deshiflix'); ?></p>
                </div>
                
            </div>
            
            <!-- Add New Premium User -->
            <div class="card" style="margin-bottom: 20px;">
                <h2><?php _e('Add New Premium User', 'deshiflix'); ?></h2>
                <form method="post" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; align-items: end;">
                    <?php wp_nonce_field('premium_users_action', 'users_nonce'); ?>
                    <input type="hidden" name="action" value="add_user">
                    
                    <div>
                        <label><strong><?php _e('Username/Email', 'deshiflix'); ?></strong></label>
                        <input type="text" name="user_identifier" placeholder="username or email" required style="width: 100%;">
                    </div>
                    
                    <div>
                        <label><strong><?php _e('Plan Duration (Days)', 'deshiflix'); ?></strong></label>
                        <select name="plan_duration" style="width: 100%;">
                            <option value="30">30 Days</option>
                            <option value="90">90 Days</option>
                            <option value="365">365 Days</option>
                            <option value="custom">Custom</option>
                        </select>
                    </div>
                    
                    <div>
                        <label><strong><?php _e('Custom Days', 'deshiflix'); ?></strong></label>
                        <input type="number" name="custom_days" placeholder="Custom days" style="width: 100%;">
                    </div>
                    
                    <div>
                        <label>&nbsp;</label>
                        <button type="submit" class="button button-primary" style="width: 100%;">
                            <?php _e('Add Premium Access', 'deshiflix'); ?>
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Premium Users List -->
            <div class="card">
                <h2><?php _e('Premium Users List', 'deshiflix'); ?></h2>
                
                <!-- Search and Filter -->
                <div style="margin-bottom: 20px; display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                    <input type="text" id="user-search" placeholder="<?php _e('Search users...', 'deshiflix'); ?>" style="flex: 1; min-width: 200px;">
                    <select id="status-filter">
                        <option value=""><?php _e('All Status', 'deshiflix'); ?></option>
                        <option value="active"><?php _e('Active', 'deshiflix'); ?></option>
                        <option value="expired"><?php _e('Expired', 'deshiflix'); ?></option>
                        <option value="trial"><?php _e('Trial', 'deshiflix'); ?></option>
                    </select>
                    <button type="button" class="button" onclick="filterUsers()"><?php _e('Filter', 'deshiflix'); ?></button>
                </div>
                
                <table class="widefat" id="premium-users-table">
                    <thead>
                        <tr>
                            <th><?php _e('User', 'deshiflix'); ?></th>
                            <th><?php _e('Email', 'deshiflix'); ?></th>
                            <th><?php _e('Plan', 'deshiflix'); ?></th>
                            <th><?php _e('Status', 'deshiflix'); ?></th>
                            <th><?php _e('Expires', 'deshiflix'); ?></th>
                            <th><?php _e('Actions', 'deshiflix'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php self::render_users_list(); ?>
                    </tbody>
                </table>
            </div>
            
        </div>
        
        <script>
        function filterUsers() {
            const search = document.getElementById('user-search').value.toLowerCase();
            const status = document.getElementById('status-filter').value;
            const rows = document.querySelectorAll('#premium-users-table tbody tr');
            
            rows.forEach(row => {
                const username = row.cells[0].textContent.toLowerCase();
                const email = row.cells[1].textContent.toLowerCase();
                const userStatus = row.cells[3].textContent.toLowerCase();
                
                const matchesSearch = username.includes(search) || email.includes(search);
                const matchesStatus = !status || userStatus.includes(status);
                
                row.style.display = matchesSearch && matchesStatus ? '' : 'none';
            });
        }
        
        function extendUser(userId, days) {
            if (confirm('Extend user subscription by ' + days + ' days?')) {
                // AJAX call to extend user
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="extend_user">
                    <input type="hidden" name="user_id" value="${userId}">
                    <input type="hidden" name="extend_days" value="${days}">
                    <input type="hidden" name="users_nonce" value="<?php echo wp_create_nonce('premium_users_action'); ?>">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function suspendUser(userId) {
            if (confirm('Suspend this user\'s premium access?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="suspend_user">
                    <input type="hidden" name="user_id" value="${userId}">
                    <input type="hidden" name="users_nonce" value="<?php echo wp_create_nonce('premium_users_action'); ?>">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        </script>
        <?php
    }
    
    /**
     * Handle user actions
     */
    private static function handle_user_actions() {
        $action = $_POST['action'];
        
        switch ($action) {
            case 'add_user':
                self::add_premium_user();
                break;
            case 'extend_user':
                self::extend_user();
                break;
            case 'suspend_user':
                self::suspend_user();
                break;
        }
    }
    
    /**
     * Add premium user
     */
    private static function add_premium_user() {
        $identifier = sanitize_text_field($_POST['user_identifier']);
        $duration = $_POST['plan_duration'];
        $custom_days = intval($_POST['custom_days']);
        
        // Get user by username or email
        $user = get_user_by('login', $identifier);
        if (!$user) {
            $user = get_user_by('email', $identifier);
        }
        
        if (!$user) {
            echo '<div class="notice notice-error"><p>' . __('User not found!', 'deshiflix') . '</p></div>';
            return;
        }
        
        $days = ($duration === 'custom') ? $custom_days : intval($duration);
        $expires_at = date('Y-m-d H:i:s', strtotime("+{$days} days"));
        
        // Add premium access (simplified - in real implementation, use database)
        update_user_meta($user->ID, 'deshiflix_premium_expires', $expires_at);
        update_user_meta($user->ID, 'deshiflix_premium_status', 'active');
        
        echo '<div class="notice notice-success"><p>' . sprintf(__('Premium access added for %s until %s', 'deshiflix'), $user->display_name, $expires_at) . '</p></div>';
    }
    
    /**
     * Get active users count
     */
    private static function get_active_users_count() {
        return 1; // Demo count
    }
    
    /**
     * Get expired users count
     */
    private static function get_expired_users_count() {
        return 0; // Demo count
    }
    
    /**
     * Get trial users count
     */
    private static function get_trial_users_count() {
        return 0; // Demo count
    }
    
    /**
     * Get total revenue
     */
    private static function get_total_revenue() {
        return 15000; // Demo revenue
    }
    
    /**
     * Render users list
     */
    private static function render_users_list() {
        // Get test user
        $test_user = get_user_by('login', 'premium_test_user');
        if ($test_user) {
            echo '<tr>';
            echo '<td><strong>' . esc_html($test_user->display_name) . '</strong></td>';
            echo '<td>' . esc_html($test_user->user_email) . '</td>';
            echo '<td>Test Premium Plan</td>';
            echo '<td><span style="color: #46b450; font-weight: bold;">Active</span></td>';
            echo '<td>' . date('Y-m-d', strtotime('+30 days')) . '</td>';
            echo '<td>';
            echo '<button type="button" class="button button-small" onclick="extendUser(' . $test_user->ID . ', 30)">Extend 30d</button> ';
            echo '<button type="button" class="button button-small" onclick="suspendUser(' . $test_user->ID . ')">Suspend</button>';
            echo '</td>';
            echo '</tr>';
        }
        
        // Demo users
        echo '<tr>';
        echo '<td><strong>Demo User 1</strong></td>';
        echo '<td><EMAIL></td>';
        echo '<td>Monthly Plan</td>';
        echo '<td><span style="color: #46b450; font-weight: bold;">Active</span></td>';
        echo '<td>' . date('Y-m-d', strtotime('+15 days')) . '</td>';
        echo '<td>';
        echo '<button type="button" class="button button-small" onclick="extendUser(999, 30)">Extend 30d</button> ';
        echo '<button type="button" class="button button-small" onclick="suspendUser(999)">Suspend</button>';
        echo '</td>';
        echo '</tr>';
    }
    
    /**
     * Extend user
     */
    private static function extend_user() {
        echo '<div class="notice notice-success"><p>' . __('User subscription extended successfully!', 'deshiflix') . '</p></div>';
    }
    
    /**
     * Suspend user
     */
    private static function suspend_user() {
        echo '<div class="notice notice-warning"><p>' . __('User premium access suspended!', 'deshiflix') . '</p></div>';
    }
}
