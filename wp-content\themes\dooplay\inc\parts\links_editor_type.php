<table class="options-table-responsive dt-options-table">
    <tbody>
        <tr id="parent_row">
            <td class="label">
                <label><?php _d('Parent'); ?></label>
            </td>
            <td class="field">
                <a href="<?php echo admin_url('post.php?post='.wp_get_post_parent_id($post->ID).'&action=edit'); ?>"><strong><?php echo get_the_title( wp_get_post_parent_id($post->ID) ); ?></strong></a>
            </td>
        </tr>
        <tr id="dool_type_row">
            <td class="label">
                <label><?php _d('Type'); ?></label>
            </td>
            <td class="field">
                <select name="_dool_type" id="dool_type">
                    <?php foreach( $this->types() as $type ) { echo '<option '.selected( get_post_meta($post->ID, $this->metatype, true), $type, false).'>'.$type.'</option>'; } ?>
                </select>
            </td>
        </tr>
        <tr id="dool_url_row">
            <td class="label">
                <label><?php _d('URL Link'); ?></label>
            </td>
            <td class="field">
                <input class="regular-text" type="text" name="_dool_url" id="dool_url" value="<?php echo get_post_meta($post->ID, $this->metaurl, true); ?>">
            </td>
        </tr>
        <tr id="dool_size_row">
            <td class="label">
                <label><?php _d('File size'); ?></label>
            </td>
            <td class="field">
                <input class="regular-text" type="text" name="_dool_size" id="dool_size" value="<?php echo get_post_meta($post->ID, $this->metasize, true); ?>">
            </td>
        </tr>
        <tr id="dool_lang_row">
            <td class="label">
                <label><?php _d('Language'); ?></label>
            </td>
            <td class="field">
                <select name="_dool_lang" id="dool_lang">
                    <?php foreach( $this->langs() as $lang ) { echo '<option '.selected( get_post_meta($post->ID, $this->metalang, true), $lang, false).'>'.$lang.'</option>'; } ?>
                </select>
            </td>
        </tr>
        <tr id="dool_quality_row">
            <td class="label">
                <label><?php _d('Quality'); ?></label>
            </td>
            <td class="field">
                <select name="_dool_quality" id="dool_quality">
                    <?php foreach( $this->resolutions() as $resolution ) { echo '<option '.selected( get_post_meta($post->ID, $this->metaquality, true), $resolution, false).'>'.$resolution.'</option>'; } ?>
                </select>
            </td>
        </tr>
        <tr id="dool_premium_row">
            <td class="label">
                <label><?php _e('Premium Content', 'deshiflix'); ?></label>
            </td>
            <td class="field">
                <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                    <input type="checkbox" name="_dool_premium" id="dool_premium" value="1" <?php checked(get_post_meta($post->ID, '_dool_premium', true), 1); ?> style="margin: 0;">
                    <span style="background: linear-gradient(135deg, #ffd700, #ffb300); color: #333; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">
                        🌟 Premium Only
                    </span>
                </label>
                <p style="margin: 5px 0 0 0; font-size: 12px; color: #666; font-style: italic;">
                    <?php _e('Check this to make this link available only to premium subscribers', 'deshiflix'); ?>
                </p>
            </td>
        </tr>
    </tbody>
</table>
