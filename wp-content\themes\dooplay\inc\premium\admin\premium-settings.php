<?php
/**
 * DeshiFlix Premium System - Settings Page
 *
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Only show if we're NOT on the new DooPlay admin pages
$current_page = isset($_GET['page']) ? $_GET['page'] : '';
if (strpos($current_page, 'deshiflix-premium') !== false) {
    return; // Don't show old settings on new admin pages
}

// Handle form submission
if (isset($_POST['save_premium_settings']) && wp_verify_nonce($_POST['premium_settings_nonce'], 'save_premium_settings')) {
    $settings = array(
        'enable_premium' => isset($_POST['enable_premium']) ? 1 : 0,
        'enable_content_lock' => isset($_POST['enable_content_lock']) ? 1 : 0,
        'enable_download_protection' => isset($_POST['enable_download_protection']) ? 1 : 0,
        'enable_ad_free' => isset($_POST['enable_ad_free']) ? 1 : 0,
        'enable_hd_quality' => isset($_POST['enable_hd_quality']) ? 1 : 0,
        'enable_early_access' => isset($_POST['enable_early_access']) ? 1 : 0,
        'max_devices_per_user' => intval($_POST['max_devices_per_user']),
        'trial_period_days' => intval($_POST['trial_period_days']),
        'auto_expire_check' => isset($_POST['auto_expire_check']) ? 1 : 0,
        
        // Payment Gateway Settings
        'bkash_enabled' => isset($_POST['bkash_enabled']) ? 1 : 0,
        'bkash_test_mode' => isset($_POST['bkash_test_mode']) ? 1 : 0,
        'bkash_api_key' => sanitize_text_field($_POST['bkash_api_key']),
        'bkash_api_secret' => sanitize_text_field($_POST['bkash_api_secret']),
        'bkash_username' => sanitize_text_field($_POST['bkash_username']),
        'bkash_password' => sanitize_text_field($_POST['bkash_password']),
        
        'nagad_enabled' => isset($_POST['nagad_enabled']) ? 1 : 0,
        'nagad_test_mode' => isset($_POST['nagad_test_mode']) ? 1 : 0,
        'nagad_merchant_id' => sanitize_text_field($_POST['nagad_merchant_id']),
        'nagad_merchant_key' => sanitize_text_field($_POST['nagad_merchant_key']),
        
        'rocket_enabled' => isset($_POST['rocket_enabled']) ? 1 : 0,
        'rocket_test_mode' => isset($_POST['rocket_test_mode']) ? 1 : 0,
        'rocket_merchant_id' => sanitize_text_field($_POST['rocket_merchant_id'] ?? ''),
        'rocket_merchant_key' => sanitize_text_field($_POST['rocket_merchant_key'] ?? ''),
        
        'sslcommerz_enabled' => isset($_POST['sslcommerz_enabled']) ? 1 : 0,
        'sslcommerz_test_mode' => isset($_POST['sslcommerz_test_mode']) ? 1 : 0,
        'sslcommerz_store_id' => sanitize_text_field($_POST['sslcommerz_store_id']),
        'sslcommerz_store_password' => sanitize_text_field($_POST['sslcommerz_store_password']),
        
        'aamarpay_enabled' => isset($_POST['aamarpay_enabled']) ? 1 : 0,
        'aamarpay_test_mode' => isset($_POST['aamarpay_test_mode']) ? 1 : 0,
        'aamarpay_store_id' => sanitize_text_field($_POST['aamarpay_store_id'] ?? ''),
        'aamarpay_signature_key' => sanitize_text_field($_POST['aamarpay_signature_key'] ?? ''),
        
        // Email Settings
        'email_notifications' => isset($_POST['email_notifications']) ? 1 : 0,
        'admin_email_notifications' => isset($_POST['admin_email_notifications']) ? 1 : 0,
        'welcome_email_template' => wp_kses_post($_POST['welcome_email_template']),
        'expiry_email_template' => wp_kses_post($_POST['expiry_email_template']),
        
        // Security Settings
        'enable_ip_restriction' => isset($_POST['enable_ip_restriction']) ? 1 : 0,
        'max_login_attempts' => intval($_POST['max_login_attempts']),
        'enable_device_tracking' => isset($_POST['enable_device_tracking']) ? 1 : 0,
        'content_encryption' => isset($_POST['content_encryption']) ? 1 : 0,
    );
    
    update_option('deshiflix_premium_settings', $settings);

    // Clean output buffer and redirect to prevent header issues
    ob_clean();
    wp_redirect(add_query_arg('settings-updated', 'true', wp_get_referer()));
    exit;
}

// Show success message if redirected after save
if (isset($_GET['settings-updated']) && $_GET['settings-updated'] === 'true') {
    echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'deshiflix') . '</p></div>';
}

// Get current settings
$settings = get_option('deshiflix_premium_settings', array());
$settings = wp_parse_args($settings, array(
    'enable_premium' => 1,
    'enable_content_lock' => 1,
    'enable_download_protection' => 1,
    'enable_ad_free' => 1,
    'enable_hd_quality' => 1,
    'enable_early_access' => 1,
    'max_devices_per_user' => 3,
    'trial_period_days' => 0,
    'auto_expire_check' => 1,
    'bkash_enabled' => 1,
    'bkash_test_mode' => 1,
    'email_notifications' => 1,
    'admin_email_notifications' => 1,
    'max_login_attempts' => 5,
    'enable_device_tracking' => 1
));
?>

<div class="wrap premium-settings-wrap">
    <h1 class="wp-heading-inline">
        <span class="dashicons dashicons-admin-settings"></span>
        <?php _e('Premium System Settings', 'deshiflix'); ?>
    </h1>
    
    <form method="post" action="">
        <?php wp_nonce_field('save_premium_settings', 'premium_settings_nonce'); ?>
        
        <div class="premium-settings-tabs">
            <nav class="nav-tab-wrapper">
                <a href="#general" class="nav-tab nav-tab-active"><?php _e('General', 'deshiflix'); ?></a>
                <a href="#payment-gateways" class="nav-tab"><?php _e('Payment Gateways', 'deshiflix'); ?></a>
                <a href="#email" class="nav-tab"><?php _e('Email Settings', 'deshiflix'); ?></a>
                <a href="#security" class="nav-tab"><?php _e('Security', 'deshiflix'); ?></a>
            </nav>
            
            <!-- General Settings Tab -->
            <div id="general" class="tab-content active">
                <h2><?php _e('General Premium Settings', 'deshiflix'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable Premium System', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_premium" value="1" <?php checked($settings['enable_premium'], 1); ?>>
                                <?php _e('Enable the premium subscription system', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Content Lock', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_content_lock" value="1" <?php checked($settings['enable_content_lock'], 1); ?>>
                                <?php _e('Lock premium content for non-premium users', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Download Protection', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_download_protection" value="1" <?php checked($settings['enable_download_protection'], 1); ?>>
                                <?php _e('Protect download links for premium users only', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Ad-Free Experience', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_ad_free" value="1" <?php checked($settings['enable_ad_free'], 1); ?>>
                                <?php _e('Remove ads for premium users', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('HD Quality Access', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_hd_quality" value="1" <?php checked($settings['enable_hd_quality'], 1); ?>>
                                <?php _e('Restrict HD quality to premium users', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Early Access', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_early_access" value="1" <?php checked($settings['enable_early_access'], 1); ?>>
                                <?php _e('Give premium users early access to new content', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Max Devices Per User', 'deshiflix'); ?></th>
                        <td>
                            <input type="number" name="max_devices_per_user" value="<?php echo esc_attr($settings['max_devices_per_user']); ?>" min="1" max="10">
                            <p class="description"><?php _e('Maximum number of devices a premium user can use simultaneously', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Trial Period', 'deshiflix'); ?></th>
                        <td>
                            <input type="number" name="trial_period_days" value="<?php echo esc_attr($settings['trial_period_days']); ?>" min="0" max="30">
                            <p class="description"><?php _e('Free trial period in days (0 to disable)', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Auto Expire Check', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="auto_expire_check" value="1" <?php checked($settings['auto_expire_check'], 1); ?>>
                                <?php _e('Automatically check and expire subscriptions', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Payment Gateways Tab -->
            <div id="payment-gateways" class="tab-content">
                <h2><?php _e('Payment Gateway Settings', 'deshiflix'); ?></h2>
                
                <!-- bKash Settings -->
                <h3><?php _e('bKash Mobile Banking', 'deshiflix'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable bKash', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="bkash_enabled" value="1" <?php checked($settings['bkash_enabled'], 1); ?>>
                                <?php _e('Enable bKash payment gateway', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Test Mode', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="bkash_test_mode" value="1" <?php checked($settings['bkash_test_mode'], 1); ?>>
                                <?php _e('Enable test mode for bKash', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('API Key', 'deshiflix'); ?></th>
                        <td>
                            <input type="text" name="bkash_api_key" value="<?php echo esc_attr($settings['bkash_api_key'] ?? ''); ?>" class="regular-text">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('API Secret', 'deshiflix'); ?></th>
                        <td>
                            <input type="password" name="bkash_api_secret" value="<?php echo esc_attr($settings['bkash_api_secret'] ?? ''); ?>" class="regular-text">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Username', 'deshiflix'); ?></th>
                        <td>
                            <input type="text" name="bkash_username" value="<?php echo esc_attr($settings['bkash_username'] ?? ''); ?>" class="regular-text">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Password', 'deshiflix'); ?></th>
                        <td>
                            <input type="password" name="bkash_password" value="<?php echo esc_attr($settings['bkash_password'] ?? ''); ?>" class="regular-text">
                        </td>
                    </tr>
                </table>
                
                <!-- Nagad Settings -->
                <h3><?php _e('Nagad Mobile Banking', 'deshiflix'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable Nagad', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="nagad_enabled" value="1" <?php checked($settings['nagad_enabled'] ?? 0, 1); ?>>
                                <?php _e('Enable Nagad payment gateway', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Test Mode', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="nagad_test_mode" value="1" <?php checked($settings['nagad_test_mode'] ?? 0, 1); ?>>
                                <?php _e('Enable test mode for Nagad', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Merchant ID', 'deshiflix'); ?></th>
                        <td>
                            <input type="text" name="nagad_merchant_id" value="<?php echo esc_attr($settings['nagad_merchant_id'] ?? ''); ?>" class="regular-text">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Merchant Key', 'deshiflix'); ?></th>
                        <td>
                            <input type="password" name="nagad_merchant_key" value="<?php echo esc_attr($settings['nagad_merchant_key'] ?? ''); ?>" class="regular-text">
                        </td>
                    </tr>
                </table>
                
                <!-- SSLCommerz Settings -->
                <h3><?php _e('SSLCommerz Payment Gateway', 'deshiflix'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable SSLCommerz', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="sslcommerz_enabled" value="1" <?php checked($settings['sslcommerz_enabled'] ?? 0, 1); ?>>
                                <?php _e('Enable SSLCommerz payment gateway', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Test Mode', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="sslcommerz_test_mode" value="1" <?php checked($settings['sslcommerz_test_mode'] ?? 0, 1); ?>>
                                <?php _e('Enable test mode for SSLCommerz', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Store ID', 'deshiflix'); ?></th>
                        <td>
                            <input type="text" name="sslcommerz_store_id" value="<?php echo esc_attr($settings['sslcommerz_store_id'] ?? ''); ?>" class="regular-text">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Store Password', 'deshiflix'); ?></th>
                        <td>
                            <input type="password" name="sslcommerz_store_password" value="<?php echo esc_attr($settings['sslcommerz_store_password'] ?? ''); ?>" class="regular-text">
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Email Settings Tab -->
            <div id="email" class="tab-content">
                <h2><?php _e('Email Notification Settings', 'deshiflix'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Email Notifications', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="email_notifications" value="1" <?php checked($settings['email_notifications'], 1); ?>>
                                <?php _e('Send email notifications to users', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Admin Notifications', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="admin_email_notifications" value="1" <?php checked($settings['admin_email_notifications'], 1); ?>>
                                <?php _e('Send email notifications to admin', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Welcome Email Template', 'deshiflix'); ?></th>
                        <td>
                            <?php
                            $welcome_template = $settings['welcome_email_template'] ?? 'Welcome to DeshiFlix Premium! Your subscription is now active.';
                            wp_editor($welcome_template, 'welcome_email_template', array(
                                'textarea_name' => 'welcome_email_template',
                                'textarea_rows' => 5,
                                'media_buttons' => false
                            ));
                            ?>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Expiry Email Template', 'deshiflix'); ?></th>
                        <td>
                            <?php
                            $expiry_template = $settings['expiry_email_template'] ?? 'Your DeshiFlix Premium subscription has expired. Renew now to continue enjoying premium content.';
                            wp_editor($expiry_template, 'expiry_email_template', array(
                                'textarea_name' => 'expiry_email_template',
                                'textarea_rows' => 5,
                                'media_buttons' => false
                            ));
                            ?>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Security Settings Tab -->
            <div id="security" class="tab-content">
                <h2><?php _e('Security Settings', 'deshiflix'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('IP Restriction', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_ip_restriction" value="1" <?php checked($settings['enable_ip_restriction'] ?? 0, 1); ?>>
                                <?php _e('Enable IP-based access restriction', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Max Login Attempts', 'deshiflix'); ?></th>
                        <td>
                            <input type="number" name="max_login_attempts" value="<?php echo esc_attr($settings['max_login_attempts']); ?>" min="3" max="10">
                            <p class="description"><?php _e('Maximum failed login attempts before blocking', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Device Tracking', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_device_tracking" value="1" <?php checked($settings['enable_device_tracking'], 1); ?>>
                                <?php _e('Track user devices for premium access', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Content Encryption', 'deshiflix'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="content_encryption" value="1" <?php checked($settings['content_encryption'] ?? 0, 1); ?>>
                                <?php _e('Enable content encryption for premium content', 'deshiflix'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <p class="submit">
            <input type="submit" name="save_premium_settings" class="button-primary" value="<?php _e('Save Settings', 'deshiflix'); ?>">
        </p>
    </form>
</div>

<style>
.premium-settings-wrap .nav-tab-wrapper {
    margin-bottom: 20px;
}

.premium-settings-wrap .tab-content {
    display: none;
}

.premium-settings-wrap .tab-content.active {
    display: block;
}

.premium-settings-wrap h3 {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.premium-settings-wrap .form-table th {
    width: 200px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Tab switching
    $('.nav-tab').click(function(e) {
        e.preventDefault();
        
        // Remove active class from all tabs and content
        $('.nav-tab').removeClass('nav-tab-active');
        $('.tab-content').removeClass('active');
        
        // Add active class to clicked tab
        $(this).addClass('nav-tab-active');
        
        // Show corresponding content
        var target = $(this).attr('href');
        $(target).addClass('active');
    });
});
</script>
