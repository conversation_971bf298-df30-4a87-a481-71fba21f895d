<?php
/*
* Live TV Grid Template for Dooplay Theme
* Following <PERSON><PERSON>'s exact structure and design
*/

// Debug
echo '<!-- Grid Template Loading -->';

// Initialize Live TV
try {
    $livetv = new DooLiveTV();
    echo '<!-- DooLiveTV initialized successfully -->';
} catch (Exception $e) {
    echo '<div style="background: #f44336; color: white; padding: 20px; margin: 20px 0;">';
    echo '<h3>Error initializing DooLiveTV</h3>';
    echo '<p>' . esc_html($e->getMessage()) . '</p>';
    echo '</div>';
    return;
}

// Get parameters
$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$category_filter = isset($_GET['category']) ? intval($_GET['category']) : 0;
$current_page = isset($_GET['page_num']) ? max(1, intval($_GET['page_num'])) : 1;
$per_page = 20;

// Get channels directly from database for testing
global $wpdb;
$table_channels = $wpdb->prefix . 'doo_livetv_channels';
$table_categories = $wpdb->prefix . 'doo_livetv_categories';

// Build WHERE clause
$where_conditions = array("c.status = 'active'");
$query_params = array();

if ($search) {
    $where_conditions[] = "c.name LIKE %s";
    $query_params[] = '%' . $wpdb->esc_like($search) . '%';
}

if ($category_filter) {
    $where_conditions[] = "c.category_id = %d";
    $query_params[] = $category_filter;
}

$where_clause = implode(' AND ', $where_conditions);
$offset = ($current_page - 1) * $per_page;

// Get channels
$sql = "SELECT c.*, cat.name as category_name, cat.color as category_color
        FROM $table_channels c
        LEFT JOIN $table_categories cat ON c.category_id = cat.id
        WHERE $where_clause
        ORDER BY c.sort_order ASC, c.name ASC
        LIMIT %d OFFSET %d";

$query_params[] = $per_page;
$query_params[] = $offset;

$channels = $wpdb->get_results($wpdb->prepare($sql, $query_params));

// Debug
echo '<!-- SQL: ' . $wpdb->prepare($sql, $query_params) . ' -->';
echo '<!-- Channels found: ' . count($channels) . ' -->';

// Get categories
$categories = $wpdb->get_results("SELECT * FROM $table_categories ORDER BY name ASC");
echo '<!-- Categories found: ' . count($categories) . ' -->';

// Get total count
$count_sql = "SELECT COUNT(*) FROM $table_channels c WHERE " . str_replace('c.category_id = %d', 'c.category_id = %d', str_replace('c.name LIKE %s', 'c.name LIKE %s', str_replace("c.status = 'active'", "c.status = 'active'", $where_clause)));
$count_params = array_slice($query_params, 0, -2); // Remove LIMIT and OFFSET params
$total_channels = $wpdb->get_var(!empty($count_params) ? $wpdb->prepare($count_sql, $count_params) : $count_sql);
$total_pages = ceil($total_channels / $per_page);

echo '<!-- Total channels: ' . $total_channels . ' -->';

// Dooplay theme options
$maxwidth = function_exists('dooplay_get_option') ? dooplay_get_option('max_width','1200') : '1200';
$maxwidth = ($maxwidth >= 1400) ? 'full' : 'normal';
?>

<!-- Live TV Module - Following Dooplay Structure -->
<header class="live-tv-header">
    <div class="header-content">
        <h2 class="live-tv-title">📺 Live TV Channels</h2>
        <div class="header-stats">
            <span class="channel-count"><?php echo number_format($total_channels); ?> Channels</span>
            <?php if ($search || $category_filter): ?>
                <span class="filter-indicator">• Filtered</span>
            <?php endif; ?>
        </div>
    </div>
    <div class="header-actions">
        <a href="<?php echo get_permalink(76); ?>" class="see-all-btn tv-focusable">
            <i class="fas fa-th-large"></i> View All
        </a>
    </div>
</header>

<!-- Search and Filter Section -->
<div class="live-tv-filters">
    <form method="get" class="filter-form">
        <div class="search-wrapper">
            <input type="text" name="search" value="<?php echo esc_attr($search); ?>"
                   placeholder="🔍 Search channels..." class="search-input tv-focusable" />
        </div>

        <?php if (!empty($categories)): ?>
        <div class="category-wrapper">
            <select name="category" onchange="this.form.submit()" class="category-select tv-focusable">
                <option value="">📂 All Categories</option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?php echo $category->id; ?>" <?php selected($category_filter, $category->id); ?>>
                        <?php echo esc_html($category->name); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        <?php endif; ?>

        <div class="action-buttons">
            <button type="submit" class="search-btn tv-focusable">
                <i class="fas fa-search"></i> Search
            </button>

            <?php if ($category_filter || $search): ?>
            <a href="<?php echo get_permalink(76); ?>" class="clear-btn tv-focusable">
                <i class="fas fa-times"></i> Clear
            </a>
            <?php endif; ?>
        </div>
    </form>
</div>

<!-- Channel Items Grid -->
<div class="items <?php echo $maxwidth; ?>">
    <?php if (empty($channels)): ?>
        <div class="no-results">
            <i class="fas fa-tv"></i>
            <h3>No Channels Found</h3>
            <p>
                <?php if ($search || $category_filter): ?>
                    No channels match your current filters. Try adjusting your search.
                <?php else: ?>
                    No live TV channels are currently available. Please check back later.
                <?php endif; ?>
            </p>
            <?php if ($search || $category_filter): ?>
                <a href="<?php echo get_permalink(76); ?>" class="button tv-focusable">Show All Channels</a>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <?php foreach ($channels as $channel): ?>
            <article class="item tv-focusable" tabindex="0" onclick="window.location.href='<?php echo add_query_arg('channel', $channel->slug, get_permalink(76)); ?>'" style="cursor: pointer;">
                <div class="poster">
                    <?php if ($channel->logo_url): ?>
                        <img src="<?php echo esc_url($channel->logo_url); ?>" alt="<?php echo esc_attr($channel->name); ?>" loading="lazy">
                    <?php else: ?>
                        <div class="no-logo" style="width: 100%; height: 100%; background: linear-gradient(135deg, #2c2c54 0%, #40407a 100%); display: flex; align-items: center; justify-content: center; flex-direction: column; color: rgba(255,255,255,0.9); position: absolute; top: 0; left: 0;">
                            <i class="fas fa-tv" style="font-size: 1.5rem; margin-bottom: 8px; opacity: 0.8;"></i>
                            <span style="font-size: 0.75rem; font-weight: bold; text-transform: uppercase; letter-spacing: 0.5px; text-align: center; padding: 0 5px; line-height: 1.2;"><?php echo esc_html($channel->name); ?></span>
                        </div>
                    <?php endif; ?>

                    <!-- Channel name overlay for cards without logo -->
                    <?php if (!$channel->logo_url): ?>
                        <div class="channel-name-overlay">
                            <span><?php echo esc_html($channel->name); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if ($channel->views > 0): ?>
                        <div class="rating"><?php echo number_format($channel->views); ?></div>
                    <?php endif; ?>

                    <div class="mepo">
                        <?php if ($channel->quality): ?>
                            <span class="quality"><?php echo esc_html($channel->quality); ?></span>
                        <?php else: ?>
                            <span class="quality hd">HD</span>
                        <?php endif; ?>

                        <?php if ($channel->status === 'active'): ?>
                            <span class="live-indicator">LIVE</span>
                        <?php endif; ?>
                    </div>

                    <a href="<?php echo esc_url(add_query_arg('channel', $channel->slug, get_permalink(76))); ?>" class="tv-focusable" data-channel-name="<?php echo esc_attr($channel->name); ?>">
                        <div class="see play1"></div>
                    </a>
                </div>

                <div class="data">
                    <h3><a href="<?php echo esc_url(add_query_arg('channel', $channel->slug, get_permalink(76))); ?>" class="tv-focusable"><?php echo esc_html($channel->name); ?></a></h3>
                    <span>
                        <?php if ($channel->category_name): ?>
                            <?php echo esc_html($channel->category_name); ?>
                        <?php endif; ?>
                        <?php if ($channel->country): ?>
                            <?php if ($channel->category_name): ?> • <?php endif; ?>
                            <?php echo esc_html($channel->country); ?>
                        <?php endif; ?>
                    </span>
                </div>
            </article>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($total_pages > 1): ?>
<div class="pagination">
    <?php if ($current_page > 1): ?>
        <a href="<?php echo add_query_arg('page_num', $current_page - 1); ?>" class="page-link prev tv-focusable">
            <i class="fas fa-chevron-left"></i> Previous
        </a>
    <?php endif; ?>

    <?php
    $start_page = max(1, $current_page - 2);
    $end_page = min($total_pages, $current_page + 2);

    for ($i = $start_page; $i <= $end_page; $i++): ?>
        <?php if ($i == $current_page): ?>
            <span class="page-link current tv-focusable"><?php echo $i; ?></span>
        <?php else: ?>
            <a href="<?php echo add_query_arg('page_num', $i); ?>" class="page-link tv-focusable"><?php echo $i; ?></a>
        <?php endif; ?>
    <?php endfor; ?>

    <?php if ($current_page < $total_pages): ?>
        <a href="<?php echo add_query_arg('page_num', $current_page + 1); ?>" class="page-link next tv-focusable">
            Next <i class="fas fa-chevron-right"></i>
        </a>
    <?php endif; ?>
</div>
<?php endif; ?>

<style>
/* Force Grid Layout - Override all other CSS with highest specificity */
html body .live-tv-container .items,
html body .items,
html body .items.full,
body .live-tv-container .items,
body .items,
body .items.full,
.live-tv-container .items,
.items,
.items.full {
    display: grid !important;
    grid-template-columns: repeat(6, 1fr) !important;
    gap: 15px !important;
    width: 100% !important;
    max-width: none !important;
    float: none !important;
    flex-direction: unset !important;
    flex-wrap: unset !important;
    list-style: none !important;
    padding: 20px 0 !important;
    margin: 0 !important;
}

/* Force Item Layout */
body .live-tv-container .item,
body .items .item,
body .item,
.live-tv-container .item,
.items .item,
.item {
    width: 100% !important;
    max-width: none !important;
    min-width: unset !important;
    float: none !important;
    display: block !important;
    flex: unset !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Header Styling */
.live-tv-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 20px !important;
    padding: 20px 0 !important;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
}

.header-content {
    flex: 1 !important;
}

.live-tv-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #fff !important;
    margin: 0 0 8px 0 !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5) !important;
}

.header-stats {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.channel-count {
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%) !important;
    color: white !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 15px rgba(0, 124, 186, 0.3) !important;
}

.filter-indicator {
    background: #e74c3c !important;
    color: white !important;
    padding: 6px 12px !important;
    border-radius: 15px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    animation: pulse 2s infinite !important;
}

.header-actions {
    display: flex !important;
    gap: 10px !important;
}

.see-all-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%) !important;
    color: white !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3) !important;
}

.see-all-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4) !important;
    color: white !important;
}

@media (max-width: 768px) {
    .live-tv-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 15px !important;
    }

    .live-tv-title {
        font-size: 2rem !important;
    }

    .header-actions {
        width: 100% !important;
        justify-content: center !important;
    }
}

/* Fix hover dark overlay issues */
.poster:hover,
.poster:hover img,
.poster:hover .no-logo {
    filter: none !important;
    opacity: 1 !important;
}

.poster:hover::before,
.poster:hover::after {
    display: none !important;
}

/* Ensure smooth hover transitions */
.poster {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
}

.poster:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

/* Better quality badges */
.mepo .quality {
    background: #e74c3c !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.mepo .quality.hd {
    background: #27ae60 !important;
}

.mepo .live-indicator {
    background: #ff4757 !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    font-size: 10px !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    margin-left: 5px !important;
    animation: pulse 2s infinite !important;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Better channel titles */
.data h3 {
    font-size: 14px !important;
    font-weight: 600 !important;
    margin-bottom: 5px !important;
    line-height: 1.3 !important;
}

.data h3 a {
    color: #fff !important;
    text-decoration: none !important;
}

.data h3 a:hover {
    color: #007cba !important;
}

.data span {
    font-size: 12px !important;
    color: #999 !important;
    text-transform: capitalize !important;
}

/* Better no-logo placeholder */
.no-logo {
    border-radius: 8px !important;
}

/* Channel name overlay */
.channel-name-overlay {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)) !important;
    padding: 15px 8px 8px 8px !important;
    color: white !important;
    text-align: center !important;
}

.channel-name-overlay span {
    font-size: 0.7rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    line-height: 1.2 !important;
    display: block !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Play button enhancement */
.see.play1 {
    background: rgba(0, 124, 186, 0.9) !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    opacity: 0 !important;
    transition: all 0.3s ease !important;
}

.see.play1::before {
    content: "▶" !important;
    color: white !important;
    font-size: 20px !important;
    margin-left: 3px !important;
}

.poster:hover .see.play1 {
    opacity: 1 !important;
    transform: translate(-50%, -50%) scale(1.1) !important;
}

.tv-focusable:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Filter Section Styling */
.live-tv-filters {
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 12px !important;
    padding: 20px !important;
    margin: 20px 0 !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.filter-form {
    display: flex !important;
    gap: 15px !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}

.search-wrapper {
    flex: 1 !important;
    min-width: 250px !important;
}

.search-input {
    width: 100% !important;
    padding: 12px 16px !important;
    border: 2px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    background: rgba(0, 0, 0, 0.3) !important;
    color: #fff !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
}

.search-input:focus {
    border-color: #007cba !important;
    background: rgba(0, 0, 0, 0.5) !important;
    outline: none !important;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
}

.category-wrapper {
    min-width: 180px !important;
}

.category-select {
    width: 100% !important;
    padding: 12px 16px !important;
    border: 2px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    background: rgba(0, 0, 0, 0.3) !important;
    color: #fff !important;
    font-size: 14px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.category-select:focus {
    border-color: #007cba !important;
    background: rgba(0, 0, 0, 0.5) !important;
    outline: none !important;
}

.category-select option {
    background: #2c2c54 !important;
    color: #fff !important;
}

.action-buttons {
    display: flex !important;
    gap: 10px !important;
}

.search-btn, .clear-btn {
    padding: 12px 20px !important;
    border: none !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.search-btn {
    background: #007cba !important;
    color: white !important;
}

.search-btn:hover {
    background: #005a87 !important;
    transform: translateY(-2px) !important;
}

.clear-btn {
    background: #e74c3c !important;
    color: white !important;
}

.clear-btn:hover {
    background: #c0392b !important;
    transform: translateY(-2px) !important;
}

/* Grid improvements - Force 6 cards per row */
.live-tv-container .items,
.live-tv-container .items.full,
.items,
.items.full {
    display: grid !important;
    grid-template-columns: repeat(6, 1fr) !important;
    gap: 15px !important;
    padding: 20px 0 !important;
    width: 100% !important;
    max-width: none !important;
}

.live-tv-container .item,
.items .item,
.item {
    width: 100% !important;
    float: none !important;
    padding: 0 !important;
    margin: 0 !important;
    position: relative !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    border: 2px solid rgba(255, 255, 255, 0.1) !important;
    min-height: 200px !important;
    max-width: none !important;
}

.live-tv-container .item:hover,
.items .item:hover,
.item:hover {
    transform: translateY(-5px) !important;
    border-color: #007cba !important;
    box-shadow: 0 10px 30px rgba(0, 124, 186, 0.3) !important;
}

/* Responsive adjustments - Force grid layout */
@media (max-width: 1400px) {
    body .live-tv-container .items,
    body .live-tv-container .items.full,
    body .items,
    body .items.full,
    .live-tv-container .items,
    .live-tv-container .items.full,
    .items,
    .items.full {
        grid-template-columns: repeat(5, 1fr) !important;
    }
}

@media (max-width: 1200px) {
    body .live-tv-container .items,
    body .live-tv-container .items.full,
    body .items,
    body .items.full,
    .live-tv-container .items,
    .live-tv-container .items.full,
    .items,
    .items.full {
        grid-template-columns: repeat(4, 1fr) !important;
    }
}

@media (max-width: 992px) {
    body .live-tv-container .items,
    body .live-tv-container .items.full,
    body .items,
    body .items.full,
    .live-tv-container .items,
    .live-tv-container .items.full,
    .items,
    .items.full {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

@media (max-width: 768px) {
    .filter-form {
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .search-wrapper,
    .category-wrapper {
        min-width: 100% !important;
    }

    .action-buttons {
        justify-content: center !important;
    }

    body .live-tv-container .items,
    body .live-tv-container .items.full,
    body .items,
    body .items.full,
    .live-tv-container .items,
    .live-tv-container .items.full,
    .items,
    .items.full {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 12px !important;
    }
}

@media (max-width: 480px) {
    .live-tv-filters {
        padding: 15px !important;
        margin: 15px 0 !important;
    }

    .search-btn, .clear-btn {
        padding: 10px 16px !important;
        font-size: 13px !important;
    }

    body .live-tv-container .items,
    body .live-tv-container .items.full,
    body .items,
    body .items.full,
    .live-tv-container .items,
    .live-tv-container .items.full,
    .items,
    .items.full {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 10px !important;
    }
}

/* Pagination Styling */
.pagination {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 10px !important;
    margin: 30px 0 !important;
    padding: 20px !important;
}

.page-link {
    padding: 10px 15px !important;
    border: 2px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    background: rgba(0, 0, 0, 0.3) !important;
    color: #fff !important;
    text-decoration: none !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 5px !important;
}

.page-link:hover {
    background: #007cba !important;
    border-color: #007cba !important;
    transform: translateY(-2px) !important;
    color: white !important;
}

.page-link.current {
    background: #007cba !important;
    border-color: #007cba !important;
    color: white !important;
    cursor: default !important;
}

.page-link.prev,
.page-link.next {
    padding: 10px 20px !important;
}

@media (max-width: 480px) {
    .pagination {
        flex-wrap: wrap !important;
        gap: 8px !important;
    }

    .page-link {
        padding: 8px 12px !important;
        font-size: 13px !important;
    }

    .page-link.prev,
    .page-link.next {
        padding: 8px 16px !important;
    }
}
</style>

<script>
// Force grid layout with JavaScript
document.addEventListener('DOMContentLoaded', function() {
    function forceGridLayout() {
        const itemsContainers = document.querySelectorAll('.items, .items.full');

        itemsContainers.forEach(function(container) {
            // Force grid properties
            container.style.setProperty('display', 'grid', 'important');
            container.style.setProperty('grid-template-columns', 'repeat(6, 1fr)', 'important');
            container.style.setProperty('gap', '15px', 'important');
            container.style.setProperty('width', '100%', 'important');
            container.style.setProperty('max-width', 'none', 'important');
            container.style.setProperty('float', 'none', 'important');
            container.style.setProperty('flex-direction', 'unset', 'important');
            container.style.setProperty('flex-wrap', 'unset', 'important');
            container.style.setProperty('padding', '20px 0', 'important');
            container.style.setProperty('margin', '0', 'important');

            // Force item properties
            const items = container.querySelectorAll('.item');
            items.forEach(function(item) {
                item.style.setProperty('width', '100%', 'important');
                item.style.setProperty('max-width', 'none', 'important');
                item.style.setProperty('float', 'none', 'important');
                item.style.setProperty('margin', '0', 'important');
                item.style.setProperty('padding', '0', 'important');
                item.style.setProperty('flex', 'unset', 'important');
            });
        });

        // Apply responsive breakpoints
        function applyResponsive() {
            const width = window.innerWidth;
            let columns = 6;

            if (width <= 480) {
                columns = 2;
            } else if (width <= 768) {
                columns = 3;
            } else if (width <= 992) {
                columns = 3;
            } else if (width <= 1200) {
                columns = 4;
            } else if (width <= 1400) {
                columns = 5;
            }

            itemsContainers.forEach(function(container) {
                container.style.setProperty('grid-template-columns', `repeat(${columns}, 1fr)`, 'important');
            });
        }

        applyResponsive();
        window.addEventListener('resize', applyResponsive);
    }

    // Run immediately and after a short delay
    forceGridLayout();
    setTimeout(forceGridLayout, 100);
    setTimeout(forceGridLayout, 500);

    // Also run when new content is loaded (for AJAX)
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
                setTimeout(forceGridLayout, 50);
            }
        });
    });

    const targetNode = document.querySelector('.live-tv-container') || document.body;
    observer.observe(targetNode, { childList: true, subtree: true });
});
</script>
