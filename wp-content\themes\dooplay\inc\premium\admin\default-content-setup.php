<?php
/**
 * Default Content Setup for Premium System
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Default_Content_Setup {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_menu', array($this, 'add_setup_menu'));
        add_action('wp_ajax_setup_default_content', array($this, 'setup_content'));
    }
    
    /**
     * Add setup menu
     */
    public function add_setup_menu() {
        add_submenu_page(
            'deshiflix-premium',
            'Content Setup',
            'Content Setup',
            'manage_options',
            'premium-content-setup',
            array($this, 'setup_page')
        );
    }
    
    /**
     * Setup page
     */
    public function setup_page() {
        if (isset($_POST['setup_content'])) {
            $this->create_default_content();
            echo '<div class="notice notice-success"><p>Default content created successfully!</p></div>';
        }
        
        $content_status = $this->check_content_status();
        ?>
        <div class="wrap">
            <h1>📝 Premium Content Setup</h1>
            
            <div class="content-setup-dashboard">
                <div class="setup-overview">
                    <h2>Content Status Overview</h2>
                    <div class="status-grid">
                        <div class="status-card">
                            <h3><?php echo $content_status['plans']; ?></h3>
                            <p>Premium Plans</p>
                            <span class="status-indicator <?php echo $content_status['plans'] > 0 ? 'active' : 'inactive'; ?>"></span>
                        </div>
                        <div class="status-card">
                            <h3><?php echo $content_status['premium_content']; ?></h3>
                            <p>Premium Content</p>
                            <span class="status-indicator <?php echo $content_status['premium_content'] > 0 ? 'active' : 'inactive'; ?>"></span>
                        </div>
                        <div class="status-card">
                            <h3><?php echo $content_status['users']; ?></h3>
                            <p>Premium Users</p>
                            <span class="status-indicator <?php echo $content_status['users'] > 0 ? 'active' : 'inactive'; ?>"></span>
                        </div>
                        <div class="status-card">
                            <h3><?php echo $content_status['transactions']; ?></h3>
                            <p>Transactions</p>
                            <span class="status-indicator <?php echo $content_status['transactions'] > 0 ? 'active' : 'inactive'; ?>"></span>
                        </div>
                    </div>
                </div>
                
                <div class="setup-actions">
                    <h2>Setup Actions</h2>
                    <form method="post">
                        <div class="action-grid">
                            <div class="action-card">
                                <h3>🎯 Create Premium Plans</h3>
                                <p>Create default subscription plans with different features and pricing.</p>
                                <label>
                                    <input type="checkbox" name="create_plans" value="1" checked>
                                    Create 3 premium plans (Basic, Standard, Premium)
                                </label>
                            </div>
                            
                            <div class="action-card">
                                <h3>🎬 Setup Premium Content</h3>
                                <p>Mark existing content as premium and create sample premium content.</p>
                                <label>
                                    <input type="checkbox" name="setup_content" value="1" checked>
                                    Mark 20% of content as premium
                                </label>
                            </div>
                            
                            <div class="action-card">
                                <h3>👤 Create Test Users</h3>
                                <p>Create test users with different premium statuses for testing.</p>
                                <label>
                                    <input type="checkbox" name="create_test_users" value="1">
                                    Create test premium users
                                </label>
                            </div>
                            
                            <div class="action-card">
                                <h3>📊 Sample Analytics</h3>
                                <p>Generate sample analytics data for dashboard testing.</p>
                                <label>
                                    <input type="checkbox" name="create_analytics" value="1">
                                    Generate sample analytics data
                                </label>
                            </div>
                        </div>
                        
                        <p class="submit">
                            <input type="submit" name="setup_content" class="button-primary" 
                                   value="Setup Selected Content" 
                                   onclick="return confirm('This will create default content. Continue?')">
                            <button type="button" class="button button-secondary" onclick="previewContent()">
                                Preview Content
                            </button>
                        </p>
                    </form>
                </div>
                
                <div class="content-preview" id="content-preview" style="display: none;">
                    <h2>Content Preview</h2>
                    <div id="preview-results"></div>
                </div>
            </div>
        </div>
        
        <style>
        .content-setup-dashboard {
            max-width: 1200px;
            margin: 20px 0;
        }
        
        .setup-overview {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            position: relative;
        }
        
        .status-card h3 {
            font-size: 2rem;
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .status-card p {
            margin: 0;
            color: #666;
        }
        
        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .status-indicator.active {
            background: #4CAF50;
        }
        
        .status-indicator.inactive {
            background: #f44336;
        }
        
        .setup-actions {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .action-card {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            transition: border-color 0.3s ease;
        }
        
        .action-card:hover {
            border-color: #0073aa;
        }
        
        .action-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .action-card p {
            color: #666;
            margin-bottom: 15px;
        }
        
        .action-card label {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
        }
        
        .content-preview {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 30px;
        }
        </style>
        
        <script>
        function previewContent() {
            var preview = document.getElementById('content-preview');
            var results = document.getElementById('preview-results');
            
            preview.style.display = 'block';
            results.innerHTML = 'Loading content preview...';
            
            // Simulate content preview
            setTimeout(function() {
                results.innerHTML = `
                    <h3>Premium Plans Preview:</h3>
                    <ul>
                        <li><strong>Basic Plan</strong> - ৳299/month - 2 devices, HD quality</li>
                        <li><strong>Standard Plan</strong> - ৳499/month - 4 devices, 4K quality</li>
                        <li><strong>Premium Plan</strong> - ৳799/month - 6 devices, unlimited downloads</li>
                    </ul>
                    
                    <h3>Premium Content Preview:</h3>
                    <ul>
                        <li>Latest Bollywood movies marked as premium</li>
                        <li>Exclusive web series content</li>
                        <li>Early access to new releases</li>
                    </ul>
                `;
            }, 1000);
        }
        </script>
        <?php
    }
    
    /**
     * Check content status
     */
    private function check_content_status() {
        global $wpdb;
        
        return array(
            'plans' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}deshiflix_premium_plans"),
            'premium_content' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}deshiflix_premium_content"),
            'users' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}deshiflix_premium_users"),
            'transactions' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}deshiflix_premium_transactions")
        );
    }
    
    /**
     * Create default content
     */
    private function create_default_content() {
        if (isset($_POST['create_plans'])) {
            $this->create_premium_plans();
        }
        
        if (isset($_POST['setup_content'])) {
            $this->setup_premium_content();
        }
        
        if (isset($_POST['create_test_users'])) {
            $this->create_test_users();
        }
        
        if (isset($_POST['create_analytics'])) {
            $this->create_sample_analytics();
        }
    }
    
    /**
     * Create premium plans
     */
    private function create_premium_plans() {
        global $wpdb;
        
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $plans = array(
            array(
                'name' => 'Basic Plan',
                'description' => 'Perfect for individual users who want premium content access',
                'price' => 299,
                'original_price' => 399,
                'duration_days' => 30,
                'max_devices' => 2,
                'download_limit' => 10,
                'quality_limit' => 'HD',
                'features' => json_encode(array(
                    'HD Quality Streaming',
                    'Ad-Free Experience',
                    '10 Downloads per month',
                    'Mobile & Desktop Access',
                    'Basic Customer Support'
                )),
                'status' => 'active',
                'sort_order' => 1
            ),
            array(
                'name' => 'Standard Plan',
                'description' => 'Great for families with multiple devices and higher quality needs',
                'price' => 499,
                'original_price' => 699,
                'duration_days' => 30,
                'max_devices' => 4,
                'download_limit' => 25,
                'quality_limit' => '4K',
                'features' => json_encode(array(
                    '4K Ultra HD Streaming',
                    'Ad-Free Experience',
                    '25 Downloads per month',
                    'All Device Access',
                    'Priority Customer Support',
                    'Early Access to New Content'
                )),
                'status' => 'active',
                'sort_order' => 2
            ),
            array(
                'name' => 'Premium Plan',
                'description' => 'Ultimate entertainment experience with unlimited features',
                'price' => 799,
                'original_price' => 999,
                'duration_days' => 30,
                'max_devices' => 6,
                'download_limit' => 100,
                'quality_limit' => '4K',
                'features' => json_encode(array(
                    '4K Ultra HD Streaming',
                    'Ad-Free Experience',
                    'Unlimited Downloads',
                    'All Device Access',
                    '24/7 Premium Support',
                    'Exclusive Premium Content',
                    'Early Access to New Content',
                    'Offline Viewing',
                    'Multiple User Profiles'
                )),
                'status' => 'active',
                'sort_order' => 3
            )
        );
        
        foreach ($plans as $plan) {
            // Check if plan already exists
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_plans WHERE name = %s",
                $plan['name']
            ));
            
            if (!$existing) {
                $wpdb->insert($table_plans, $plan);
            }
        }
    }
    
    /**
     * Setup premium content
     */
    private function setup_premium_content() {
        global $wpdb;
        
        // Get random 20% of movies and TV shows
        $movies = get_posts(array(
            'post_type' => 'movies',
            'numberposts' => -1,
            'fields' => 'ids'
        ));
        
        $tvshows = get_posts(array(
            'post_type' => 'tvshows',
            'numberposts' => -1,
            'fields' => 'ids'
        ));
        
        $all_content = array_merge($movies, $tvshows);
        $total_content = count($all_content);
        $premium_count = ceil($total_content * 0.2); // 20% premium
        
        // Randomly select content to make premium
        $premium_content = array_rand(array_flip($all_content), min($premium_count, $total_content));
        
        if (!is_array($premium_content)) {
            $premium_content = array($premium_content);
        }
        
        $table_content = $wpdb->prefix . 'deshiflix_premium_content';
        
        foreach ($premium_content as $post_id) {
            // Check if already premium
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_content WHERE post_id = %d",
                $post_id
            ));
            
            if (!$existing) {
                $wpdb->insert($table_content, array(
                    'post_id' => $post_id,
                    'premium_level' => rand(1, 3), // Random premium level
                    'required_plan_id' => rand(1, 3), // Random required plan
                    'status' => 'active'
                ));
                
                // Update post meta
                update_post_meta($post_id, '_is_premium_content', true);
                update_post_meta($post_id, '_premium_level', rand(1, 3));
            }
        }
    }
    
    /**
     * Create test users
     */
    private function create_test_users() {
        $test_users = array(
            array(
                'username' => 'premium_user_basic',
                'email' => '<EMAIL>',
                'password' => 'test123',
                'plan_id' => 1,
                'display_name' => 'Basic Premium User'
            ),
            array(
                'username' => 'premium_user_standard',
                'email' => '<EMAIL>',
                'password' => 'test123',
                'plan_id' => 2,
                'display_name' => 'Standard Premium User'
            ),
            array(
                'username' => 'premium_user_premium',
                'email' => '<EMAIL>',
                'password' => 'test123',
                'plan_id' => 3,
                'display_name' => 'Premium User'
            )
        );
        
        foreach ($test_users as $user_data) {
            // Check if user exists
            if (!username_exists($user_data['username'])) {
                $user_id = wp_create_user(
                    $user_data['username'],
                    $user_data['password'],
                    $user_data['email']
                );
                
                if (!is_wp_error($user_id)) {
                    // Update display name
                    wp_update_user(array(
                        'ID' => $user_id,
                        'display_name' => $user_data['display_name']
                    ));
                    
                    // Activate premium subscription
                    if (function_exists('deshiflix_premium')) {
                        $premium_user = DeshiFlix_Premium_User::get_instance();
                        $premium_user->activate_premium_subscription(
                            $user_id,
                            $user_data['plan_id'],
                            30, // 30 days
                            'test'
                        );
                    }
                }
            }
        }
    }
    
    /**
     * Create sample analytics
     */
    private function create_sample_analytics() {
        global $wpdb;
        
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        // Get some users
        $users = get_users(array('number' => 10));
        $posts = get_posts(array('numberposts' => 20, 'fields' => 'ids'));
        
        $events = array('content_view', 'download', 'search', 'login', 'subscription_purchase');
        
        // Generate 100 sample analytics entries
        for ($i = 0; $i < 100; $i++) {
            $user = $users[array_rand($users)];
            $post_id = $posts[array_rand($posts)];
            $event = $events[array_rand($events)];
            
            $wpdb->insert($table_analytics, array(
                'user_id' => $user->ID,
                'event_type' => $event,
                'event_data' => json_encode(array(
                    'post_id' => $post_id,
                    'timestamp' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days')),
                    'device' => array_rand(array_flip(array('mobile', 'desktop', 'tablet')))
                )),
                'post_id' => $post_id,
                'ip_address' => '192.168.1.' . rand(1, 255),
                'user_agent' => 'Mozilla/5.0 (Test Browser)'
            ));
        }
    }
}

// Initialize default content setup
DeshiFlix_Default_Content_Setup::get_instance();
