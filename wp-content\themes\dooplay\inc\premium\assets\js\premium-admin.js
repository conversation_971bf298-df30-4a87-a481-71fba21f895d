/**
 * DeshiFlix Premium Admin JavaScript
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

(function($) {
    'use strict';
    
    // Premium Admin object
    var PremiumAdmin = {
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.initTabs();
            this.initToggles();
        },
        
        // Bind events
        bindEvents: function() {
            $(document).on('click', '.nav-tab', this.handleTabClick);
            $(document).on('change', '.feature-toggle-input', this.handleFeatureToggle);
            $(document).on('click', '.test-gateway', this.handleGatewayTest);
            $(document).on('click', '.save-settings', this.handleSaveSettings);
        },
        
        // Initialize tabs
        initTabs: function() {
            $('.nav-tab-wrapper .nav-tab').first().addClass('nav-tab-active');
            $('.tab-content').first().addClass('active');
        },
        
        // Initialize toggles
        initToggles: function() {
            $('.toggle-switch input').each(function() {
                var $card = $(this).closest('.feature-card, .gateway-card');
                var enabled = $(this).is(':checked');
                
                if (enabled) {
                    $card.removeClass('disabled').addClass('enabled');
                } else {
                    $card.removeClass('enabled').addClass('disabled');
                }
            });
        },
        
        // Handle tab click
        handleTabClick: function(e) {
            e.preventDefault();
            
            var $tab = $(this);
            var target = $tab.attr('href');
            
            // Update tab states
            $('.nav-tab').removeClass('nav-tab-active');
            $tab.addClass('nav-tab-active');
            
            // Update content states
            $('.tab-content').removeClass('active');
            $(target).addClass('active');
        },
        
        // Handle feature toggle
        handleFeatureToggle: function() {
            var $input = $(this);
            var $card = $input.closest('.feature-card');
            var feature = $input.data('feature');
            var enabled = $input.is(':checked');
            
            // Update card appearance
            if (enabled) {
                $card.removeClass('disabled').addClass('enabled');
                $card.find('.status-badge').removeClass('disabled').addClass('enabled').text('Enabled');
            } else {
                $card.removeClass('enabled').addClass('disabled');
                $card.find('.status-badge').removeClass('enabled').addClass('disabled').text('Disabled');
            }
            
            // Save via AJAX if nonce is available
            if (typeof premium_admin !== 'undefined' && premium_admin.nonce) {
                $.ajax({
                    url: premium_admin.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'toggle_premium_feature',
                        feature: feature,
                        enabled: enabled ? 1 : 0,
                        nonce: premium_admin.nonce
                    },
                    success: function(response) {
                        if (!response.success) {
                            // Revert on error
                            $input.prop('checked', !enabled);
                            PremiumAdmin.showNotice('Failed to update feature', 'error');
                        }
                    },
                    error: function() {
                        // Revert on error
                        $input.prop('checked', !enabled);
                        PremiumAdmin.showNotice('An error occurred', 'error');
                    }
                });
            }
        },
        
        // Handle gateway test
        handleGatewayTest: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var gateway = $button.data('gateway');
            
            $button.prop('disabled', true).text('Testing...');
            
            if (typeof premium_admin !== 'undefined') {
                $.ajax({
                    url: premium_admin.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'test_payment_gateway',
                        gateway: gateway,
                        nonce: premium_admin.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            PremiumAdmin.showNotice('Gateway test successful', 'success');
                        } else {
                            PremiumAdmin.showNotice(response.data.message || 'Gateway test failed', 'error');
                        }
                        $button.prop('disabled', false).text('Test');
                    },
                    error: function() {
                        PremiumAdmin.showNotice('An error occurred during testing', 'error');
                        $button.prop('disabled', false).text('Test');
                    }
                });
            }
        },
        
        // Handle save settings
        handleSaveSettings: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $form = $button.closest('form');
            
            $button.prop('disabled', true).text('Saving...');
            
            $.ajax({
                url: $form.attr('action') || window.location.href,
                type: 'POST',
                data: $form.serialize(),
                success: function(response) {
                    PremiumAdmin.showNotice('Settings saved successfully', 'success');
                    $button.prop('disabled', false).text('Save Settings');
                },
                error: function() {
                    PremiumAdmin.showNotice('Failed to save settings', 'error');
                    $button.prop('disabled', false).text('Save Settings');
                }
            });
        },
        
        // Show notice
        showNotice: function(message, type) {
            type = type || 'info';
            
            var $notice = $('<div class="notice premium-notice notice-' + type + ' is-dismissible">' +
                           '<p>' + message + '</p>' +
                           '<button type="button" class="notice-dismiss">' +
                           '<span class="screen-reader-text">Dismiss this notice.</span>' +
                           '</button>' +
                           '</div>');
            
            // Remove existing notices
            $('.premium-notice').remove();
            
            // Add new notice
            $('.wrap').first().after($notice);
            
            // Auto dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
            
            // Manual dismiss
            $notice.on('click', '.notice-dismiss', function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            });
        },
        
        // Format currency
        formatCurrency: function(amount) {
            return '৳' + parseFloat(amount).toLocaleString('en-BD', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            });
        },
        
        // Format number
        formatNumber: function(num) {
            return new Intl.NumberFormat('en-BD').format(num);
        },
        
        // Confirm action
        confirmAction: function(message) {
            return confirm(message || 'Are you sure you want to perform this action?');
        }
    };
    
    // Analytics specific functions
    var PremiumAnalytics = {
        
        // Initialize analytics
        init: function() {
            this.bindEvents();
            this.loadData();
        },
        
        // Bind events
        bindEvents: function() {
            $(document).on('click', '#update-analytics', this.updateAnalytics);
            $(document).on('click', '#export-analytics', this.exportAnalytics);
            $(document).on('change', '#analytics-period, #analytics-metric', this.updateAnalytics);
        },
        
        // Load analytics data
        loadData: function() {
            if ($('#revenueChart').length > 0) {
                this.updateAnalytics();
            }
        },
        
        // Update analytics
        updateAnalytics: function() {
            var period = $('#analytics-period').val() || 30;
            var metric = $('#analytics-metric').val() || 'revenue';
            
            if (typeof premium_admin === 'undefined') {
                return;
            }
            
            $.ajax({
                url: premium_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_analytics_data',
                    period: period,
                    metric: metric,
                    nonce: premium_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        PremiumAnalytics.updateMetrics(response.data.metrics);
                        PremiumAnalytics.updateCharts(response.data.charts);
                    }
                },
                error: function() {
                    PremiumAdmin.showNotice('Failed to load analytics data', 'error');
                }
            });
        },
        
        // Update metrics
        updateMetrics: function(metrics) {
            $('#total-revenue').text(PremiumAdmin.formatCurrency(metrics.total_revenue));
            $('#total-subscriptions').text(PremiumAdmin.formatNumber(metrics.total_subscriptions));
            $('#conversion-rate').text(metrics.conversion_rate + '%');
            $('#churn-rate').text(metrics.churn_rate + '%');
            
            // Update changes
            this.updateMetricChange('#revenue-change', metrics.revenue_change);
            this.updateMetricChange('#subscriptions-change', metrics.subscriptions_change);
            this.updateMetricChange('#conversion-change', metrics.conversion_change);
            this.updateMetricChange('#churn-change', metrics.churn_change);
        },
        
        // Update metric change
        updateMetricChange: function(selector, change) {
            var $element = $(selector);
            var prefix = change >= 0 ? '+' : '';
            $element.text(prefix + change + '%');
            $element.removeClass('positive negative');
            $element.addClass(change >= 0 ? 'positive' : 'negative');
        },
        
        // Update charts
        updateCharts: function(chartData) {
            // Simple chart updates - in production, use Chart.js
            console.log('Chart data:', chartData);
        },
        
        // Export analytics
        exportAnalytics: function() {
            var period = $('#analytics-period').val() || 30;
            
            if (typeof premium_admin === 'undefined') {
                return;
            }
            
            $.ajax({
                url: premium_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'export_analytics_data',
                    period: period,
                    nonce: premium_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Create download link
                        var blob = new Blob([response.data.csv], { type: 'text/csv' });
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = 'premium-analytics-' + new Date().toISOString().split('T')[0] + '.csv';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        window.URL.revokeObjectURL(url);
                        
                        PremiumAdmin.showNotice('Analytics data exported successfully', 'success');
                    } else {
                        PremiumAdmin.showNotice('Failed to export analytics data', 'error');
                    }
                },
                error: function() {
                    PremiumAdmin.showNotice('An error occurred during export', 'error');
                }
            });
        }
    };
    
    // User Management functions
    var PremiumUserManagement = {
        
        // Initialize
        init: function() {
            this.bindEvents();
        },
        
        // Bind events
        bindEvents: function() {
            $(document).on('click', '.manage-premium-user', this.managePremiumUser);
            $(document).on('click', '.view-user-devices', this.viewUserDevices);
        },
        
        // Manage premium user
        managePremiumUser: function(e) {
            e.preventDefault();
            
            var userId = $(this).data('user-id');
            
            // Show modal or redirect to management page
            window.location.href = 'user-edit.php?user_id=' + userId + '#premium-section';
        },
        
        // View user devices
        viewUserDevices: function(e) {
            e.preventDefault();
            
            var userId = $(this).data('user-id');
            
            if (typeof premium_admin === 'undefined') {
                return;
            }
            
            $.ajax({
                url: premium_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_user_devices',
                    user_id: userId,
                    nonce: premium_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show devices in modal or alert
                        var devices = response.data.devices;
                        var deviceList = devices.map(function(device) {
                            return device.device_name + ' (' + device.status + ')';
                        }).join('\n');
                        
                        alert('User Devices:\n' + deviceList);
                    }
                },
                error: function() {
                    PremiumAdmin.showNotice('Failed to load user devices', 'error');
                }
            });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        PremiumAdmin.init();
        PremiumAnalytics.init();
        PremiumUserManagement.init();
    });
    
    // Make functions available globally
    window.PremiumAdmin = PremiumAdmin;
    window.PremiumAnalytics = PremiumAnalytics;
    window.PremiumUserManagement = PremiumUserManagement;
    
    // Global functions for inline use
    window.managePremiumUser = function(userId) {
        PremiumUserManagement.managePremiumUser.call($('<div data-user-id="' + userId + '"></div>')[0]);
    };
    
    window.viewUserDevices = function(userId) {
        PremiumUserManagement.viewUserDevices.call($('<div data-user-id="' + userId + '"></div>')[0]);
    };
    
})(jQuery);
