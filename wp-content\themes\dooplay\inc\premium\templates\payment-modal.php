<?php
/**
 * Premium Payment Modal Template
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}
?>

<div id="premium-payment-modal" class="premium-modal" style="display: none;">
    <div class="premium-modal-overlay"></div>
    <div class="premium-modal-content">
        <div class="premium-modal-header">
            <h3><?php _e('Choose Payment Method', 'deshiflix'); ?></h3>
            <button class="premium-modal-close">&times;</button>
        </div>
        
        <div class="premium-modal-body">
            <div class="selected-plan-info">
                <h4 id="selected-plan-name"></h4>
                <p class="plan-price">
                    <span id="selected-plan-price"></span>
                    <span class="currency">৳</span>
                </p>
            </div>
            
            <div class="payment-methods">
                <h5><?php _e('Mobile Banking', 'deshiflix'); ?></h5>
                <div class="payment-method-grid">
                    <button class="payment-method-btn" data-method="bkash">
                        <img src="<?php echo get_template_directory_uri(); ?>/inc/premium/assets/images/bkash.png" alt="bKash">
                        <span>bKash</span>
                    </button>
                    
                    <button class="payment-method-btn" data-method="nagad">
                        <img src="<?php echo get_template_directory_uri(); ?>/inc/premium/assets/images/nagad.png" alt="Nagad">
                        <span>Nagad</span>
                    </button>
                    
                    <button class="payment-method-btn" data-method="rocket">
                        <img src="<?php echo get_template_directory_uri(); ?>/inc/premium/assets/images/rocket.png" alt="Rocket">
                        <span>Rocket</span>
                    </button>
                    
                    <button class="payment-method-btn" data-method="upay">
                        <img src="<?php echo get_template_directory_uri(); ?>/inc/premium/assets/images/upay.png" alt="Upay">
                        <span>Upay</span>
                    </button>
                </div>
                
                <h5><?php _e('Card Payment', 'deshiflix'); ?></h5>
                <div class="payment-method-grid">
                    <button class="payment-method-btn" data-method="sslcommerz">
                        <img src="<?php echo get_template_directory_uri(); ?>/inc/premium/assets/images/sslcommerz.png" alt="SSLCommerz">
                        <span>Card Payment</span>
                    </button>
                    
                    <button class="payment-method-btn" data-method="aamarpay">
                        <img src="<?php echo get_template_directory_uri(); ?>/inc/premium/assets/images/aamarpay.png" alt="aamarPay">
                        <span>aamarPay</span>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="premium-modal-footer">
            <button class="btn btn-secondary premium-modal-close">
                <?php _e('Cancel', 'deshiflix'); ?>
            </button>
            <button id="proceed-payment" class="btn btn-primary" disabled>
                <?php _e('Proceed to Payment', 'deshiflix'); ?>
            </button>
        </div>
    </div>
</div>

<style>
.premium-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
}

.premium-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.premium-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.premium-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.premium-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.premium-modal-body {
    padding: 20px;
}

.selected-plan-info {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border-radius: 8px;
    color: #000;
}

.plan-price {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
}

.payment-methods h5 {
    margin: 20px 0 10px 0;
    color: #333;
}

.payment-method-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.payment-method-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method-btn:hover {
    border-color: #FFD700;
    transform: translateY(-2px);
}

.payment-method-btn.selected {
    border-color: #FFD700;
    background: #FFF9E6;
}

.payment-method-btn img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-bottom: 8px;
}

.payment-method-btn span {
    font-size: 12px;
    font-weight: bold;
}

.premium-modal-footer {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    border-top: 1px solid #eee;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #FFD700;
    color: #000;
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}
</style>

<script>
jQuery(document).ready(function($) {
    let selectedPlan = null;
    let selectedMethod = null;
    
    // Show payment modal
    window.showPaymentModal = function(planId, planName, planPrice) {
        selectedPlan = {
            id: planId,
            name: planName,
            price: planPrice
        };
        
        $('#selected-plan-name').text(planName);
        $('#selected-plan-price').text(planPrice);
        $('#premium-payment-modal').fadeIn();
    };
    
    // Close modal
    $('.premium-modal-close, .premium-modal-overlay').click(function() {
        $('#premium-payment-modal').fadeOut();
    });
    
    // Select payment method
    $('.payment-method-btn').click(function() {
        $('.payment-method-btn').removeClass('selected');
        $(this).addClass('selected');
        selectedMethod = $(this).data('method');
        $('#proceed-payment').prop('disabled', false);
    });
    
    // Proceed to payment
    $('#proceed-payment').click(function() {
        if (selectedPlan && selectedMethod) {
            // Redirect to payment processing
            window.location.href = '<?php echo admin_url('admin-ajax.php'); ?>?action=process_premium_payment&plan_id=' + selectedPlan.id + '&method=' + selectedMethod + '&nonce=<?php echo wp_create_nonce('premium_payment'); ?>';
        }
    });
});
</script>
