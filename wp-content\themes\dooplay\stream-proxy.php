<?php
/*
* Stream Proxy for CORS Issues
* This proxy helps bypass CORS restrictions for HLS streams
*/

// Security headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get stream URL from parameter
$stream_url = isset($_GET['url']) ? $_GET['url'] : '';

if (empty($stream_url)) {
    http_response_code(400);
    echo json_encode(['error' => 'Stream URL is required']);
    exit;
}

// Validate URL
if (!filter_var($stream_url, FILTER_VALIDATE_URL)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid URL format']);
    exit;
}

// Security check - only allow specific domains
$allowed_domains = [
    'byphdgllyk.gpcdn.net',
    'maxplay-tv.fun',
    'stream.example.com',
    // Add more trusted domains here
];

$parsed_url = parse_url($stream_url);
$domain = $parsed_url['host'] ?? '';

$domain_allowed = false;
foreach ($allowed_domains as $allowed) {
    if (strpos($domain, $allowed) !== false) {
        $domain_allowed = true;
        break;
    }
}

if (!$domain_allowed) {
    http_response_code(403);
    echo json_encode(['error' => 'Domain not allowed']);
    exit;
}

// Set up cURL with proper headers
$ch = curl_init();

curl_setopt_array($ch, [
    CURLOPT_URL => $stream_url,
    CURLOPT_RETURNTRANSFER => false,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_MAXREDIRS => 5,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    CURLOPT_HTTPHEADER => [
        'Accept: */*',
        'Accept-Language: en-US,en;q=0.9',
        'Cache-Control: no-cache',
        'Pragma: no-cache',
        'Referer: ' . ($_SERVER['HTTP_REFERER'] ?? ''),
        'Origin: ' . ($_SERVER['HTTP_ORIGIN'] ?? ''),
    ],
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_HEADERFUNCTION => function($curl, $header) {
        $len = strlen($header);
        $header = explode(':', $header, 2);
        
        if (count($header) < 2) {
            return $len;
        }
        
        $name = strtolower(trim($header[0]));
        $value = trim($header[1]);
        
        // Forward important headers
        switch ($name) {
            case 'content-type':
                header('Content-Type: ' . $value);
                break;
            case 'content-length':
                header('Content-Length: ' . $value);
                break;
            case 'cache-control':
                header('Cache-Control: ' . $value);
                break;
            case 'expires':
                header('Expires: ' . $value);
                break;
            case 'last-modified':
                header('Last-Modified: ' . $value);
                break;
            case 'etag':
                header('ETag: ' . $value);
                break;
        }
        
        return $len;
    }
]);

// Execute request and stream response
$result = curl_exec($ch);

// Check for errors
if (curl_error($ch)) {
    http_response_code(500);
    echo json_encode(['error' => 'Proxy error: ' . curl_error($ch)]);
} else {
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    if ($http_code !== 200) {
        http_response_code($http_code);
        echo json_encode(['error' => 'Stream returned HTTP ' . $http_code]);
    }
}

curl_close($ch);
?>
