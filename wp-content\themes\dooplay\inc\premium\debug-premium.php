<?php
/**
 * DeshiFlix Premium System Debug
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

// Only run if WordPress is loaded
if (!defined('ABSPATH')) {
    exit('WordPress not loaded');
}

// Check if premium system is loaded
if (!class_exists('DeshiFlix_Premium_Core')) {
    exit('Premium system not loaded');
}

echo '<h1>DeshiFlix Premium System Debug</h1>';

// Check core class
echo '<h2>Core System Status</h2>';
echo '<ul>';
echo '<li>Core Class: ' . (class_exists('DeshiFlix_Premium_Core') ? '✅ Loaded' : '❌ Not Loaded') . '</li>';
echo '<li>Premium Instance: ' . (function_exists('deshiflix_premium') ? '✅ Available' : '❌ Not Available') . '</li>';

// Check database tables
echo '<li>Database Tables:</li>';
echo '<ul>';

global $wpdb;
$tables = array(
    'deshiflix_premium_plans',
    'deshiflix_premium_users', 
    'deshiflix_premium_transactions',
    'deshiflix_premium_content',
    'deshiflix_premium_analytics',
    'deshiflix_premium_devices'
);

foreach ($tables as $table) {
    $full_table_name = $wpdb->prefix . $table;
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$full_table_name'") === $full_table_name;
    echo '<li>' . $table . ': ' . ($exists ? '✅ Exists' : '❌ Missing') . '</li>';
}

echo '</ul>';

// Check classes
echo '<li>Classes:</li>';
echo '<ul>';

$classes = array(
    'DeshiFlix_Premium_Core',
    'DeshiFlix_Premium_User',
    'DeshiFlix_Premium_Payment',
    'DeshiFlix_Premium_Content',
    'DeshiFlix_Premium_Analytics',
    'DeshiFlix_Premium_Features',
    'DeshiFlix_Premium_Security',
    'DeshiFlix_Premium_User_Management'
);

foreach ($classes as $class) {
    echo '<li>' . $class . ': ' . (class_exists($class) ? '✅ Loaded' : '❌ Not Loaded') . '</li>';
}

echo '</ul>';

// Check files
echo '<li>Files:</li>';
echo '<ul>';

$files = array(
    'core/class-premium-core.php',
    'core/class-premium-user.php',
    'core/class-premium-payment.php',
    'core/class-premium-content.php',
    'core/class-premium-analytics.php',
    'core/class-premium-features.php',
    'core/class-premium-security.php',
    'core/class-premium-user-management.php',
    'frontend/premium-subscription.php',
    'frontend/premium-user-dashboard.php',
    'frontend/premium-content-lock.php'
);

$base_path = get_template_directory() . '/inc/premium/';

foreach ($files as $file) {
    $file_path = $base_path . $file;
    echo '<li>' . $file . ': ' . (file_exists($file_path) ? '✅ Exists' : '❌ Missing') . '</li>';
}

echo '</ul>';
echo '</ul>';

// Check settings
echo '<h2>Settings</h2>';
$settings = get_option('deshiflix_premium_settings', array());
echo '<pre>' . print_r($settings, true) . '</pre>';

// Check current user
echo '<h2>Current User</h2>';
if (is_user_logged_in()) {
    $user_id = get_current_user_id();
    $user = wp_get_current_user();
    
    echo '<ul>';
    echo '<li>User ID: ' . $user_id . '</li>';
    echo '<li>Username: ' . $user->user_login . '</li>';
    echo '<li>Email: ' . $user->user_email . '</li>';
    
    if (function_exists('deshiflix_premium')) {
        $is_premium = deshiflix_premium()->is_user_premium($user_id);
        echo '<li>Premium Status: ' . ($is_premium ? '✅ Premium' : '❌ Free') . '</li>';
    }
    
    echo '</ul>';
} else {
    echo '<p>Not logged in</p>';
}

// Check hooks
echo '<h2>WordPress Hooks</h2>';
echo '<ul>';

$hooks_to_check = array(
    'init',
    'wp_footer',
    'admin_menu',
    'wp_enqueue_scripts'
);

foreach ($hooks_to_check as $hook) {
    $has_actions = has_action($hook);
    echo '<li>' . $hook . ': ' . ($has_actions ? '✅ Has Actions' : '❌ No Actions') . '</li>';
}

echo '</ul>';

// Memory usage
echo '<h2>System Info</h2>';
echo '<ul>';
echo '<li>PHP Version: ' . PHP_VERSION . '</li>';
echo '<li>WordPress Version: ' . get_bloginfo('version') . '</li>';
echo '<li>Memory Usage: ' . size_format(memory_get_usage(true)) . '</li>';
echo '<li>Memory Limit: ' . ini_get('memory_limit') . '</li>';
echo '</ul>';

// Test basic functionality
echo '<h2>Functionality Tests</h2>';
echo '<ul>';

// Test database connection
try {
    $wpdb->get_var("SELECT 1");
    echo '<li>Database Connection: ✅ Working</li>';
} catch (Exception $e) {
    echo '<li>Database Connection: ❌ Error - ' . $e->getMessage() . '</li>';
}

// Test premium system initialization
try {
    if (function_exists('deshiflix_premium')) {
        $premium = deshiflix_premium();
        echo '<li>Premium System Init: ✅ Working</li>';
    } else {
        echo '<li>Premium System Init: ❌ Function not available</li>';
    }
} catch (Exception $e) {
    echo '<li>Premium System Init: ❌ Error - ' . $e->getMessage() . '</li>';
}

echo '</ul>';

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>';
?>
