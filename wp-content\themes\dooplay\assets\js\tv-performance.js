/**
 * Android TV Performance Optimization
 * Handles lazy loading, memory management, and rendering optimizations
 */

(function($) {
    'use strict';

    var TVPerformance = {
        isAndroidTV: false,
        lazyLoadObserver: null,
        intersectionObserver: null,
        memoryMonitor: null,
        renderQueue: [],
        isProcessingQueue: false,
        
        // Performance settings
        settings: {
            lazyLoadThreshold: 0.1,
            maxConcurrentImages: 6,
            memoryCheckInterval: 30000, // 30 seconds
            maxMemoryUsage: 0.8, // 80% of available memory
            imageQuality: 0.8,
            enableWebP: true,
            preloadDistance: 2, // Number of items to preload ahead
            debounceDelay: 100
        },
        
        init: function() {
            if (!window.AndroidTV || !window.AndroidTV.isAndroidTV) return;
            
            this.isAndroidTV = true;
            this.detectDeviceCapabilities();
            this.setupLazyLoading();
            this.setupMemoryMonitoring();
            this.setupRenderOptimization();
            this.optimizeImages();
            this.bindEvents();
            
            console.log('TV Performance optimization initialized');
        },
        
        detectDeviceCapabilities: function() {
            // Detect device capabilities and adjust settings
            var memory = navigator.deviceMemory || 4; // Default to 4GB
            var cores = navigator.hardwareConcurrency || 4;
            
            // Adjust settings based on device capabilities
            if (memory < 2) {
                this.settings.maxConcurrentImages = 3;
                this.settings.imageQuality = 0.6;
                this.settings.preloadDistance = 1;
            } else if (memory < 4) {
                this.settings.maxConcurrentImages = 4;
                this.settings.imageQuality = 0.7;
                this.settings.preloadDistance = 1;
            }
            
            if (cores < 4) {
                this.settings.debounceDelay = 150;
            }
            
            console.log('Device capabilities:', { memory: memory + 'GB', cores: cores });
        },
        
        setupLazyLoading: function() {
            if (!('IntersectionObserver' in window)) {
                // Fallback for older browsers
                this.setupFallbackLazyLoading();
                return;
            }
            
            var self = this;
            
            this.lazyLoadObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        self.loadImage(entry.target);
                        self.lazyLoadObserver.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px',
                threshold: this.settings.lazyLoadThreshold
            });
            
            // Observe all images
            this.observeImages();
        },
        
        observeImages: function() {
            var images = $('img[data-src], .item .poster img').get();
            var self = this;
            
            images.forEach(function(img) {
                if (!img.dataset.observed) {
                    // Replace src with data-src for lazy loading
                    if (img.src && !img.dataset.src) {
                        img.dataset.src = img.src;
                        img.src = self.generatePlaceholder(img);
                    }
                    
                    img.dataset.observed = 'true';
                    self.lazyLoadObserver.observe(img);
                }
            });
        },
        
        generatePlaceholder: function(img) {
            // Generate a lightweight placeholder
            var width = img.offsetWidth || 300;
            var height = img.offsetHeight || 200;
            
            var canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            
            var ctx = canvas.getContext('2d');
            var gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#2c2c54');
            gradient.addColorStop(1, '#40407a');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // Add TV icon
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.font = Math.min(width, height) / 4 + 'px FontAwesome';
            ctx.textAlign = 'center';
            ctx.fillText('📺', width / 2, height / 2 + 10);
            
            return canvas.toDataURL('image/jpeg', 0.3);
        },
        
        loadImage: function(img) {
            var self = this;
            var src = img.dataset.src;
            
            if (!src) return;
            
            // Add to render queue
            this.renderQueue.push({
                type: 'image',
                element: img,
                src: src,
                priority: this.getImagePriority(img)
            });
            
            this.processRenderQueue();
        },
        
        getImagePriority: function(img) {
            var rect = img.getBoundingClientRect();
            var viewportHeight = window.innerHeight;
            
            // Higher priority for images in viewport
            if (rect.top >= 0 && rect.top <= viewportHeight) {
                return 1; // High priority
            } else if (rect.top <= viewportHeight * 2) {
                return 2; // Medium priority
            } else {
                return 3; // Low priority
            }
        },
        
        processRenderQueue: function() {
            if (this.isProcessingQueue) return;
            
            this.isProcessingQueue = true;
            var self = this;
            
            // Sort by priority
            this.renderQueue.sort(function(a, b) {
                return a.priority - b.priority;
            });
            
            // Process items in batches
            var batchSize = this.settings.maxConcurrentImages;
            var batch = this.renderQueue.splice(0, batchSize);
            
            var promises = batch.map(function(item) {
                return self.processRenderItem(item);
            });
            
            Promise.all(promises).then(function() {
                self.isProcessingQueue = false;
                
                // Process next batch if queue not empty
                if (self.renderQueue.length > 0) {
                    setTimeout(function() {
                        self.processRenderQueue();
                    }, self.settings.debounceDelay);
                }
            });
        },
        
        processRenderItem: function(item) {
            return new Promise(function(resolve) {
                if (item.type === 'image') {
                    var img = new Image();
                    
                    img.onload = function() {
                        item.element.src = item.src;
                        item.element.classList.add('loaded');
                        resolve();
                    };
                    
                    img.onerror = function() {
                        item.element.classList.add('error');
                        resolve();
                    };
                    
                    img.src = item.src;
                } else {
                    resolve();
                }
            });
        },
        
        setupMemoryMonitoring: function() {
            if (!('memory' in performance)) return;
            
            var self = this;
            
            this.memoryMonitor = setInterval(function() {
                self.checkMemoryUsage();
            }, this.settings.memoryCheckInterval);
        },
        
        checkMemoryUsage: function() {
            if (!('memory' in performance)) return;
            
            var memory = performance.memory;
            var usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
            
            if (usageRatio > this.settings.maxMemoryUsage) {
                console.warn('High memory usage detected:', Math.round(usageRatio * 100) + '%');
                this.performMemoryCleanup();
            }
        },
        
        performMemoryCleanup: function() {
            // Clear render queue
            this.renderQueue = [];
            
            // Remove non-visible images from DOM
            this.unloadOffscreenImages();
            
            // Force garbage collection if available
            if (window.gc) {
                window.gc();
            }
            
            console.log('Memory cleanup performed');
        },
        
        unloadOffscreenImages: function() {
            var images = $('img.loaded').get();
            var viewportHeight = window.innerHeight;
            
            images.forEach(function(img) {
                var rect = img.getBoundingClientRect();
                
                // Unload images far from viewport
                if (rect.bottom < -viewportHeight || rect.top > viewportHeight * 3) {
                    if (img.dataset.src) {
                        img.src = img.dataset.placeholder || '';
                        img.classList.remove('loaded');
                    }
                }
            });
        },
        
        setupRenderOptimization: function() {
            // Use requestAnimationFrame for smooth animations
            this.setupRAFOptimization();
            
            // Optimize CSS animations for TV
            this.optimizeCSSAnimations();
            
            // Setup viewport-based rendering
            this.setupViewportRendering();
        },
        
        setupRAFOptimization: function() {
            var self = this;
            var rafCallbacks = [];
            var isRAFScheduled = false;
            
            function processRAFCallbacks() {
                var callbacks = rafCallbacks.slice();
                rafCallbacks = [];
                isRAFScheduled = false;
                
                callbacks.forEach(function(callback) {
                    try {
                        callback();
                    } catch (e) {
                        console.error('RAF callback error:', e);
                    }
                });
            }
            
            window.TVPerformance.scheduleRAF = function(callback) {
                rafCallbacks.push(callback);
                
                if (!isRAFScheduled) {
                    isRAFScheduled = true;
                    requestAnimationFrame(processRAFCallbacks);
                }
            };
        },
        
        optimizeCSSAnimations: function() {
            // Add CSS rules for better TV performance
            var style = document.createElement('style');
            style.textContent = `
                .android-tv-mode * {
                    transform-style: preserve-3d;
                    backface-visibility: hidden;
                }
                
                .android-tv-mode .tv-focused {
                    will-change: transform, box-shadow;
                }
                
                .android-tv-mode .item {
                    contain: layout style paint;
                }
                
                @media (prefers-reduced-motion: reduce) {
                    .android-tv-mode * {
                        animation-duration: 0.01ms !important;
                        animation-iteration-count: 1 !important;
                        transition-duration: 0.01ms !important;
                    }
                }
            `;
            document.head.appendChild(style);
        },
        
        setupViewportRendering: function() {
            if (!('IntersectionObserver' in window)) return;
            
            var self = this;
            
            this.intersectionObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    var element = entry.target;
                    
                    if (entry.isIntersecting) {
                        element.classList.add('in-viewport');
                        self.enableElement(element);
                    } else {
                        element.classList.remove('in-viewport');
                        self.disableElement(element);
                    }
                });
            }, {
                rootMargin: '100px',
                threshold: 0
            });
            
            // Observe all items
            $('.item').each(function() {
                self.intersectionObserver.observe(this);
            });
        },
        
        enableElement: function(element) {
            // Enable animations and interactions
            $(element).removeClass('tv-disabled');
        },
        
        disableElement: function(element) {
            // Disable animations for off-screen elements
            $(element).addClass('tv-disabled');
        },
        
        optimizeImages: function() {
            // Convert images to WebP if supported
            if (this.settings.enableWebP && this.supportsWebP()) {
                this.convertToWebP();
            }
            
            // Optimize image loading
            this.setupImageOptimization();
        },
        
        supportsWebP: function() {
            var canvas = document.createElement('canvas');
            canvas.width = 1;
            canvas.height = 1;
            return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
        },
        
        convertToWebP: function() {
            $('img[data-src]').each(function() {
                var src = this.dataset.src;
                if (src && !src.includes('.webp')) {
                    // Add WebP version if available
                    var webpSrc = src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
                    this.dataset.srcWebp = webpSrc;
                }
            });
        },
        
        setupImageOptimization: function() {
            // Preload critical images
            this.preloadCriticalImages();
            
            // Setup progressive loading
            this.setupProgressiveLoading();
        },
        
        preloadCriticalImages: function() {
            var criticalImages = $('.item:lt(6) img[data-src]').get();
            var self = this;
            
            criticalImages.forEach(function(img) {
                self.loadImage(img);
            });
        },
        
        setupProgressiveLoading: function() {
            var self = this;
            
            $(window).on('scroll.tv-performance', this.debounce(function() {
                self.observeImages();
            }, this.settings.debounceDelay));
        },
        
        setupFallbackLazyLoading: function() {
            var self = this;
            
            $(window).on('scroll.tv-fallback resize.tv-fallback', this.debounce(function() {
                self.checkImagesInViewport();
            }, this.settings.debounceDelay));
            
            this.checkImagesInViewport();
        },
        
        checkImagesInViewport: function() {
            var self = this;
            
            $('img[data-src]').each(function() {
                if (self.isInViewport(this)) {
                    self.loadImage(this);
                }
            });
        },
        
        isInViewport: function(element) {
            var rect = element.getBoundingClientRect();
            var viewportHeight = window.innerHeight;
            
            return rect.top < viewportHeight && rect.bottom > 0;
        },
        
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        bindEvents: function() {
            var self = this;
            
            // Handle page visibility changes
            $(document).on('visibilitychange', function() {
                if (document.hidden) {
                    self.pauseOptimizations();
                } else {
                    self.resumeOptimizations();
                }
            });
            
            // Handle focus changes
            $(document).on('tv-focus', function() {
                self.optimizeForFocus();
            });
        },
        
        pauseOptimizations: function() {
            // Pause memory monitoring
            if (this.memoryMonitor) {
                clearInterval(this.memoryMonitor);
            }
            
            // Clear render queue
            this.renderQueue = [];
        },
        
        resumeOptimizations: function() {
            // Resume memory monitoring
            this.setupMemoryMonitoring();
            
            // Re-observe images
            this.observeImages();
        },
        
        optimizeForFocus: function() {
            // Preload images around focused element
            var focused = $('.tv-focused');
            if (focused.length > 0) {
                var siblings = focused.parent().siblings().slice(0, this.settings.preloadDistance);
                siblings.find('img[data-src]').each(function() {
                    TVPerformance.loadImage(this);
                });
            }
        },
        
        // Cleanup
        destroy: function() {
            if (this.lazyLoadObserver) {
                this.lazyLoadObserver.disconnect();
            }
            
            if (this.intersectionObserver) {
                this.intersectionObserver.disconnect();
            }
            
            if (this.memoryMonitor) {
                clearInterval(this.memoryMonitor);
            }
            
            $(window).off('.tv-performance .tv-fallback');
            $(document).off('.tv-performance');
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        TVPerformance.init();
    });

    // Make TVPerformance globally available
    window.TVPerformance = TVPerformance;

})(jQuery);
