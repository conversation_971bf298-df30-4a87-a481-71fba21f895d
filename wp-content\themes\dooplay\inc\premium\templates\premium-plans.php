<?php
/**
 * Premium Plans Template
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get premium plans
global $wpdb;
$table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
$plans = $wpdb->get_results("SELECT * FROM $table_plans WHERE status = 'active' ORDER BY price ASC");

$current_user_id = get_current_user_id();
$is_premium = false;
$current_plan = null;

if ($current_user_id && function_exists('deshiflix_premium')) {
    $premium_core = deshiflix_premium();
    $is_premium = $premium_core->is_user_premium($current_user_id);
    if ($is_premium) {
        $current_plan = $premium_core->get_user_premium_details($current_user_id);
    }
}
?>

<div class="premium-plans-page">
    <div class="container">
        <!-- Header Section -->
        <div class="plans-header">
            <h1 class="plans-title">
                <i class="fas fa-crown"></i>
                <?php _e('Choose Your Premium Plan', 'deshiflix'); ?>
            </h1>
            <p class="plans-subtitle">
                <?php _e('Unlock unlimited entertainment with our premium subscription plans', 'deshiflix'); ?>
            </p>
            
            <?php if ($is_premium && $current_plan): ?>
                <div class="current-plan-status">
                    <div class="status-card">
                        <i class="fas fa-check-circle"></i>
                        <span><?php printf(__('You are currently on %s plan', 'deshiflix'), $current_plan['plan_name']); ?></span>
                        <small><?php printf(__('Expires: %s', 'deshiflix'), date('F j, Y', strtotime($current_plan['expires_at']))); ?></small>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Features Comparison -->
        <div class="features-comparison">
            <h2><?php _e('What You Get with Premium', 'deshiflix'); ?></h2>
            <div class="features-grid">
                <div class="feature-item">
                    <i class="fas fa-video"></i>
                    <h3><?php _e('HD & 4K Quality', 'deshiflix'); ?></h3>
                    <p><?php _e('Watch in stunning high definition and 4K resolution', 'deshiflix'); ?></p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-download"></i>
                    <h3><?php _e('Download Content', 'deshiflix'); ?></h3>
                    <p><?php _e('Download movies and shows for offline viewing', 'deshiflix'); ?></p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-ban"></i>
                    <h3><?php _e('Ad-Free Experience', 'deshiflix'); ?></h3>
                    <p><?php _e('Enjoy uninterrupted streaming without ads', 'deshiflix'); ?></p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-clock"></i>
                    <h3><?php _e('Early Access', 'deshiflix'); ?></h3>
                    <p><?php _e('Get early access to new releases and exclusive content', 'deshiflix'); ?></p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-devices"></i>
                    <h3><?php _e('Multiple Devices', 'deshiflix'); ?></h3>
                    <p><?php _e('Stream on multiple devices simultaneously', 'deshiflix'); ?></p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-headset"></i>
                    <h3><?php _e('Priority Support', 'deshiflix'); ?></h3>
                    <p><?php _e('Get priority customer support and assistance', 'deshiflix'); ?></p>
                </div>
            </div>
        </div>

        <!-- Pricing Plans -->
        <div class="pricing-plans">
            <h2><?php _e('Choose Your Plan', 'deshiflix'); ?></h2>
            <div class="plans-grid">
                <?php foreach ($plans as $index => $plan): 
                    $features = json_decode($plan->features, true) ?: array();
                    $is_popular = $index === 1; // Make middle plan popular
                    $is_current_plan = $is_premium && $current_plan && $current_plan['plan_id'] == $plan->id;
                ?>
                <div class="plan-card <?php echo $is_popular ? 'popular' : ''; ?> <?php echo $is_current_plan ? 'current' : ''; ?>">
                    <?php if ($is_popular): ?>
                        <div class="popular-badge">
                            <i class="fas fa-star"></i>
                            <?php _e('Most Popular', 'deshiflix'); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($is_current_plan): ?>
                        <div class="current-badge">
                            <i class="fas fa-check"></i>
                            <?php _e('Current Plan', 'deshiflix'); ?>
                        </div>
                    <?php endif; ?>

                    <div class="plan-header">
                        <h3 class="plan-name"><?php echo esc_html($plan->name); ?></h3>
                        <div class="plan-price">
                            <span class="currency">৳</span>
                            <span class="amount"><?php echo number_format($plan->price); ?></span>
                            <span class="period">/<?php echo $plan->duration_days; ?> days</span>
                        </div>
                        <?php if ($plan->original_price > $plan->price): ?>
                            <div class="plan-discount">
                                <span class="original-price">৳<?php echo number_format($plan->original_price); ?></span>
                                <span class="discount-percent">
                                    <?php echo round((($plan->original_price - $plan->price) / $plan->original_price) * 100); ?>% OFF
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="plan-description">
                        <p><?php echo esc_html($plan->description); ?></p>
                    </div>

                    <div class="plan-features">
                        <ul>
                            <li><i class="fas fa-check"></i> <?php printf(__('Up to %d devices', 'deshiflix'), $plan->max_devices); ?></li>
                            <li><i class="fas fa-check"></i> <?php printf(__('%d downloads per month', 'deshiflix'), $plan->download_limit); ?></li>
                            <li><i class="fas fa-check"></i> <?php printf(__('%s quality streaming', 'deshiflix'), $plan->quality_limit); ?></li>
                            
                            <?php if (!empty($features)): ?>
                                <?php foreach ($features as $feature): ?>
                                    <li><i class="fas fa-check"></i> <?php echo esc_html($feature); ?></li>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </ul>
                    </div>

                    <div class="plan-action">
                        <?php if (!is_user_logged_in()): ?>
                            <a href="<?php echo wp_login_url(get_permalink()); ?>" class="btn btn-plan">
                                <?php _e('Login to Subscribe', 'deshiflix'); ?>
                            </a>
                        <?php elseif ($is_current_plan): ?>
                            <a href="/premium-dashboard/" class="btn btn-current">
                                <?php _e('Manage Plan', 'deshiflix'); ?>
                            </a>
                        <?php else: ?>
                            <button class="btn btn-plan" onclick="showPaymentModal(<?php echo $plan->id; ?>, '<?php echo esc_js($plan->name); ?>', <?php echo $plan->price; ?>)">
                                <?php echo $is_premium ? __('Upgrade Plan', 'deshiflix') : __('Subscribe Now', 'deshiflix'); ?>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="plans-faq">
            <h2><?php _e('Frequently Asked Questions', 'deshiflix'); ?></h2>
            <div class="faq-grid">
                <div class="faq-item">
                    <h3><?php _e('Can I cancel anytime?', 'deshiflix'); ?></h3>
                    <p><?php _e('Yes, you can cancel your subscription at any time. Your access will continue until the end of your billing period.', 'deshiflix'); ?></p>
                </div>
                <div class="faq-item">
                    <h3><?php _e('What payment methods do you accept?', 'deshiflix'); ?></h3>
                    <p><?php _e('We accept bKash, Nagad, Rocket, and all major credit/debit cards through SSLCommerz and aamarPay.', 'deshiflix'); ?></p>
                </div>
                <div class="faq-item">
                    <h3><?php _e('Can I change my plan later?', 'deshiflix'); ?></h3>
                    <p><?php _e('Yes, you can upgrade or downgrade your plan at any time. Changes will take effect immediately.', 'deshiflix'); ?></p>
                </div>
                <div class="faq-item">
                    <h3><?php _e('Is there a free trial?', 'deshiflix'); ?></h3>
                    <p><?php _e('New users get 3 days free trial with any premium plan. No commitment required.', 'deshiflix'); ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.premium-plans-page {
    padding: 40px 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.plans-header {
    text-align: center;
    margin-bottom: 60px;
}

.plans-title {
    font-size: 3rem;
    color: #333;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.plans-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 30px;
}

.current-plan-status {
    margin-top: 20px;
}

.status-card {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    padding: 15px 25px;
    border-radius: 25px;
    font-weight: bold;
}

.features-comparison {
    margin-bottom: 60px;
}

.features-comparison h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 40px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.feature-item {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-item i {
    font-size: 3rem;
    color: #FFD700;
    margin-bottom: 20px;
}

.feature-item h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 15px;
}

.pricing-plans h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 40px;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.plan-card {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.plan-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.plan-card.popular {
    border: 3px solid #FFD700;
    transform: scale(1.05);
}

.plan-card.current {
    border: 3px solid #4CAF50;
}

.popular-badge, .current-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.popular-badge {
    background: linear-gradient(45deg, #FFD700, #FFA500);
}

.current-badge {
    background: linear-gradient(45deg, #4CAF50, #45a049);
}

.plan-header {
    margin-bottom: 30px;
}

.plan-name {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 15px;
}

.plan-price {
    font-size: 3rem;
    font-weight: bold;
    color: #FFD700;
    margin-bottom: 10px;
}

.plan-price .period {
    font-size: 1rem;
    color: #666;
}

.plan-discount {
    display: flex;
    justify-content: center;
    gap: 10px;
    align-items: center;
}

.original-price {
    text-decoration: line-through;
    color: #999;
}

.discount-percent {
    background: #ff4444;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 30px 0;
}

.plan-features li {
    padding: 8px 0;
    color: #555;
}

.plan-features i {
    color: #4CAF50;
    margin-right: 10px;
}

.btn {
    display: inline-block;
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.btn-plan {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #000;
}

.btn-plan:hover {
    background: linear-gradient(45deg, #FFA500, #FF8C00);
    transform: translateY(-2px);
}

.btn-current {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.plans-faq {
    background: white;
    padding: 60px 40px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.plans-faq h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 40px;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.faq-item h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .plans-title {
        font-size: 2rem;
    }
    
    .plans-grid {
        grid-template-columns: 1fr;
    }
    
    .plan-card.popular {
        transform: none;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}
</style>
