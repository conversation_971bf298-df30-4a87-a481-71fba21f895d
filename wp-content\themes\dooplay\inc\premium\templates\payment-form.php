<?php
/**
 * Premium Payment Form Template
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

$plan_id = isset($atts['plan_id']) ? intval($atts['plan_id']) : 0;
$user_id = get_current_user_id();

// Get payment gateways
$payment_system = DeshiFlix_Premium_Payment::get_instance();
$gateways = $payment_system->get_enabled_gateways();

// Get plan details if plan_id is provided
$plan = null;
if ($plan_id) {
    global $wpdb;
    $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
    $plan = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_plans WHERE id = %d AND status = 'active'",
        $plan_id
    ));
}

// Get all plans if no specific plan
if (!$plan) {
    $premium_user = DeshiFlix_Premium_User::get_instance();
    $all_plans = $premium_user->get_premium_plans();
}
?>

<div class="premium-payment-form-container">
    <div class="payment-form-header">
        <h2 class="payment-title">
            <span class="payment-icon">💳</span>
            <?php _e('Complete Your Premium Subscription', 'deshiflix'); ?>
        </h2>
        <p class="payment-subtitle">
            <?php _e('Choose your payment method and complete the subscription process', 'deshiflix'); ?>
        </p>
    </div>
    
    <div class="payment-form-content">
        <div class="payment-form-layout">
            <!-- Plan Selection Section -->
            <div class="payment-plan-section">
                <h3><?php _e('Selected Plan', 'deshiflix'); ?></h3>
                
                <?php if ($plan): ?>
                    <div class="selected-plan-card">
                        <div class="plan-info">
                            <h4 class="plan-name"><?php echo esc_html($plan->name); ?></h4>
                            <p class="plan-description"><?php echo esc_html($plan->description); ?></p>
                            <div class="plan-price">
                                <span class="price-amount">৳<?php echo number_format($plan->price, 0); ?></span>
                                <?php if ($plan->original_price && $plan->original_price > $plan->price): ?>
                                    <span class="price-original">৳<?php echo number_format($plan->original_price, 0); ?></span>
                                    <span class="price-discount">
                                        <?php 
                                        $discount = round((($plan->original_price - $plan->price) / $plan->original_price) * 100);
                                        printf(__('%d%% OFF', 'deshiflix'), $discount);
                                        ?>
                                    </span>
                                <?php endif; ?>
                                <span class="price-period">
                                    /<?php echo $plan->duration_days > 30 ? __('year', 'deshiflix') : __('month', 'deshiflix'); ?>
                                </span>
                            </div>
                            
                            <div class="plan-features">
                                <?php 
                                $features = json_decode($plan->features, true);
                                if ($features):
                                ?>
                                <ul class="features-list">
                                    <?php if (isset($features['hd_quality']) && $features['hd_quality']): ?>
                                        <li>✅ <?php _e('HD/4K Quality', 'deshiflix'); ?></li>
                                    <?php endif; ?>
                                    <?php if (isset($features['ad_free']) && $features['ad_free']): ?>
                                        <li>✅ <?php _e('Ad-Free Experience', 'deshiflix'); ?></li>
                                    <?php endif; ?>
                                    <?php if (isset($features['download_links']) && $features['download_links']): ?>
                                        <li>✅ <?php _e('Download Access', 'deshiflix'); ?></li>
                                    <?php endif; ?>
                                    <?php if (isset($features['early_access']) && $features['early_access']): ?>
                                        <li>✅ <?php _e('Early Access', 'deshiflix'); ?></li>
                                    <?php endif; ?>
                                    <li>📱 <?php printf(__('Up to %d devices', 'deshiflix'), $plan->max_devices); ?></li>
                                </ul>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <button class="btn btn-change-plan" id="change-plan-btn">
                            <?php _e('Change Plan', 'deshiflix'); ?>
                        </button>
                    </div>
                <?php else: ?>
                    <div class="plan-selection">
                        <p><?php _e('Please select a plan to continue:', 'deshiflix'); ?></p>
                        <div class="plans-grid">
                            <?php if (isset($all_plans)): foreach ($all_plans as $available_plan): ?>
                                <div class="plan-option" data-plan-id="<?php echo esc_attr($available_plan->id); ?>">
                                    <div class="plan-option-header">
                                        <h4><?php echo esc_html($available_plan->name); ?></h4>
                                        <div class="plan-option-price">
                                            ৳<?php echo number_format($available_plan->price, 0); ?>
                                            <span>/<?php echo $available_plan->duration_days > 30 ? __('year', 'deshiflix') : __('month', 'deshiflix'); ?></span>
                                        </div>
                                    </div>
                                    <button class="btn btn-select-plan" data-plan-id="<?php echo esc_attr($available_plan->id); ?>">
                                        <?php _e('Select', 'deshiflix'); ?>
                                    </button>
                                </div>
                            <?php endforeach; endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Payment Method Section -->
            <div class="payment-method-section">
                <h3><?php _e('Choose Payment Method', 'deshiflix'); ?></h3>
                
                <div class="payment-methods-grid">
                    <?php foreach ($gateways as $gateway_id => $gateway): ?>
                        <div class="payment-method-option" data-gateway="<?php echo esc_attr($gateway_id); ?>">
                            <input type="radio" name="payment_method" value="<?php echo esc_attr($gateway_id); ?>" id="payment_<?php echo esc_attr($gateway_id); ?>">
                            <label for="payment_<?php echo esc_attr($gateway_id); ?>" class="payment-method-label">
                                <div class="payment-method-icon">
                                    <img src="<?php echo esc_url($gateway['icon']); ?>" alt="<?php echo esc_attr($gateway['title']); ?>">
                                </div>
                                <div class="payment-method-info">
                                    <h4><?php echo esc_html($gateway['title']); ?></h4>
                                    <p><?php echo esc_html($gateway['description']); ?></p>
                                    <?php if ($gateway['test_mode']): ?>
                                        <span class="test-mode-badge"><?php _e('Test Mode', 'deshiflix'); ?></span>
                                    <?php endif; ?>
                                </div>
                            </label>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- User Information Section -->
            <div class="payment-user-section">
                <h3><?php _e('Billing Information', 'deshiflix'); ?></h3>
                
                <div class="user-info-form">
                    <?php $current_user = wp_get_current_user(); ?>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="billing_name"><?php _e('Full Name', 'deshiflix'); ?></label>
                            <input type="text" id="billing_name" name="billing_name" value="<?php echo esc_attr($current_user->display_name); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="billing_email"><?php _e('Email Address', 'deshiflix'); ?></label>
                            <input type="email" id="billing_email" name="billing_email" value="<?php echo esc_attr($current_user->user_email); ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="billing_phone"><?php _e('Phone Number', 'deshiflix'); ?></label>
                            <input type="tel" id="billing_phone" name="billing_phone" placeholder="01XXXXXXXXX" required>
                        </div>
                        <div class="form-group">
                            <label for="billing_city"><?php _e('City', 'deshiflix'); ?></label>
                            <select id="billing_city" name="billing_city" required>
                                <option value=""><?php _e('Select City', 'deshiflix'); ?></option>
                                <option value="dhaka"><?php _e('Dhaka', 'deshiflix'); ?></option>
                                <option value="chittagong"><?php _e('Chittagong', 'deshiflix'); ?></option>
                                <option value="sylhet"><?php _e('Sylhet', 'deshiflix'); ?></option>
                                <option value="rajshahi"><?php _e('Rajshahi', 'deshiflix'); ?></option>
                                <option value="khulna"><?php _e('Khulna', 'deshiflix'); ?></option>
                                <option value="barisal"><?php _e('Barisal', 'deshiflix'); ?></option>
                                <option value="rangpur"><?php _e('Rangpur', 'deshiflix'); ?></option>
                                <option value="mymensingh"><?php _e('Mymensingh', 'deshiflix'); ?></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Order Summary Section -->
            <div class="payment-summary-section">
                <h3><?php _e('Order Summary', 'deshiflix'); ?></h3>
                
                <div class="order-summary">
                    <?php if ($plan): ?>
                        <div class="summary-item">
                            <span class="summary-label"><?php echo esc_html($plan->name); ?></span>
                            <span class="summary-value">৳<?php echo number_format($plan->price, 2); ?></span>
                        </div>
                        
                        <?php if ($plan->original_price && $plan->original_price > $plan->price): ?>
                        <div class="summary-item discount">
                            <span class="summary-label"><?php _e('Discount', 'deshiflix'); ?></span>
                            <span class="summary-value">-৳<?php echo number_format($plan->original_price - $plan->price, 2); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <div class="summary-divider"></div>
                        
                        <div class="summary-item total">
                            <span class="summary-label"><?php _e('Total Amount', 'deshiflix'); ?></span>
                            <span class="summary-value">৳<?php echo number_format($plan->price, 2); ?></span>
                        </div>
                    <?php else: ?>
                        <div class="summary-placeholder">
                            <p><?php _e('Select a plan to see pricing details', 'deshiflix'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Terms and Conditions -->
            <div class="payment-terms-section">
                <div class="terms-checkbox">
                    <input type="checkbox" id="accept_terms" name="accept_terms" required>
                    <label for="accept_terms">
                        <?php printf(
                            __('I agree to the %s and %s', 'deshiflix'),
                            '<a href="' . home_url('/terms-of-service/') . '" target="_blank">' . __('Terms of Service', 'deshiflix') . '</a>',
                            '<a href="' . home_url('/privacy-policy/') . '" target="_blank">' . __('Privacy Policy', 'deshiflix') . '</a>'
                        ); ?>
                    </label>
                </div>
                
                <div class="auto-renew-checkbox">
                    <input type="checkbox" id="auto_renew" name="auto_renew" checked>
                    <label for="auto_renew">
                        <?php _e('Automatically renew my subscription', 'deshiflix'); ?>
                        <small><?php _e('You can cancel anytime from your dashboard', 'deshiflix'); ?></small>
                    </label>
                </div>
            </div>
            
            <!-- Payment Button -->
            <div class="payment-action-section">
                <button type="button" id="process-payment-btn" class="btn btn-payment" disabled>
                    <span class="btn-text"><?php _e('Complete Payment', 'deshiflix'); ?></span>
                    <span class="btn-loading" style="display: none;">
                        <span class="spinner"></span>
                        <?php _e('Processing...', 'deshiflix'); ?>
                    </span>
                </button>
                
                <div class="payment-security">
                    <div class="security-badges">
                        <span class="security-badge">
                            <span class="security-icon">🔒</span>
                            <?php _e('Secure Payment', 'deshiflix'); ?>
                        </span>
                        <span class="security-badge">
                            <span class="security-icon">🛡️</span>
                            <?php _e('SSL Protected', 'deshiflix'); ?>
                        </span>
                        <span class="security-badge">
                            <span class="security-icon">💳</span>
                            <?php _e('Bank Grade Security', 'deshiflix'); ?>
                        </span>
                    </div>
                    <p class="security-note">
                        <?php _e('Your payment information is encrypted and secure. We do not store your payment details.', 'deshiflix'); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.premium-payment-form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.payment-form-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.payment-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.payment-icon {
    font-size: 2.5rem;
}

.payment-subtitle {
    color: #666;
    font-size: 1.1rem;
}

.payment-form-content h3 {
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.selected-plan-card {
    background: #f8f9fa;
    border: 2px solid #667eea;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.plan-name {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 5px;
}

.plan-description {
    color: #666;
    margin-bottom: 15px;
}

.plan-price {
    margin-bottom: 15px;
}

.price-amount {
    font-size: 1.8rem;
    font-weight: bold;
    color: #667eea;
}

.price-original {
    text-decoration: line-through;
    color: #999;
    margin-left: 10px;
}

.price-discount {
    background: #e74c3c;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-left: 10px;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    padding: 5px 0;
    color: #333;
}

.payment-methods-grid {
    display: grid;
    gap: 15px;
    margin-bottom: 20px;
}

.payment-method-option {
    border: 2px solid #eee;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.payment-method-option:hover {
    border-color: #667eea;
}

.payment-method-option input[type="radio"] {
    display: none;
}

.payment-method-option input[type="radio"]:checked + .payment-method-label {
    border-color: #667eea;
    background: #f0f4ff;
}

.payment-method-label {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.payment-method-icon img {
    height: 40px;
    width: auto;
}

.payment-method-info h4 {
    margin: 0 0 5px 0;
    color: #333;
}

.payment-method-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.test-mode-badge {
    background: #fff3cd;
    color: #856404;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.order-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.summary-item.total {
    font-weight: bold;
    font-size: 1.1rem;
    color: #333;
}

.summary-item.discount .summary-value {
    color: #e74c3c;
}

.summary-divider {
    height: 1px;
    background: #ddd;
    margin: 15px 0;
}

.terms-checkbox,
.auto-renew-checkbox {
    margin-bottom: 15px;
}

.terms-checkbox label,
.auto-renew-checkbox label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
}

.terms-checkbox input,
.auto-renew-checkbox input {
    margin-top: 3px;
}

.btn-payment {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.btn-payment:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-payment:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 10px;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-size: 0.9rem;
}

.security-note {
    text-align: center;
    color: #666;
    font-size: 0.8rem;
    margin: 0;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .security-badges {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    var selectedPlan = <?php echo $plan ? json_encode($plan) : 'null'; ?>;
    var selectedGateway = null;
    
    // Plan selection
    $('.btn-select-plan').click(function() {
        var planId = $(this).data('plan-id');
        // Reload page with selected plan
        window.location.href = window.location.pathname + '?plan_id=' + planId;
    });
    
    // Payment method selection
    $('input[name="payment_method"]').change(function() {
        selectedGateway = $(this).val();
        updatePaymentButton();
    });
    
    // Terms acceptance
    $('#accept_terms').change(function() {
        updatePaymentButton();
    });
    
    // Update payment button state
    function updatePaymentButton() {
        var termsAccepted = $('#accept_terms').is(':checked');
        var methodSelected = selectedGateway !== null;
        var planSelected = selectedPlan !== null;
        
        $('#process-payment-btn').prop('disabled', !(termsAccepted && methodSelected && planSelected));
    }
    
    // Process payment
    $('#process-payment-btn').click(function() {
        if (!selectedPlan || !selectedGateway) {
            alert('Please select a plan and payment method');
            return;
        }
        
        var $btn = $(this);
        $btn.prop('disabled', true);
        $('.btn-text').hide();
        $('.btn-loading').show();
        
        var formData = {
            action: 'process_premium_payment',
            nonce: deshiflix_premium.nonce,
            plan_id: selectedPlan.id,
            gateway: selectedGateway,
            billing_name: $('#billing_name').val(),
            billing_email: $('#billing_email').val(),
            billing_phone: $('#billing_phone').val(),
            billing_city: $('#billing_city').val(),
            auto_renew: $('#auto_renew').is(':checked') ? 1 : 0
        };
        
        $.ajax({
            url: deshiflix_premium.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Redirect to payment gateway
                    window.location.href = response.data.payment_url;
                } else {
                    alert(response.data.message || 'Payment processing failed');
                    resetPaymentButton();
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
                resetPaymentButton();
            }
        });
    });
    
    function resetPaymentButton() {
        var $btn = $('#process-payment-btn');
        $btn.prop('disabled', false);
        $('.btn-text').show();
        $('.btn-loading').hide();
    }
    
    // Initialize
    updatePaymentButton();
});
</script>
