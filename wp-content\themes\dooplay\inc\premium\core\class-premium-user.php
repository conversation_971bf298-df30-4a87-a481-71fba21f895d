<?php
/**
 * DeshiFlix Premium System - User Management Class
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Premium_User {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize user management
     */
    private function init() {
        add_action('init', array($this, 'init_hooks'));
        add_action('user_register', array($this, 'setup_new_user'));
        add_action('wp_login', array($this, 'check_premium_status'), 10, 2);
    }
    
    /**
     * Initialize hooks
     */
    public function init_hooks() {
        // User profile hooks
        add_action('show_user_profile', array($this, 'add_premium_profile_fields'));
        add_action('edit_user_profile', array($this, 'add_premium_profile_fields'));
        add_action('personal_options_update', array($this, 'save_premium_profile_fields'));
        add_action('edit_user_profile_update', array($this, 'save_premium_profile_fields'));
        
        // AJAX hooks
        add_action('wp_ajax_upgrade_to_premium', array($this, 'ajax_upgrade_to_premium'));
        add_action('wp_ajax_cancel_premium', array($this, 'ajax_cancel_premium'));
        add_action('wp_ajax_get_premium_status', array($this, 'ajax_get_premium_status'));
        
        // Shortcodes
        add_shortcode('premium_status', array($this, 'premium_status_shortcode'));
        add_shortcode('premium_upgrade_button', array($this, 'premium_upgrade_button_shortcode'));
    }
    
    /**
     * Setup new user
     */
    public function setup_new_user($user_id) {
        // Initialize premium status for new user
        $premium_status = array(
            'status' => 'inactive',
            'plan' => null,
            'expires' => null,
            'created' => current_time('mysql')
        );
        
        update_user_meta($user_id, '_deshiflix_premium_status', $premium_status);
    }
    
    /**
     * Check premium status on login
     */
    public function check_premium_status($user_login, $user) {
        $this->update_expired_subscriptions($user->ID);
    }
    
    /**
     * Update expired subscriptions
     */
    public function update_expired_subscriptions($user_id = null) {
        if (!$user_id) {
            // Update all expired subscriptions
            global $wpdb;
            
            $table_premium_users = $wpdb->prefix . 'deshiflix_premium_users';
            
            $expired_users = $wpdb->get_results(
                "SELECT user_id FROM $table_premium_users 
                 WHERE status = 'active' AND end_date < NOW()"
            );
            
            foreach ($expired_users as $expired_user) {
                $this->expire_user_subscription($expired_user->user_id);
            }
        } else {
            // Check specific user
            $premium_status = get_user_meta($user_id, '_deshiflix_premium_status', true);
            
            if ($premium_status && 
                isset($premium_status['status']) && 
                $premium_status['status'] === 'active' &&
                isset($premium_status['expires']) &&
                strtotime($premium_status['expires']) <= time()) {
                
                $this->expire_user_subscription($user_id);
            }
        }
    }
    
    /**
     * Expire user subscription
     */
    public function expire_user_subscription($user_id) {
        global $wpdb;
        
        // Update database
        $table_premium_users = $wpdb->prefix . 'deshiflix_premium_users';
        $wpdb->update(
            $table_premium_users,
            array('status' => 'expired'),
            array('user_id' => $user_id, 'status' => 'active')
        );
        
        // Update user meta
        $premium_status = get_user_meta($user_id, '_deshiflix_premium_status', true);
        if ($premium_status) {
            $premium_status['status'] = 'expired';
            update_user_meta($user_id, '_deshiflix_premium_status', $premium_status);
        }
        
        // Remove premium role
        $user = get_user_by('ID', $user_id);
        if ($user) {
            $user->remove_role('premium_user');
        }
        
        // Send expiration notification
        $this->send_expiration_notification($user_id);
    }
    
    /**
     * Activate premium subscription
     */
    public function activate_premium_subscription($user_id, $plan_id, $duration_days = 30, $payment_method = '') {
        global $wpdb;
        
        $start_date = current_time('mysql');
        $end_date = date('Y-m-d H:i:s', strtotime("+{$duration_days} days"));
        
        // Insert into premium users table
        $table_premium_users = $wpdb->prefix . 'deshiflix_premium_users';
        $result = $wpdb->insert(
            $table_premium_users,
            array(
                'user_id' => $user_id,
                'plan_id' => $plan_id,
                'start_date' => $start_date,
                'end_date' => $end_date,
                'status' => 'active',
                'payment_method' => $payment_method
            )
        );
        
        if ($result) {
            // Update user meta
            $premium_status = array(
                'status' => 'active',
                'plan' => $plan_id,
                'expires' => $end_date,
                'activated' => $start_date
            );
            
            update_user_meta($user_id, '_deshiflix_premium_status', $premium_status);
            
            // Add premium role
            $user = get_user_by('ID', $user_id);
            if ($user) {
                $user->add_role('premium_user');
            }
            
            // Send activation notification
            $this->send_activation_notification($user_id, $plan_id);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Cancel premium subscription
     */
    public function cancel_premium_subscription($user_id) {
        global $wpdb;
        
        // Update database
        $table_premium_users = $wpdb->prefix . 'deshiflix_premium_users';
        $wpdb->update(
            $table_premium_users,
            array('status' => 'cancelled'),
            array('user_id' => $user_id, 'status' => 'active')
        );
        
        // Update user meta
        $premium_status = get_user_meta($user_id, '_deshiflix_premium_status', true);
        if ($premium_status) {
            $premium_status['status'] = 'cancelled';
            update_user_meta($user_id, '_deshiflix_premium_status', $premium_status);
        }
        
        // Remove premium role
        $user = get_user_by('ID', $user_id);
        if ($user) {
            $user->remove_role('premium_user');
        }
        
        // Send cancellation notification
        $this->send_cancellation_notification($user_id);
        
        return true;
    }
    
    /**
     * Get user premium details
     */
    public function get_user_premium_details($user_id) {
        global $wpdb;
        
        $table_premium_users = $wpdb->prefix . 'deshiflix_premium_users';
        $table_premium_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $details = $wpdb->get_row($wpdb->prepare(
            "SELECT pu.*, pp.name as plan_name, pp.price, pp.features 
             FROM $table_premium_users pu 
             LEFT JOIN $table_premium_plans pp ON pu.plan_id = pp.id 
             WHERE pu.user_id = %d AND pu.status = 'active' 
             ORDER BY pu.created_at DESC LIMIT 1",
            $user_id
        ));
        
        return $details;
    }
    
    /**
     * Get premium plans
     */
    public function get_premium_plans() {
        global $wpdb;
        
        $table_premium_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $plans = $wpdb->get_results(
            "SELECT * FROM $table_premium_plans WHERE status = 'active' ORDER BY price ASC"
        );
        
        return $plans;
    }
    
    /**
     * Add premium profile fields
     */
    public function add_premium_profile_fields($user) {
        if (!current_user_can('edit_user', $user->ID)) {
            return;
        }
        
        $premium_details = $this->get_user_premium_details($user->ID);
        $premium_status = get_user_meta($user->ID, '_deshiflix_premium_status', true);
        ?>
        <h3><?php _e('DeshiFlix Premium Status', 'deshiflix'); ?></h3>
        <table class="form-table">
            <tr>
                <th><label><?php _e('Premium Status', 'deshiflix'); ?></label></th>
                <td>
                    <?php if ($premium_details && $premium_details->status === 'active'): ?>
                        <span class="premium-status active">✅ <?php _e('Active', 'deshiflix'); ?></span>
                        <p><strong><?php _e('Plan:', 'deshiflix'); ?></strong> <?php echo esc_html($premium_details->plan_name); ?></p>
                        <p><strong><?php _e('Expires:', 'deshiflix'); ?></strong> <?php echo date('F j, Y', strtotime($premium_details->end_date)); ?></p>
                    <?php else: ?>
                        <span class="premium-status inactive">❌ <?php _e('Inactive', 'deshiflix'); ?></span>
                    <?php endif; ?>
                </td>
            </tr>
            <?php if (current_user_can('manage_options')): ?>
            <tr>
                <th><label><?php _e('Admin Actions', 'deshiflix'); ?></label></th>
                <td>
                    <button type="button" class="button" onclick="managePremiumUser(<?php echo $user->ID; ?>)">
                        <?php _e('Manage Premium', 'deshiflix'); ?>
                    </button>
                </td>
            </tr>
            <?php endif; ?>
        </table>
        <?php
    }
    
    /**
     * Save premium profile fields
     */
    public function save_premium_profile_fields($user_id) {
        if (!current_user_can('edit_user', $user_id)) {
            return;
        }
        
        // Admin can manually update premium status
        if (current_user_can('manage_options') && isset($_POST['premium_status'])) {
            $status = sanitize_text_field($_POST['premium_status']);
            $premium_status = get_user_meta($user_id, '_deshiflix_premium_status', true);
            
            if (!$premium_status) {
                $premium_status = array();
            }
            
            $premium_status['status'] = $status;
            update_user_meta($user_id, '_deshiflix_premium_status', $premium_status);
        }
    }
    
    /**
     * AJAX upgrade to premium
     */
    public function ajax_upgrade_to_premium() {
        check_ajax_referer('deshiflix_premium_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        $plan_id = intval($_POST['plan_id']);
        
        if (!$user_id) {
            wp_send_json_error(array('message' => __('Please login first', 'deshiflix')));
        }
        
        // Get plan details
        global $wpdb;
        $table_premium_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_premium_plans WHERE id = %d AND status = 'active'",
            $plan_id
        ));
        
        if (!$plan) {
            wp_send_json_error(array('message' => __('Invalid plan selected', 'deshiflix')));
        }
        
        // Return payment URL or form
        $payment_url = $this->generate_payment_url($user_id, $plan_id);
        
        wp_send_json_success(array(
            'payment_url' => $payment_url,
            'plan' => $plan
        ));
    }
    
    /**
     * AJAX cancel premium
     */
    public function ajax_cancel_premium() {
        check_ajax_referer('deshiflix_premium_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            wp_send_json_error(array('message' => __('Please login first', 'deshiflix')));
        }
        
        $result = $this->cancel_premium_subscription($user_id);
        
        if ($result) {
            wp_send_json_success(array('message' => __('Premium subscription cancelled', 'deshiflix')));
        } else {
            wp_send_json_error(array('message' => __('Failed to cancel subscription', 'deshiflix')));
        }
    }
    
    /**
     * AJAX get premium status
     */
    public function ajax_get_premium_status() {
        check_ajax_referer('deshiflix_premium_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            wp_send_json_error(array('message' => __('Please login first', 'deshiflix')));
        }
        
        $details = $this->get_user_premium_details($user_id);
        $premium_status = get_user_meta($user_id, '_deshiflix_premium_status', true);
        
        wp_send_json_success(array(
            'details' => $details,
            'status' => $premium_status
        ));
    }
    
    /**
     * Premium status shortcode
     */
    public function premium_status_shortcode($atts) {
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            return '<p>' . __('Please login to view premium status', 'deshiflix') . '</p>';
        }
        
        $details = $this->get_user_premium_details($user_id);
        
        ob_start();
        ?>
        <div class="premium-status-widget">
            <?php if ($details && $details->status === 'active'): ?>
                <div class="premium-active">
                    <h4>✅ <?php _e('Premium Active', 'deshiflix'); ?></h4>
                    <p><strong><?php _e('Plan:', 'deshiflix'); ?></strong> <?php echo esc_html($details->plan_name); ?></p>
                    <p><strong><?php _e('Expires:', 'deshiflix'); ?></strong> <?php echo date('F j, Y', strtotime($details->end_date)); ?></p>
                </div>
            <?php else: ?>
                <div class="premium-inactive">
                    <h4>❌ <?php _e('Premium Inactive', 'deshiflix'); ?></h4>
                    <p><?php _e('Upgrade to premium for exclusive content and features', 'deshiflix'); ?></p>
                </div>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Premium upgrade button shortcode
     */
    public function premium_upgrade_button_shortcode($atts) {
        $atts = shortcode_atts(array(
            'text' => __('Upgrade to Premium', 'deshiflix'),
            'class' => 'btn-premium-upgrade'
        ), $atts);
        
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            return '<a href="' . wp_login_url() . '" class="' . esc_attr($atts['class']) . '">' . 
                   __('Login to Upgrade', 'deshiflix') . '</a>';
        }
        
        if (deshiflix_premium()->is_user_premium($user_id)) {
            return '<span class="premium-active-badge">✅ ' . __('Premium Active', 'deshiflix') . '</span>';
        }
        
        return '<button class="' . esc_attr($atts['class']) . '" onclick="showPremiumUpgrade()">' . 
               esc_html($atts['text']) . '</button>';
    }
    
    /**
     * Generate payment URL
     */
    private function generate_payment_url($user_id, $plan_id) {
        // This will be implemented in the payment class
        return add_query_arg(array(
            'action' => 'premium_payment',
            'user_id' => $user_id,
            'plan_id' => $plan_id,
            'nonce' => wp_create_nonce('premium_payment_' . $user_id . '_' . $plan_id)
        ), home_url('/premium-payment/'));
    }
    
    /**
     * Send activation notification
     */
    private function send_activation_notification($user_id, $plan_id) {
        $user = get_user_by('ID', $user_id);
        if (!$user) return;
        
        $subject = __('Premium Subscription Activated - DeshiFlix', 'deshiflix');
        $message = sprintf(
            __('Congratulations! Your premium subscription has been activated. Enjoy exclusive content and features.', 'deshiflix')
        );
        
        wp_mail($user->user_email, $subject, $message);
    }
    
    /**
     * Send expiration notification
     */
    private function send_expiration_notification($user_id) {
        $user = get_user_by('ID', $user_id);
        if (!$user) return;
        
        $subject = __('Premium Subscription Expired - DeshiFlix', 'deshiflix');
        $message = sprintf(
            __('Your premium subscription has expired. Renew now to continue enjoying exclusive content.', 'deshiflix')
        );
        
        wp_mail($user->user_email, $subject, $message);
    }
    
    /**
     * Send cancellation notification
     */
    private function send_cancellation_notification($user_id) {
        $user = get_user_by('ID', $user_id);
        if (!$user) return;
        
        $subject = __('Premium Subscription Cancelled - DeshiFlix', 'deshiflix');
        $message = sprintf(
            __('Your premium subscription has been cancelled. We hope to see you back soon!', 'deshiflix')
        );
        
        wp_mail($user->user_email, $subject, $message);
    }
}
