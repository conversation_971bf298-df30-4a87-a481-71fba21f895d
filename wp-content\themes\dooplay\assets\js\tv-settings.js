/**
 * Android TV Settings and Preferences Manager
 * Handles TV-specific settings, preferences, and optimizations
 */

(function($) {
    'use strict';

    var TVSettings = {
        settings: {
            highContrast: true,
            largeText: false,
            reducedMotion: false,
            autoplay: true,
            volume: 0.8,
            subtitles: false,
            channelHistory: [],
            favorites: [],
            lastChannel: null,
            focusSound: false,
            navigationSpeed: 'normal' // slow, normal, fast
        },
        
        init: function() {
            this.loadSettings();
            this.applySettings();
            this.setupSettingsMenu();
            this.bindEvents();
        },
        
        loadSettings: function() {
            var saved = localStorage.getItem('tv-settings');
            if (saved) {
                try {
                    var parsed = JSON.parse(saved);
                    this.settings = $.extend(this.settings, parsed);
                } catch (e) {
                    console.warn('Failed to parse TV settings:', e);
                }
            }
        },
        
        saveSettings: function() {
            localStorage.setItem('tv-settings', JSON.stringify(this.settings));
        },
        
        applySettings: function() {
            var body = $('body');
            
            // High contrast
            if (this.settings.highContrast) {
                body.addClass('tv-high-contrast');
            } else {
                body.removeClass('tv-high-contrast');
            }
            
            // Large text
            if (this.settings.largeText) {
                body.addClass('tv-large-text');
            } else {
                body.removeClass('tv-large-text');
            }
            
            // Reduced motion
            if (this.settings.reducedMotion) {
                body.addClass('tv-reduced-motion');
            } else {
                body.removeClass('tv-reduced-motion');
            }
            
            // Navigation speed
            body.removeClass('tv-nav-slow tv-nav-normal tv-nav-fast')
                .addClass('tv-nav-' + this.settings.navigationSpeed);
        },
        
        setupSettingsMenu: function() {
            if (!window.AndroidTV || !window.AndroidTV.isAndroidTV) return;
            
            var settingsMenu = $(`
                <div class="tv-settings-overlay" id="tv-settings" style="display: none;">
                    <div class="tv-settings-panel">
                        <h2>TV Settings</h2>
                        <div class="tv-settings-content">
                            <div class="setting-group">
                                <h3>Display</h3>
                                <div class="setting-item">
                                    <label>High Contrast</label>
                                    <button class="setting-toggle tv-focusable" data-setting="highContrast">
                                        <span class="toggle-state"></span>
                                    </button>
                                </div>
                                <div class="setting-item">
                                    <label>Large Text</label>
                                    <button class="setting-toggle tv-focusable" data-setting="largeText">
                                        <span class="toggle-state"></span>
                                    </button>
                                </div>
                                <div class="setting-item">
                                    <label>Reduced Motion</label>
                                    <button class="setting-toggle tv-focusable" data-setting="reducedMotion">
                                        <span class="toggle-state"></span>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Playback</h3>
                                <div class="setting-item">
                                    <label>Autoplay</label>
                                    <button class="setting-toggle tv-focusable" data-setting="autoplay">
                                        <span class="toggle-state"></span>
                                    </button>
                                </div>
                                <div class="setting-item">
                                    <label>Default Volume</label>
                                    <div class="volume-slider">
                                        <button class="volume-btn tv-focusable" data-action="volume-down">-</button>
                                        <span class="volume-display">80%</span>
                                        <button class="volume-btn tv-focusable" data-action="volume-up">+</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Navigation</h3>
                                <div class="setting-item">
                                    <label>Navigation Speed</label>
                                    <div class="speed-selector">
                                        <button class="speed-btn tv-focusable" data-speed="slow">Slow</button>
                                        <button class="speed-btn tv-focusable" data-speed="normal">Normal</button>
                                        <button class="speed-btn tv-focusable" data-speed="fast">Fast</button>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <label>Focus Sound</label>
                                    <button class="setting-toggle tv-focusable" data-setting="focusSound">
                                        <span class="toggle-state"></span>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="setting-group">
                                <h3>Data</h3>
                                <div class="setting-item">
                                    <button class="btn btn-secondary tv-focusable" id="clear-history">Clear History</button>
                                    <button class="btn btn-secondary tv-focusable" id="clear-favorites">Clear Favorites</button>
                                </div>
                                <div class="setting-item">
                                    <button class="btn btn-primary tv-focusable" id="reset-settings">Reset All Settings</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-footer">
                            <button class="btn btn-primary tv-focusable" id="close-settings">Close</button>
                        </div>
                    </div>
                </div>
            `);
            
            $('body').append(settingsMenu);
            this.updateSettingsDisplay();
        },
        
        bindEvents: function() {
            var self = this;
            
            // Settings toggle buttons
            $(document).on('click', '.setting-toggle', function() {
                var setting = $(this).data('setting');
                self.settings[setting] = !self.settings[setting];
                self.saveSettings();
                self.applySettings();
                self.updateSettingsDisplay();
                
                if (window.AndroidTV) {
                    window.AndroidTV.showNotification(
                        setting.charAt(0).toUpperCase() + setting.slice(1) + 
                        (self.settings[setting] ? ' Enabled' : ' Disabled')
                    );
                }
            });
            
            // Volume controls
            $(document).on('click', '.volume-btn', function() {
                var action = $(this).data('action');
                if (action === 'volume-up') {
                    self.settings.volume = Math.min(1, self.settings.volume + 0.1);
                } else {
                    self.settings.volume = Math.max(0, self.settings.volume - 0.1);
                }
                self.saveSettings();
                self.updateSettingsDisplay();
            });
            
            // Speed selector
            $(document).on('click', '.speed-btn', function() {
                var speed = $(this).data('speed');
                self.settings.navigationSpeed = speed;
                self.saveSettings();
                self.applySettings();
                self.updateSettingsDisplay();
                
                if (window.AndroidTV) {
                    window.AndroidTV.showNotification('Navigation Speed: ' + speed.charAt(0).toUpperCase() + speed.slice(1));
                }
            });
            
            // Clear data buttons
            $(document).on('click', '#clear-history', function() {
                self.settings.channelHistory = [];
                self.saveSettings();
                if (window.AndroidTV) {
                    window.AndroidTV.showNotification('History Cleared');
                }
            });
            
            $(document).on('click', '#clear-favorites', function() {
                self.settings.favorites = [];
                self.saveSettings();
                if (window.AndroidTV) {
                    window.AndroidTV.showNotification('Favorites Cleared');
                }
            });
            
            // Reset settings
            $(document).on('click', '#reset-settings', function() {
                if (confirm('Reset all settings to default?')) {
                    localStorage.removeItem('tv-settings');
                    location.reload();
                }
            });
            
            // Close settings
            $(document).on('click', '#close-settings', function() {
                self.hideSettings();
            });
            
            // Global settings hotkey (Menu button)
            $(document).on('keydown', function(e) {
                if (e.keyCode === 18 || e.keyCode === 93) { // Alt or Menu key
                    e.preventDefault();
                    self.toggleSettings();
                }
            });
        },
        
        updateSettingsDisplay: function() {
            var self = this;
            
            // Update toggle states
            $('.setting-toggle').each(function() {
                var setting = $(this).data('setting');
                var isEnabled = self.settings[setting];
                $(this).toggleClass('enabled', isEnabled);
                $(this).find('.toggle-state').text(isEnabled ? 'ON' : 'OFF');
            });
            
            // Update volume display
            $('.volume-display').text(Math.round(this.settings.volume * 100) + '%');
            
            // Update speed selector
            $('.speed-btn').removeClass('active');
            $('.speed-btn[data-speed="' + this.settings.navigationSpeed + '"]').addClass('active');
        },
        
        showSettings: function() {
            $('#tv-settings').fadeIn(300);
            // Focus first setting
            setTimeout(function() {
                $('#tv-settings .tv-focusable').first().addClass('tv-focused');
            }, 100);
        },
        
        hideSettings: function() {
            $('#tv-settings').fadeOut(300);
            $('.tv-focused').removeClass('tv-focused');
            // Restore focus to main content
            if (window.AndroidTV) {
                window.AndroidTV.initializeFocus();
            }
        },
        
        toggleSettings: function() {
            if ($('#tv-settings').is(':visible')) {
                this.hideSettings();
            } else {
                this.showSettings();
            }
        },
        
        // Channel management
        addToHistory: function(channelId, channelName) {
            var historyItem = {
                id: channelId,
                name: channelName,
                timestamp: Date.now()
            };
            
            // Remove if already exists
            this.settings.channelHistory = this.settings.channelHistory.filter(function(item) {
                return item.id !== channelId;
            });
            
            // Add to beginning
            this.settings.channelHistory.unshift(historyItem);
            
            // Keep only last 20 items
            this.settings.channelHistory = this.settings.channelHistory.slice(0, 20);
            
            this.saveSettings();
        },
        
        addToFavorites: function(channelId, channelName) {
            var favoriteItem = {
                id: channelId,
                name: channelName,
                timestamp: Date.now()
            };
            
            // Check if already exists
            var exists = this.settings.favorites.some(function(item) {
                return item.id === channelId;
            });
            
            if (!exists) {
                this.settings.favorites.push(favoriteItem);
                this.saveSettings();
                return true;
            }
            
            return false;
        },
        
        removeFromFavorites: function(channelId) {
            this.settings.favorites = this.settings.favorites.filter(function(item) {
                return item.id !== channelId;
            });
            this.saveSettings();
        },
        
        isFavorite: function(channelId) {
            return this.settings.favorites.some(function(item) {
                return item.id === channelId;
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        TVSettings.init();
    });

    // Make TVSettings globally available
    window.TVSettings = TVSettings;

})(jQuery);
