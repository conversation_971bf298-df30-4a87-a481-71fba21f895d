/**
 * DeshiFlix Premium Admin Styles
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

/* Admin Dashboard Styles */
.premium-admin-dashboard {
    margin: 20px 0;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    border-left: 4px solid #0073aa;
}

.stat-card.revenue {
    border-left-color: #46b450;
}

.stat-card.users {
    border-left-color: #0073aa;
}

.stat-card.conversion {
    border-left-color: #ffb900;
}

.stat-card.churn {
    border-left-color: #dc3232;
}

.stat-icon {
    font-size: 2.5rem;
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    color: #333;
}

.stat-content p {
    margin: 5px 0;
    color: #666;
}

.stat-change {
    font-size: 0.9rem;
    font-weight: bold;
}

.stat-change.positive {
    color: #46b450;
}

.stat-change.negative {
    color: #dc3232;
}

.stat-change.neutral {
    color: #666;
}

/* Dashboard Actions */
.dashboard-actions {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-buttons .button {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Recent Activity */
.dashboard-recent-activity {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-description {
    color: #333;
}

.activity-time {
    color: #666;
    font-size: 0.9rem;
}

/* Settings Page */
.premium-settings-wrap .nav-tab-wrapper {
    margin-bottom: 20px;
}

.premium-settings-wrap .tab-content {
    display: none;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.premium-settings-wrap .tab-content.active {
    display: block;
}

.settings-section {
    margin-bottom: 30px;
}

.settings-section h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
}

.form-table td {
    padding: 15px 10px;
}

/* Payment Gateway Settings */
.gateway-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.gateway-card.enabled {
    border-color: #46b450;
    box-shadow: 0 2px 8px rgba(70, 180, 80, 0.1);
}

.gateway-card.disabled {
    border-color: #dc3232;
    box-shadow: 0 2px 8px rgba(220, 50, 50, 0.1);
}

.gateway-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.gateway-logo img {
    height: 40px;
    width: auto;
}

.gateway-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.gateway-status.enabled {
    background: #d4edda;
    color: #155724;
}

.gateway-status.disabled {
    background: #f8d7da;
    color: #721c24;
}

/* User Management */
.premium-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.premium-badge.active {
    background: #d4edda;
    color: #155724;
}

.premium-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Analytics Page */
.premium-analytics-wrap {
    margin: 20px 0;
}

.analytics-filters {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
    align-items: end;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: bold;
    color: #333;
}

.filter-group select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.metric-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.metric-icon {
    font-size: 2.5rem;
}

.metric-content h3 {
    margin: 0;
    font-size: 2rem;
    color: #333;
}

.metric-content p {
    margin: 5px 0;
    color: #666;
}

.metric-change {
    font-size: 0.9rem;
    font-weight: bold;
}

.metric-change.positive {
    color: #46b450;
}

.metric-change.negative {
    color: #dc3232;
}

.analytics-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.chart-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-container h3 {
    margin-top: 0;
    color: #333;
}

/* Features Management */
.premium-features-wrap .nav-tab-wrapper {
    margin-bottom: 20px;
}

.premium-features-wrap .tab-content {
    display: none;
}

.premium-features-wrap .tab-content.active {
    display: block;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.feature-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
}

.feature-card.enabled {
    border-color: #46b450;
    box-shadow: 0 2px 8px rgba(70, 180, 80, 0.1);
}

.feature-card.disabled {
    border-color: #dc3232;
    box-shadow: 0 2px 8px rgba(220, 50, 50, 0.1);
}

.feature-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.feature-header h3 {
    margin: 0;
    color: #333;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #46b450;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.feature-description p {
    color: #666;
    margin: 0;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-badge.enabled {
    background: #d4edda;
    color: #155724;
}

.status-badge.disabled {
    background: #f8d7da;
    color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .analytics-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .analytics-charts {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.notice.premium-notice {
    border-left-width: 4px;
    padding: 12px;
}

.notice.premium-notice.notice-success {
    border-left-color: #46b450;
    background: #f0f8f0;
}

.notice.premium-notice.notice-error {
    border-left-color: #dc3232;
    background: #fdf0f0;
}

.notice.premium-notice.notice-warning {
    border-left-color: #ffb900;
    background: #fffbf0;
}

.notice.premium-notice.notice-info {
    border-left-color: #0073aa;
    background: #f0f6fc;
}
