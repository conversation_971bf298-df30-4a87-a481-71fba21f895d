<?php
/*
* ----------------------------------------------------
* @author: DeshiFlix
* @copyright: (c) 2025 DeshiFlix. All rights reserved
* ----------------------------------------------------
* Live TV System for Dooplay Theme
* @since 1.0.0
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class DooLiveTV {
    
    private $table_channels;
    private $table_categories;
    private $table_playlists;
    
    public function __construct() {
        global $wpdb;
        
        $this->table_channels = $wpdb->prefix . 'doo_livetv_channels';
        $this->table_categories = $wpdb->prefix . 'doo_livetv_categories';
        $this->table_playlists = $wpdb->prefix . 'doo_livetv_playlists';
        
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_doo_livetv_import_playlist', array($this, 'ajax_import_playlist'));
        add_action('wp_ajax_doo_livetv_delete_channel', array($this, 'ajax_delete_channel'));
        add_action('wp_ajax_doo_livetv_update_channel', array($this, 'ajax_update_channel'));
        add_action('wp_ajax_doo_livetv_bulk_action', array($this, 'ajax_bulk_action'));
        add_action('wp_ajax_doo_livetv_delete_playlist', array($this, 'ajax_delete_playlist'));
        add_action('wp_ajax_test_stream', array($this, 'ajax_test_stream'));
        add_action('wp_ajax_doo_livetv_check_channels', array($this, 'ajax_check_channels'));
        add_action('wp_ajax_doo_livetv_smart_import', array($this, 'ajax_smart_import'));
        add_action('wp_ajax_doo_livetv_auto_categorize', array($this, 'ajax_auto_categorize'));
        add_action('wp_ajax_doo_livetv_auto_logo', array($this, 'ajax_auto_logo'));
        
        // Create tables on activation
        register_activation_hook(__FILE__, array($this, 'create_tables'));
        
        // Create tables immediately if they don't exist
        add_action('after_setup_theme', array($this, 'check_and_create_tables'));

        // Create Live TV page
        add_action('wp_loaded', array($this, 'create_live_tv_page'));
    }
    
    public function init() {
        // Register custom post type for live TV channels
        $this->register_post_type();

        // Disable custom rewrite rules for now - use direct page approach
        // add_rewrite_rule('^live-tv/?$', 'index.php?live_tv_page=1', 'top');
        // add_rewrite_rule('^live-tv/([^/]+)/?$', 'index.php?live_tv_page=1&channel_slug=$matches[1]', 'top');

        // Add query vars
        add_filter('query_vars', array($this, 'add_query_vars'));

        // Template redirect
        // add_action('template_redirect', array($this, 'template_redirect'));
        add_action('template_redirect', array($this, 'handle_channel_redirect'));

        // Flush rewrite rules on activation
        add_action('wp_loaded', array($this, 'flush_rewrite_rules_maybe'));

        // Add shortcodes
        add_shortcode('live_tv_channels', array($this, 'live_tv_channels_shortcode'));
        add_shortcode('live_tv_single', array($this, 'live_tv_single_shortcode'));
    }
    
    public function check_and_create_tables() {
        global $wpdb;

        // Check if tables exist
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->table_channels}'");

        if ($table_exists != $this->table_channels) {
            $this->create_tables();
        }

        // Force recreate for testing (remove this in production)
        if (!get_option('doo_livetv_tables_created_v3')) {
            $this->create_tables();
            update_option('doo_livetv_tables_created_v3', true);
        }
    }

    public function create_live_tv_page() {
        // Only run once
        if (get_option('doo_livetv_page_created_v2')) {
            return;
        }

        // Check if Live TV page already exists
        $page_exists = get_page_by_path('live-tv');

        if (!$page_exists) {
            // Create Live TV page
            $page_data = array(
                'post_title' => 'Live TV',
                'post_content' => '<p>Watch live TV channels online in HD quality.</p>',
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_name' => 'live-tv',
                'post_author' => 1,
                'menu_order' => 0
            );

            $page_id = wp_insert_post($page_data);

            if ($page_id) {
                // Add custom template meta
                update_post_meta($page_id, '_wp_page_template', 'page-live-tv.php');

                // Mark as created
                update_option('doo_livetv_page_created_v2', true);

                // Force flush rewrite rules
                flush_rewrite_rules();
            }
        } else {
            // Page exists, update template
            update_post_meta($page_exists->ID, '_wp_page_template', 'page-live-tv.php');

            // Mark as created
            update_option('doo_livetv_page_created_v2', true);
        }
    }
    
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Channels table
        $sql_channels = "CREATE TABLE {$this->table_channels} (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            slug varchar(255) NOT NULL,
            description text,
            logo_url varchar(500),
            stream_url varchar(1000) NOT NULL,
            backup_urls text,
            category_id int(11),
            country varchar(100),
            language varchar(100),
            quality varchar(50) DEFAULT 'HD',
            status enum('active','inactive') DEFAULT 'active',
            views int(11) DEFAULT 0,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY slug (slug),
            KEY category_id (category_id),
            KEY status (status),
            KEY sort_order (sort_order)
        ) $charset_collate;";
        
        // Categories table
        $sql_categories = "CREATE TABLE {$this->table_categories} (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            slug varchar(255) NOT NULL,
            description text,
            icon varchar(255),
            color varchar(7) DEFAULT '#007cba',
            sort_order int(11) DEFAULT 0,
            status enum('active','inactive') DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY slug (slug),
            KEY status (status),
            KEY sort_order (sort_order)
        ) $charset_collate;";
        
        // Playlists table
        $sql_playlists = "CREATE TABLE {$this->table_playlists} (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            url varchar(1000) NOT NULL,
            description text,
            last_imported datetime,
            import_count int(11) DEFAULT 0,
            status enum('active','inactive') DEFAULT 'active',
            auto_import enum('yes','no') DEFAULT 'no',
            import_frequency varchar(50) DEFAULT 'daily',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY status (status),
            KEY auto_import (auto_import)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_channels);
        dbDelta($sql_categories);
        dbDelta($sql_playlists);
        
        // Insert default categories
        $this->insert_default_categories();

        // Insert sample channel for testing
        $this->insert_sample_channel();

        // Set flag to flush rewrite rules
        update_option('doo_livetv_flush_rewrite_rules', true);
    }
    
    private function insert_default_categories() {
        global $wpdb;
        
        $default_categories = array(
            array('name' => 'Entertainment', 'slug' => 'entertainment', 'icon' => 'fas fa-tv', 'color' => '#e74c3c'),
            array('name' => 'News', 'slug' => 'news', 'icon' => 'fas fa-newspaper', 'color' => '#3498db'),
            array('name' => 'Sports', 'slug' => 'sports', 'icon' => 'fas fa-futbol', 'color' => '#2ecc71'),
            array('name' => 'Movies', 'slug' => 'movies', 'icon' => 'fas fa-film', 'color' => '#9b59b6'),
            array('name' => 'Music', 'slug' => 'music', 'icon' => 'fas fa-music', 'color' => '#f39c12'),
            array('name' => 'Kids', 'slug' => 'kids', 'icon' => 'fas fa-child', 'color' => '#ff6b6b'),
            array('name' => 'Documentary', 'slug' => 'documentary', 'icon' => 'fas fa-book', 'color' => '#34495e'),
            array('name' => 'Religious', 'slug' => 'religious', 'icon' => 'fas fa-pray', 'color' => '#27ae60'),
            array('name' => 'International', 'slug' => 'international', 'icon' => 'fas fa-globe', 'color' => '#e67e22'),
            array('name' => 'Local', 'slug' => 'local', 'icon' => 'fas fa-map-marker-alt', 'color' => '#1abc9c')
        );
        
        foreach ($default_categories as $category) {
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$this->table_categories} WHERE slug = %s",
                $category['slug']
            ));
            
            if (!$exists) {
                $wpdb->insert($this->table_categories, $category);
            }
        }
    }

    private function insert_sample_channel() {
        global $wpdb;

        // Sample channels data
        $sample_channels = array(
            array(
                'name' => 'Sample News Channel',
                'slug' => 'sample-news',
                'description' => 'Sample news channel for testing.',
                'stream_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                'logo_url' => 'https://via.placeholder.com/300x200/e74c3c/ffffff?text=NEWS',
                'category' => 'news',
                'country' => 'Bangladesh',
                'language' => 'Bengali',
                'quality' => 'HD'
            ),
            array(
                'name' => 'Sample Entertainment',
                'slug' => 'sample-entertainment',
                'description' => 'Sample entertainment channel.',
                'stream_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
                'logo_url' => 'https://via.placeholder.com/300x200/9b59b6/ffffff?text=ENT',
                'category' => 'entertainment',
                'country' => 'Bangladesh',
                'language' => 'Bengali',
                'quality' => 'FHD'
            ),
            array(
                'name' => 'Sample Sports Channel',
                'slug' => 'sample-sports',
                'description' => 'Sample sports channel.',
                'stream_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
                'logo_url' => 'https://via.placeholder.com/300x200/2ecc71/ffffff?text=SPORTS',
                'category' => 'sports',
                'country' => 'Bangladesh',
                'language' => 'Bengali',
                'quality' => 'HD'
            ),
            array(
                'name' => 'Sample Movie Channel',
                'slug' => 'sample-movies',
                'description' => 'Sample movie channel.',
                'stream_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
                'logo_url' => 'https://via.placeholder.com/300x200/f39c12/ffffff?text=MOVIES',
                'category' => 'movies',
                'country' => 'Bangladesh',
                'language' => 'Bengali',
                'quality' => 'FHD'
            ),
            array(
                'name' => 'Sample Music Channel',
                'slug' => 'sample-music',
                'description' => 'Sample music channel.',
                'stream_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
                'logo_url' => 'https://via.placeholder.com/300x200/e67e22/ffffff?text=MUSIC',
                'category' => 'music',
                'country' => 'Bangladesh',
                'language' => 'Bengali',
                'quality' => 'HD'
            ),
            array(
                'name' => 'Sample Kids Channel',
                'slug' => 'sample-kids',
                'description' => 'Sample kids channel.',
                'stream_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4',
                'logo_url' => 'https://via.placeholder.com/300x200/ff6b6b/ffffff?text=KIDS',
                'category' => 'kids',
                'country' => 'Bangladesh',
                'language' => 'Bengali',
                'quality' => 'HD'
            )
        );

        foreach ($sample_channels as $channel_data) {
            // Check if channel already exists
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$this->table_channels} WHERE slug = %s",
                $channel_data['slug']
            ));

            if (!$exists) {
                // Get category ID
                $category_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT id FROM {$this->table_categories} WHERE slug = %s",
                    $channel_data['category']
                ));

                // Insert channel
                $wpdb->insert(
                    $this->table_channels,
                    array(
                        'name' => $channel_data['name'],
                        'slug' => $channel_data['slug'],
                        'description' => $channel_data['description'],
                        'stream_url' => $channel_data['stream_url'],
                        'logo_url' => $channel_data['logo_url'],
                        'category_id' => $category_id,
                        'country' => $channel_data['country'],
                        'language' => $channel_data['language'],
                        'quality' => $channel_data['quality'],
                        'status' => 'active',
                        'views' => rand(100, 5000),
                        'sort_order' => 0
                    ),
                    array('%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%d', '%d')
                );
            }
        }
    }

    public function register_post_type() {
        // This will be used for SEO and additional features
        register_post_type('livetv_channels', array(
            'labels' => array(
                'name' => 'Live TV Channels',
                'singular_name' => 'Live TV Channel',
                'add_new' => 'Add New Channel',
                'add_new_item' => 'Add New Live TV Channel',
                'edit_item' => 'Edit Live TV Channel',
                'new_item' => 'New Live TV Channel',
                'view_item' => 'View Live TV Channel',
                'search_items' => 'Search Live TV Channels',
                'not_found' => 'No Live TV Channels found',
                'not_found_in_trash' => 'No Live TV Channels found in Trash'
            ),
            'public' => true,
            'has_archive' => true,
            'rewrite' => array('slug' => 'live-tv-channel'),
            'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
            'menu_icon' => 'dashicons-video-alt3',
            'show_in_rest' => true,
            'capability_type' => 'post',
            'hierarchical' => false,
            'menu_position' => 25
        ));
    }
    
    public function flush_rewrite_rules_maybe() {
        if (get_option('doo_livetv_flush_rewrite_rules', false)) {
            flush_rewrite_rules();
            delete_option('doo_livetv_flush_rewrite_rules');
        }
    }

    public function add_query_vars($vars) {
        $vars[] = 'live_tv_page';
        $vars[] = 'channel_slug';
        return $vars;
    }

    public function handle_channel_redirect() {
        // If we're on any page with channel parameter but not page 76, redirect
        if (isset($_GET['channel']) && !is_admin() && get_the_ID() != 76) {
            $redirect_url = add_query_arg('channel', sanitize_text_field($_GET['channel']), get_permalink(76));
            wp_redirect($redirect_url, 301);
            exit;
        }
    }
    
    public function template_redirect() {
        // Check for live-tv URL
        $request_uri = $_SERVER['REQUEST_URI'];
        $parsed_url = parse_url($request_uri);
        $path = trim($parsed_url['path'], '/');

        // Handle live-tv URLs
        if ($path === 'live-tv' || strpos($path, 'live-tv') === 0) {
            $this->load_live_tv_template();
            exit;
        }

        if (get_query_var('live_tv_page')) {
            $this->load_live_tv_template();
            exit;
        }
    }
    
    private function load_live_tv_template() {
        // Enqueue scripts and styles
        wp_enqueue_script('doo-livetv-js', get_template_directory_uri() . '/assets/js/livetv.js', array('jquery'), '1.0.0', true);
        wp_enqueue_style('doo-livetv-css', get_template_directory_uri() . '/assets/css/livetv.css', array(), '1.0.0');

        // Localize script for AJAX
        wp_localize_script('doo-livetv-js', 'doo_livetv_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('doo_livetv_nonce'),
            'loading_text' => 'Loading...',
            'error_text' => 'An error occurred. Please try again.',
            'success_text' => 'Operation completed successfully!'
        ));

        // Check for channel parameter
        $channel_slug = isset($_GET['channel']) ? sanitize_text_field($_GET['channel']) : get_query_var('channel_slug');

        if ($channel_slug) {
            // Load single channel template
            $channel = $this->get_channel_by_slug($channel_slug);
            if ($channel) {
                $this->increment_views($channel->id);
                include get_template_directory() . '/inc/parts/livetv-single.php';
            } else {
                // Channel not found, redirect to main page
                wp_redirect(home_url('/live-tv'));
                exit;
            }
        } else {
            // Load live TV grid template
            include get_template_directory() . '/inc/parts/livetv-grid.php';
        }
    }
    
    public function admin_menu() {
        add_menu_page(
            'Live TV Management',
            'Live TV',
            'manage_options',
            'doo-livetv',
            array($this, 'admin_page'),
            'dashicons-video-alt3',
            26
        );
        
        add_submenu_page(
            'doo-livetv',
            'All Channels',
            'All Channels',
            'manage_options',
            'doo-livetv',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'doo-livetv',
            'Add New Channel',
            'Add New',
            'manage_options',
            'doo-livetv-add',
            array($this, 'admin_add_channel')
        );
        
        add_submenu_page(
            'doo-livetv',
            'Categories',
            'Categories',
            'manage_options',
            'doo-livetv-categories',
            array($this, 'admin_categories')
        );
        
        add_submenu_page(
            'doo-livetv',
            'Import Playlist',
            'Import Playlist',
            'manage_options',
            'doo-livetv-import',
            array($this, 'admin_import')
        );
        
        add_submenu_page(
            'doo-livetv',
            'Stream Tester',
            'Stream Tester',
            'manage_options',
            'doo-livetv-stream-tester',
            array($this, 'admin_stream_tester')
        );

        add_submenu_page(
            'doo-livetv',
            'Channel Checker',
            'Channel Checker',
            'manage_options',
            'doo-livetv-channel-checker',
            array($this, 'admin_channel_checker')
        );

        add_submenu_page(
            'doo-livetv',
            'Smart Import',
            'Smart Import',
            'manage_options',
            'doo-livetv-smart-import',
            array($this, 'admin_smart_import')
        );

        add_submenu_page(
            'doo-livetv',
            'Auto Categorize',
            'Auto Categorize',
            'manage_options',
            'doo-livetv-auto-categorize',
            array($this, 'admin_auto_categorize')
        );

        add_submenu_page(
            'doo-livetv',
            'Auto Logo',
            'Auto Logo',
            'manage_options',
            'doo-livetv-auto-logo',
            array($this, 'admin_auto_logo')
        );

        add_submenu_page(
            'doo-livetv',
            'Settings',
            'Settings',
            'manage_options',
            'doo-livetv-settings',
            array($this, 'admin_settings')
        );
    }
    
    public function enqueue_scripts() {
        if (get_query_var('live_tv_page') || is_admin()) {
            wp_enqueue_script('doo-livetv-js', get_template_directory_uri() . '/assets/js/livetv.js', array('jquery'), '1.0.0', true);
            wp_enqueue_style('doo-livetv-css', get_template_directory_uri() . '/assets/css/livetv.css', array(), '1.0.0');
            
            // Localize script for AJAX
            wp_localize_script('doo-livetv-js', 'doo_livetv_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('doo_livetv_nonce'),
                'loading_text' => 'Loading...',
                'error_text' => 'An error occurred. Please try again.',
                'success_text' => 'Operation completed successfully!'
            ));
        }
    }
    
    // Admin page methods
    public function admin_page() {
        include get_template_directory() . '/inc/parts/admin/livetv-channels.php';
    }
    
    public function admin_add_channel() {
        include get_template_directory() . '/inc/parts/admin/livetv-add-channel.php';
    }
    
    public function admin_categories() {
        include get_template_directory() . '/inc/parts/admin/livetv-categories.php';
    }
    
    public function admin_import() {
        include get_template_directory() . '/inc/parts/admin/livetv-import.php';
    }
    
    public function admin_stream_tester() {
        include get_template_directory() . '/inc/parts/admin/livetv-stream-tester.php';
    }

    public function admin_channel_checker() {
        include get_template_directory() . '/inc/parts/admin/livetv-channel-checker.php';
    }

    public function admin_smart_import() {
        include get_template_directory() . '/inc/parts/admin/livetv-smart-import.php';
    }

    public function admin_auto_categorize() {
        include get_template_directory() . '/inc/parts/admin/livetv-auto-categorize.php';
    }

    public function admin_auto_logo() {
        include get_template_directory() . '/inc/parts/admin/livetv-auto-logo.php';
    }

    public function admin_settings() {
        include get_template_directory() . '/inc/parts/admin/livetv-settings.php';
    }

    // M3U Playlist Parser
    public function parse_m3u_playlist($url) {
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ));

        if (is_wp_error($response)) {
            return array('error' => 'Failed to fetch playlist: ' . $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $lines = explode("\n", $body);
        $channels = array();
        $current_channel = array();

        foreach ($lines as $line) {
            $line = trim($line);

            if (strpos($line, '#EXTINF:') === 0) {
                // Parse channel info
                $info = substr($line, 8); // Remove #EXTINF:

                // Extract duration (usually -1 for live streams)
                preg_match('/^(-?\d+)/', $info, $duration_match);
                $duration = isset($duration_match[1]) ? $duration_match[1] : -1;

                // Extract attributes
                preg_match_all('/(\w+)="([^"]*)"/', $info, $attr_matches, PREG_SET_ORDER);
                $attributes = array();
                foreach ($attr_matches as $match) {
                    $attributes[$match[1]] = $match[2];
                }

                // Extract channel name (after last comma)
                $comma_pos = strrpos($info, ',');
                if ($comma_pos !== false) {
                    $name_part = substr($info, $comma_pos + 1);
                    $name = trim($name_part);
                } else {
                    $name = 'Unknown Channel';
                }

                // Clean up name - remove extra attributes
                $name = preg_replace('/\s+/', ' ', $name);
                $name = trim($name);

                $current_channel = array(
                    'name' => $name,
                    'logo' => isset($attributes['tvg-logo']) ? $attributes['tvg-logo'] : '',
                    'group' => isset($attributes['group-title']) ? $attributes['group-title'] : 'General',
                    'country' => isset($attributes['tvg-country']) ? $attributes['tvg-country'] : '',
                    'language' => isset($attributes['tvg-language']) ? $attributes['tvg-language'] : '',
                    'id' => isset($attributes['tvg-id']) ? $attributes['tvg-id'] : ''
                );

            } elseif (!empty($line) && strpos($line, '#') !== 0 && !empty($current_channel)) {
                // This is the stream URL
                $current_channel['url'] = $line;
                $current_channel['slug'] = sanitize_title($current_channel['name']);

                // Validate URL
                if ($this->validate_stream_url($line)) {
                    $channels[] = $current_channel;
                }

                $current_channel = array();
            }
        }

        return array('channels' => $channels, 'total' => count($channels));
    }

    private function validate_stream_url($url) {
        // Basic URL validation
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        // Check if it's a streaming URL
        $valid_extensions = array('.m3u8', '.ts', '.mp4', '.flv', '.rtmp');
        $valid_protocols = array('http://', 'https://', 'rtmp://', 'rtmps://');

        $has_valid_protocol = false;
        foreach ($valid_protocols as $protocol) {
            if (strpos($url, $protocol) === 0) {
                $has_valid_protocol = true;
                break;
            }
        }

        return $has_valid_protocol;
    }

    // AJAX handlers
    public function ajax_import_playlist() {
        check_ajax_referer('doo_livetv_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $playlist_url = sanitize_url($_POST['playlist_url']);
        $auto_categorize = isset($_POST['auto_categorize']) ? true : false;

        if (empty($playlist_url)) {
            wp_send_json_error('Playlist URL is required');
        }

        $result = $this->parse_m3u_playlist($playlist_url);

        if (isset($result['error'])) {
            wp_send_json_error($result['error']);
        }

        $imported = 0;
        $skipped = 0;
        $errors = array();

        global $wpdb;

        foreach ($result['channels'] as $channel) {
            // Check if channel already exists
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$this->table_channels} WHERE slug = %s OR stream_url = %s",
                $channel['slug'],
                $channel['url']
            ));

            if ($exists) {
                $skipped++;
                continue;
            }

            // Get or create category - Auto categorize all channels
            $category_id = null;
            if (!empty($channel['group'])) {
                $category_id = $this->get_or_create_category($channel['group']);
            } else {
                // Default category if no group specified
                $category_id = $this->get_or_create_category('General');
            }

            // Ensure slug is unique
            $original_slug = $channel['slug'];
            $counter = 1;
            while ($wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$this->table_channels} WHERE slug = %s",
                $channel['slug']
            ))) {
                $channel['slug'] = $original_slug . '-' . $counter;
                $counter++;
            }

            // Insert channel with all required fields
            $insert_result = $wpdb->insert(
                $this->table_channels,
                array(
                    'name' => $channel['name'],
                    'slug' => $channel['slug'],
                    'description' => 'Imported from M3U playlist',
                    'stream_url' => $channel['url'],
                    'backup_urls' => '',
                    'logo_url' => $channel['logo'],
                    'category_id' => $category_id,
                    'country' => $channel['country'],
                    'language' => $channel['language'],
                    'quality' => 'HD',
                    'status' => 'active',
                    'views' => 0,
                    'sort_order' => 0,
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ),
                array('%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%d', '%d', '%s', '%s')
            );

            if ($insert_result) {
                $imported++;
            } else {
                $errors[] = "Failed to import: " . $channel['name'];
            }
        }

        // Save playlist info
        $wpdb->insert(
            $this->table_playlists,
            array(
                'name' => 'Imported Playlist - ' . date('Y-m-d H:i:s'),
                'url' => $playlist_url,
                'last_imported' => current_time('mysql'),
                'import_count' => $imported,
                'status' => 'active'
            )
        );

        wp_send_json_success(array(
            'imported' => $imported,
            'skipped' => $skipped,
            'total' => count($result['channels']),
            'errors' => $errors
        ));
    }

    public function ajax_check_channels() {
        // Include the channel checker functions
        include get_template_directory() . '/inc/parts/admin/livetv-channel-checker.php';
    }

    public function ajax_smart_import() {
        // Include the smart import functions
        include get_template_directory() . '/inc/parts/admin/livetv-smart-import.php';
    }

    public function ajax_auto_categorize() {
        // Include the auto categorize functions
        include get_template_directory() . '/inc/parts/admin/livetv-auto-categorize.php';
    }

    public function ajax_auto_logo() {
        // Include the auto logo functions
        include get_template_directory() . '/inc/parts/admin/livetv-auto-logo.php';
    }

    private function get_or_create_category($group_name) {
        global $wpdb;

        $slug = sanitize_title($group_name);

        // Check if category exists
        $category_id = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$this->table_categories} WHERE slug = %s",
            $slug
        ));

        if (!$category_id) {
            // Create new category
            $wpdb->insert(
                $this->table_categories,
                array(
                    'name' => $group_name,
                    'slug' => $slug,
                    'status' => 'active'
                )
            );
            $category_id = $wpdb->insert_id;
        }

        return $category_id;
    }

    public function ajax_delete_channel() {
        check_ajax_referer('doo_livetv_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $channel_id = intval($_POST['channel_id']);

        global $wpdb;
        $result = $wpdb->delete($this->table_channels, array('id' => $channel_id), array('%d'));

        if ($result) {
            wp_send_json_success('Channel deleted successfully');
        } else {
            wp_send_json_error('Failed to delete channel');
        }
    }

    public function ajax_update_channel() {
        check_ajax_referer('doo_livetv_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $channel_id = intval($_POST['channel_id']);
        $field = sanitize_text_field($_POST['field']);
        $value = sanitize_text_field($_POST['value']);

        $allowed_fields = array('name', 'status', 'category_id', 'stream_url', 'logo_url', 'country', 'language', 'quality');

        if (!in_array($field, $allowed_fields)) {
            wp_send_json_error('Invalid field');
        }

        global $wpdb;
        $result = $wpdb->update(
            $this->table_channels,
            array($field => $value),
            array('id' => $channel_id),
            array('%s'),
            array('%d')
        );

        if ($result !== false) {
            wp_send_json_success('Channel updated successfully');
        } else {
            wp_send_json_error('Failed to update channel');
        }
    }

    public function ajax_bulk_action() {
        check_ajax_referer('doo_livetv_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $action = sanitize_text_field($_POST['action_type']);
        $channel_ids = array_map('intval', $_POST['channel_ids']);

        if (empty($channel_ids)) {
            wp_send_json_error('No channels selected');
        }

        global $wpdb;
        $placeholders = implode(',', array_fill(0, count($channel_ids), '%d'));

        switch ($action) {
            case 'delete':
                $result = $wpdb->query($wpdb->prepare(
                    "DELETE FROM {$this->table_channels} WHERE id IN ($placeholders)",
                    $channel_ids
                ));
                break;

            case 'activate':
                $result = $wpdb->query($wpdb->prepare(
                    "UPDATE {$this->table_channels} SET status = 'active' WHERE id IN ($placeholders)",
                    $channel_ids
                ));
                break;

            case 'deactivate':
                $result = $wpdb->query($wpdb->prepare(
                    "UPDATE {$this->table_channels} SET status = 'inactive' WHERE id IN ($placeholders)",
                    $channel_ids
                ));
                break;

            default:
                wp_send_json_error('Invalid action');
        }

        if ($result !== false) {
            wp_send_json_success("Bulk action completed. Affected channels: " . $result);
        } else {
            wp_send_json_error('Failed to perform bulk action');
        }
    }

    public function ajax_delete_playlist() {
        check_ajax_referer('doo_livetv_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $playlist_id = intval($_POST['playlist_id']);

        global $wpdb;
        $result = $wpdb->delete($this->table_playlists, array('id' => $playlist_id), array('%d'));

        if ($result) {
            wp_send_json_success('Playlist deleted successfully');
        } else {
            wp_send_json_error('Failed to delete playlist');
        }
    }

    // Helper methods for frontend
    public function get_channels($args = array()) {
        global $wpdb;

        $defaults = array(
            'category_id' => null,
            'status' => 'active',
            'limit' => 50,
            'offset' => 0,
            'search' => '',
            'orderby' => 'sort_order',
            'order' => 'ASC',
            'exclude_id' => null
        );

        $args = wp_parse_args($args, $defaults);

        $where = array("c.status = %s");
        $values = array($args['status']);

        if ($args['category_id']) {
            $where[] = "c.category_id = %d";
            $values[] = $args['category_id'];
        }

        if ($args['search']) {
            $where[] = "c.name LIKE %s";
            $values[] = '%' . $wpdb->esc_like($args['search']) . '%';
        }

        if ($args['exclude_id']) {
            $where[] = "c.id != %d";
            $values[] = $args['exclude_id'];
        }

        $where_clause = implode(' AND ', $where);
        $order_clause = sprintf("ORDER BY c.%s %s, c.name ASC", $args['orderby'], $args['order']);
        $limit_clause = sprintf("LIMIT %d OFFSET %d", $args['limit'], $args['offset']);

        $sql = "SELECT c.*, cat.name as category_name, cat.color as category_color
                FROM {$this->table_channels} c
                LEFT JOIN {$this->table_categories} cat ON c.category_id = cat.id
                WHERE $where_clause $order_clause $limit_clause";

        if (!empty($values)) {
            $results = $wpdb->get_results($wpdb->prepare($sql, $values));
        } else {
            $results = $wpdb->get_results($sql);
        }

        return $results;
    }

    public function get_categories($status = 'active') {
        global $wpdb;

        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$this->table_categories} WHERE status = %s ORDER BY sort_order ASC, name ASC",
            $status
        ));
    }

    public function get_channel_by_slug($slug) {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare(
            "SELECT c.*, cat.name as category_name, cat.color as category_color
             FROM {$this->table_channels} c
             LEFT JOIN {$this->table_categories} cat ON c.category_id = cat.id
             WHERE c.slug = %s AND c.status = 'active'",
            $slug
        ));
    }

    public function increment_views($channel_id) {
        global $wpdb;

        $wpdb->query($wpdb->prepare(
            "UPDATE {$this->table_channels} SET views = views + 1 WHERE id = %d",
            $channel_id
        ));
    }

    // Shortcode methods
    public function live_tv_channels_shortcode($atts) {
        $atts = shortcode_atts(array(
            'category' => '',
            'limit' => 24,
            'search' => ''
        ), $atts);

        ob_start();

        // Check if single channel is requested
        if (isset($_GET['channel'])) {
            $channel_slug = sanitize_text_field($_GET['channel']);
            $channel = $this->get_channel_by_slug($channel_slug);

            if ($channel) {
                $this->increment_views($channel->id);
                include get_template_directory() . '/inc/parts/livetv-single.php';
            } else {
                echo '<p>Channel not found.</p>';
            }
        } else {
            // Show channel grid
            include get_template_directory() . '/inc/parts/livetv-grid.php';
        }

        return ob_get_clean();
    }

    public function live_tv_single_shortcode($atts) {
        $atts = shortcode_atts(array(
            'channel' => ''
        ), $atts);

        if (empty($atts['channel'])) {
            return '<p>Channel slug is required.</p>';
        }

        $channel = $this->get_channel_by_slug($atts['channel']);

        if (!$channel) {
            return '<p>Channel not found.</p>';
        }

        $this->increment_views($channel->id);

        ob_start();
        include get_template_directory() . '/inc/parts/livetv-single.php';
        return ob_get_clean();
    }

    // AJAX handler for stream testing
    public function ajax_test_stream() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'livetv_stream_test')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $stream_url = sanitize_url($_POST['stream_url']);
        $result = $this->test_stream_url($stream_url);

        wp_send_json($result);
    }

    // Test stream URL functionality
    public function test_stream_url($url) {
        if (empty($url)) {
            return array(
                'success' => false,
                'message' => 'URL is required',
                'details' => array()
            );
        }

        // Basic URL validation
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return array(
                'success' => false,
                'message' => 'Invalid URL format',
                'details' => array('url' => $url)
            );
        }

        $details = array();
        $details['url'] = $url;
        $details['timestamp'] = current_time('mysql');
        $details['test_method'] = 'WordPress HTTP API';

        // Check URL accessibility with extended timeout
        $response = wp_remote_head($url, array(
            'timeout' => 15,
            'user-agent' => 'DooPlay Live TV Stream Tester/1.0',
            'headers' => array(
                'Accept' => 'application/vnd.apple.mpegurl, video/mp4, video/*, */*'
            ),
            'sslverify' => false // Allow self-signed certificates for testing
        ));

        if (is_wp_error($response)) {
            // Try with GET request if HEAD fails
            $response = wp_remote_get($url, array(
                'timeout' => 10,
                'user-agent' => 'DooPlay Live TV Stream Tester/1.0',
                'headers' => array(
                    'Range' => 'bytes=0-1024' // Only get first 1KB
                ),
                'sslverify' => false
            ));

            if (is_wp_error($response)) {
                return array(
                    'success' => false,
                    'message' => 'Failed to connect: ' . $response->get_error_message(),
                    'details' => $details
                );
            }

            $details['test_method'] = 'WordPress HTTP API (GET with Range)';
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $headers = wp_remote_retrieve_headers($response);

        $details['response_code'] = $response_code;
        $details['content_type'] = isset($headers['content-type']) ? $headers['content-type'] : 'Unknown';
        $details['content_length'] = isset($headers['content-length']) ? $headers['content-length'] : 'Unknown';
        $details['server'] = isset($headers['server']) ? $headers['server'] : 'Unknown';

        // Check for streaming-specific headers
        if (isset($headers['accept-ranges'])) {
            $details['accept_ranges'] = $headers['accept-ranges'];
        }

        // Check if it's a valid streaming URL
        $valid_types = array(
            'application/vnd.apple.mpegurl', // HLS
            'application/x-mpegurl',         // HLS alternative
            'video/mp4',                     // MP4
            'video/webm',                    // WebM
            'video/ogg',                     // OGG
            'application/dash+xml',          // DASH
            'video/mp2t',                    // MPEG-TS
            'application/octet-stream'       // Generic binary (often used for streams)
        );

        $is_valid_stream = false;
        $detected_format = 'Unknown';

        foreach ($valid_types as $type) {
            if (strpos($details['content_type'], $type) !== false) {
                $is_valid_stream = true;
                $detected_format = $type;
                break;
            }
        }

        // Check URL patterns for stream formats
        $url_lower = strtolower($url);
        if (strpos($url_lower, '.m3u8') !== false) {
            $is_valid_stream = true;
            $detected_format = 'HLS (.m3u8)';
        } elseif (strpos($url_lower, '.mpd') !== false) {
            $is_valid_stream = true;
            $detected_format = 'DASH (.mpd)';
        } elseif (strpos($url_lower, '.mp4') !== false) {
            $is_valid_stream = true;
            $detected_format = 'MP4 Video';
        } elseif (strpos($url_lower, 'playlist') !== false) {
            $is_valid_stream = true;
            $detected_format = 'Playlist (likely HLS)';
        } elseif (strpos($url_lower, 'manifest') !== false) {
            $is_valid_stream = true;
            $detected_format = 'Manifest (likely DASH)';
        }

        $details['detected_format'] = $detected_format;

        // Additional checks for common streaming patterns
        if (strpos($url_lower, 'rtmp://') === 0 || strpos($url_lower, 'rtmps://') === 0) {
            $details['protocol'] = 'RTMP';
            $details['note'] = 'RTMP streams may not work in HTML5 players';
        } elseif (strpos($url_lower, 'http://') === 0) {
            $details['protocol'] = 'HTTP';
            $details['note'] = 'HTTP streams may have mixed content issues on HTTPS sites';
        } elseif (strpos($url_lower, 'https://') === 0) {
            $details['protocol'] = 'HTTPS';
        }

        // Analyze response for success
        if ($response_code === 200 || $response_code === 206) { // 206 for partial content
            if ($is_valid_stream) {
                return array(
                    'success' => true,
                    'message' => 'Stream URL appears to be valid and accessible (' . $detected_format . ')',
                    'details' => $details
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'URL is accessible but may not be a valid stream format. Detected: ' . $detected_format,
                    'details' => $details
                );
            }
        } elseif ($response_code === 302 || $response_code === 301) {
            $details['note'] = 'URL redirects to another location';
            return array(
                'success' => true,
                'message' => 'Stream URL redirects (this is normal for some streams)',
                'details' => $details
            );
        } elseif ($response_code === 403) {
            return array(
                'success' => false,
                'message' => 'Access forbidden - stream may require authentication or have geo-restrictions',
                'details' => $details
            );
        } elseif ($response_code === 404) {
            return array(
                'success' => false,
                'message' => 'Stream not found (404) - URL may be incorrect or stream is offline',
                'details' => $details
            );
        } else {
            return array(
                'success' => false,
                'message' => 'HTTP Error: ' . $response_code . ' - ' . $this->get_http_status_message($response_code),
                'details' => $details
            );
        }
    }

    // Get human-readable HTTP status messages
    private function get_http_status_message($code) {
        $status_messages = array(
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            405 => 'Method Not Allowed',
            408 => 'Request Timeout',
            500 => 'Internal Server Error',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable',
            504 => 'Gateway Timeout'
        );

        return isset($status_messages[$code]) ? $status_messages[$code] : 'Unknown Status';
    }
}

// Initialize the Live TV system
new DooLiveTV();
