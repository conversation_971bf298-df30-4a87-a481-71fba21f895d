<?php
/**
 * DeshiFlix Premium System - Core Class
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Premium_Core {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Premium system version
     */
    public $version = '1.0.0';
    
    /**
     * Premium database version
     */
    public $db_version = '1.0';
    
    /**
     * Premium options key
     */
    public $options_key = '_deshiflix_premium_options';
    
    /**
     * Premium user meta key
     */
    public $user_meta_key = '_deshiflix_premium_status';
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Include configuration
        $config_path = get_template_directory() . '/inc/premium/config.php';
        if (file_exists($config_path)) {
            require_once $config_path;
        }

        $this->init();
    }
    
    /**
     * Initialize premium system
     */
    private function init() {
        // Define premium constants
        $this->define_constants();
        
        // Include required files
        $this->includes();
        
        // Initialize hooks
        $this->init_hooks();
        
        // Check if premium system is enabled
        if ($this->is_premium_enabled()) {
            $this->init_premium_features();
        }
    }
    
    /**
     * Define premium constants
     */
    private function define_constants() {
        define('DESHIFLIX_PREMIUM_VERSION', $this->version);
        define('DESHIFLIX_PREMIUM_DB_VERSION', $this->db_version);
        define('DESHIFLIX_PREMIUM_PATH', get_template_directory() . '/inc/premium/');
        define('DESHIFLIX_PREMIUM_URL', get_template_directory_uri() . '/inc/premium/');
        define('DESHIFLIX_PREMIUM_ASSETS_URL', DESHIFLIX_PREMIUM_URL . 'assets/');
        
        // Premium features flags
        define('DESHIFLIX_PREMIUM_CONTENT_LOCK', true);
        define('DESHIFLIX_PREMIUM_AD_FREE', true);
        define('DESHIFLIX_PREMIUM_HD_QUALITY', true);
        define('DESHIFLIX_PREMIUM_DOWNLOAD_LINKS', true);
        define('DESHIFLIX_PREMIUM_EARLY_ACCESS', true);
    }
    
    /**
     * Include required files
     */
    private function includes() {
        // Core classes
        require_once DESHIFLIX_PREMIUM_PATH . 'core/class-premium-user.php';
        require_once DESHIFLIX_PREMIUM_PATH . 'core/class-premium-content.php';
        require_once DESHIFLIX_PREMIUM_PATH . 'core/class-premium-payment.php';
        
        // Admin classes
        if (is_admin()) {
            $admin_files = array(
                'admin/premium-dooplay-admin.php',
                'admin/premium-settings.php',
                'admin/premium-analytics.php',
                'admin/premium-bulk-actions.php'
            );

            foreach ($admin_files as $file) {
                $file_path = DESHIFLIX_PREMIUM_PATH . $file;
                if (file_exists($file_path)) {
                    require_once $file_path;
                }
            }
        }
        
        // Frontend classes
        if (!is_admin()) {
            $frontend_files = array(
                'frontend/premium-user-dashboard.php',
                'frontend/premium-subscription.php',
                'frontend/premium-content-lock.php'
            );

            foreach ($frontend_files as $file) {
                $file_path = DESHIFLIX_PREMIUM_PATH . $file;
                if (file_exists($file_path)) {
                    require_once $file_path;
                }
            }
        }
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Activation/Deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // WordPress hooks
        add_action('init', array($this, 'init_premium_system'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_footer', array($this, 'add_premium_dashboard_link'));

        // Admin hooks - handled by separate admin menu class

        // AJAX hooks
        add_action('wp_ajax_premium_check_status', array($this, 'ajax_check_premium_status'));
        add_action('wp_ajax_nopriv_premium_check_status', array($this, 'ajax_check_premium_status'));

        // Content hooks
        add_filter('the_content', array($this, 'filter_premium_content'), 10);
        add_action('wp_head', array($this, 'add_premium_meta_tags'));
    }
    
    /**
     * Initialize premium system
     */
    public function init_premium_system() {
        // Create database tables if needed
        $this->maybe_create_tables();
        
        // Initialize premium user system
        DeshiFlix_Premium_User::get_instance();
        
        // Initialize premium content system
        DeshiFlix_Premium_Content::get_instance();
        
        // Initialize payment system
        DeshiFlix_Premium_Payment::get_instance();
    }
    
    /**
     * Check if premium system is enabled
     */
    public function is_premium_enabled() {
        $options = get_option($this->options_key, array());
        return isset($options['enable_premium']) ? $options['enable_premium'] : true;
    }
    
    /**
     * Initialize premium features
     */
    private function init_premium_features() {
        // Add premium user role
        $this->add_premium_user_role();
        
        // Initialize premium content protection
        $this->init_content_protection();
        
        // Initialize premium user dashboard
        $this->init_user_dashboard();
    }
    
    /**
     * Add premium user role
     */
    private function add_premium_user_role() {
        add_role('premium_user', __('Premium User', 'deshiflix'), array(
            'read' => true,
            'premium_access' => true,
            'download_content' => true,
            'hd_quality' => true,
            'ad_free' => true
        ));
    }
    
    /**
     * Initialize content protection
     */
    private function init_content_protection() {
        // Hook into content display
        add_filter('dooplay_player_content', array($this, 'protect_player_content'));
        add_filter('dooplay_download_links', array($this, 'protect_download_links'));
    }
    
    /**
     * Initialize user dashboard
     */
    private function init_user_dashboard() {
        // Add premium dashboard to user menu
        add_action('wp_footer', array($this, 'add_premium_dashboard_link'));
    }
    
    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        wp_enqueue_style(
            'deshiflix-premium-frontend',
            DESHIFLIX_PREMIUM_ASSETS_URL . 'css/premium-frontend.css',
            array(),
            $this->version
        );
        
        wp_enqueue_script(
            'deshiflix-premium-frontend',
            DESHIFLIX_PREMIUM_ASSETS_URL . 'js/premium-frontend.js',
            array('jquery'),
            $this->version,
            true
        );
        
        // Localize script
        wp_localize_script('deshiflix-premium-frontend', 'deshiflix_premium', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('deshiflix_premium_nonce'),
            'is_user_premium' => $this->is_user_premium(),
            'messages' => array(
                'premium_required' => __('Premium membership required', 'deshiflix'),
                'upgrade_now' => __('Upgrade Now', 'deshiflix'),
                'loading' => __('Loading...', 'deshiflix')
            )
        ));
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on premium admin pages
        if (strpos($hook, 'deshiflix-premium') === false) {
            return;
        }
        
        wp_enqueue_style(
            'deshiflix-premium-admin',
            DESHIFLIX_PREMIUM_ASSETS_URL . 'css/premium-admin.css',
            array(),
            $this->version
        );
        
        wp_enqueue_script(
            'deshiflix-premium-admin',
            DESHIFLIX_PREMIUM_ASSETS_URL . 'js/premium-admin.js',
            array('jquery', 'wp-util'),
            $this->version,
            true
        );
    }
    
    /**
     * Check if current user is premium
     */
    public function is_user_premium($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return false;
        }
        
        $premium_status = get_user_meta($user_id, $this->user_meta_key, true);
        
        if (!$premium_status || !is_array($premium_status)) {
            return false;
        }
        
        // Check if subscription is active and not expired
        return isset($premium_status['status']) && 
               $premium_status['status'] === 'active' &&
               isset($premium_status['expires']) &&
               strtotime($premium_status['expires']) > time();
    }
    
    /**
     * Get user premium plan
     */
    public function get_user_premium_plan($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        $premium_status = get_user_meta($user_id, $this->user_meta_key, true);
        
        if (!$premium_status || !is_array($premium_status)) {
            return false;
        }
        
        return isset($premium_status['plan']) ? $premium_status['plan'] : false;
    }
    
    /**
     * AJAX check premium status
     */
    public function ajax_check_premium_status() {
        check_ajax_referer('deshiflix_premium_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        $is_premium = $this->is_user_premium($user_id);
        $plan = $this->get_user_premium_plan($user_id);
        
        wp_send_json_success(array(
            'is_premium' => $is_premium,
            'plan' => $plan,
            'user_id' => $user_id
        ));
    }
    
    /**
     * Filter premium content
     */
    public function filter_premium_content($content) {
        global $post;
        
        if (!$post || is_admin()) {
            return $content;
        }
        
        // Check if content is premium
        $is_premium_content = get_post_meta($post->ID, '_is_premium_content', true);
        
        if ($is_premium_content && !$this->is_user_premium()) {
            // Show premium content lock
            return $this->get_premium_content_lock_html($post);
        }
        
        return $content;
    }
    
    /**
     * Get premium content lock HTML
     */
    private function get_premium_content_lock_html($post) {
        global $post; // Make $post available to the template
        ob_start();
        include DESHIFLIX_PREMIUM_PATH . 'templates/premium-content-overlay.php';
        return ob_get_clean();
    }

    /**
     * Add premium dashboard link to footer
     */
    public function add_premium_dashboard_link() {
        if (!is_user_logged_in()) {
            return;
        }

        $user_id = get_current_user_id();
        $is_premium = $this->is_user_premium($user_id);

        if (!$is_premium) {
            return;
        }

        ?>
        <div class="premium-dashboard-link-footer">
            <a href="/premium-dashboard/" class="premium-dashboard-btn">
                <i class="fas fa-crown"></i>
                <?php _e('Premium Dashboard', 'deshiflix'); ?>
            </a>
        </div>

        <style>
        .premium-dashboard-link-footer {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
        }

        .premium-dashboard-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }

        .premium-dashboard-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
            color: white;
            text-decoration: none;
        }

        .premium-dashboard-btn i {
            color: #ffd700;
        }

        @media (max-width: 768px) {
            .premium-dashboard-link-footer {
                bottom: 10px;
                right: 10px;
            }

            .premium-dashboard-btn {
                padding: 10px 16px;
                font-size: 0.9rem;
            }
        }
        </style>
        <?php
    }
    
    /**
     * Add premium meta tags
     */
    public function add_premium_meta_tags() {
        if ($this->is_user_premium()) {
            echo '<meta name="deshiflix-premium-user" content="true">' . "\n";
        }
    }



    /**
     * Get premium plan by ID
     */
    public function get_premium_plan($plan_id) {
        global $wpdb;

        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_plans WHERE id = %d",
            $plan_id
        ));
    }

    /**
     * Get all premium plans
     */
    public function get_premium_plans() {
        global $wpdb;

        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';

        return $wpdb->get_results(
            "SELECT * FROM $table_plans WHERE status = 'active' ORDER BY price ASC"
        );
    }

    /**
     * Check if user has specific feature
     */
    public function user_has_feature($user_id, $feature) {
        if (!$this->is_user_premium($user_id)) {
            return false;
        }

        if (class_exists('DeshiFlix_Premium_Features')) {
            $features_manager = DeshiFlix_Premium_Features::get_instance();
            return $features_manager->user_has_feature($user_id, $feature);
        }

        return false;
    }


    
    /**
     * Maybe create database tables
     */
    private function maybe_create_tables() {
        $installed_version = get_option('deshiflix_premium_db_version');
        
        if ($installed_version !== $this->db_version) {
            $this->create_tables();
            update_option('deshiflix_premium_db_version', $this->db_version);
        }
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Premium users table
        $table_premium_users = $wpdb->prefix . 'deshiflix_premium_users';
        $sql_users = "CREATE TABLE $table_premium_users (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11) NOT NULL,
            plan_id int(11) NOT NULL,
            start_date datetime NOT NULL,
            end_date datetime NOT NULL,
            status enum('active','expired','cancelled') DEFAULT 'active',
            payment_method varchar(50),
            auto_renew tinyint(1) DEFAULT 0,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY plan_id (plan_id),
            KEY status (status),
            KEY end_date (end_date)
        ) $charset_collate;";

        // Premium plans table
        $table_premium_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $sql_plans = "CREATE TABLE $table_premium_plans (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            description text,
            price decimal(10,2) NOT NULL,
            original_price decimal(10,2),
            duration_days int(11) NOT NULL,
            features longtext,
            max_devices int(11) DEFAULT 1,
            download_limit int(11) DEFAULT 0,
            quality_limit varchar(20) DEFAULT 'HD',
            status enum('active','inactive') DEFAULT 'active',
            sort_order int(11) DEFAULT 0,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY status (status),
            KEY sort_order (sort_order)
        ) $charset_collate;";

        // Premium content table
        $table_premium_content = $wpdb->prefix . 'deshiflix_premium_content';
        $sql_content = "CREATE TABLE $table_premium_content (
            id int(11) NOT NULL AUTO_INCREMENT,
            post_id int(11) NOT NULL,
            is_premium tinyint(1) DEFAULT 0,
            premium_level enum('basic','standard','premium') DEFAULT 'basic',
            unlock_date datetime,
            early_access_days int(11) DEFAULT 0,
            download_allowed tinyint(1) DEFAULT 1,
            quality_restriction varchar(20),
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY post_id (post_id),
            KEY is_premium (is_premium),
            KEY premium_level (premium_level),
            KEY unlock_date (unlock_date)
        ) $charset_collate;";

        // Premium transactions table
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        $sql_transactions = "CREATE TABLE $table_transactions (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11) NOT NULL,
            plan_id int(11) NOT NULL,
            amount decimal(10,2) NOT NULL,
            currency varchar(3) DEFAULT 'BDT',
            payment_method varchar(50),
            transaction_id varchar(100) UNIQUE,
            gateway_transaction_id varchar(100),
            status enum('pending','completed','failed','refunded','cancelled') DEFAULT 'pending',
            gateway_response longtext,
            ip_address varchar(45),
            user_agent text,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY transaction_id (transaction_id),
            KEY user_id (user_id),
            KEY plan_id (plan_id),
            KEY status (status),
            KEY payment_method (payment_method),
            KEY created_at (created_at)
        ) $charset_collate;";

        // Premium analytics table
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        $sql_analytics = "CREATE TABLE $table_analytics (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11),
            event_type varchar(50) NOT NULL,
            event_data longtext,
            post_id int(11),
            session_id varchar(100),
            ip_address varchar(45),
            user_agent text,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY event_type (event_type),
            KEY post_id (post_id),
            KEY created_at (created_at),
            KEY session_id (session_id)
        ) $charset_collate;";

        // Premium user devices table
        $table_devices = $wpdb->prefix . 'deshiflix_premium_devices';
        $sql_devices = "CREATE TABLE $table_devices (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11) NOT NULL,
            device_id varchar(100) NOT NULL,
            device_name varchar(100),
            device_type varchar(50),
            browser varchar(100),
            os varchar(100),
            ip_address varchar(45),
            last_active timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            status enum('active','inactive') DEFAULT 'active',
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_device (user_id, device_id),
            KEY user_id (user_id),
            KEY status (status),
            KEY last_active (last_active)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_users);
        dbDelta($sql_plans);
        dbDelta($sql_content);
        dbDelta($sql_transactions);
        dbDelta($sql_analytics);
        dbDelta($sql_devices);

        // Insert default plans
        $this->insert_default_plans();
    }
    
    /**
     * Insert default premium plans
     */
    private function insert_default_plans() {
        global $wpdb;

        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';

        // Check if plans already exist
        $existing_plans = $wpdb->get_var("SELECT COUNT(*) FROM $table_plans");
        if ($existing_plans > 0) {
            return; // Plans already exist
        }

        $default_plans = array(
            array(
                'name' => 'বেসিক প্ল্যান',
                'description' => 'HD কোয়ালিটি এবং বিজ্ঞাপন মুক্ত অভিজ্ঞতা',
                'price' => 199.00,
                'original_price' => 299.00,
                'duration_days' => 30,
                'max_devices' => 1,
                'download_limit' => 5,
                'quality_limit' => 'HD',
                'features' => json_encode(array(
                    'hd_quality' => true,
                    'ad_free' => true,
                    'download_links' => false,
                    'early_access' => false,
                    'multiple_servers' => false,
                    'offline_viewing' => false
                )),
                'sort_order' => 1
            ),
            array(
                'name' => 'স্ট্যান্ডার্ড প্ল্যান',
                'description' => 'HD কোয়ালিটি, ডাউনলোড লিংক এবং মাল্টিপল সার্ভার',
                'price' => 349.00,
                'original_price' => 499.00,
                'duration_days' => 30,
                'max_devices' => 2,
                'download_limit' => 20,
                'quality_limit' => 'FHD',
                'features' => json_encode(array(
                    'hd_quality' => true,
                    'ad_free' => true,
                    'download_links' => true,
                    'early_access' => false,
                    'multiple_servers' => true,
                    'offline_viewing' => true
                )),
                'sort_order' => 2
            ),
            array(
                'name' => 'প্রিমিয়াম প্ল্যান',
                'description' => 'সকল ফিচার সহ আর্লি এক্সেস এবং 4K কোয়ালিটি',
                'price' => 549.00,
                'original_price' => 799.00,
                'duration_days' => 30,
                'max_devices' => 4,
                'download_limit' => 0, // Unlimited
                'quality_limit' => '4K',
                'features' => json_encode(array(
                    'hd_quality' => true,
                    'ad_free' => true,
                    'download_links' => true,
                    'early_access' => true,
                    'multiple_servers' => true,
                    'offline_viewing' => true,
                    'priority_support' => true,
                    'exclusive_content' => true
                )),
                'sort_order' => 3
            ),
            array(
                'name' => 'বার্ষিক প্ল্যান',
                'description' => 'সকল প্রিমিয়াম ফিচার সহ ১২ মাসের জন্য (৪০% ছাড়)',
                'price' => 3999.00,
                'original_price' => 6588.00,
                'duration_days' => 365,
                'max_devices' => 6,
                'download_limit' => 0, // Unlimited
                'quality_limit' => '4K',
                'features' => json_encode(array(
                    'hd_quality' => true,
                    'ad_free' => true,
                    'download_links' => true,
                    'early_access' => true,
                    'multiple_servers' => true,
                    'offline_viewing' => true,
                    'priority_support' => true,
                    'exclusive_content' => true,
                    'family_sharing' => true,
                    'annual_discount' => true
                )),
                'sort_order' => 4
            )
        );

        foreach ($default_plans as $plan) {
            $wpdb->insert($table_plans, $plan);
        }
    }
    
    /**
     * Activation hook
     */
    public function activate() {
        $this->create_tables();
        flush_rewrite_rules();
    }
    
    /**
     * Deactivation hook
     */
    public function deactivate() {
        flush_rewrite_rules();
    }
}

// Initialize premium system
function deshiflix_premium() {
    return DeshiFlix_Premium_Core::get_instance();
}

// Helper functions
if (!function_exists('is_premium_content')) {
    /**
     * Check if content is premium
     */
    function is_premium_content($post_id = null) {
        if (!$post_id) {
            global $post;
            $post_id = $post ? $post->ID : 0;
        }

        return get_post_meta($post_id, '_is_premium_content', true) == 1;
    }
}

if (!function_exists('user_has_premium_access')) {
    /**
     * Check if user has premium access to content
     */
    function user_has_premium_access($post_id = null, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        if (!$post_id) {
            global $post;
            $post_id = $post ? $post->ID : 0;
        }

        if (class_exists('DeshiFlix_Premium_Content')) {
            $content_manager = DeshiFlix_Premium_Content::get_instance();
            return $content_manager->user_has_content_access($post_id, $user_id);
        }

        return false;
    }
}

if (!function_exists('get_premium_plans')) {
    /**
     * Get all premium plans
     */
    function get_premium_plans() {
        return deshiflix_premium()->get_premium_plans();
    }
}

if (!function_exists('user_has_premium_feature')) {
    /**
     * Check if user has specific premium feature
     */
    function user_has_premium_feature($feature, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        return deshiflix_premium()->user_has_feature($user_id, $feature);
    }

}

// Start the premium system
deshiflix_premium();
