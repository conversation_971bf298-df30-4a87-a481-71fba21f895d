<?php
/*
Template Name: Live TV
*/

get_header(); 

// Enqueue Live TV assets
wp_enqueue_script('doo-livetv-js', get_template_directory_uri() . '/assets/js/livetv.js', array('jquery'), '1.0.0', true);
wp_enqueue_script('doo-focus-manager-js', get_template_directory_uri() . '/assets/js/focus-manager.js', array('jquery'), '1.0.0', true);
wp_enqueue_script('doo-tv-performance-js', get_template_directory_uri() . '/assets/js/tv-performance.js', array('jquery'), '1.0.0', true);
wp_enqueue_script('doo-android-tv-js', get_template_directory_uri() . '/assets/js/android-tv.js', array('jquery', 'doo-livetv-js', 'doo-focus-manager-js', 'doo-tv-performance-js'), '1.0.0', true);
wp_enqueue_script('doo-tv-player-js', get_template_directory_uri() . '/assets/js/tv-player.js', array('jquery', 'doo-android-tv-js'), '1.0.0', true);
wp_enqueue_script('doo-tv-settings-js', get_template_directory_uri() . '/assets/js/tv-settings.js', array('jquery', 'doo-android-tv-js'), '1.0.0', true);
wp_enqueue_style('doo-livetv-css', get_template_directory_uri() . '/assets/css/livetv.css', array(), '1.0.0');

// Localize script for AJAX
wp_localize_script('doo-livetv-js', 'doo_livetv_ajax', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('doo_livetv_nonce'),
    'loading_text' => 'Loading...',
    'error_text' => 'An error occurred. Please try again.',
    'success_text' => 'Operation completed successfully!'
));
?>

<div id="single">
    <div class="content">
        <div class="sheader">
            <div class="data">
                <h1>Live TV</h1>
                <div class="extra">
                    <span class="date">Watch live TV channels online in HD quality</span>
                </div>
            </div>
        </div>
        
        <div class="module">
            <div class="content">
                <?php
                // Debug information
                echo '<!-- Debug: Page ID = ' . get_the_ID() . ', Channel = ' . (isset($_GET['channel']) ? $_GET['channel'] : 'none') . ' -->';

                // Check if DooLiveTV class exists
                if (!class_exists('DooLiveTV')) {
                    echo '<div style="background: #f44336; color: white; padding: 20px; margin: 20px 0; border-radius: 5px;">';
                    echo '<h3>Error: DooLiveTV class not found</h3>';
                    echo '<p>The Live TV plugin is not properly loaded. Please check if the plugin files are correctly included.</p>';
                    echo '</div>';
                    return;
                }

                // Force redirect if we're not on the correct page
                if (get_the_ID() != 76 && isset($_GET['channel'])) {
                    $redirect_url = add_query_arg('channel', sanitize_text_field($_GET['channel']), get_permalink(76));
                    wp_redirect($redirect_url);
                    exit;
                }

                try {
                    // Check if single channel is requested
                    if (isset($_GET['channel'])) {
                        // Single channel view
                        $channel_slug = sanitize_text_field($_GET['channel']);
                        $livetv = new DooLiveTV();
                        $channel = $livetv->get_channel_by_slug($channel_slug);

                        if ($channel) {
                            $livetv->increment_views($channel->id);
                            // Use simple player for all devices
                            include get_template_directory() . '/inc/parts/livetv-simple-player.php';
                        } else {
                            echo '<h2>Channel Not Found</h2><p>The requested channel could not be found.</p><a href="' . get_permalink(76) . '" class="button">Back to Live TV</a>';
                        }
                    } else {
                        // Channel grid view
                        include get_template_directory() . '/inc/parts/livetv-grid-new.php';
                    }
                } catch (Exception $e) {
                    echo '<div style="background: #f44336; color: white; padding: 20px; margin: 20px 0; border-radius: 5px;">';
                    echo '<h3>Error Loading Live TV</h3>';
                    echo '<p>Error: ' . esc_html($e->getMessage()) . '</p>';
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>
