/**
 * Debug CSS - Force load premium admin styles
 */

/* Test if CSS is loading */
body.wp-admin::before {
    content: "DeshiFlix Premium CSS Loaded!";
    position: fixed;
    top: 32px;
    right: 20px;
    background: #46b450;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    z-index: 99999;
    font-size: 12px;
    font-weight: bold;
}

/* Force premium admin styles */
.wrap.dooplay-admin-wrap {
    margin: 20px 0 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #dee2e6 !important;
}

.dooplay-admin-breadcrumb {
    background: white !important;
    padding: 15px 30px !important;
    border-bottom: 2px solid #667eea !important;
    font-size: 14px !important;
    margin: 0 !important;
}

.dooplay-admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 40px 30px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin: 0 !important;
    position: relative !important;
}

.dooplay-admin-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') !important;
    opacity: 0.3 !important;
}

.dooplay-admin-title h1 {
    margin: 0 !important;
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    color: white !important;
    position: relative !important;
    z-index: 1 !important;
}

.dooplay-admin-title .dashicons {
    color: #ffd700 !important;
    font-size: 2.5rem !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.dooplay-admin-title .description {
    margin: 10px 0 0 0 !important;
    opacity: 0.9 !important;
    font-size: 1.1rem !important;
    color: white !important;
    position: relative !important;
    z-index: 1 !important;
}

.dooplay-admin-content {
    background: white !important;
    min-height: 500px !important;
    padding: 40px 30px !important;
    margin: 0 !important;
}

.plans-overview-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 25px !important;
    margin-bottom: 40px !important;
}

.overview-card {
    background: white !important;
    border-radius: 15px !important;
    padding: 30px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    align-items: center !important;
    gap: 20px !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border-left: 5px solid #667eea !important;
    margin: 0 !important;
    position: relative !important;
    overflow: hidden !important;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.overview-card:hover::before {
    opacity: 1;
}

.overview-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15) !important;
}

.overview-card.total-plans {
    border-left-color: #667eea !important;
}

.overview-card.active-subscriptions {
    border-left-color: #46b450 !important;
}

.overview-card.monthly-revenue {
    border-left-color: #ffb900 !important;
}

.overview-card.popular-plan {
    border-left-color: #dc3545 !important;
}

.overview-card .card-icon {
    width: 60px !important;
    height: 60px !important;
    border-radius: 15px !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
    position: relative !important;
    z-index: 1 !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.overview-card .card-icon .dashicons {
    font-size: 24px !important;
    color: white !important;
}

.overview-card .card-content {
    position: relative !important;
    z-index: 1 !important;
}

.overview-card .card-content h3 {
    margin: 0 0 8px 0 !important;
    font-size: 2rem !important;
    font-weight: 800 !important;
    color: #333 !important;
    line-height: 1 !important;
}

.overview-card .card-content p {
    margin: 0 !important;
    color: #666 !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
}

/* Section headers */
.dooplay-section-header {
    margin-bottom: 30px !important;
    padding-bottom: 20px !important;
    border-bottom: 2px solid #f1f1f1 !important;
}

.dooplay-section-header .header-content h2 {
    margin: 0 0 10px 0 !important;
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #333 !important;
}

.dooplay-section-header .header-content p {
    margin: 0 !important;
    color: #666 !important;
    font-size: 1.1rem !important;
}

/* Buttons */
.button.button-primary {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    border: none !important;
    color: white !important;
    padding: 12px 24px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    text-shadow: none !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.button.button-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
    background: linear-gradient(135deg, #5a6fd8, #6a5acd) !important;
}

.button.button-secondary {
    background: white !important;
    border: 2px solid #667eea !important;
    color: #667eea !important;
    padding: 10px 22px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.button.button-secondary:hover {
    background: #667eea !important;
    color: white !important;
    transform: translateY(-2px) !important;
}

/* Loading animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.overview-card {
    animation: pulse 2s infinite !important;
}

.overview-card:hover {
    animation: none !important;
}

/* Responsive */
@media (max-width: 768px) {
    .dooplay-admin-header {
        flex-direction: column !important;
        gap: 20px !important;
        text-align: center !important;
        padding: 30px 20px !important;
    }
    
    .dooplay-admin-content {
        padding: 20px !important;
    }
    
    .plans-overview-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }
    
    .overview-card {
        padding: 20px !important;
    }
}
