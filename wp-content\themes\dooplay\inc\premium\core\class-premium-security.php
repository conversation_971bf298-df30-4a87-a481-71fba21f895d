<?php
/**
 * DeshiFlix Premium System - Security Class
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Premium_Security {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Security settings
     */
    private $settings = array();
    
    /**
     * Failed login attempts
     */
    private $failed_attempts = array();
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize security system
     */
    private function init() {
        $this->load_settings();
        $this->init_hooks();
    }
    
    /**
     * Load security settings
     */
    private function load_settings() {
        $this->settings = get_option('deshiflix_premium_security', array(
            'enable_content_protection' => true,
            'enable_download_protection' => true,
            'enable_ip_restriction' => false,
            'enable_device_tracking' => true,
            'max_login_attempts' => 5,
            'lockout_duration' => 30, // minutes
            'enable_two_factor' => false,
            'enable_watermark' => true,
            'enable_right_click_protection' => true,
            'enable_developer_tools_protection' => true,
            'enable_hotlink_protection' => true,
            'enable_vpn_detection' => false,
            'allowed_countries' => array('BD'), // Bangladesh only
            'encryption_key' => $this->get_or_generate_encryption_key()
        ));
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Authentication security
        add_action('wp_login_failed', array($this, 'handle_failed_login'));
        add_filter('authenticate', array($this, 'check_login_attempts'), 30, 3);
        add_action('wp_login', array($this, 'handle_successful_login'), 10, 2);
        
        // Content protection
        add_action('wp_head', array($this, 'add_content_protection_scripts'));
        add_action('wp_footer', array($this, 'add_content_protection_footer'));
        add_filter('the_content', array($this, 'protect_content'), 999);
        
        // Download protection
        add_action('init', array($this, 'handle_protected_downloads'));
        add_filter('dooplay_download_links', array($this, 'protect_download_links'), 10, 2);
        
        // Device tracking
        add_action('wp_login', array($this, 'track_user_device'), 10, 2);
        add_action('wp_ajax_remove_device', array($this, 'ajax_remove_device'));
        add_action('wp_ajax_nopriv_check_device_limit', array($this, 'ajax_check_device_limit'));
        
        // IP and location security
        add_action('init', array($this, 'check_ip_restrictions'));
        add_action('wp_login', array($this, 'check_location_restrictions'), 10, 2);
        
        // Payment security
        add_filter('deshiflix_payment_data', array($this, 'encrypt_payment_data'));
        add_action('deshiflix_payment_webhook', array($this, 'verify_payment_webhook'));
        
        // Admin security
        add_action('admin_init', array($this, 'check_admin_access'));
        add_filter('wp_ajax_premium_admin_action', array($this, 'verify_admin_nonce'));
        
        // Anti-piracy measures
        add_action('wp_head', array($this, 'add_anti_piracy_headers'));
        add_action('template_redirect', array($this, 'prevent_direct_access'));
        
        // AJAX hooks
        add_action('wp_ajax_report_piracy', array($this, 'ajax_report_piracy'));
        add_action('wp_ajax_nopriv_report_piracy', array($this, 'ajax_report_piracy'));
    }
    
    /**
     * Handle failed login attempts
     */
    public function handle_failed_login($username) {
        $ip = $this->get_client_ip();
        $key = 'failed_login_' . md5($ip . $username);
        
        $attempts = get_transient($key) ?: 0;
        $attempts++;
        
        set_transient($key, $attempts, $this->settings['lockout_duration'] * 60);
        
        // Log security event
        $this->log_security_event('failed_login', array(
            'username' => $username,
            'ip' => $ip,
            'attempts' => $attempts,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ));
        
        // Send alert if too many attempts
        if ($attempts >= $this->settings['max_login_attempts']) {
            $this->send_security_alert('Multiple failed login attempts', array(
                'username' => $username,
                'ip' => $ip,
                'attempts' => $attempts
            ));
        }
    }
    
    /**
     * Check login attempts
     */
    public function check_login_attempts($user, $username, $password) {
        if (empty($username) || empty($password)) {
            return $user;
        }
        
        $ip = $this->get_client_ip();
        $key = 'failed_login_' . md5($ip . $username);
        
        $attempts = get_transient($key) ?: 0;
        
        if ($attempts >= $this->settings['max_login_attempts']) {
            return new WP_Error('too_many_attempts', 
                sprintf(__('Too many failed login attempts. Please try again in %d minutes.', 'deshiflix'), 
                        $this->settings['lockout_duration']));
        }
        
        return $user;
    }
    
    /**
     * Handle successful login
     */
    public function handle_successful_login($user_login, $user) {
        $ip = $this->get_client_ip();
        $key = 'failed_login_' . md5($ip . $user_login);
        
        // Clear failed attempts
        delete_transient($key);
        
        // Log successful login
        $this->log_security_event('successful_login', array(
            'user_id' => $user->ID,
            'username' => $user_login,
            'ip' => $ip,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ));
        
        // Check for suspicious login
        if ($this->is_suspicious_login($user->ID, $ip)) {
            $this->send_security_alert('Suspicious login detected', array(
                'user_id' => $user->ID,
                'username' => $user_login,
                'ip' => $ip
            ));
        }
    }
    
    /**
     * Add content protection scripts
     */
    public function add_content_protection_scripts() {
        if (!$this->settings['enable_content_protection']) {
            return;
        }
        
        ?>
        <script>
        // Disable right-click
        <?php if ($this->settings['enable_right_click_protection']): ?>
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });
        <?php endif; ?>
        
        // Disable developer tools
        <?php if ($this->settings['enable_developer_tools_protection']): ?>
        document.addEventListener('keydown', function(e) {
            // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
            if (e.keyCode === 123 || 
                (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) ||
                (e.ctrlKey && e.keyCode === 85)) {
                e.preventDefault();
                return false;
            }
        });
        
        // Detect developer tools
        var devtools = {
            open: false,
            orientation: null
        };
        
        setInterval(function() {
            if (window.outerHeight - window.innerHeight > 200 || 
                window.outerWidth - window.innerWidth > 200) {
                if (!devtools.open) {
                    devtools.open = true;
                    console.clear();
                    console.log('%cContent is protected!', 'color: red; font-size: 20px; font-weight: bold;');
                }
            } else {
                devtools.open = false;
            }
        }, 500);
        <?php endif; ?>
        
        // Disable text selection
        document.onselectstart = function() { return false; };
        document.onmousedown = function() { return false; };
        
        // Disable drag and drop
        document.ondragstart = function() { return false; };
        
        // Disable print screen
        document.addEventListener('keyup', function(e) {
            if (e.keyCode === 44) {
                alert('Screenshots are not allowed!');
            }
        });
        </script>
        
        <style>
        /* Disable text selection */
        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        /* Disable image dragging */
        img {
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
            user-drag: none;
            pointer-events: none;
        }
        
        /* Hide video controls in some cases */
        .protected-video video::-webkit-media-controls {
            display: none !important;
        }
        
        .protected-video video::-webkit-media-controls-enclosure {
            display: none !important;
        }
        </style>
        <?php
    }
    
    /**
     * Add content protection footer
     */
    public function add_content_protection_footer() {
        if (!$this->settings['enable_content_protection']) {
            return;
        }
        
        ?>
        <script>
        // Additional protection measures
        (function() {
            // Disable console
            var _log = console.log;
            console.log = function() {
                _log.apply(console, ['%cAccess Denied', 'color: red; font-size: 16px;']);
            };
            
            // Monitor for suspicious activity
            var suspiciousActivity = 0;
            
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    suspiciousActivity++;
                    if (suspiciousActivity > 10) {
                        // Report suspicious activity
                        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'action=report_piracy&type=suspicious_activity&nonce=<?php echo wp_create_nonce('security_nonce'); ?>'
                        });
                    }
                }
            });
            
            // Watermark for premium content
            <?php if ($this->settings['enable_watermark']): ?>
            if (document.querySelector('.premium-content')) {
                var watermark = document.createElement('div');
                watermark.innerHTML = 'DeshiFlix Premium - <?php echo get_current_user_id(); ?>';
                watermark.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); opacity: 0.1; font-size: 24px; color: white; pointer-events: none; z-index: 9999; font-weight: bold;';
                document.body.appendChild(watermark);
            }
            <?php endif; ?>
        })();
        </script>
        <?php
    }
    
    /**
     * Protect content
     */
    public function protect_content($content) {
        global $post;
        
        if (!$post || is_admin()) {
            return $content;
        }
        
        // Check if content is premium
        $is_premium = get_post_meta($post->ID, '_is_premium_content', true);
        
        if ($is_premium && !deshiflix_premium()->user_has_content_access($post->ID)) {
            // Replace content with protection message
            return $this->get_content_protection_message();
        }
        
        // Add protection wrapper for premium content
        if ($is_premium) {
            $content = '<div class="premium-content protected-content">' . $content . '</div>';
        }
        
        return $content;
    }
    
    /**
     * Get content protection message
     */
    private function get_content_protection_message() {
        return '<div class="content-protection-message">
                    <h3>' . __('Premium Content', 'deshiflix') . '</h3>
                    <p>' . __('This content is available for premium subscribers only.', 'deshiflix') . '</p>
                    <a href="' . home_url('/premium-plans/') . '" class="btn btn-premium">' . __('Upgrade Now', 'deshiflix') . '</a>
                </div>';
    }
    
    /**
     * Handle protected downloads
     */
    public function handle_protected_downloads() {
        if (!isset($_GET['deshiflix_download']) || !isset($_GET['token'])) {
            return;
        }
        
        $download_id = sanitize_text_field($_GET['deshiflix_download']);
        $token = sanitize_text_field($_GET['token']);
        
        // Verify token
        if (!$this->verify_download_token($download_id, $token)) {
            wp_die(__('Invalid download token', 'deshiflix'));
        }
        
        // Check user permissions
        if (!is_user_logged_in() || !deshiflix_premium()->is_user_premium()) {
            wp_die(__('Premium subscription required', 'deshiflix'));
        }
        
        // Check download limits
        $user_id = get_current_user_id();
        if (!$this->check_download_limits($user_id)) {
            wp_die(__('Download limit exceeded', 'deshiflix'));
        }
        
        // Get download URL
        $download_url = $this->get_protected_download_url($download_id);
        
        if (!$download_url) {
            wp_die(__('Download not found', 'deshiflix'));
        }
        
        // Log download
        $this->log_download($user_id, $download_id);
        
        // Redirect to actual download
        wp_redirect($download_url);
        exit;
    }
    
    /**
     * Protect download links
     */
    public function protect_download_links($links, $post_id) {
        if (!$this->settings['enable_download_protection']) {
            return $links;
        }
        
        $protected_links = array();
        
        foreach ($links as $quality => $url) {
            $download_id = md5($post_id . $quality . $url);
            $token = $this->generate_download_token($download_id);
            
            $protected_url = add_query_arg(array(
                'deshiflix_download' => $download_id,
                'token' => $token
            ), home_url('/'));
            
            $protected_links[$quality] = $protected_url;
        }
        
        return $protected_links;
    }
    
    /**
     * Generate download token
     */
    private function generate_download_token($download_id) {
        $user_id = get_current_user_id();
        $timestamp = time();
        $data = $download_id . $user_id . $timestamp;
        
        $token = hash_hmac('sha256', $data, $this->settings['encryption_key']);
        
        // Store token temporarily
        set_transient('download_token_' . $download_id, array(
            'user_id' => $user_id,
            'timestamp' => $timestamp,
            'token' => $token
        ), 3600); // 1 hour
        
        return $token;
    }
    
    /**
     * Verify download token
     */
    private function verify_download_token($download_id, $token) {
        $stored_data = get_transient('download_token_' . $download_id);
        
        if (!$stored_data) {
            return false;
        }
        
        // Check if token matches
        if ($stored_data['token'] !== $token) {
            return false;
        }
        
        // Check if user matches
        if ($stored_data['user_id'] !== get_current_user_id()) {
            return false;
        }
        
        // Check if token is not expired (1 hour)
        if (time() - $stored_data['timestamp'] > 3600) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Track user device
     */
    public function track_user_device($user_login, $user) {
        if (!$this->settings['enable_device_tracking']) {
            return;
        }
        
        $device_info = $this->get_device_info();
        $device_fingerprint = $this->generate_device_fingerprint();
        
        global $wpdb;
        $table_devices = $wpdb->prefix . 'deshiflix_premium_devices';
        
        // Check if device exists
        $existing_device = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_devices WHERE user_id = %d AND device_id = %s",
            $user->ID,
            $device_fingerprint
        ));
        
        if ($existing_device) {
            // Update last active
            $wpdb->update(
                $table_devices,
                array('last_active' => current_time('mysql')),
                array('id' => $existing_device->id)
            );
        } else {
            // Check device limit
            $device_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_devices WHERE user_id = %d AND status = 'active'",
                $user->ID
            ));
            
            $max_devices = $this->get_user_max_devices($user->ID);
            
            if ($device_count >= $max_devices) {
                // Remove oldest device
                $oldest_device = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $table_devices 
                     WHERE user_id = %d AND status = 'active' 
                     ORDER BY last_active ASC LIMIT 1",
                    $user->ID
                ));
                
                if ($oldest_device) {
                    $wpdb->update(
                        $table_devices,
                        array('status' => 'inactive'),
                        array('id' => $oldest_device->id)
                    );
                }
            }
            
            // Add new device
            $wpdb->insert($table_devices, array(
                'user_id' => $user->ID,
                'device_id' => $device_fingerprint,
                'device_name' => $device_info['name'],
                'device_type' => $device_info['type'],
                'browser' => $device_info['browser'],
                'os' => $device_info['os'],
                'ip_address' => $this->get_client_ip(),
                'status' => 'active'
            ));
        }
    }
    
    /**
     * Generate device fingerprint
     */
    private function generate_device_fingerprint() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $accept_language = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
        $accept_encoding = $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '';
        
        $fingerprint_data = $user_agent . $accept_language . $accept_encoding;
        
        return hash('sha256', $fingerprint_data);
    }
    
    /**
     * Get device info
     */
    private function get_device_info() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Simple device detection
        $device_type = 'desktop';
        if (preg_match('/Mobile|Android|iPhone/', $user_agent)) {
            $device_type = 'mobile';
        } elseif (preg_match('/Tablet|iPad/', $user_agent)) {
            $device_type = 'tablet';
        }
        
        // Browser detection
        $browser = 'Unknown';
        if (preg_match('/Chrome/', $user_agent)) {
            $browser = 'Chrome';
        } elseif (preg_match('/Firefox/', $user_agent)) {
            $browser = 'Firefox';
        } elseif (preg_match('/Safari/', $user_agent)) {
            $browser = 'Safari';
        } elseif (preg_match('/Edge/', $user_agent)) {
            $browser = 'Edge';
        }
        
        // OS detection
        $os = 'Unknown';
        if (preg_match('/Windows/', $user_agent)) {
            $os = 'Windows';
        } elseif (preg_match('/Mac/', $user_agent)) {
            $os = 'macOS';
        } elseif (preg_match('/Linux/', $user_agent)) {
            $os = 'Linux';
        } elseif (preg_match('/Android/', $user_agent)) {
            $os = 'Android';
        } elseif (preg_match('/iOS/', $user_agent)) {
            $os = 'iOS';
        }
        
        return array(
            'name' => $browser . ' on ' . $os,
            'type' => $device_type,
            'browser' => $browser,
            'os' => $os
        );
    }
    
    /**
     * Check IP restrictions
     */
    public function check_ip_restrictions() {
        if (!$this->settings['enable_ip_restriction']) {
            return;
        }
        
        $ip = $this->get_client_ip();
        $country = $this->get_country_from_ip($ip);
        
        if (!in_array($country, $this->settings['allowed_countries'])) {
            $this->log_security_event('blocked_country_access', array(
                'ip' => $ip,
                'country' => $country
            ));
            
            wp_die(__('Access denied from your location', 'deshiflix'));
        }
    }
    
    /**
     * Check location restrictions
     */
    public function check_location_restrictions($user_login, $user) {
        if (!$this->settings['enable_ip_restriction']) {
            return;
        }
        
        $ip = $this->get_client_ip();
        $country = $this->get_country_from_ip($ip);
        
        // Check if user is logging in from a different country
        $last_country = get_user_meta($user->ID, '_last_login_country', true);
        
        if ($last_country && $last_country !== $country) {
            $this->send_security_alert('Login from new location', array(
                'user_id' => $user->ID,
                'username' => $user_login,
                'ip' => $ip,
                'country' => $country,
                'last_country' => $last_country
            ));
        }
        
        update_user_meta($user->ID, '_last_login_country', $country);
    }
    
    /**
     * Get client IP
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Get country from IP
     */
    private function get_country_from_ip($ip) {
        // Simple IP to country detection
        // In production, use a proper GeoIP service
        
        $cache_key = 'country_' . md5($ip);
        $country = get_transient($cache_key);
        
        if ($country === false) {
            // Use a free GeoIP service
            $response = wp_remote_get("http://ip-api.com/json/{$ip}?fields=countryCode");
            
            if (!is_wp_error($response)) {
                $body = wp_remote_retrieve_body($response);
                $data = json_decode($body, true);
                
                $country = $data['countryCode'] ?? 'Unknown';
                set_transient($cache_key, $country, DAY_IN_SECONDS);
            } else {
                $country = 'Unknown';
            }
        }
        
        return $country;
    }
    
    /**
     * Encrypt payment data
     */
    public function encrypt_payment_data($data) {
        $encrypted_data = array();
        
        foreach ($data as $key => $value) {
            if (in_array($key, array('card_number', 'cvv', 'pin'))) {
                $encrypted_data[$key] = $this->encrypt_string($value);
            } else {
                $encrypted_data[$key] = $value;
            }
        }
        
        return $encrypted_data;
    }
    
    /**
     * Encrypt string
     */
    private function encrypt_string($string) {
        $method = 'AES-256-CBC';
        $key = hash('sha256', $this->settings['encryption_key']);
        $iv = openssl_random_pseudo_bytes(16);
        
        $encrypted = openssl_encrypt($string, $method, $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Decrypt string
     */
    private function decrypt_string($encrypted_string) {
        $method = 'AES-256-CBC';
        $key = hash('sha256', $this->settings['encryption_key']);
        
        $data = base64_decode($encrypted_string);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, $method, $key, 0, $iv);
    }
    
    /**
     * Verify payment webhook
     */
    public function verify_payment_webhook($data) {
        $signature = $_SERVER['HTTP_X_SIGNATURE'] ?? '';
        
        if (empty($signature)) {
            return false;
        }
        
        $expected_signature = hash_hmac('sha256', json_encode($data), $this->settings['encryption_key']);
        
        return hash_equals($signature, $expected_signature);
    }
    
    /**
     * Add anti-piracy headers
     */
    public function add_anti_piracy_headers() {
        header('X-Frame-Options: SAMEORIGIN');
        header('X-Content-Type-Options: nosniff');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        if ($this->settings['enable_hotlink_protection']) {
            header('X-Permitted-Cross-Domain-Policies: none');
        }
    }
    
    /**
     * Prevent direct access
     */
    public function prevent_direct_access() {
        // Prevent direct access to media files
        if (preg_match('/\.(mp4|mkv|avi|mov|wmv|flv|webm)$/i', $_SERVER['REQUEST_URI'])) {
            if (!is_user_logged_in() || !deshiflix_premium()->is_user_premium()) {
                status_header(403);
                exit('Access Denied');
            }
        }
    }
    
    /**
     * Log security event
     */
    private function log_security_event($event_type, $data) {
        global $wpdb;
        
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        $wpdb->insert($table_analytics, array(
            'user_id' => get_current_user_id() ?: null,
            'event_type' => 'security_' . $event_type,
            'event_data' => json_encode($data),
            'session_id' => session_id(),
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ));
    }
    
    /**
     * Send security alert
     */
    private function send_security_alert($subject, $data) {
        $admin_email = get_option('admin_email');
        
        $message = "Security Alert: {$subject}\n\n";
        $message .= "Details:\n";
        
        foreach ($data as $key => $value) {
            $message .= ucfirst(str_replace('_', ' ', $key)) . ": {$value}\n";
        }
        
        $message .= "\nTime: " . current_time('mysql') . "\n";
        $message .= "Site: " . home_url() . "\n";
        
        wp_mail($admin_email, "[DeshiFlix Security] {$subject}", $message);
    }
    
    /**
     * Is suspicious login
     */
    private function is_suspicious_login($user_id, $ip) {
        // Check if login is from a different IP than usual
        $last_ip = get_user_meta($user_id, '_last_login_ip', true);
        
        if ($last_ip && $last_ip !== $ip) {
            // Check if IPs are from different countries
            $last_country = $this->get_country_from_ip($last_ip);
            $current_country = $this->get_country_from_ip($ip);
            
            if ($last_country !== $current_country) {
                return true;
            }
        }
        
        update_user_meta($user_id, '_last_login_ip', $ip);
        
        return false;
    }
    
    /**
     * Get or generate encryption key
     */
    private function get_or_generate_encryption_key() {
        $key = get_option('deshiflix_encryption_key');
        
        if (!$key) {
            $key = wp_generate_password(64, true, true);
            update_option('deshiflix_encryption_key', $key);
        }
        
        return $key;
    }
    
    /**
     * Check download limits
     */
    private function check_download_limits($user_id) {
        $premium_features = DeshiFlix_Premium_Features::get_instance();
        $download_check = $premium_features->check_download_limits($user_id);
        
        return $download_check['allowed'];
    }
    
    /**
     * Get user max devices
     */
    private function get_user_max_devices($user_id) {
        $user_plan_id = deshiflix_premium()->get_user_premium_plan($user_id);
        
        if (!$user_plan_id) {
            return 1;
        }
        
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT max_devices FROM $table_plans WHERE id = %d",
            $user_plan_id
        ));
        
        return $plan ? $plan->max_devices : 1;
    }
    
    /**
     * Get protected download URL
     */
    private function get_protected_download_url($download_id) {
        // This would retrieve the actual download URL from database
        // For now, return a placeholder
        return get_transient('download_url_' . $download_id);
    }
    
    /**
     * Log download
     */
    private function log_download($user_id, $download_id) {
        $this->log_security_event('download_access', array(
            'user_id' => $user_id,
            'download_id' => $download_id
        ));
    }
    
    /**
     * Check admin access
     */
    public function check_admin_access() {
        if (!is_admin()) {
            return;
        }
        
        // Additional admin security checks
        $user_id = get_current_user_id();
        
        if ($user_id && current_user_can('manage_options')) {
            $this->log_security_event('admin_access', array(
                'user_id' => $user_id,
                'page' => $_GET['page'] ?? 'dashboard'
            ));
        }
    }
    
    /**
     * Verify admin nonce
     */
    public function verify_admin_nonce($action) {
        if (!wp_verify_nonce($_POST['nonce'], 'premium_admin_nonce')) {
            wp_die(__('Security check failed', 'deshiflix'));
        }
        
        return $action;
    }
    
    /**
     * AJAX remove device
     */
    public function ajax_remove_device() {
        check_ajax_referer('security_nonce', 'nonce');
        
        $device_id = sanitize_text_field($_POST['device_id']);
        $user_id = get_current_user_id();
        
        global $wpdb;
        $table_devices = $wpdb->prefix . 'deshiflix_premium_devices';
        
        $result = $wpdb->update(
            $table_devices,
            array('status' => 'inactive'),
            array('user_id' => $user_id, 'device_id' => $device_id)
        );
        
        if ($result) {
            wp_send_json_success(array('message' => __('Device removed', 'deshiflix')));
        } else {
            wp_send_json_error(array('message' => __('Failed to remove device', 'deshiflix')));
        }
    }
    
    /**
     * AJAX check device limit
     */
    public function ajax_check_device_limit() {
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            wp_send_json_error(array('message' => __('Not logged in', 'deshiflix')));
        }
        
        global $wpdb;
        $table_devices = $wpdb->prefix . 'deshiflix_premium_devices';
        
        $device_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_devices WHERE user_id = %d AND status = 'active'",
            $user_id
        ));
        
        $max_devices = $this->get_user_max_devices($user_id);
        
        wp_send_json_success(array(
            'current_devices' => $device_count,
            'max_devices' => $max_devices,
            'can_add_device' => $device_count < $max_devices
        ));
    }
    
    /**
     * AJAX report piracy
     */
    public function ajax_report_piracy() {
        check_ajax_referer('security_nonce', 'nonce');
        
        $type = sanitize_text_field($_POST['type']);
        $details = sanitize_textarea_field($_POST['details'] ?? '');
        
        $this->log_security_event('piracy_report', array(
            'type' => $type,
            'details' => $details,
            'url' => $_SERVER['HTTP_REFERER'] ?? ''
        ));
        
        $this->send_security_alert('Piracy Report', array(
            'type' => $type,
            'details' => $details,
            'url' => $_SERVER['HTTP_REFERER'] ?? '',
            'ip' => $this->get_client_ip()
        ));
        
        wp_send_json_success(array('message' => __('Report submitted', 'deshiflix')));
    }
}

// Initialize security system
DeshiFlix_Premium_Security::get_instance();
