<?php
/*
* Live TV Stream Tester for Admin Panel
* Test stream URLs before adding them to channels
*/

// Security check
if (!defined('ABSPATH')) {
    exit;
}

// Check admin permissions
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Handle AJAX stream test
if (isset($_POST['action']) && $_POST['action'] === 'test_stream') {
    check_ajax_referer('livetv_stream_test', 'nonce');
    
    $stream_url = sanitize_url($_POST['stream_url']);
    $result = test_stream_url($stream_url);
    
    wp_send_json($result);
    exit;
}

function test_stream_url($url) {
    if (empty($url)) {
        return array(
            'success' => false,
            'message' => 'URL is required',
            'details' => array()
        );
    }
    
    // Basic URL validation
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return array(
            'success' => false,
            'message' => 'Invalid URL format',
            'details' => array('url' => $url)
        );
    }
    
    $details = array();
    $details['url'] = $url;
    $details['timestamp'] = current_time('mysql');
    
    // Check URL accessibility
    $response = wp_remote_head($url, array(
        'timeout' => 10,
        'user-agent' => 'DooPlay Live TV Stream Tester/1.0'
    ));
    
    if (is_wp_error($response)) {
        return array(
            'success' => false,
            'message' => 'Failed to connect: ' . $response->get_error_message(),
            'details' => $details
        );
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $headers = wp_remote_retrieve_headers($response);
    
    $details['response_code'] = $response_code;
    $details['content_type'] = isset($headers['content-type']) ? $headers['content-type'] : 'Unknown';
    $details['content_length'] = isset($headers['content-length']) ? $headers['content-length'] : 'Unknown';
    
    // Check if it's a valid streaming URL
    $valid_types = array(
        'application/vnd.apple.mpegurl',
        'application/x-mpegurl',
        'video/mp4',
        'video/webm',
        'video/ogg',
        'application/dash+xml'
    );
    
    $is_valid_stream = false;
    foreach ($valid_types as $type) {
        if (strpos($details['content_type'], $type) !== false) {
            $is_valid_stream = true;
            break;
        }
    }
    
    // Check URL patterns
    $url_lower = strtolower($url);
    if (strpos($url_lower, '.m3u8') !== false || 
        strpos($url_lower, '.mpd') !== false ||
        strpos($url_lower, 'playlist') !== false) {
        $is_valid_stream = true;
    }
    
    if ($response_code === 200) {
        if ($is_valid_stream) {
            return array(
                'success' => true,
                'message' => 'Stream URL appears to be valid and accessible',
                'details' => $details
            );
        } else {
            return array(
                'success' => false,
                'message' => 'URL is accessible but may not be a valid stream format',
                'details' => $details
            );
        }
    } else {
        return array(
            'success' => false,
            'message' => 'HTTP Error: ' . $response_code,
            'details' => $details
        );
    }
}
?>

<div class="wrap">
    <h1>Live TV Stream Tester</h1>
    <p>Test stream URLs to verify they are working before adding them to channels.</p>
    
    <div class="stream-tester-container">
        <div class="test-form-section">
            <h2>Test Stream URL</h2>
            <form id="stream-test-form">
                <?php wp_nonce_field('livetv_stream_test', 'nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="stream_url">Stream URL</label>
                        </th>
                        <td>
                            <input type="url" 
                                   id="stream_url" 
                                   name="stream_url" 
                                   class="regular-text" 
                                   placeholder="https://example.com/stream.m3u8"
                                   required>
                            <p class="description">
                                Enter the complete stream URL (HLS .m3u8, DASH .mpd, or direct video URL)
                            </p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <button type="submit" class="button button-primary" id="test-stream-btn">
                        <span class="dashicons dashicons-media-video"></span> Test Stream
                    </button>
                    <button type="button" class="button" id="clear-results-btn">
                        <span class="dashicons dashicons-dismiss"></span> Clear Results
                    </button>
                </p>
            </form>
        </div>
        
        <div class="test-results-section">
            <h2>Test Results</h2>
            <div id="test-results" class="test-results-container">
                <p class="description">Enter a stream URL and click "Test Stream" to see results.</p>
            </div>
        </div>
        
        <div class="stream-preview-section" style="display: none;">
            <h2>Stream Preview</h2>
            <div id="stream-preview-container">
                <video id="preview-video" 
                       controls 
                       muted 
                       style="width: 100%; max-width: 800px; height: 450px; background: #000;">
                    Your browser does not support the video tag.
                </video>
                <p class="description">
                    <strong>Note:</strong> Some streams may not play due to CORS restrictions or require specific players.
                </p>
            </div>
        </div>
    </div>
    
    <div class="stream-tips-section">
        <h2>Stream URL Tips</h2>
        <div class="tips-grid">
            <div class="tip-card">
                <h3><span class="dashicons dashicons-format-video"></span> HLS Streams (.m3u8)</h3>
                <p>Most common format for live streaming. Usually ends with .m3u8</p>
                <code>https://example.com/stream/playlist.m3u8</code>
            </div>
            
            <div class="tip-card">
                <h3><span class="dashicons dashicons-media-video"></span> Direct Video</h3>
                <p>Direct video files (MP4, WebM, etc.)</p>
                <code>https://example.com/video.mp4</code>
            </div>
            
            <div class="tip-card">
                <h3><span class="dashicons dashicons-admin-settings"></span> DASH Streams (.mpd)</h3>
                <p>Dynamic Adaptive Streaming format</p>
                <code>https://example.com/stream/manifest.mpd</code>
            </div>
            
            <div class="tip-card">
                <h3><span class="dashicons dashicons-warning"></span> Common Issues</h3>
                <ul>
                    <li>CORS restrictions</li>
                    <li>Geo-blocking</li>
                    <li>Authentication required</li>
                    <li>Temporary offline</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.stream-tester-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 20px 0;
}

.test-form-section,
.test-results-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
}

.stream-preview-section {
    grid-column: 1 / -1;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.test-results-container {
    min-height: 200px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.result-success {
    color: #46b450;
    border-left: 4px solid #46b450;
    padding-left: 15px;
}

.result-error {
    color: #dc3232;
    border-left: 4px solid #dc3232;
    padding-left: 15px;
}

.result-details {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
    font-family: monospace;
    font-size: 12px;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.tip-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 15px;
}

.tip-card h3 {
    margin-top: 0;
    color: #23282d;
}

.tip-card .dashicons {
    color: #0073aa;
    margin-right: 5px;
}

.tip-card code {
    display: block;
    background: #f1f1f1;
    padding: 5px;
    border-radius: 3px;
    margin-top: 10px;
    word-break: break-all;
}

.tip-card ul {
    margin: 10px 0 0 20px;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

#test-stream-btn.loading::before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 5px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .stream-tester-container {
        grid-template-columns: 1fr;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    var testForm = $('#stream-test-form');
    var testBtn = $('#test-stream-btn');
    var clearBtn = $('#clear-results-btn');
    var resultsContainer = $('#test-results');
    var previewSection = $('.stream-preview-section');
    var previewVideo = $('#preview-video');
    
    testForm.on('submit', function(e) {
        e.preventDefault();
        
        var streamUrl = $('#stream_url').val().trim();
        if (!streamUrl) {
            alert('Please enter a stream URL');
            return;
        }
        
        // Show loading state
        testBtn.addClass('loading').prop('disabled', true);
        resultsContainer.html('<p>Testing stream URL...</p>');
        
        // Test the stream
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'test_stream',
                stream_url: streamUrl,
                nonce: $('[name="nonce"]').val()
            },
            success: function(response) {
                displayResults(response);
                if (response.success) {
                    showPreview(streamUrl);
                }
            },
            error: function() {
                displayResults({
                    success: false,
                    message: 'Failed to test stream URL',
                    details: {}
                });
            },
            complete: function() {
                testBtn.removeClass('loading').prop('disabled', false);
            }
        });
    });
    
    clearBtn.on('click', function() {
        resultsContainer.html('<p class="description">Enter a stream URL and click "Test Stream" to see results.</p>');
        previewSection.hide();
        $('#stream_url').val('');
    });
    
    function displayResults(result) {
        var html = '';
        
        if (result.success) {
            html += '<div class="result-success">';
            html += '<h3><span class="dashicons dashicons-yes"></span> Success</h3>';
            html += '<p>' + result.message + '</p>';
            html += '</div>';
        } else {
            html += '<div class="result-error">';
            html += '<h3><span class="dashicons dashicons-no"></span> Error</h3>';
            html += '<p>' + result.message + '</p>';
            html += '</div>';
        }
        
        if (result.details && Object.keys(result.details).length > 0) {
            html += '<div class="result-details">';
            html += '<strong>Details:</strong><br>';
            for (var key in result.details) {
                html += key + ': ' + result.details[key] + '<br>';
            }
            html += '</div>';
        }
        
        resultsContainer.html(html);
    }
    
    function showPreview(streamUrl) {
        previewSection.show();
        previewVideo.attr('src', streamUrl);
        
        // Try to load the video
        previewVideo[0].load();
        
        previewVideo.on('error', function() {
            previewSection.find('.description').html(
                '<strong>Preview Error:</strong> Unable to preview this stream. ' +
                'This may be due to CORS restrictions or the stream format. ' +
                'The stream may still work in the actual player.'
            );
        });
    }
});
</script>
