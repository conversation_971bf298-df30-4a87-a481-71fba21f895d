# Copyright (C) 2023 <PERSON><PERSON><PERSON>
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Payment Gateway bKash for WC 3.0.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/bKash-woocommerce\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2023-04-09T18:11:33+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.7.1\n"
"X-Domain: dc-bkash\n"

#. Plugin Name of the plugin
msgid "Payment Gateway bKash for WC"
msgstr ""

#. Plugin URI of the plugin
msgid "https://wordpress.org/plugins/woo-payment-bkash/"
msgstr ""

#. Description of the plugin
msgid "An eCommerce payment method that helps you sell anything. Beautifully."
msgstr ""

#. Author of the plugin
msgid "Kapil Paul"
msgstr ""

#. Author URI of the plugin
msgid "https://kapilpaul.me"
msgstr ""

#: includes/Abstracts/BkashProcessor.php:588
msgid "Refund amount"
msgstr ""

#: includes/Admin/Menu.php:46
#: includes/Gateway/Bkash.php:47
#: includes/Gateway/Bkash.php:50
msgid "bKash"
msgstr ""

#: includes/Admin/Menu.php:49
#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:165
msgid "Transactions"
msgstr ""

#: includes/Admin/Menu.php:51
msgid "Search Transaction"
msgstr ""

#: includes/Admin/Menu.php:53
#: assets/js/app.js:1
#: assets/src/admin/Pages/refund-container.js:29
#: assets/src/admin/Pages/transactions.js:192
msgid "Refund"
msgstr ""

#: includes/Admin/Menu.php:55
#: assets/js/app.js:1
#: assets/src/admin/Pages/settings.js:146
msgid "Settings"
msgstr ""

#: includes/Admin/Settings.php:36
#: assets/js/app.js:1
#: assets/src/admin/Pages/search-transaction.js:100
msgid "Title"
msgstr ""

#: includes/Admin/Settings.php:38
msgid "bKash Payment"
msgstr ""

#: includes/Admin/Settings.php:41
msgid "Integration Type"
msgstr ""

#: includes/Admin/Settings.php:44
msgid "Checkout (Pop Up)"
msgstr ""

#: includes/Admin/Settings.php:45
msgid "Checkout URL"
msgstr ""

#: includes/Admin/Settings.php:47
msgid "checkout_url"
msgstr ""

#: includes/Admin/Settings.php:50
msgid "Test Mode"
msgstr ""

#: includes/Admin/Settings.php:53
msgid "ON"
msgstr ""

#: includes/Admin/Settings.php:54
msgid "OFF"
msgstr ""

#: includes/Admin/Settings.php:56
msgid "off"
msgstr ""

#: includes/Admin/Settings.php:59
msgid "Test Mode Type"
msgstr ""

#: includes/Admin/Settings.php:65
msgid "with_key"
msgstr ""

#: includes/Admin/Settings.php:80
msgid "User Name"
msgstr ""

#: includes/Admin/Settings.php:91
msgid "Password"
msgstr ""

#: includes/Admin/Settings.php:102
msgid "App Key"
msgstr ""

#: includes/Admin/Settings.php:113
msgid "App Secret"
msgstr ""

#: includes/Admin/Settings.php:124
msgid "Sandbox User Name"
msgstr ""

#: includes/Admin/Settings.php:140
msgid "Sandbox Password"
msgstr ""

#: includes/Admin/Settings.php:156
msgid "Sandbox App Key"
msgstr ""

#: includes/Admin/Settings.php:172
msgid "Sandbox App Secret"
msgstr ""

#: includes/Admin/Settings.php:188
msgid "Enable bKash Charge"
msgstr ""

#: includes/Admin/Settings.php:197
msgid "Charge Type"
msgstr ""

#: includes/Admin/Settings.php:204
#: includes/Admin/Settings.php:217
msgid "This option will only work when the bKash Charge is enabled"
msgstr ""

#: includes/Admin/Settings.php:214
msgid "Charge Amount"
msgstr ""

#: includes/Admin/Settings.php:227
msgid "Description"
msgstr ""

#: includes/Admin/Settings.php:229
msgid "Payment method description that the customer will see on your checkout."
msgstr ""

#: includes/Admin/Settings.php:230
msgid "Pay via bKash"
msgstr ""

#: includes/Ajax.php:38
#: includes/Ajax.php:116
msgid "Something went wrong here!"
msgstr ""

#: includes/Ajax.php:42
#: includes/Ajax.php:120
msgid "Empty value is not allowed"
msgstr ""

#: includes/Ajax.php:51
#: includes/Ajax.php:128
msgid "Wrong or invalid order ID"
msgstr ""

#: includes/Ajax.php:68
#: includes/Ajax.php:137
msgid "Something went wrong!"
msgstr ""

#: includes/API/BkashBaseRestController.php:49
#: includes/API/Upgrade.php:98
msgid "You have no permission to do that"
msgstr ""

#: includes/API/Payment.php:109
msgid "Grant Token"
msgstr ""

#: includes/API/Payment.php:198
msgid "Execute Payment"
msgstr ""

#: includes/API/Payment.php:236
msgid "Query Payment"
msgstr ""

#: includes/API/Payment.php:268
msgid "Search Transaction Details"
msgstr ""

#: includes/API/Payment.php:370
msgid "Unique identifier for the payment."
msgstr ""

#: includes/API/Upgrade.php:67
msgid "There is an upgrading process going on."
msgstr ""

#: includes/API/Upgrade.php:71
msgid "Update is not required"
msgstr ""

#: includes/Gateway/Bkash.php:48
msgid "Pay via bKash payment"
msgstr ""

#. translators: %1$d: page number %2$d: max page number
#: includes/Gateway/Bkash.php:73
msgid "%1$sYou will get %2$s setting options in %3$s here %4$s.%5$s"
msgstr ""

#: includes/Gateway/IntegrationTypes/Checkout.php:198
msgid "No API keys available"
msgstr ""

#. translators: %1$s: Transaction ID, %2$s: Grand Total.
#: includes/Gateway/Manager.php:129
msgid "bKash payment completed. Transaction ID #%1$s. Amount: %2$s"
msgstr ""

#. translators: %1$s: Transaction ID, %2$s: Payment Amount.
#: includes/Gateway/Manager.php:151
msgid "Partial payment. Transaction ID #%1$s! Amount: %2$s"
msgstr ""

#: includes/Gateway/Manager.php:300
#: templates/frontend/transaction-charge.php:12
msgid "bKash Charge"
msgstr ""

#. translators: %1$s: Refund Amount
#: includes/Gateway/Manager.php:380
msgid "BDT %s has been refunded by bKash"
msgstr ""

#: templates/admin/transaction-charge.php:11
msgid "bKash Charge:"
msgstr ""

#: templates/frontend/payment-details.php:14
msgid "bKash Transaction ID:"
msgstr ""

#: templates/frontend/payment-details.php:17
msgid "Amount:"
msgstr ""

#: templates/frontend/payment-details.php:24
msgid "Payment Status:"
msgstr ""

#: templates/frontend/transaction-charge.php:11
msgid "bKash Charge "
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/Doc/doc-container.js:319
msgid "Doc Generation Done."
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/Doc/doc-container.js:335
msgid "API Request/Response"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/Doc/doc-container.js:235
msgid "Error Message Implimentation"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/Doc/doc-container.js:236
msgid "Case #1"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/Doc/doc-container.js:206
#: assets/src/admin/Pages/Doc/doc-container.js:238
msgid "Invoice Number: "
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/Doc/doc-container.js:210
#: assets/src/admin/Pages/Doc/doc-container.js:242
msgid "Time of Transaction: "
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/Doc/doc-container.js:214
#: assets/src/admin/Pages/Doc/doc-container.js:246
msgid "Screenshot"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/Doc/doc-container.js:204
msgid "Case #2"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/generatedoc.js:33
msgid "Generate Doc"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/generatedoc.js:37
msgid "You may generate API Request/Response doc from here."
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/generatedoc.js:46
msgid "In case, if you need sandbox mobile number and OTP then you may use the below number."
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/generatedoc.js:76
msgid "Generating"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/generatedoc.js:77
msgid "Generate"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/generatedoc.js:89
msgid "Download"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/generatedoc.js:95
msgid "Before generate the doc, you must have to add sandbox keys in settings."
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/refund-container.js:17
msgid "Before refund, you must have to add API keys in "
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/search-transaction.js:125
msgid "Search Transaction "
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/search-transaction.js:70
msgid "Transaction ID"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/search-transaction.js:88
msgid "Searching"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/search-transaction.js:89
#: assets/src/admin/Pages/transactions.js:173
msgid "Search"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/search-transaction.js:95
msgid "Transaction Details"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/search-transaction.js:101
msgid "Data"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/search-transaction.js:30
msgid "Before search transaction, you must have to add API keys in "
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/settings.js:112
msgid "Saved Successfully!"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/settings.js:198
msgid "Saving"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/settings.js:199
msgid "Save"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:184
msgid "Order Number"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/refund.js:139
#: assets/src/admin/Pages/transactions.js:185
msgid "Amount"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:186
msgid "Payment ID"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/refund.js:129
#: assets/src/admin/Pages/transactions.js:187
msgid "Trx ID"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:188
msgid "Invoice No"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:189
msgid "Trx Status"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:190
msgid "Verification Status"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:191
msgid "Payment Time"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:193
msgid "Refund Amount"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:194
msgid "Refund Charge"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:195
msgid "Refund ID"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:196
msgid "Refund Reason"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:197
msgid "Action"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:221
msgid "Refunded"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:107
msgid "Successfully verified!"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:114
msgid "Problem in verification!"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/Pages/transactions.js:86
msgid "Verify"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/api-response.js:40
msgid "API Title: "
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/api-response.js:44
msgid "API URL: "
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/refund.js:100
msgid "Search Order ID"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/refund.js:112
msgid "You may type your order number or transaction ID here."
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/refund.js:120
msgid "A merchant can do refund only once for a transaction, it can be a full refund or partial amount refund."
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/refund.js:152
msgid "Reason"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/refund.js:171
msgid "Create refund on WooCommerce?"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/refund.js:87
msgid "Refund Successfully!"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/refund.js:184
msgid "Submitting"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/bKash/refund.js:185
msgid "Submit"
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/components/loader.js:9
msgid "Loading..."
msgstr ""

#: assets/js/app.js:1
#: assets/src/admin/utils/bkash.js:63
msgid "bKash Processing..."
msgstr ""

#: assets/js/upgrade.js:1
#: assets/src/upgrade/Pages/Upgrades.js:66
msgid "bKash Data Update Required"
msgstr ""

#: assets/js/upgrade.js:1
#: assets/src/upgrade/Pages/Upgrades.js:72
msgid "We need to update your install to the latest version"
msgstr ""

#: assets/js/upgrade.js:1
#: assets/src/upgrade/Pages/Upgrades.js:40
msgid "Updated Successfully!"
msgstr ""

#: assets/js/upgrade.js:1
#: assets/src/upgrade/Pages/Upgrades.js:87
msgid "Updating"
msgstr ""

#: assets/js/upgrade.js:1
#: assets/src/upgrade/Pages/Upgrades.js:88
msgid "Update"
msgstr ""
