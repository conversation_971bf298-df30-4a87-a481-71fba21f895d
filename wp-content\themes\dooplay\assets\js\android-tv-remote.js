/*
* Android TV Remote Control Support
* Handles D-pad navigation, focus management, and remote control events
*/

class AndroidTVRemote {
    constructor() {
        this.currentFocus = null;
        this.focusableElements = [];
        this.isAndroidTV = this.detectAndroidTV();
        this.init();
    }

    detectAndroidTV() {
        const userAgent = navigator.userAgent.toLowerCase();
        return userAgent.includes('android') && 
               (userAgent.includes('tv') || 
                userAgent.includes('googletv') || 
                userAgent.includes('androidtv') ||
                window.innerWidth >= 1280); // Assume TV if large screen on Android
    }

    init() {
        if (!this.isAndroidTV) return;

        console.log('Android TV Remote Control initialized');
        
        // Add TV-specific CSS
        this.addTVStyles();
        
        // Setup focus management
        this.setupFocusManagement();
        
        // Setup keyboard navigation
        this.setupKeyboardNavigation();
        
        // Setup initial focus
        this.setInitialFocus();
        
        // Setup focus indicators
        this.setupFocusIndicators();
    }

    addTVStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* Android TV Focus Styles - Comprehensive */
            .tv-focusable,
            .item,
            .poster a,
            .thumbnail a,
            .result-item,
            .main-header a,
            .resp a,
            .menu-item a,
            button,
            .btn,
            .button,
            a[href],
            input,
            select,
            textarea,
            video {
                outline: none !important;
                transition: all 0.3s ease !important;
                border: 2px solid transparent !important;
            }

            /* Focus states for all elements */
            .tv-focusable:focus,
            .tv-focused,
            .item.tv-focused,
            .poster a.tv-focused,
            .thumbnail a.tv-focused,
            .result-item.tv-focused,
            .main-header a.tv-focused,
            .resp a.tv-focused,
            .menu-item a.tv-focused,
            button.tv-focused,
            .btn.tv-focused,
            .button.tv-focused,
            a[href].tv-focused,
            input.tv-focused,
            select.tv-focused,
            textarea.tv-focused,
            video.tv-focused {
                border: 3px solid #00ff00 !important;
                box-shadow: 0 0 15px rgba(0, 255, 0, 0.5) !important;
                transform: scale(1.05) !important;
                z-index: 1000 !important;
                position: relative !important;
                background: rgba(0, 255, 0, 0.1) !important;
            }

            /* Hide mouse cursor on TV */
            body.android-tv {
                cursor: none !important;
            }

            body.android-tv * {
                cursor: none !important;
            }

            /* TV-specific button styles */
            body.android-tv .tv-button,
            body.android-tv button,
            body.android-tv .btn,
            body.android-tv .button {
                padding: 15px 25px !important;
                font-size: 18px !important;
                border-radius: 8px !important;
                min-width: 120px !important;
                min-height: 50px !important;
            }

            /* Movie/Series card focus improvements */
            body.android-tv .items .item.tv-focused,
            body.android-tv .movie-card.tv-focused,
            body.android-tv .series-card.tv-focused,
            body.android-tv .episode-card.tv-focused {
                transform: scale(1.1) !important;
                z-index: 100 !important;
                box-shadow: 0 0 20px rgba(0, 255, 0, 0.6) !important;
            }

            /* Navigation menu focus */
            body.android-tv .main-header a.tv-focused,
            body.android-tv .resp a.tv-focused,
            body.android-tv .menu-item a.tv-focused {
                background: rgba(0, 255, 0, 0.2) !important;
                color: #00ff00 !important;
                font-weight: bold !important;
            }

            /* Player controls focus */
            body.android-tv video:focus,
            body.android-tv video.tv-focused {
                outline: 3px solid #00ff00 !important;
                outline-offset: 5px !important;
                box-shadow: 0 0 25px rgba(0, 255, 0, 0.7) !important;
            }

            /* Form elements focus */
            body.android-tv input.tv-focused,
            body.android-tv select.tv-focused,
            body.android-tv textarea.tv-focused {
                border-color: #00ff00 !important;
                background: rgba(0, 255, 0, 0.1) !important;
                color: #fff !important;
            }

            /* Search results focus */
            body.android-tv .result-item.tv-focused {
                background: rgba(0, 255, 0, 0.15) !important;
                border-left: 5px solid #00ff00 !important;
            }

            /* Pagination focus */
            body.android-tv .pagination a.tv-focused,
            body.android-tv .page-numbers.tv-focused {
                background: #00ff00 !important;
                color: #000 !important;
                font-weight: bold !important;
            }

            /* Player specific controls */
            body.android-tv .play-btn.tv-focused,
            body.android-tv .download-btn.tv-focused,
            body.android-tv .favorite-btn.tv-focused,
            body.android-tv .watchlist-btn.tv-focused {
                background: #00ff00 !important;
                color: #000 !important;
                transform: scale(1.2) !important;
            }

            /* Episode/Season list focus */
            body.android-tv .episode-list a.tv-focused,
            body.android-tv .season-list a.tv-focused {
                background: rgba(0, 255, 0, 0.2) !important;
                border-left: 4px solid #00ff00 !important;
            }

            /* Genre and filter focus */
            body.android-tv .genre-list a.tv-focused,
            body.android-tv .year-filter a.tv-focused,
            body.android-tv .quality-filter a.tv-focused {
                background: #00ff00 !important;
                color: #000 !important;
                border-radius: 20px !important;
            }

            /* Responsive TV adjustments */
            @media (min-width: 1280px) {
                body.android-tv .items .item {
                    margin-bottom: 20px !important;
                }

                body.android-tv .main-header a,
                body.android-tv .resp a {
                    padding: 12px 20px !important;
                    font-size: 16px !important;
                }
            }
        `;
        document.head.appendChild(style);
        
        // Add Android TV class to body
        document.body.classList.add('android-tv');
    }

    setupFocusManagement() {
        // Find all focusable elements
        this.updateFocusableElements();
        
        // Update focusable elements when DOM changes
        const observer = new MutationObserver(() => {
            this.updateFocusableElements();
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    updateFocusableElements() {
        // Get all focusable elements - comprehensive list for entire site
        const selectors = [
            // Existing TV focusable elements
            '.tv-focusable',

            // Movie/Series cards and items
            '.item',
            '.poster a',
            '.thumbnail a',
            '.result-item a',
            '.movie-card',
            '.series-card',
            '.episode-card',

            // Navigation and menus
            '.main-header a',
            '.resp a',
            '.menu-item a',
            '.nav_items_module a',
            '.see-all',

            // Buttons and controls
            'button:not([disabled])',
            '.btn:not([disabled])',
            '.button:not([disabled])',
            '.play-btn',
            '.download-btn',
            '.favorite-btn',
            '.watchlist-btn',

            // Form elements
            'a[href]',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',

            // Player elements
            'video',
            '.player-controls button',
            '.plyr__controls button',
            '.vjs-control-bar button',

            // Search and filters
            '.search-form input',
            '.filter-select',
            '.pagination a',
            '.page-numbers',

            // Content specific
            '.episode-list a',
            '.season-list a',
            '.genre-list a',
            '.year-filter a',
            '.quality-filter a',

            // Generic focusable
            '[tabindex]:not([tabindex="-1"])',
            '[role="button"]',
            '[role="link"]'
        ];
        
        this.focusableElements = Array.from(
            document.querySelectorAll(selectors.join(', '))
        ).filter(el => {
            return el.offsetParent !== null && // Element is visible
                   !el.disabled &&
                   !el.hasAttribute('disabled');
        });
        
        // Sort by position (top to bottom, left to right)
        this.focusableElements.sort((a, b) => {
            const rectA = a.getBoundingClientRect();
            const rectB = b.getBoundingClientRect();
            
            if (Math.abs(rectA.top - rectB.top) < 10) {
                return rectA.left - rectB.left;
            }
            return rectA.top - rectB.top;
        });
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });
        
        // Handle gamepad events if available
        if ('getGamepads' in navigator) {
            this.setupGamepadSupport();
        }
    }

    handleKeyDown(e) {
        const key = e.key || e.keyCode;
        
        switch (key) {
            case 'ArrowUp':
            case 38:
                e.preventDefault();
                this.navigateUp();
                break;
                
            case 'ArrowDown':
            case 40:
                e.preventDefault();
                this.navigateDown();
                break;
                
            case 'ArrowLeft':
            case 37:
                e.preventDefault();
                this.navigateLeft();
                break;
                
            case 'ArrowRight':
            case 39:
                e.preventDefault();
                this.navigateRight();
                break;
                
            case 'Enter':
            case 13:
            case ' ':
            case 32:
                e.preventDefault();
                this.activateCurrentElement();
                break;
                
            case 'Escape':
            case 27:
                e.preventDefault();
                this.handleBack();
                break;
                
            // Android TV specific keys
            case 'MediaPlayPause':
                e.preventDefault();
                this.togglePlayPause();
                break;
                
            case 'MediaStop':
                e.preventDefault();
                this.stopMedia();
                break;
        }
    }

    navigateUp() {
        if (!this.currentFocus) {
            this.setInitialFocus();
            return;
        }
        
        const currentRect = this.currentFocus.getBoundingClientRect();
        const candidates = this.focusableElements.filter(el => {
            const rect = el.getBoundingClientRect();
            return rect.bottom <= currentRect.top + 10 &&
                   Math.abs(rect.left - currentRect.left) < 200;
        });
        
        if (candidates.length > 0) {
            const closest = candidates.reduce((prev, curr) => {
                const prevRect = prev.getBoundingClientRect();
                const currRect = curr.getBoundingClientRect();
                return (currentRect.top - currRect.bottom) < (currentRect.top - prevRect.bottom) ? curr : prev;
            });
            this.setFocus(closest);
        }
    }

    navigateDown() {
        if (!this.currentFocus) {
            this.setInitialFocus();
            return;
        }
        
        const currentRect = this.currentFocus.getBoundingClientRect();
        const candidates = this.focusableElements.filter(el => {
            const rect = el.getBoundingClientRect();
            return rect.top >= currentRect.bottom - 10 &&
                   Math.abs(rect.left - currentRect.left) < 200;
        });
        
        if (candidates.length > 0) {
            const closest = candidates.reduce((prev, curr) => {
                const prevRect = prev.getBoundingClientRect();
                const currRect = curr.getBoundingClientRect();
                return (currRect.top - currentRect.bottom) < (prevRect.top - currentRect.bottom) ? curr : prev;
            });
            this.setFocus(closest);
        }
    }

    navigateLeft() {
        if (!this.currentFocus) {
            this.setInitialFocus();
            return;
        }
        
        const currentRect = this.currentFocus.getBoundingClientRect();
        const candidates = this.focusableElements.filter(el => {
            const rect = el.getBoundingClientRect();
            return rect.right <= currentRect.left + 10 &&
                   Math.abs(rect.top - currentRect.top) < 100;
        });
        
        if (candidates.length > 0) {
            const closest = candidates.reduce((prev, curr) => {
                const prevRect = prev.getBoundingClientRect();
                const currRect = curr.getBoundingClientRect();
                return (currentRect.left - currRect.right) < (currentRect.left - prevRect.right) ? curr : prev;
            });
            this.setFocus(closest);
        }
    }

    navigateRight() {
        if (!this.currentFocus) {
            this.setInitialFocus();
            return;
        }
        
        const currentRect = this.currentFocus.getBoundingClientRect();
        const candidates = this.focusableElements.filter(el => {
            const rect = el.getBoundingClientRect();
            return rect.left >= currentRect.right - 10 &&
                   Math.abs(rect.top - currentRect.top) < 100;
        });
        
        if (candidates.length > 0) {
            const closest = candidates.reduce((prev, curr) => {
                const prevRect = prev.getBoundingClientRect();
                const currRect = curr.getBoundingClientRect();
                return (currRect.left - currentRect.right) < (prevRect.left - currentRect.right) ? curr : prev;
            });
            this.setFocus(closest);
        }
    }

    setFocus(element) {
        // Remove focus from current element
        if (this.currentFocus) {
            this.currentFocus.classList.remove('tv-focused');
            this.currentFocus.blur();
        }
        
        // Set focus to new element
        this.currentFocus = element;
        if (element) {
            element.classList.add('tv-focused');
            element.focus();
            
            // Scroll into view if needed
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
            });
        }
    }

    setInitialFocus() {
        if (this.focusableElements.length > 0) {
            // Try to focus on the first visible element
            const firstVisible = this.focusableElements.find(el => {
                const rect = el.getBoundingClientRect();
                return rect.top >= 0 && rect.left >= 0;
            });
            
            this.setFocus(firstVisible || this.focusableElements[0]);
        }
    }

    activateCurrentElement() {
        if (!this.currentFocus) return;

        // Handle different element types for entire site
        const element = this.currentFocus;
        const tagName = element.tagName.toLowerCase();

        // Direct clickable elements
        if (tagName === 'a' || tagName === 'button') {
            element.click();
            return;
        }

        // Video player controls
        if (tagName === 'video') {
            this.togglePlayPause();
            return;
        }

        // Movie/Series cards
        if (element.classList.contains('item') ||
            element.classList.contains('movie-card') ||
            element.classList.contains('series-card') ||
            element.classList.contains('episode-card')) {

            // Try to find the main link
            const link = element.querySelector('a') ||
                        element.querySelector('.poster a') ||
                        element.querySelector('.thumbnail a');

            if (link) {
                link.click();
            } else if (element.onclick) {
                element.click();
            }
            return;
        }

        // Search results
        if (element.classList.contains('result-item')) {
            const link = element.querySelector('a');
            if (link) {
                link.click();
            }
            return;
        }

        // Form elements
        if (tagName === 'input') {
            if (element.type === 'submit' || element.type === 'button') {
                element.click();
            } else {
                element.focus();
            }
            return;
        }

        if (tagName === 'select') {
            // For select elements, trigger change if it's a filter
            if (element.classList.contains('filter-select') ||
                element.onchange) {
                element.focus();
                // Simulate opening dropdown
                const event = new Event('mousedown', { bubbles: true });
                element.dispatchEvent(event);
            }
            return;
        }

        // Player specific controls
        if (element.classList.contains('play-btn')) {
            this.togglePlayPause();
            return;
        }

        if (element.classList.contains('download-btn')) {
            element.click();
            return;
        }

        if (element.classList.contains('favorite-btn') ||
            element.classList.contains('watchlist-btn')) {
            element.click();
            return;
        }

        // Navigation elements
        if (element.classList.contains('see-all') ||
            element.classList.contains('nav_items_module')) {
            element.click();
            return;
        }

        // Pagination
        if (element.classList.contains('page-numbers') ||
            element.classList.contains('pagination')) {
            element.click();
            return;
        }

        // Generic fallback
        if (element.onclick || element.href) {
            element.click();
        } else {
            // Try to find any clickable child
            const clickableChild = element.querySelector('a, button, [onclick]');
            if (clickableChild) {
                clickableChild.click();
            }
        }
    }

    handleBack() {
        // Handle back button - go to previous page or close modals
        if (document.querySelector('.modal:not([style*="display: none"])')) {
            // Close modal if open
            const closeBtn = document.querySelector('.modal .close, .modal .btn-close');
            if (closeBtn) {
                closeBtn.click();
            }
        } else {
            // Go back in history
            window.history.back();
        }
    }

    togglePlayPause() {
        const video = document.querySelector('video');
        if (video) {
            if (video.paused) {
                video.play();
            } else {
                video.pause();
            }
        }
    }

    stopMedia() {
        const video = document.querySelector('video');
        if (video) {
            video.pause();
            video.currentTime = 0;
        }
    }

    setupGamepadSupport() {
        // Handle gamepad input for Android TV boxes with game controllers
        let gamepadIndex = null;
        
        window.addEventListener('gamepadconnected', (e) => {
            gamepadIndex = e.gamepad.index;
            console.log('Gamepad connected:', e.gamepad.id);
        });
        
        window.addEventListener('gamepaddisconnected', (e) => {
            gamepadIndex = null;
            console.log('Gamepad disconnected');
        });
        
        // Poll gamepad state
        const pollGamepad = () => {
            if (gamepadIndex !== null) {
                const gamepad = navigator.getGamepads()[gamepadIndex];
                if (gamepad) {
                    this.handleGamepadInput(gamepad);
                }
            }
            requestAnimationFrame(pollGamepad);
        };
        
        pollGamepad();
    }

    handleGamepadInput(gamepad) {
        // Handle D-pad and button inputs
        const threshold = 0.5;
        
        // D-pad or left stick
        if (gamepad.axes[0] < -threshold || gamepad.buttons[14]?.pressed) {
            this.navigateLeft();
        } else if (gamepad.axes[0] > threshold || gamepad.buttons[15]?.pressed) {
            this.navigateRight();
        }
        
        if (gamepad.axes[1] < -threshold || gamepad.buttons[12]?.pressed) {
            this.navigateUp();
        } else if (gamepad.axes[1] > threshold || gamepad.buttons[13]?.pressed) {
            this.navigateDown();
        }
        
        // A button (activate)
        if (gamepad.buttons[0]?.pressed) {
            this.activateCurrentElement();
        }
        
        // B button (back)
        if (gamepad.buttons[1]?.pressed) {
            this.handleBack();
        }
    }

    setupFocusIndicators() {
        // Add visual feedback for focus changes
        document.addEventListener('focusin', (e) => {
            if (this.focusableElements.includes(e.target)) {
                this.currentFocus = e.target;
                e.target.classList.add('tv-focused');
            }
        });
        
        document.addEventListener('focusout', (e) => {
            e.target.classList.remove('tv-focused');
        });
    }
}

// Initialize Android TV Remote when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.androidTVRemote = new AndroidTVRemote();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AndroidTVRemote;
}
