<?php
/**
 * DeshiFlix Premium System - Features Management Class
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Premium_Features {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Available premium features
     */
    private $features = array();
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize features management
     */
    private function init() {
        $this->define_features();
        $this->init_hooks();
    }
    
    /**
     * Define available premium features
     */
    private function define_features() {
        $this->features = array(
            'ad_free' => array(
                'name' => __('Ad-Free Experience', 'deshiflix'),
                'description' => __('Remove all advertisements for premium users', 'deshiflix'),
                'default' => true,
                'category' => 'user_experience',
                'priority' => 1
            ),
            'hd_quality' => array(
                'name' => __('HD/4K Quality Access', 'deshiflix'),
                'description' => __('Access to high-definition and 4K video quality', 'deshiflix'),
                'default' => true,
                'category' => 'video_quality',
                'priority' => 2
            ),
            'download_links' => array(
                'name' => __('Download Access', 'deshiflix'),
                'description' => __('Access to download links for offline viewing', 'deshiflix'),
                'default' => true,
                'category' => 'content_access',
                'priority' => 3
            ),
            'early_access' => array(
                'name' => __('Early Access', 'deshiflix'),
                'description' => __('Get early access to new content before public release', 'deshiflix'),
                'default' => true,
                'category' => 'content_access',
                'priority' => 4
            ),
            'multiple_servers' => array(
                'name' => __('Multiple Servers', 'deshiflix'),
                'description' => __('Access to multiple streaming servers for better performance', 'deshiflix'),
                'default' => true,
                'category' => 'streaming',
                'priority' => 5
            ),
            'offline_viewing' => array(
                'name' => __('Offline Viewing', 'deshiflix'),
                'description' => __('Download content for offline viewing', 'deshiflix'),
                'default' => false,
                'category' => 'content_access',
                'priority' => 6
            ),
            'priority_support' => array(
                'name' => __('Priority Support', 'deshiflix'),
                'description' => __('Get priority customer support', 'deshiflix'),
                'default' => false,
                'category' => 'support',
                'priority' => 7
            ),
            'exclusive_content' => array(
                'name' => __('Exclusive Content', 'deshiflix'),
                'description' => __('Access to premium-only exclusive content', 'deshiflix'),
                'default' => false,
                'category' => 'content_access',
                'priority' => 8
            ),
            'family_sharing' => array(
                'name' => __('Family Sharing', 'deshiflix'),
                'description' => __('Share premium benefits with family members', 'deshiflix'),
                'default' => false,
                'category' => 'user_management',
                'priority' => 9
            ),
            'custom_subtitles' => array(
                'name' => __('Custom Subtitles', 'deshiflix'),
                'description' => __('Upload and use custom subtitle files', 'deshiflix'),
                'default' => false,
                'category' => 'video_features',
                'priority' => 10
            ),
            'watchlist_sync' => array(
                'name' => __('Watchlist Sync', 'deshiflix'),
                'description' => __('Sync watchlist across all devices', 'deshiflix'),
                'default' => true,
                'category' => 'user_experience',
                'priority' => 11
            ),
            'advanced_search' => array(
                'name' => __('Advanced Search', 'deshiflix'),
                'description' => __('Advanced search filters and recommendations', 'deshiflix'),
                'default' => true,
                'category' => 'user_experience',
                'priority' => 12
            )
        );
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Content filtering hooks
        add_filter('dooplay_ads_display', array($this, 'filter_ads_display'), 10, 2);
        add_filter('dooplay_video_quality_options', array($this, 'filter_video_quality'), 10, 2);
        add_filter('dooplay_download_links_display', array($this, 'filter_download_links'), 10, 2);
        add_filter('dooplay_server_options', array($this, 'filter_server_options'), 10, 2);
        
        // Admin hooks
        add_action('admin_menu', array($this, 'add_features_admin_menu'));
        add_action('wp_ajax_toggle_premium_feature', array($this, 'ajax_toggle_feature'));
        add_action('wp_ajax_save_feature_settings', array($this, 'ajax_save_feature_settings'));
        
        // Frontend hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_feature_scripts'));
        add_action('wp_head', array($this, 'add_feature_styles'));
        
        // Shortcodes
        add_shortcode('premium_features_list', array($this, 'features_list_shortcode'));
        add_shortcode('feature_check', array($this, 'feature_check_shortcode'));
    }
    
    /**
     * Check if a feature is enabled
     */
    public function is_feature_enabled($feature_key) {
        $settings = get_option('deshiflix_premium_features', array());
        
        if (!isset($this->features[$feature_key])) {
            return false;
        }
        
        // Check global feature setting
        if (isset($settings[$feature_key])) {
            return $settings[$feature_key];
        }
        
        // Return default value
        return $this->features[$feature_key]['default'];
    }
    
    /**
     * Check if user has access to a feature
     */
    public function user_has_feature_access($feature_key, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        // Check if feature is globally enabled
        if (!$this->is_feature_enabled($feature_key)) {
            return false;
        }
        
        // Check if user is premium
        if (!deshiflix_premium()->is_user_premium($user_id)) {
            return false;
        }
        
        // Check user's plan features
        $user_plan_id = deshiflix_premium()->get_user_premium_plan($user_id);
        
        if (!$user_plan_id) {
            return false;
        }
        
        return $this->plan_has_feature($user_plan_id, $feature_key);
    }
    
    /**
     * Check if plan has a specific feature
     */
    public function plan_has_feature($plan_id, $feature_key) {
        global $wpdb;
        
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT features FROM $table_plans WHERE id = %d",
            $plan_id
        ));
        
        if (!$plan) {
            return false;
        }
        
        $plan_features = json_decode($plan->features, true);
        
        if (!$plan_features) {
            return false;
        }
        
        return isset($plan_features[$feature_key]) && $plan_features[$feature_key];
    }
    
    /**
     * Filter ads display
     */
    public function filter_ads_display($show_ads, $context) {
        $user_id = get_current_user_id();
        
        if ($this->user_has_feature_access('ad_free', $user_id)) {
            return false;
        }
        
        return $show_ads;
    }
    
    /**
     * Filter video quality options
     */
    public function filter_video_quality($quality_options, $post_id) {
        $user_id = get_current_user_id();
        
        if (!$this->user_has_feature_access('hd_quality', $user_id)) {
            // Remove HD and 4K options for non-premium users
            $filtered_options = array();
            foreach ($quality_options as $quality => $url) {
                if (!in_array(strtolower($quality), array('hd', '720p', '1080p', '4k', '2160p'))) {
                    $filtered_options[$quality] = $url;
                }
            }
            return $filtered_options;
        }
        
        return $quality_options;
    }
    
    /**
     * Filter download links display
     */
    public function filter_download_links($download_links, $post_id) {
        $user_id = get_current_user_id();
        
        if (!$this->user_has_feature_access('download_links', $user_id)) {
            return array();
        }
        
        return $download_links;
    }
    
    /**
     * Filter server options
     */
    public function filter_server_options($server_options, $post_id) {
        $user_id = get_current_user_id();
        
        if (!$this->user_has_feature_access('multiple_servers', $user_id)) {
            // Return only the first server for non-premium users
            return array_slice($server_options, 0, 1, true);
        }
        
        return $server_options;
    }
    
    /**
     * Get all features
     */
    public function get_all_features() {
        return $this->features;
    }
    
    /**
     * Get features by category
     */
    public function get_features_by_category($category) {
        $filtered_features = array();
        
        foreach ($this->features as $key => $feature) {
            if ($feature['category'] === $category) {
                $filtered_features[$key] = $feature;
            }
        }
        
        // Sort by priority
        uasort($filtered_features, function($a, $b) {
            return $a['priority'] - $b['priority'];
        });
        
        return $filtered_features;
    }
    
    /**
     * Get feature categories
     */
    public function get_feature_categories() {
        $categories = array();
        
        foreach ($this->features as $feature) {
            if (!in_array($feature['category'], $categories)) {
                $categories[] = $feature['category'];
            }
        }
        
        return $categories;
    }
    
    /**
     * Enable feature
     */
    public function enable_feature($feature_key) {
        if (!isset($this->features[$feature_key])) {
            return false;
        }
        
        $settings = get_option('deshiflix_premium_features', array());
        $settings[$feature_key] = true;
        
        return update_option('deshiflix_premium_features', $settings);
    }
    
    /**
     * Disable feature
     */
    public function disable_feature($feature_key) {
        if (!isset($this->features[$feature_key])) {
            return false;
        }
        
        $settings = get_option('deshiflix_premium_features', array());
        $settings[$feature_key] = false;
        
        return update_option('deshiflix_premium_features', $settings);
    }
    
    /**
     * Toggle feature
     */
    public function toggle_feature($feature_key) {
        if ($this->is_feature_enabled($feature_key)) {
            return $this->disable_feature($feature_key);
        } else {
            return $this->enable_feature($feature_key);
        }
    }
    
    /**
     * Add features admin menu
     */
    public function add_features_admin_menu() {
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Features', 'deshiflix'),
            __('Features', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-features',
            array($this, 'features_admin_page')
        );
    }
    
    /**
     * Features admin page
     */
    public function features_admin_page() {
        $categories = $this->get_feature_categories();
        ?>
        <div class="wrap premium-features-wrap">
            <h1 class="wp-heading-inline">
                <span class="dashicons dashicons-admin-settings"></span>
                <?php _e('Premium Features Management', 'deshiflix'); ?>
            </h1>
            
            <div class="features-tabs">
                <nav class="nav-tab-wrapper">
                    <?php foreach ($categories as $index => $category): ?>
                        <a href="#<?php echo esc_attr($category); ?>" class="nav-tab <?php echo $index === 0 ? 'nav-tab-active' : ''; ?>">
                            <?php echo esc_html(ucwords(str_replace('_', ' ', $category))); ?>
                        </a>
                    <?php endforeach; ?>
                </nav>
                
                <?php foreach ($categories as $index => $category): ?>
                    <div id="<?php echo esc_attr($category); ?>" class="tab-content <?php echo $index === 0 ? 'active' : ''; ?>">
                        <h2><?php echo esc_html(ucwords(str_replace('_', ' ', $category))); ?> Features</h2>
                        
                        <div class="features-grid">
                            <?php 
                            $category_features = $this->get_features_by_category($category);
                            foreach ($category_features as $feature_key => $feature):
                                $is_enabled = $this->is_feature_enabled($feature_key);
                            ?>
                                <div class="feature-card <?php echo $is_enabled ? 'enabled' : 'disabled'; ?>">
                                    <div class="feature-header">
                                        <h3><?php echo esc_html($feature['name']); ?></h3>
                                        <div class="feature-toggle">
                                            <label class="toggle-switch">
                                                <input type="checkbox" 
                                                       class="feature-toggle-input" 
                                                       data-feature="<?php echo esc_attr($feature_key); ?>"
                                                       <?php checked($is_enabled); ?>>
                                                <span class="toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="feature-description">
                                        <p><?php echo esc_html($feature['description']); ?></p>
                                    </div>
                                    
                                    <div class="feature-status">
                                        <span class="status-badge <?php echo $is_enabled ? 'enabled' : 'disabled'; ?>">
                                            <?php echo $is_enabled ? __('Enabled', 'deshiflix') : __('Disabled', 'deshiflix'); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="features-actions">
                <button class="button button-primary" id="save-all-features">
                    <?php _e('Save All Changes', 'deshiflix'); ?>
                </button>
                <button class="button" id="reset-features">
                    <?php _e('Reset to Defaults', 'deshiflix'); ?>
                </button>
            </div>
        </div>
        
        <style>
        .premium-features-wrap .nav-tab-wrapper {
            margin-bottom: 20px;
        }
        
        .premium-features-wrap .tab-content {
            display: none;
        }
        
        .premium-features-wrap .tab-content.active {
            display: block;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .feature-card.enabled {
            border-color: #46b450;
            box-shadow: 0 2px 8px rgba(70, 180, 80, 0.1);
        }
        
        .feature-card.disabled {
            border-color: #dc3232;
            box-shadow: 0 2px 8px rgba(220, 50, 50, 0.1);
        }
        
        .feature-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .feature-header h3 {
            margin: 0;
            color: #333;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: #46b450;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
        
        .feature-description p {
            color: #666;
            margin: 0;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-badge.enabled {
            background: #d4edda;
            color: #155724;
        }
        
        .status-badge.disabled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .features-actions {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // Tab switching
            $('.nav-tab').click(function(e) {
                e.preventDefault();
                
                $('.nav-tab').removeClass('nav-tab-active');
                $('.tab-content').removeClass('active');
                
                $(this).addClass('nav-tab-active');
                var target = $(this).attr('href');
                $(target).addClass('active');
            });
            
            // Feature toggle
            $('.feature-toggle-input').change(function() {
                var $card = $(this).closest('.feature-card');
                var feature = $(this).data('feature');
                var enabled = $(this).is(':checked');
                
                // Update card appearance
                if (enabled) {
                    $card.removeClass('disabled').addClass('enabled');
                    $card.find('.status-badge').removeClass('disabled').addClass('enabled').text('Enabled');
                } else {
                    $card.removeClass('enabled').addClass('disabled');
                    $card.find('.status-badge').removeClass('enabled').addClass('disabled').text('Disabled');
                }
                
                // AJAX toggle
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'toggle_premium_feature',
                        feature: feature,
                        enabled: enabled ? 1 : 0,
                        nonce: '<?php echo wp_create_nonce('premium_features_nonce'); ?>'
                    },
                    success: function(response) {
                        if (!response.success) {
                            // Revert on error
                            $(this).prop('checked', !enabled);
                            alert('Failed to update feature');
                        }
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * AJAX toggle feature
     */
    public function ajax_toggle_feature() {
        check_ajax_referer('premium_features_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied', 'deshiflix')));
        }
        
        $feature = sanitize_text_field($_POST['feature']);
        $enabled = intval($_POST['enabled']);
        
        if (!isset($this->features[$feature])) {
            wp_send_json_error(array('message' => __('Invalid feature', 'deshiflix')));
        }
        
        $settings = get_option('deshiflix_premium_features', array());
        $settings[$feature] = $enabled;
        
        if (update_option('deshiflix_premium_features', $settings)) {
            wp_send_json_success(array('message' => __('Feature updated', 'deshiflix')));
        } else {
            wp_send_json_error(array('message' => __('Failed to update feature', 'deshiflix')));
        }
    }
    
    /**
     * Enqueue feature scripts
     */
    public function enqueue_feature_scripts() {
        wp_enqueue_script('deshiflix-premium-features', 
                         DESHIFLIX_PREMIUM_ASSETS_URL . 'js/premium-features.js', 
                         array('jquery'), DESHIFLIX_PREMIUM_VERSION, true);
        
        wp_localize_script('deshiflix-premium-features', 'premium_features', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('premium_features_nonce'),
            'user_features' => $this->get_user_features()
        ));
    }
    
    /**
     * Get user features
     */
    private function get_user_features() {
        $user_id = get_current_user_id();
        $user_features = array();
        
        foreach ($this->features as $feature_key => $feature) {
            $user_features[$feature_key] = $this->user_has_feature_access($feature_key, $user_id);
        }
        
        return $user_features;
    }
    
    /**
     * Add feature styles
     */
    public function add_feature_styles() {
        $user_id = get_current_user_id();
        
        if ($this->user_has_feature_access('ad_free', $user_id)) {
            echo '<style>.advertisement, .ads-container { display: none !important; }</style>';
        }
    }
    
    /**
     * Features list shortcode
     */
    public function features_list_shortcode($atts) {
        $atts = shortcode_atts(array(
            'category' => '',
            'show_status' => 'true'
        ), $atts);
        
        $features = $atts['category'] ? 
                   $this->get_features_by_category($atts['category']) : 
                   $this->get_all_features();
        
        ob_start();
        ?>
        <div class="premium-features-list">
            <?php foreach ($features as $feature_key => $feature): ?>
                <div class="feature-item">
                    <h4><?php echo esc_html($feature['name']); ?></h4>
                    <p><?php echo esc_html($feature['description']); ?></p>
                    <?php if ($atts['show_status'] === 'true'): ?>
                        <span class="feature-status <?php echo $this->is_feature_enabled($feature_key) ? 'enabled' : 'disabled'; ?>">
                            <?php echo $this->is_feature_enabled($feature_key) ? __('Available', 'deshiflix') : __('Not Available', 'deshiflix'); ?>
                        </span>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Feature check shortcode
     */
    public function feature_check_shortcode($atts, $content = '') {
        $atts = shortcode_atts(array(
            'feature' => '',
            'user_id' => get_current_user_id()
        ), $atts);
        
        if (!$atts['feature']) {
            return '';
        }
        
        if ($this->user_has_feature_access($atts['feature'], $atts['user_id'])) {
            return do_shortcode($content);
        }
        
        return '';
    }
}

// Initialize premium features
DeshiFlix_Premium_Features::get_instance();
