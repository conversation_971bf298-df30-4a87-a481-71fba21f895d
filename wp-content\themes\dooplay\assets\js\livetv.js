/**
 * Live TV JavaScript for Dooplay Theme
 */

(function($) {
    'use strict';
    
    // Live TV functionality
    var LiveTV = {
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.initializePlayer();
            this.loadFavorites();
            this.loadRecentlyWatched();
        },
        
        // Bind events
        bindEvents: function() {
            // Favorite functionality
            $(document).on('click', '.favorite-btn', this.toggleFavorite);
            
            // Share functionality
            $(document).on('click', '.share-btn', this.shareChannel);
            
            // Search functionality
            $(document).on('submit', '.filter-form', this.handleSearch);
            $(document).on('keyup', '.search-input', this.debounceSearch);
            
            // Category filtering
            $(document).on('change', '.category-filter select', this.filterByCategory);
            
            // Channel card interactions
            $(document).on('mouseenter', '.channel-card', this.showChannelPreview);
            $(document).on('mouseleave', '.channel-card', this.hideChannelPreview);
            
            // Player controls
            $(document).on('click', '.control-btn', this.handlePlayerControls);
            
            // Quality selector
            $(document).on('click', '.quality-btn', this.toggleQualityMenu);
            $(document).on('click', '.quality-option', this.changeQuality);
            
            // Error handling
            $(document).on('click', '.retry-btn', this.retryStream);
            $(document).on('click', '.back-btn', this.goBack);
            
            // Keyboard shortcuts
            $(document).on('keydown', this.handleKeyboardShortcuts);
            
            // Window events
            $(window).on('resize', this.handleResize);
            $(window).on('beforeunload', this.saveWatchTime);
        },
        
        // Toggle favorite
        toggleFavorite: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $btn = $(this);
            var channelId = $btn.data('channel-id');
            var $icon = $btn.find('i');
            
            var favorites = LiveTV.getFavorites();
            var isFavorited = favorites.indexOf(channelId) !== -1;
            
            if (isFavorited) {
                // Remove from favorites
                favorites = favorites.filter(function(id) {
                    return id !== channelId;
                });
                $icon.removeClass('fa-heart').addClass('fa-heart-o');
                $btn.removeClass('favorited');
                LiveTV.showNotification('Removed from favorites', 'info');
            } else {
                // Add to favorites
                favorites.push(channelId);
                $icon.removeClass('fa-heart-o').addClass('fa-heart');
                $btn.addClass('favorited');
                LiveTV.showNotification('Added to favorites', 'success');
            }
            
            localStorage.setItem('livetv_favorites', JSON.stringify(favorites));
        },
        
        // Get favorites from localStorage
        getFavorites: function() {
            var favorites = localStorage.getItem('livetv_favorites');
            return favorites ? JSON.parse(favorites) : [];
        },
        
        // Load favorites state
        loadFavorites: function() {
            var favorites = this.getFavorites();
            
            $('.favorite-btn').each(function() {
                var channelId = $(this).data('channel-id');
                var $icon = $(this).find('i');
                
                if (favorites.indexOf(channelId) !== -1) {
                    $icon.removeClass('fa-heart-o').addClass('fa-heart');
                    $(this).addClass('favorited');
                }
            });
        },
        
        // Share channel
        shareChannel: function(e) {
            e.preventDefault();
            
            var url = $(this).data('url') || window.location.href;
            var title = document.title;
            
            if (navigator.share) {
                navigator.share({
                    title: title,
                    url: url
                }).catch(function(error) {
                    console.log('Error sharing:', error);
                });
            } else {
                // Fallback: copy to clipboard
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(url).then(function() {
                        LiveTV.showNotification('Link copied to clipboard!', 'success');
                    }).catch(function() {
                        LiveTV.fallbackCopyToClipboard(url);
                    });
                } else {
                    LiveTV.fallbackCopyToClipboard(url);
                }
            }
        },
        
        // Fallback copy to clipboard
        fallbackCopyToClipboard: function(text) {
            var textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                LiveTV.showNotification('Link copied to clipboard!', 'success');
            } catch (err) {
                LiveTV.showNotification('Failed to copy link', 'error');
            }
            
            document.body.removeChild(textArea);
        },
        
        // Handle search
        handleSearch: function(e) {
            // Let the form submit naturally
            LiveTV.showLoading();
        },
        
        // Debounced search
        debounceSearch: function() {
            clearTimeout(LiveTV.searchTimeout);
            LiveTV.searchTimeout = setTimeout(function() {
                // Implement live search if needed
            }, 500);
        },
        
        // Filter by category
        filterByCategory: function() {
            $(this).closest('form').submit();
        },
        
        // Show channel preview
        showChannelPreview: function() {
            var $card = $(this);
            var $overlay = $card.find('.channel-overlay');
            
            $overlay.fadeIn(200);
            
            // Add preview functionality here if needed
        },
        
        // Hide channel preview
        hideChannelPreview: function() {
            var $card = $(this);
            var $overlay = $card.find('.channel-overlay');
            
            $overlay.fadeOut(200);
        },
        
        // Initialize player
        initializePlayer: function() {
            if ($('#video-element').length === 0) return;
            
            var video = document.getElementById('video-element');
            
            // Add event listeners
            video.addEventListener('loadstart', function() {
                $('.player-loading').fadeOut();
            });
            
            video.addEventListener('error', function() {
                LiveTV.handlePlayerError();
            });
            
            video.addEventListener('play', function() {
                $('.play-pause-btn i').removeClass('fa-play').addClass('fa-pause');
            });
            
            video.addEventListener('pause', function() {
                $('.play-pause-btn i').removeClass('fa-pause').addClass('fa-play');
            });
            
            video.addEventListener('volumechange', function() {
                var $icon = $('.volume-btn i');
                if (video.muted || video.volume === 0) {
                    $icon.removeClass('fa-volume-up fa-volume-down').addClass('fa-volume-off');
                } else if (video.volume < 0.5) {
                    $icon.removeClass('fa-volume-up fa-volume-off').addClass('fa-volume-down');
                } else {
                    $icon.removeClass('fa-volume-down fa-volume-off').addClass('fa-volume-up');
                }
            });
            
            // Track watch time
            video.addEventListener('timeupdate', function() {
                LiveTV.updateWatchTime();
            });
        },
        
        // Handle player controls
        handlePlayerControls: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var video = document.getElementById('video-element');
            
            if (!video) return;
            
            if ($btn.hasClass('play-pause-btn')) {
                if (video.paused) {
                    video.play();
                } else {
                    video.pause();
                }
            } else if ($btn.hasClass('volume-btn')) {
                video.muted = !video.muted;
            } else if ($btn.hasClass('fullscreen-btn')) {
                if (video.requestFullscreen) {
                    video.requestFullscreen();
                } else if (video.webkitRequestFullscreen) {
                    video.webkitRequestFullscreen();
                } else if (video.msRequestFullscreen) {
                    video.msRequestFullscreen();
                }
            } else if ($btn.hasClass('pip-btn')) {
                if (document.pictureInPictureEnabled && !video.disablePictureInPicture) {
                    video.requestPictureInPicture().catch(function(error) {
                        console.log('PiP error:', error);
                    });
                }
            }
        },
        
        // Toggle quality menu
        toggleQualityMenu: function(e) {
            e.preventDefault();
            $('.quality-menu').toggle();
        },
        
        // Change quality
        changeQuality: function(e) {
            e.preventDefault();
            
            var $option = $(this);
            var newUrl = $option.data('url');
            var video = document.getElementById('video-element');
            
            if (!video || !newUrl) return;
            
            $('.quality-option').removeClass('active');
            $option.addClass('active');
            $('.quality-menu').hide();
            
            // Save current time
            var currentTime = video.currentTime;
            
            // Change source
            video.src = newUrl;
            video.load();
            
            // Restore time when loaded
            video.addEventListener('loadedmetadata', function() {
                video.currentTime = currentTime;
            }, { once: true });
            
            LiveTV.showNotification('Quality changed', 'info');
        },
        
        // Handle player error
        handlePlayerError: function() {
            $('.video-player').addClass('error-state');
            $('.player-error').fadeIn();
        },
        
        // Retry stream
        retryStream: function(e) {
            e.preventDefault();
            
            $('.video-player').removeClass('error-state');
            $('.player-error').fadeOut();
            $('.player-loading').fadeIn();
            
            var video = document.getElementById('video-element');
            if (video) {
                video.load();
            }
        },
        
        // Go back
        goBack: function(e) {
            if (e) e.preventDefault();
            window.history.back();
        },

        // Android TV Navigation Functions
        navigateLeft: function() {
            var currentFocus = this.getCurrentFocusedElement();
            var leftElement = this.findNavigableElement(currentFocus, 'left');
            if (leftElement) {
                this.setFocus(leftElement);
            }
        },

        navigateRight: function() {
            var currentFocus = this.getCurrentFocusedElement();
            var rightElement = this.findNavigableElement(currentFocus, 'right');
            if (rightElement) {
                this.setFocus(rightElement);
            }
        },

        navigateUp: function() {
            var currentFocus = this.getCurrentFocusedElement();
            var upElement = this.findNavigableElement(currentFocus, 'up');
            if (upElement) {
                this.setFocus(upElement);
            }
        },

        navigateDown: function() {
            var currentFocus = this.getCurrentFocusedElement();
            var downElement = this.findNavigableElement(currentFocus, 'down');
            if (downElement) {
                this.setFocus(downElement);
            }
        },

        activateElement: function() {
            var currentFocus = this.getCurrentFocusedElement();
            if (currentFocus) {
                if (currentFocus.tagName === 'A' || currentFocus.tagName === 'BUTTON') {
                    currentFocus.click();
                } else if (currentFocus.tagName === 'INPUT') {
                    currentFocus.focus();
                }
            }
        },

        getCurrentFocusedElement: function() {
            var focused = $('.tv-focused');
            if (focused.length > 0) {
                return focused[0];
            }

            // If no element is focused, focus on the first navigable element
            var firstNavigable = this.getNavigableElements()[0];
            if (firstNavigable) {
                this.setFocus(firstNavigable);
                return firstNavigable;
            }

            return null;
        },

        setFocus: function(element) {
            $('.tv-focused').removeClass('tv-focused');
            $(element).addClass('tv-focused');

            // Scroll element into view if needed
            if (element.scrollIntoView) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'center'
                });
            }
        },

        getNavigableElements: function() {
            return $('.item a, .search-form input, .search-form button, .search-form select, .page-link, .category-tab, .control-btn, .btn').get();
        },

        findNavigableElement: function(currentElement, direction) {
            if (!currentElement) return null;

            var navigableElements = this.getNavigableElements();
            var currentRect = currentElement.getBoundingClientRect();
            var candidates = [];

            navigableElements.forEach(function(element) {
                if (element === currentElement) return;

                var rect = element.getBoundingClientRect();
                var isCandidate = false;

                switch(direction) {
                    case 'left':
                        isCandidate = rect.right <= currentRect.left &&
                                    Math.abs(rect.top - currentRect.top) < currentRect.height;
                        break;
                    case 'right':
                        isCandidate = rect.left >= currentRect.right &&
                                    Math.abs(rect.top - currentRect.top) < currentRect.height;
                        break;
                    case 'up':
                        isCandidate = rect.bottom <= currentRect.top &&
                                    Math.abs(rect.left - currentRect.left) < currentRect.width;
                        break;
                    case 'down':
                        isCandidate = rect.top >= currentRect.bottom &&
                                    Math.abs(rect.left - currentRect.left) < currentRect.width;
                        break;
                }

                if (isCandidate) {
                    var distance = Math.sqrt(
                        Math.pow(rect.left - currentRect.left, 2) +
                        Math.pow(rect.top - currentRect.top, 2)
                    );
                    candidates.push({element: element, distance: distance});
                }
            });

            if (candidates.length > 0) {
                candidates.sort(function(a, b) { return a.distance - b.distance; });
                return candidates[0].element;
            }

            return null;
        },

        togglePlayPause: function() {
            var video = document.getElementById('video-element');
            if (video) {
                if (video.paused) {
                    video.play();
                } else {
                    video.pause();
                }
            }
        },

        toggleMute: function() {
            var video = document.getElementById('video-element');
            if (video) {
                video.muted = !video.muted;
            }
        },

        toggleFullscreen: function() {
            var video = document.getElementById('video-element');
            if (video) {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    video.requestFullscreen();
                }
            }
        },
        
        // Handle keyboard shortcuts
        handleKeyboardShortcuts: function(e) {
            if ($('input, textarea').is(':focus')) return;

            var video = document.getElementById('video-element');
            if (!video) return;

            // Android TV Remote Control Support
            switch(e.keyCode) {
                case 37: // Left Arrow / D-pad Left
                    e.preventDefault();
                    this.navigateLeft();
                    break;
                case 38: // Up Arrow / D-pad Up
                    e.preventDefault();
                    this.navigateUp();
                    break;
                case 39: // Right Arrow / D-pad Right
                    e.preventDefault();
                    this.navigateRight();
                    break;
                case 40: // Down Arrow / D-pad Down
                    e.preventDefault();
                    this.navigateDown();
                    break;
                case 13: // Enter / OK button
                    e.preventDefault();
                    this.activateElement();
                    break;
                case 8: // Backspace / Back button
                case 27: // Escape / Back button
                    e.preventDefault();
                    this.goBack();
                    break;
                case 32: // Space / Play/Pause
                    e.preventDefault();
                    this.togglePlayPause();
                    break;
                case 77: // M / Mute
                    e.preventDefault();
                    this.toggleMute();
                    break;
                case 70: // F / Fullscreen
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
            }
            
            switch (e.keyCode) {
                case 32: // Space - play/pause
                    e.preventDefault();
                    if (video.paused) {
                        video.play();
                    } else {
                        video.pause();
                    }
                    break;
                    
                case 77: // M - mute
                    e.preventDefault();
                    video.muted = !video.muted;
                    break;
                    
                case 70: // F - fullscreen
                    e.preventDefault();
                    if (video.requestFullscreen) {
                        video.requestFullscreen();
                    }
                    break;
                    
                case 37: // Left arrow - seek back
                    e.preventDefault();
                    video.currentTime = Math.max(0, video.currentTime - 10);
                    break;
                    
                case 39: // Right arrow - seek forward
                    e.preventDefault();
                    video.currentTime = Math.min(video.duration, video.currentTime + 10);
                    break;
            }
        },
        
        // Handle window resize
        handleResize: function() {
            // Adjust player size if needed
        },
        
        // Recently watched functionality
        loadRecentlyWatched: function() {
            var recent = localStorage.getItem('livetv_recent');
            return recent ? JSON.parse(recent) : [];
        },
        
        // Add to recently watched
        addToRecentlyWatched: function(channelId) {
            var recent = this.loadRecentlyWatched();
            
            // Remove if already exists
            recent = recent.filter(function(item) {
                return item.id !== channelId;
            });
            
            // Add to beginning
            recent.unshift({
                id: channelId,
                timestamp: Date.now()
            });
            
            // Keep only last 10
            recent = recent.slice(0, 10);
            
            localStorage.setItem('livetv_recent', JSON.stringify(recent));
        },
        
        // Update watch time
        updateWatchTime: function() {
            var channelId = $('.channel-card, .channel-info-bar').first().data('channel-id');
            if (channelId) {
                this.addToRecentlyWatched(channelId);
            }
        },
        
        // Save watch time on page unload
        saveWatchTime: function() {
            // Save any pending watch time data
        },
        
        // Show loading
        showLoading: function() {
            if ($('.loading-overlay').length === 0) {
                $('body').append('<div class="loading-overlay"><div class="loading-spinner"></div></div>');
            }
        },
        
        // Hide loading
        hideLoading: function() {
            $('.loading-overlay').remove();
        },
        
        // Show notification
        showNotification: function(message, type) {
            type = type || 'info';
            
            var $notification = $('<div class="livetv-notification livetv-notification-' + type + '">' + message + '</div>');
            
            $('body').append($notification);
            
            setTimeout(function() {
                $notification.addClass('show');
            }, 100);
            
            setTimeout(function() {
                $notification.removeClass('show');
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            }, 3000);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        LiveTV.init();
    });
    
    // Expose LiveTV object globally
    window.LiveTV = LiveTV;
    
})(jQuery);
