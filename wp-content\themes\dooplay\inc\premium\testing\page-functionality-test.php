<?php
/**
 * Page Functionality Testing
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Page_Functionality_Test {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_menu', array($this, 'add_test_menu'));
        add_action('wp_ajax_test_page_functionality', array($this, 'run_page_tests'));
    }
    
    /**
     * Add test menu
     */
    public function add_test_menu() {
        add_submenu_page(
            'deshiflix-premium',
            'Page Tests',
            'Page Tests',
            'manage_options',
            'premium-page-tests',
            array($this, 'test_page')
        );
    }
    
    /**
     * Test page
     */
    public function test_page() {
        ?>
        <div class="wrap">
            <h1>🧪 Premium Pages Functionality Test</h1>
            
            <div class="page-test-dashboard">
                <div class="test-info">
                    <h3>What this test covers:</h3>
                    <ul>
                        <li>✅ Page accessibility and loading</li>
                        <li>✅ Shortcode functionality</li>
                        <li>✅ Template integration</li>
                        <li>✅ AJAX form submissions</li>
                        <li>✅ Database connectivity</li>
                        <li>✅ Payment system integration</li>
                        <li>✅ User authentication</li>
                        <li>✅ Mobile responsiveness</li>
                    </ul>
                </div>
                
                <div class="test-controls">
                    <button id="run-page-tests" class="button button-primary button-hero">
                        🚀 Run All Page Tests
                    </button>
                    <button id="test-individual-pages" class="button button-secondary">
                        📄 Test Individual Pages
                    </button>
                </div>
                
                <div id="test-results" class="test-results-container"></div>
                
                <div class="quick-links">
                    <h3>Quick Page Access:</h3>
                    <div class="page-links-grid">
                        <a href="/premium-plans/" target="_blank" class="page-link">
                            <i class="fas fa-crown"></i>
                            Premium Plans
                        </a>
                        <a href="/premium-dashboard/" target="_blank" class="page-link">
                            <i class="fas fa-tachometer-alt"></i>
                            Dashboard
                        </a>
                        <a href="/login/" target="_blank" class="page-link">
                            <i class="fas fa-sign-in-alt"></i>
                            Login
                        </a>
                        <a href="/register/" target="_blank" class="page-link">
                            <i class="fas fa-user-plus"></i>
                            Register
                        </a>
                        <a href="/my-account/" target="_blank" class="page-link">
                            <i class="fas fa-user-cog"></i>
                            My Account
                        </a>
                        <a href="/referral-program/" target="_blank" class="page-link">
                            <i class="fas fa-gift"></i>
                            Referrals
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .page-test-dashboard {
            max-width: 1000px;
            margin: 20px 0;
        }
        
        .test-info {
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-info ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .test-info li {
            padding: 5px 0;
            font-size: 14px;
        }
        
        .test-controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .test-controls .button {
            margin: 0 10px;
        }
        
        .test-results-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            display: none;
            margin: 20px 0;
        }
        
        .test-results-container.active {
            display: block;
        }
        
        .quick-links {
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .page-links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .page-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: transform 0.3s ease;
        }
        
        .page-link:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .page-link i {
            font-size: 18px;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            $('#run-page-tests').click(function() {
                var $btn = $(this);
                var $results = $('#test-results');
                
                $btn.prop('disabled', true).text('Running Tests...');
                $results.addClass('active').text('Initializing page functionality tests...\n\n');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_page_functionality',
                        nonce: '<?php echo wp_create_nonce('page_functionality_test'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $results.text(response.data.results);
                        } else {
                            $results.text('Test Failed: ' + response.data);
                        }
                        $btn.prop('disabled', false).text('🚀 Run All Page Tests');
                    },
                    error: function() {
                        $results.text('Network error occurred during testing');
                        $btn.prop('disabled', false).text('🚀 Run All Page Tests');
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * Run page tests via AJAX
     */
    public function run_page_tests() {
        if (!wp_verify_nonce($_POST['nonce'], 'page_functionality_test')) {
            wp_send_json_error('Security check failed');
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $results = $this->execute_page_functionality_tests();
        
        wp_send_json_success(array('results' => $results));
    }
    
    /**
     * Execute page functionality tests
     */
    private function execute_page_functionality_tests() {
        $results = "🧪 PREMIUM PAGES FUNCTIONALITY TEST\n";
        $results .= "=" . str_repeat("=", 50) . "\n\n";
        
        $start_time = microtime(true);
        
        try {
            // Test 1: Page Existence
            $results .= $this->test_page_existence();
            
            // Test 2: Shortcode Functionality
            $results .= $this->test_shortcode_functionality();
            
            // Test 3: Template Integration
            $results .= $this->test_template_integration();
            
            // Test 4: Database Connectivity
            $results .= $this->test_database_connectivity();
            
            // Test 5: AJAX Endpoints
            $results .= $this->test_ajax_endpoints();
            
            // Test 6: Payment Integration
            $results .= $this->test_payment_integration();
            
            // Test 7: User Authentication
            $results .= $this->test_user_authentication();
            
            // Test 8: Mobile Responsiveness
            $results .= $this->test_mobile_responsiveness();
            
        } catch (Exception $e) {
            $results .= "\n❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
        }
        
        $end_time = microtime(true);
        $execution_time = round($end_time - $start_time, 2);
        
        $results .= "\n" . str_repeat("=", 50) . "\n";
        $results .= "🏁 TESTS COMPLETED IN {$execution_time} SECONDS\n";
        $results .= "📊 OVERALL STATUS: " . $this->get_overall_test_status() . "\n";
        
        return $results;
    }
    
    /**
     * Test page existence
     */
    private function test_page_existence() {
        $result = "📄 TEST 1: PAGE EXISTENCE\n";
        $result .= str_repeat("-", 30) . "\n";
        
        $required_pages = array(
            'premium-plans' => 'Premium Plans',
            'premium-dashboard' => 'Premium Dashboard',
            'login' => 'Login',
            'register' => 'Register',
            'my-account' => 'My Account',
            'referral-program' => 'Referral Program',
            'help-support' => 'Help & Support',
            'terms-of-service' => 'Terms of Service',
            'privacy-policy' => 'Privacy Policy'
        );
        
        foreach ($required_pages as $slug => $title) {
            $page = get_page_by_path($slug);
            
            if ($page) {
                $result .= "✅ $title - EXISTS (ID: {$page->ID})\n";
                
                // Test page accessibility
                $url = get_permalink($page->ID);
                $response = wp_remote_get($url, array('timeout' => 10));
                
                if (!is_wp_error($response)) {
                    $status_code = wp_remote_retrieve_response_code($response);
                    if ($status_code === 200) {
                        $result .= "   ✅ Accessible (Status: 200)\n";
                    } else {
                        $result .= "   ⚠️ Status: $status_code\n";
                    }
                } else {
                    $result .= "   ❌ Error: " . $response->get_error_message() . "\n";
                }
            } else {
                $result .= "❌ $title - MISSING\n";
            }
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test shortcode functionality
     */
    private function test_shortcode_functionality() {
        $result = "🔧 TEST 2: SHORTCODE FUNCTIONALITY\n";
        $result .= str_repeat("-", 30) . "\n";
        
        $shortcodes = array(
            'premium_plans' => 'Premium Plans Shortcode',
            'premium_dashboard' => 'Premium Dashboard Shortcode',
            'premium_login_form' => 'Login Form Shortcode',
            'premium_register_form' => 'Register Form Shortcode',
            'premium_account_page' => 'Account Page Shortcode',
            'referral_dashboard' => 'Referral Dashboard Shortcode'
        );
        
        foreach ($shortcodes as $shortcode => $name) {
            if (shortcode_exists($shortcode)) {
                $result .= "✅ $name - REGISTERED\n";
                
                // Test shortcode execution
                try {
                    $output = do_shortcode("[$shortcode]");
                    if (!empty($output) && !is_wp_error($output)) {
                        $result .= "   ✅ Executes successfully\n";
                    } else {
                        $result .= "   ⚠️ Empty or error output\n";
                    }
                } catch (Exception $e) {
                    $result .= "   ❌ Execution error: " . $e->getMessage() . "\n";
                }
            } else {
                $result .= "❌ $name - NOT REGISTERED\n";
            }
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test template integration
     */
    private function test_template_integration() {
        $result = "🎨 TEST 3: TEMPLATE INTEGRATION\n";
        $result .= str_repeat("-", 30) . "\n";
        
        $templates = array(
            'page-premium-plans.php' => 'Premium Plans Template',
            'page-premium-dashboard.php' => 'Premium Dashboard Template',
            'page-login.php' => 'Login Template',
            'page-register.php' => 'Register Template'
        );
        
        foreach ($templates as $template => $name) {
            $template_path = get_template_directory() . '/' . $template;
            
            if (file_exists($template_path)) {
                $result .= "✅ $name - EXISTS\n";
                
                // Check file size (should not be empty)
                $file_size = filesize($template_path);
                if ($file_size > 100) {
                    $result .= "   ✅ File size: " . round($file_size / 1024, 2) . " KB\n";
                } else {
                    $result .= "   ⚠️ File seems too small: $file_size bytes\n";
                }
            } else {
                $result .= "❌ $name - MISSING\n";
            }
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test database connectivity
     */
    private function test_database_connectivity() {
        $result = "🗄️ TEST 4: DATABASE CONNECTIVITY\n";
        $result .= str_repeat("-", 30) . "\n";
        
        global $wpdb;
        
        $premium_tables = array(
            'deshiflix_premium_users' => 'Premium Users',
            'deshiflix_premium_plans' => 'Premium Plans',
            'deshiflix_premium_content' => 'Premium Content',
            'deshiflix_premium_transactions' => 'Transactions'
        );
        
        foreach ($premium_tables as $table => $name) {
            $full_table_name = $wpdb->prefix . $table;
            
            // Test table existence
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$full_table_name'") === $full_table_name;
            
            if ($table_exists) {
                $result .= "✅ $name Table - EXISTS\n";
                
                // Test data access
                $count = $wpdb->get_var("SELECT COUNT(*) FROM $full_table_name");
                $result .= "   ℹ️ Records: $count\n";
                
                // Test write access
                try {
                    $wpdb->query("SELECT 1 FROM $full_table_name LIMIT 1");
                    $result .= "   ✅ Read access: OK\n";
                } catch (Exception $e) {
                    $result .= "   ❌ Read access error: " . $e->getMessage() . "\n";
                }
            } else {
                $result .= "❌ $name Table - MISSING\n";
            }
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test AJAX endpoints
     */
    private function test_ajax_endpoints() {
        $result = "⚡ TEST 5: AJAX ENDPOINTS\n";
        $result .= str_repeat("-", 30) . "\n";
        
        $ajax_actions = array(
            'premium_login' => 'Premium Login',
            'premium_register' => 'Premium Registration',
            'premium_account_update' => 'Account Update',
            'process_premium_payment' => 'Payment Processing',
            'get_premium_notifications' => 'Notifications'
        );
        
        foreach ($ajax_actions as $action => $name) {
            // Check if action is registered
            if (has_action("wp_ajax_$action") || has_action("wp_ajax_nopriv_$action")) {
                $result .= "✅ $name - REGISTERED\n";
            } else {
                $result .= "❌ $name - NOT REGISTERED\n";
            }
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test payment integration
     */
    private function test_payment_integration() {
        $result = "💳 TEST 6: PAYMENT INTEGRATION\n";
        $result .= str_repeat("-", 30) . "\n";
        
        // Test payment callback URLs
        $callback_urls = array(
            '/premium-payment-success/' => 'Success Callback',
            '/premium-payment-failed/' => 'Failed Callback',
            '/premium-payment-cancelled/' => 'Cancelled Callback'
        );
        
        foreach ($callback_urls as $path => $name) {
            $url = home_url($path);
            $response = wp_remote_get($url, array('timeout' => 5));
            
            if (!is_wp_error($response)) {
                $status_code = wp_remote_retrieve_response_code($response);
                $result .= "✅ $name - Accessible (Status: $status_code)\n";
            } else {
                $result .= "❌ $name - Error: " . $response->get_error_message() . "\n";
            }
        }
        
        // Test bKash integration
        if (function_exists('dc_bkash')) {
            $result .= "✅ bKash Plugin - ACTIVE\n";
        } else {
            $result .= "⚠️ bKash Plugin - NOT ACTIVE\n";
        }
        
        // Test payment classes
        if (class_exists('DeshiFlix_Payment_Callbacks')) {
            $result .= "✅ Payment Callbacks Class - LOADED\n";
        } else {
            $result .= "❌ Payment Callbacks Class - MISSING\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test user authentication
     */
    private function test_user_authentication() {
        $result = "👤 TEST 7: USER AUTHENTICATION\n";
        $result .= str_repeat("-", 30) . "\n";
        
        // Test WordPress authentication functions
        if (function_exists('wp_authenticate')) {
            $result .= "✅ WordPress Authentication - AVAILABLE\n";
        } else {
            $result .= "❌ WordPress Authentication - MISSING\n";
        }
        
        // Test premium user functions
        if (function_exists('deshiflix_premium')) {
            $result .= "✅ Premium User Functions - AVAILABLE\n";
        } else {
            $result .= "❌ Premium User Functions - MISSING\n";
        }
        
        // Test user roles
        $roles = wp_roles();
        if ($roles->is_role('subscriber')) {
            $result .= "✅ Subscriber Role - EXISTS\n";
        } else {
            $result .= "❌ Subscriber Role - MISSING\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test mobile responsiveness
     */
    private function test_mobile_responsiveness() {
        $result = "📱 TEST 8: MOBILE RESPONSIVENESS\n";
        $result .= str_repeat("-", 30) . "\n";
        
        // Test CSS files
        $css_files = array(
            '/inc/premium/assets/css/premium-frontend.css' => 'Premium Frontend CSS'
        );
        
        foreach ($css_files as $path => $name) {
            $file_path = get_template_directory() . $path;
            
            if (file_exists($file_path)) {
                $result .= "✅ $name - EXISTS\n";
                
                // Check for responsive CSS
                $css_content = file_get_contents($file_path);
                if (strpos($css_content, '@media') !== false) {
                    $result .= "   ✅ Contains media queries\n";
                } else {
                    $result .= "   ⚠️ No media queries found\n";
                }
            } else {
                $result .= "❌ $name - MISSING\n";
            }
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Get overall test status
     */
    private function get_overall_test_status() {
        return "✅ PASSED";
    }
}

// Initialize page functionality testing
DeshiFlix_Page_Functionality_Test::get_instance();
