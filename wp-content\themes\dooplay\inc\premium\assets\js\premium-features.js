/**
 * DeshiFlix Premium Features JavaScript
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

(function($) {
    'use strict';
    
    // Premium Features object
    var PremiumFeatures = {
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.checkUserFeatures();
        },
        
        // Bind events
        bindEvents: function() {
            $(document).on('click', '.premium-feature-toggle', this.toggleFeature);
            $(document).on('click', '.check-feature-access', this.checkFeatureAccess);
        },
        
        // Check user features
        checkUserFeatures: function() {
            if (typeof premium_features === 'undefined') {
                return;
            }
            
            var userFeatures = premium_features.user_features;
            
            // Update UI based on user features
            for (var feature in userFeatures) {
                if (userFeatures.hasOwnProperty(feature)) {
                    var hasAccess = userFeatures[feature];
                    this.updateFeatureUI(feature, hasAccess);
                }
            }
        },
        
        // Update feature UI
        updateFeatureUI: function(feature, hasAccess) {
            var $elements = $('.feature-' + feature);
            
            if (hasAccess) {
                $elements.removeClass('feature-locked').addClass('feature-unlocked');
                $elements.find('.feature-lock-message').hide();
                $elements.find('.feature-content').show();
            } else {
                $elements.removeClass('feature-unlocked').addClass('feature-locked');
                $elements.find('.feature-content').hide();
                $elements.find('.feature-lock-message').show();
            }
        },
        
        // Toggle feature
        toggleFeature: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var feature = $button.data('feature');
            
            if (typeof premium_features === 'undefined') {
                return;
            }
            
            $.ajax({
                url: premium_features.ajax_url,
                type: 'POST',
                data: {
                    action: 'toggle_user_feature',
                    feature: feature,
                    nonce: premium_features.nonce
                },
                success: function(response) {
                    if (response.success) {
                        var hasAccess = response.data.has_access;
                        PremiumFeatures.updateFeatureUI(feature, hasAccess);
                        
                        if (hasAccess) {
                            PremiumFeatures.showNotice('Feature enabled successfully', 'success');
                        } else {
                            PremiumFeatures.showNotice('Feature disabled', 'info');
                        }
                    } else {
                        PremiumFeatures.showNotice(response.data.message || 'Failed to toggle feature', 'error');
                    }
                },
                error: function() {
                    PremiumFeatures.showNotice('An error occurred', 'error');
                }
            });
        },
        
        // Check feature access
        checkFeatureAccess: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var feature = $button.data('feature');
            
            if (typeof premium_features === 'undefined') {
                return;
            }
            
            $.ajax({
                url: premium_features.ajax_url,
                type: 'POST',
                data: {
                    action: 'check_feature_access',
                    feature: feature,
                    nonce: premium_features.nonce
                },
                success: function(response) {
                    if (response.success) {
                        var hasAccess = response.data.has_access;
                        var message = hasAccess ? 
                                     'You have access to this feature' : 
                                     'You need a premium subscription to access this feature';
                        
                        PremiumFeatures.showNotice(message, hasAccess ? 'success' : 'warning');
                    }
                },
                error: function() {
                    PremiumFeatures.showNotice('Failed to check feature access', 'error');
                }
            });
        },
        
        // Show notice
        showNotice: function(message, type) {
            type = type || 'info';
            
            var $notice = $('<div class="premium-feature-notice notice-' + type + '">' +
                           '<span class="notice-message">' + message + '</span>' +
                           '<button class="notice-close">&times;</button>' +
                           '</div>');
            
            // Remove existing notices
            $('.premium-feature-notice').remove();
            
            // Add new notice
            $('body').append($notice);
            
            // Position notice
            $notice.css({
                position: 'fixed',
                top: '20px',
                right: '20px',
                background: this.getNoticeColor(type),
                color: 'white',
                padding: '15px 20px',
                borderRadius: '5px',
                zIndex: 9999,
                maxWidth: '300px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
            });
            
            // Auto hide after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
            
            // Manual close
            $notice.on('click', '.notice-close', function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            });
        },
        
        // Get notice color
        getNoticeColor: function(type) {
            switch (type) {
                case 'success':
                    return '#46b450';
                case 'error':
                    return '#dc3232';
                case 'warning':
                    return '#ffb900';
                case 'info':
                default:
                    return '#0073aa';
            }
        },
        
        // Check if user has feature
        hasFeature: function(feature) {
            if (typeof premium_features === 'undefined') {
                return false;
            }
            
            return premium_features.user_features[feature] || false;
        },
        
        // Show upgrade prompt
        showUpgradePrompt: function(feature) {
            var message = 'This feature requires a premium subscription. Would you like to upgrade?';
            
            if (confirm(message)) {
                window.location.href = '/premium-plans/';
            }
        }
    };
    
    // Content Protection functions
    var ContentProtection = {
        
        // Initialize
        init: function() {
            this.protectContent();
            this.addWatermarks();
        },
        
        // Protect content
        protectContent: function() {
            // Disable right-click on premium content
            $('.premium-content').on('contextmenu', function(e) {
                e.preventDefault();
                ContentProtection.showProtectionMessage();
                return false;
            });
            
            // Disable text selection
            $('.premium-content').css({
                '-webkit-user-select': 'none',
                '-moz-user-select': 'none',
                '-ms-user-select': 'none',
                'user-select': 'none'
            });
            
            // Disable drag and drop
            $('.premium-content img, .premium-content video').on('dragstart', function(e) {
                e.preventDefault();
                return false;
            });
        },
        
        // Add watermarks
        addWatermarks: function() {
            $('.premium-video-player').each(function() {
                var $player = $(this);
                
                if (!$player.find('.video-watermark').length) {
                    var watermark = $('<div class="video-watermark">DeshiFlix Premium</div>');
                    watermark.css({
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        opacity: '0.1',
                        fontSize: '24px',
                        color: 'white',
                        pointerEvents: 'none',
                        zIndex: '9999',
                        fontWeight: 'bold'
                    });
                    
                    $player.css('position', 'relative').append(watermark);
                }
            });
        },
        
        // Show protection message
        showProtectionMessage: function() {
            PremiumFeatures.showNotice('This content is protected', 'warning');
        }
    };
    
    // Download Protection functions
    var DownloadProtection = {
        
        // Initialize
        init: function() {
            this.protectDownloads();
        },
        
        // Protect downloads
        protectDownloads: function() {
            $('.premium-download-link').on('click', function(e) {
                var $link = $(this);
                var requiresPremium = $link.data('requires-premium');
                
                if (requiresPremium && !PremiumFeatures.hasFeature('download_links')) {
                    e.preventDefault();
                    PremiumFeatures.showUpgradePrompt('download_links');
                    return false;
                }
                
                // Track download
                DownloadProtection.trackDownload($link.data('post-id'));
            });
        },
        
        // Track download
        trackDownload: function(postId) {
            if (typeof premium_features === 'undefined') {
                return;
            }
            
            $.ajax({
                url: premium_features.ajax_url,
                type: 'POST',
                data: {
                    action: 'track_premium_download',
                    post_id: postId,
                    nonce: premium_features.nonce
                }
            });
        }
    };
    
    // Quality Control functions
    var QualityControl = {
        
        // Initialize
        init: function() {
            this.filterQualityOptions();
        },
        
        // Filter quality options
        filterQualityOptions: function() {
            $('.video-quality-selector option').each(function() {
                var $option = $(this);
                var quality = $option.val();
                var isHD = quality.includes('HD') || quality.includes('720') || quality.includes('1080') || quality.includes('4K');
                
                if (isHD && !PremiumFeatures.hasFeature('hd_quality')) {
                    $option.prop('disabled', true).text($option.text() + ' (Premium)');
                }
            });
            
            // Handle quality change
            $('.video-quality-selector').on('change', function() {
                var selectedQuality = $(this).val();
                var isHD = selectedQuality.includes('HD') || selectedQuality.includes('720') || selectedQuality.includes('1080') || selectedQuality.includes('4K');
                
                if (isHD && !PremiumFeatures.hasFeature('hd_quality')) {
                    $(this).val('480p'); // Fallback to standard quality
                    PremiumFeatures.showUpgradePrompt('hd_quality');
                }
            });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        PremiumFeatures.init();
        ContentProtection.init();
        DownloadProtection.init();
        QualityControl.init();
    });
    
    // Make functions available globally
    window.PremiumFeatures = PremiumFeatures;
    window.ContentProtection = ContentProtection;
    window.DownloadProtection = DownloadProtection;
    window.QualityControl = QualityControl;
    
})(jQuery);
