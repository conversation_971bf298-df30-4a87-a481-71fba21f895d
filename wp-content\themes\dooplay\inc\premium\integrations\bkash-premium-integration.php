<?php
/**
 * bKash Premium Integration
 * Integrates existing bKash plugin with DeshiFlix Premium System
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_BKash_Premium_Integration {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Check if bKash plugin is active
        add_action('plugins_loaded', array($this, 'check_bkash_plugin'));
        
        // Hook into bKash payment success
        add_action('dc_bkash_execute_payment_success', array($this, 'handle_bkash_success'), 10, 2);
        
        // Custom bKash integration for premium
        add_action('wp_ajax_premium_bkash_payment', array($this, 'create_premium_bkash_payment'));
        add_action('wp_ajax_nopriv_premium_bkash_payment', array($this, 'create_premium_bkash_payment'));
        
        // bKash webhook for premium
        add_action('wp_ajax_premium_bkash_webhook', array($this, 'handle_premium_bkash_webhook'));
        add_action('wp_ajax_nopriv_premium_bkash_webhook', array($this, 'handle_premium_bkash_webhook'));
    }
    
    /**
     * Check if bKash plugin is active
     */
    public function check_bkash_plugin() {
        if (!function_exists('dc_bkash')) {
            add_action('admin_notices', array($this, 'bkash_plugin_notice'));
            return;
        }
        
        // Plugin is active, integrate with it
        $this->integrate_with_bkash_plugin();
    }
    
    /**
     * Show notice if bKash plugin is not active
     */
    public function bkash_plugin_notice() {
        ?>
        <div class="notice notice-warning">
            <p>
                <strong>DeshiFlix Premium:</strong> 
                bKash payment gateway plugin is not active. 
                <a href="<?php echo admin_url('plugins.php'); ?>">Activate it</a> 
                to enable bKash payments for premium subscriptions.
            </p>
        </div>
        <?php
    }
    
    /**
     * Integrate with existing bKash plugin
     */
    private function integrate_with_bkash_plugin() {
        // Add premium-specific bKash settings
        add_filter('dc_bkash_settings_fields', array($this, 'add_premium_bkash_settings'));
        
        // Modify bKash gateway for premium use
        add_filter('dc_bkash_payment_args', array($this, 'modify_bkash_payment_args'), 10, 2);
    }
    
    /**
     * Add premium-specific bKash settings
     */
    public function add_premium_bkash_settings($fields) {
        $premium_fields = array(
            array(
                'name' => 'premium_integration',
                'label' => __('Premium Integration', 'dc-bkash'),
                'type' => 'checkbox',
                'desc' => __('Enable bKash for DeshiFlix Premium subscriptions', 'dc-bkash'),
                'default' => 'yes'
            ),
            array(
                'name' => 'premium_webhook_url',
                'label' => __('Premium Webhook URL', 'dc-bkash'),
                'type' => 'text',
                'desc' => __('Webhook URL for premium payment notifications', 'dc-bkash'),
                'default' => home_url('/wp-admin/admin-ajax.php?action=premium_bkash_webhook')
            )
        );
        
        return array_merge($fields, $premium_fields);
    }
    
    /**
     * Create premium bKash payment
     */
    public function create_premium_bkash_payment() {
        if (!wp_verify_nonce($_POST['nonce'], 'premium_bkash_payment')) {
            wp_send_json_error('Security check failed');
        }
        
        $plan_id = intval($_POST['plan_id']);
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            wp_send_json_error('User not logged in');
        }
        
        // Get plan details
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_plans WHERE id = %d AND status = 'active'",
            $plan_id
        ));
        
        if (!$plan) {
            wp_send_json_error('Invalid plan');
        }
        
        // Create transaction
        $transaction_id = 'DFLIX_BKASH_' . time() . '_' . $user_id;
        $this->create_premium_transaction($user_id, $plan, $transaction_id);
        
        // Create bKash payment using existing plugin
        $payment_data = $this->create_bkash_payment_request($plan, $transaction_id, $user_id);
        
        if (is_wp_error($payment_data)) {
            wp_send_json_error($payment_data->get_error_message());
        }
        
        wp_send_json_success($payment_data);
    }
    
    /**
     * Create bKash payment request
     */
    private function create_bkash_payment_request($plan, $transaction_id, $user_id) {
        $user = get_user_by('ID', $user_id);
        
        // Get bKash settings
        $bkash_settings = get_option('dc_bkash_settings', array());
        
        if (empty($bkash_settings['app_key']) || empty($bkash_settings['app_secret'])) {
            return new WP_Error('bkash_config', 'bKash not configured properly');
        }
        
        // Prepare payment data
        $payment_data = array(
            'mode' => '0011',
            'payerReference' => $transaction_id,
            'callbackURL' => home_url('/premium-payment-success/?transaction_id=' . $transaction_id . '&method=bkash'),
            'amount' => $plan->price,
            'currency' => 'BDT',
            'intent' => 'sale',
            'merchantInvoiceNumber' => $transaction_id,
            'merchantAssociationInfo' => 'DeshiFlix Premium - ' . $plan->name
        );
        
        // Get access token
        $token = $this->get_bkash_access_token($bkash_settings);
        
        if (!$token) {
            return new WP_Error('bkash_token', 'Failed to get bKash access token');
        }
        
        // Create payment
        $api_url = $this->get_bkash_api_url('create', $bkash_settings);
        
        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token,
                'X-APP-Key' => $bkash_settings['app_key']
            ),
            'body' => json_encode($payment_data),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (isset($response_body['bkashURL'])) {
            // Store payment ID for later verification
            update_user_meta($user_id, '_bkash_payment_id_' . $transaction_id, $response_body['paymentID']);
            
            return array(
                'payment_url' => $response_body['bkashURL'],
                'payment_id' => $response_body['paymentID'],
                'transaction_id' => $transaction_id
            );
        } else {
            return new WP_Error('bkash_create', $response_body['errorMessage'] ?? 'Failed to create bKash payment');
        }
    }
    
    /**
     * Get bKash access token
     */
    private function get_bkash_access_token($settings) {
        // Check if we have a cached token
        $cached_token = get_transient('deshiflix_bkash_token');
        if ($cached_token) {
            return $cached_token;
        }
        
        $token_url = $this->get_bkash_api_url('token', $settings);
        
        $response = wp_remote_post($token_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'username' => $settings['username'],
                'password' => $settings['password']
            ),
            'body' => json_encode(array(
                'app_key' => $settings['app_key'],
                'app_secret' => $settings['app_secret']
            )),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (isset($response_body['id_token'])) {
            // Cache token for 50 minutes (expires in 1 hour)
            set_transient('deshiflix_bkash_token', $response_body['id_token'], 50 * 60);
            return $response_body['id_token'];
        }
        
        return false;
    }
    
    /**
     * Get bKash API URL
     */
    private function get_bkash_api_url($endpoint, $settings) {
        $is_sandbox = isset($settings['sandbox']) && $settings['sandbox'] === 'yes';
        $base_url = $is_sandbox ? 
            'https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/' :
            'https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized/checkout/';
        
        switch ($endpoint) {
            case 'token':
                return $base_url . 'token/grant';
            case 'create':
                return $base_url . 'create';
            case 'execute':
                return $base_url . 'execute';
            case 'query':
                return $base_url . 'payment/status';
            default:
                return $base_url;
        }
    }
    
    /**
     * Handle bKash payment success
     */
    public function handle_bkash_success($order, $payment_data) {
        // This is called when bKash payment is successful
        // Check if this is a premium subscription payment
        
        if (isset($payment_data['merchantInvoiceNumber'])) {
            $transaction_id = $payment_data['merchantInvoiceNumber'];
            
            // Check if this is a premium transaction
            if (strpos($transaction_id, 'DFLIX_BKASH_') === 0) {
                $this->complete_premium_bkash_payment($transaction_id, $payment_data);
            }
        }
    }
    
    /**
     * Complete premium bKash payment
     */
    private function complete_premium_bkash_payment($transaction_id, $payment_data) {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        // Get transaction
        $transaction = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_transactions WHERE transaction_id = %s",
            $transaction_id
        ));
        
        if (!$transaction) {
            return false;
        }
        
        // Update transaction with bKash data
        $wpdb->update(
            $table_transactions,
            array(
                'status' => 'completed',
                'gateway_transaction_id' => $payment_data['trxID'] ?? '',
                'gateway_response' => json_encode($payment_data)
            ),
            array('transaction_id' => $transaction_id)
        );
        
        // Activate premium subscription
        $this->activate_premium_subscription($transaction);
        
        return true;
    }
    
    /**
     * Create premium transaction
     */
    private function create_premium_transaction($user_id, $plan, $transaction_id) {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        $wpdb->insert(
            $table_transactions,
            array(
                'user_id' => $user_id,
                'plan_id' => $plan->id,
                'amount' => $plan->price,
                'currency' => 'BDT',
                'payment_method' => 'bkash',
                'transaction_id' => $transaction_id,
                'status' => 'pending',
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT']
            )
        );
    }
    
    /**
     * Activate premium subscription
     */
    private function activate_premium_subscription($transaction) {
        // Get plan details
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_plans WHERE id = %d",
            $transaction->plan_id
        ));
        
        if (!$plan) {
            return false;
        }
        
        // Activate subscription
        if (function_exists('deshiflix_premium')) {
            $premium_user = DeshiFlix_Premium_User::get_instance();
            $premium_user->activate_premium_subscription(
                $transaction->user_id,
                $plan->id,
                $plan->duration_days,
                'bkash'
            );
            
            // Trigger premium activation hook
            do_action('deshiflix_premium_activated', $transaction->user_id);
            
            // Send notification
            if (class_exists('DeshiFlix_Premium_Notifications')) {
                $notifications = DeshiFlix_Premium_Notifications::get_instance();
                $notifications->send_welcome_notification($transaction->user_id);
            }
        }
        
        return true;
    }
    
    /**
     * Handle premium bKash webhook
     */
    public function handle_premium_bkash_webhook() {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            $data = $_POST;
        }
        
        // Log webhook data
        error_log('Premium bKash Webhook: ' . json_encode($data));
        
        // Verify webhook signature if available
        if (isset($data['merchantInvoiceNumber'])) {
            $transaction_id = $data['merchantInvoiceNumber'];
            
            // Check if this is a premium transaction
            if (strpos($transaction_id, 'DFLIX_BKASH_') === 0) {
                $status = isset($data['transactionStatus']) && $data['transactionStatus'] === 'Completed' ? 'completed' : 'failed';
                
                // Update transaction status
                global $wpdb;
                $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
                
                $wpdb->update(
                    $table_transactions,
                    array(
                        'status' => $status,
                        'gateway_transaction_id' => $data['trxID'] ?? '',
                        'gateway_response' => json_encode($data)
                    ),
                    array('transaction_id' => $transaction_id)
                );
                
                // If successful, activate subscription
                if ($status === 'completed') {
                    $transaction = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $table_transactions WHERE transaction_id = %s",
                        $transaction_id
                    ));
                    
                    if ($transaction) {
                        $this->activate_premium_subscription($transaction);
                    }
                }
            }
        }
        
        // Respond to webhook
        http_response_code(200);
        echo 'OK';
        exit;
    }
    
    /**
     * Modify bKash payment args for premium
     */
    public function modify_bkash_payment_args($args, $order) {
        // Add premium-specific modifications if needed
        return $args;
    }
}

// Initialize bKash integration
DeshiFlix_BKash_Premium_Integration::get_instance();
