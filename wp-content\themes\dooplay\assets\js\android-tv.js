/**
 * Android TV Optimization Script for DooPlay Live TV
 * Provides seamless navigation and control for Android TV devices
 */

(function($) {
    'use strict';

    var AndroidTV = {
        isAndroidTV: false,
        isInitialized: false,
        currentFocusIndex: 0,
        navigableElements: [],
        
        // Initialize Android TV functionality
        init: function() {
            if (this.isInitialized) return;
            
            this.detectAndroidTV();
            this.setupEventListeners();
            this.initializeFocus();
            this.showControlsHint();
            
            this.isInitialized = true;
            console.log('Android TV mode initialized:', this.isAndroidTV);
        },
        
        // Detect if running on Android TV
        detectAndroidTV: function() {
            var userAgent = navigator.userAgent.toLowerCase();
            var isTV = false;
            
            // Check for Android TV user agents
            if (userAgent.includes('android') && 
                (userAgent.includes('tv') || 
                 userAgent.includes('googletv') || 
                 userAgent.includes('smart-tv') || 
                 userAgent.includes('smarttv'))) {
                isTV = true;
            }
            
            // Check for TV-like screen dimensions
            if (window.screen.width >= 1920 && window.screen.height >= 1080) {
                isTV = true;
            }
            
            // Check for lack of touch support (typical for TV)
            if (!('ontouchstart' in window) && window.screen.width >= 1280) {
                isTV = true;
            }
            
            this.isAndroidTV = isTV;
            
            if (this.isAndroidTV) {
                $('body').addClass('android-tv-mode tv-high-contrast');
                $('.video-player').addClass('tv-mode');
                $('.player-controls-overlay').addClass('tv-mode');
            }
        },
        
        // Setup event listeners for remote control
        setupEventListeners: function() {
            var self = this;
            
            // Keyboard/Remote control events
            $(document).on('keydown', function(e) {
                if (self.isAndroidTV) {
                    self.handleRemoteControl(e);
                }
            });
            
            // Focus management
            $(document).on('focus', '.tv-focusable', function() {
                self.updateFocus($(this));
            });
            
            // Window resize for TV orientation changes
            $(window).on('resize', function() {
                if (self.isAndroidTV) {
                    self.refreshNavigableElements();
                }
            });
            
            // Page load events
            $(document).on('DOMContentLoaded', function() {
                if (self.isAndroidTV) {
                    self.refreshNavigableElements();
                }
            });
        },
        
        // Handle remote control input
        handleRemoteControl: function(e) {
            // Prevent default behavior for navigation keys
            var navigationKeys = [37, 38, 39, 40, 13, 8, 27, 32, 77, 70];
            if (navigationKeys.includes(e.keyCode)) {
                e.preventDefault();
                e.stopPropagation();
            }

            // Add visual feedback for button press
            this.showButtonPress(e.keyCode);

            switch(e.keyCode) {
                case 37: // Left / D-pad Left
                    this.navigateLeft();
                    break;
                case 38: // Up / D-pad Up
                    this.navigateUp();
                    break;
                case 39: // Right / D-pad Right
                    this.navigateRight();
                    break;
                case 40: // Down / D-pad Down
                    this.navigateDown();
                    break;
                case 13: // Enter / OK
                    this.activateCurrentElement();
                    break;
                case 8: // Backspace / Back
                case 27: // Escape / Back
                    this.goBack();
                    break;
                case 32: // Space / Play/Pause
                    this.togglePlayPause();
                    break;
                case 77: // M / Mute
                    this.toggleMute();
                    break;
                case 70: // F / Fullscreen
                    this.toggleFullscreen();
                    break;
                case 82: // R / Refresh/Reload
                    this.refreshPage();
                    break;
                case 72: // H / Home
                    this.goHome();
                    break;
                case 83: // S / Search
                    this.focusSearch();
                    break;
                case 85: // U / Volume Up
                    this.volumeUp();
                    break;
                case 68: // D / Volume Down
                    this.volumeDown();
                    break;
                case 73: // I / Info
                    this.showChannelInfo();
                    break;
                case 67: // C / Channel List
                    this.showChannelList();
                    break;
                case 78: // N / Next Channel
                    this.nextChannel();
                    break;
                case 80: // P / Previous Channel
                    this.previousChannel();
                    break;
                case 71: // G / Guide/EPG
                    this.showGuide();
                    break;
                case 76: // L / Last Channel
                    this.lastChannel();
                    break;
                // Android TV specific keycodes
                case 166: // Media Previous
                    this.previousChannel();
                    break;
                case 167: // Media Next
                    this.nextChannel();
                    break;
                case 179: // Media Play/Pause
                    this.togglePlayPause();
                    break;
                case 172: // Media Home
                    this.goHome();
                    break;
                case 174: // Volume Down
                    this.volumeDown();
                    break;
                case 175: // Volume Up
                    this.volumeUp();
                    break;
                case 173: // Mute
                    this.toggleMute();
                    break;
            }
        },
        
        // Initialize focus on first navigable element
        initializeFocus: function() {
            this.refreshNavigableElements();
            if (this.navigableElements.length > 0) {
                this.setFocus(0);
            }
        },
        
        // Refresh list of navigable elements
        refreshNavigableElements: function() {
            this.navigableElements = $('.item a, .search-form input, .search-form button, .search-form select, .page-link, .category-tab, .control-btn, .btn, .favorite-btn').get();
            
            // Add tv-focusable class to all navigable elements
            $(this.navigableElements).addClass('tv-focusable');
        },
        
        // Set focus to element by index
        setFocus: function(index) {
            if (index < 0 || index >= this.navigableElements.length) return;
            
            // Remove previous focus
            $('.tv-focused').removeClass('tv-focused');
            
            // Set new focus
            this.currentFocusIndex = index;
            var element = $(this.navigableElements[index]);
            element.addClass('tv-focused');
            
            // Scroll into view
            this.scrollToElement(element[0]);
        },
        
        // Navigate left
        navigateLeft: function() {
            if (window.FocusManager) {
                window.FocusManager.navigate('left');
            } else {
                // Fallback to basic navigation
                var currentElement = this.navigableElements[this.currentFocusIndex];
                if (!currentElement) return;

                var newIndex = this.findElementInDirection(currentElement, 'left');
                if (newIndex !== -1) {
                    this.setFocus(newIndex);
                }
            }
        },

        // Navigate right
        navigateRight: function() {
            if (window.FocusManager) {
                window.FocusManager.navigate('right');
            } else {
                // Fallback to basic navigation
                var currentElement = this.navigableElements[this.currentFocusIndex];
                if (!currentElement) return;

                var newIndex = this.findElementInDirection(currentElement, 'right');
                if (newIndex !== -1) {
                    this.setFocus(newIndex);
                }
            }
        },

        // Navigate up
        navigateUp: function() {
            if (window.FocusManager) {
                window.FocusManager.navigate('up');
            } else {
                // Fallback to basic navigation
                var currentElement = this.navigableElements[this.currentFocusIndex];
                if (!currentElement) return;

                var newIndex = this.findElementInDirection(currentElement, 'up');
                if (newIndex !== -1) {
                    this.setFocus(newIndex);
                }
            }
        },

        // Navigate down
        navigateDown: function() {
            if (window.FocusManager) {
                window.FocusManager.navigate('down');
            } else {
                // Fallback to basic navigation
                var currentElement = this.navigableElements[this.currentFocusIndex];
                if (!currentElement) return;

                var newIndex = this.findElementInDirection(currentElement, 'down');
                if (newIndex !== -1) {
                    this.setFocus(newIndex);
                }
            }
        },
        
        // Find element in specific direction
        findElementInDirection: function(currentElement, direction) {
            var currentRect = currentElement.getBoundingClientRect();
            var candidates = [];
            
            for (var i = 0; i < this.navigableElements.length; i++) {
                if (i === this.currentFocusIndex) continue;
                
                var element = this.navigableElements[i];
                var rect = element.getBoundingClientRect();
                var isCandidate = false;
                
                switch(direction) {
                    case 'left':
                        isCandidate = rect.right <= currentRect.left && 
                                    Math.abs(rect.top + rect.height/2 - currentRect.top - currentRect.height/2) < currentRect.height;
                        break;
                    case 'right':
                        isCandidate = rect.left >= currentRect.right && 
                                    Math.abs(rect.top + rect.height/2 - currentRect.top - currentRect.height/2) < currentRect.height;
                        break;
                    case 'up':
                        isCandidate = rect.bottom <= currentRect.top && 
                                    Math.abs(rect.left + rect.width/2 - currentRect.left - currentRect.width/2) < currentRect.width;
                        break;
                    case 'down':
                        isCandidate = rect.top >= currentRect.bottom && 
                                    Math.abs(rect.left + rect.width/2 - currentRect.left - currentRect.width/2) < currentRect.width;
                        break;
                }
                
                if (isCandidate) {
                    var distance = Math.sqrt(
                        Math.pow((rect.left + rect.width/2) - (currentRect.left + currentRect.width/2), 2) + 
                        Math.pow((rect.top + rect.height/2) - (currentRect.top + currentRect.height/2), 2)
                    );
                    candidates.push({index: i, distance: distance});
                }
            }
            
            if (candidates.length > 0) {
                candidates.sort(function(a, b) { return a.distance - b.distance; });
                return candidates[0].index;
            }
            
            return -1;
        },
        
        // Activate current focused element
        activateCurrentElement: function() {
            var element = this.navigableElements[this.currentFocusIndex];
            if (element) {
                if (element.tagName === 'A' || element.tagName === 'BUTTON') {
                    element.click();
                } else if (element.tagName === 'INPUT') {
                    element.focus();
                }
            }
        },
        
        // Scroll element into view smoothly
        scrollToElement: function(element) {
            if (element && element.scrollIntoView) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'center'
                });
            }
        },
        
        // Update focus when element receives focus
        updateFocus: function(element) {
            var index = this.navigableElements.indexOf(element[0]);
            if (index !== -1) {
                this.currentFocusIndex = index;
            }
        },
        
        // Media control functions
        togglePlayPause: function() {
            var video = document.getElementById('video-element');
            if (video) {
                if (video.paused) {
                    video.play();
                    this.showNotification('Playing');
                } else {
                    video.pause();
                    this.showNotification('Paused');
                }
            }
        },
        
        toggleMute: function() {
            var video = document.getElementById('video-element');
            if (video) {
                video.muted = !video.muted;
                this.showNotification(video.muted ? 'Muted' : 'Unmuted');
            }
        },
        
        toggleFullscreen: function() {
            var video = document.getElementById('video-element');
            if (video) {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                    this.showNotification('Exited Fullscreen');
                } else {
                    video.requestFullscreen();
                    this.showNotification('Fullscreen');
                }
            }
        },
        
        // Navigation functions
        goBack: function() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                this.goHome();
            }
        },
        
        goHome: function() {
            window.location.href = '/live-tv';
        },
        
        refreshPage: function() {
            window.location.reload();
        },
        
        focusSearch: function() {
            var searchInput = $('.search-form input[type="text"]');
            if (searchInput.length > 0) {
                var index = this.navigableElements.indexOf(searchInput[0]);
                if (index !== -1) {
                    this.setFocus(index);
                }
            }
        },
        
        // Show control hints
        showControlsHint: function() {
            if (!this.isAndroidTV) return;
            
            var hint = $('<div class="tv-controls-hint">Use D-pad to navigate • OK to select • Back to go back • Space to play/pause</div>');
            $('body').append(hint);
            
            setTimeout(function() {
                hint.addClass('show');
            }, 1000);
            
            setTimeout(function() {
                hint.removeClass('show');
                setTimeout(function() {
                    hint.remove();
                }, 300);
            }, 5000);
        },
        
        // Show notification
        showNotification: function(message) {
            var notification = $('.tv-notification');
            if (notification.length === 0) {
                notification = $('<div class="tv-notification"></div>');
                $('body').append(notification);
            }

            notification.text(message).addClass('show');

            setTimeout(function() {
                notification.removeClass('show');
            }, 2000);
        },

        // Additional Android TV Remote Functions
        showButtonPress: function(keyCode) {
            var currentElement = $('.tv-focused');
            if (currentElement.length > 0) {
                currentElement.addClass('tv-button-press');
                setTimeout(function() {
                    currentElement.removeClass('tv-button-press');
                }, 100);
            }
        },

        volumeUp: function() {
            var video = document.getElementById('video-element');
            if (video) {
                video.volume = Math.min(1, video.volume + 0.1);
                this.showNotification('Volume: ' + Math.round(video.volume * 100) + '%');
            }
        },

        volumeDown: function() {
            var video = document.getElementById('video-element');
            if (video) {
                video.volume = Math.max(0, video.volume - 0.1);
                this.showNotification('Volume: ' + Math.round(video.volume * 100) + '%');
            }
        },

        showChannelInfo: function() {
            var channelName = $('[data-channel-name]').first().data('channel-name');
            if (channelName) {
                this.showNotification('Now Playing: ' + channelName);
            }
        },

        showChannelList: function() {
            // Navigate to channel list if not already there
            if (window.location.href.indexOf('channel=') !== -1) {
                window.location.href = '/live-tv';
            } else {
                this.showNotification('Channel List');
            }
        },

        nextChannel: function() {
            var currentLink = $('.related-link.tv-focused');
            if (currentLink.length > 0) {
                currentLink[0].click();
            } else {
                // Find next channel in related channels
                var relatedChannels = $('.related-link');
                if (relatedChannels.length > 0) {
                    relatedChannels[0].click();
                }
            }
        },

        previousChannel: function() {
            var relatedChannels = $('.related-link');
            if (relatedChannels.length > 1) {
                relatedChannels[relatedChannels.length - 1].click();
            }
        },

        showGuide: function() {
            this.showNotification('Electronic Program Guide not available');
        },

        lastChannel: function() {
            var lastChannel = localStorage.getItem('lastChannel');
            if (lastChannel && lastChannel !== window.location.href) {
                window.location.href = lastChannel;
            } else {
                this.showNotification('No previous channel');
            }
        },

        // Store current channel as last channel
        storeLastChannel: function() {
            if (window.location.href.indexOf('channel=') !== -1) {
                localStorage.setItem('lastChannel', window.location.href);
            }
        },

        // Enhanced focus management for complex layouts
        findBestFocusCandidate: function(currentElement, direction) {
            var candidates = this.findElementInDirection(currentElement, direction);
            if (candidates === -1) {
                // If no direct candidate found, try to find the closest element in that general direction
                return this.findClosestElementInDirection(currentElement, direction);
            }
            return candidates;
        },

        findClosestElementInDirection: function(currentElement, direction) {
            var currentRect = currentElement.getBoundingClientRect();
            var allElements = this.navigableElements;
            var bestCandidate = -1;
            var bestDistance = Infinity;

            for (var i = 0; i < allElements.length; i++) {
                if (i === this.currentFocusIndex) continue;

                var element = allElements[i];
                var rect = element.getBoundingClientRect();
                var distance = Infinity;

                switch(direction) {
                    case 'left':
                        if (rect.right <= currentRect.left) {
                            distance = currentRect.left - rect.right + Math.abs(rect.top - currentRect.top);
                        }
                        break;
                    case 'right':
                        if (rect.left >= currentRect.right) {
                            distance = rect.left - currentRect.right + Math.abs(rect.top - currentRect.top);
                        }
                        break;
                    case 'up':
                        if (rect.bottom <= currentRect.top) {
                            distance = currentRect.top - rect.bottom + Math.abs(rect.left - currentRect.left);
                        }
                        break;
                    case 'down':
                        if (rect.top >= currentRect.bottom) {
                            distance = rect.top - currentRect.bottom + Math.abs(rect.left - currentRect.left);
                        }
                        break;
                }

                if (distance < bestDistance) {
                    bestDistance = distance;
                    bestCandidate = i;
                }
            }

            return bestCandidate;
        },

        // Enhanced activation with haptic feedback simulation
        activateCurrentElement: function() {
            var element = this.navigableElements[this.currentFocusIndex];
            if (element) {
                // Visual feedback
                $(element).addClass('tv-button-press');
                setTimeout(function() {
                    $(element).removeClass('tv-button-press');
                }, 150);

                // Store current channel before navigation
                this.storeLastChannel();

                // Activate element
                if (element.tagName === 'A' || element.tagName === 'BUTTON') {
                    element.click();
                } else if (element.tagName === 'INPUT') {
                    element.focus();
                    // Show virtual keyboard hint for TV
                    this.showNotification('Use remote to type or connect a keyboard');
                }
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        AndroidTV.init();
    });

    // Make AndroidTV object globally available
    window.AndroidTV = AndroidTV;

})(jQuery);
