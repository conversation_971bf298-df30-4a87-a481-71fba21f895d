/**
 * DeshiFlix Premium - DooPlay Style Admin JavaScript
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

(function($) {
    'use strict';
    
    // DooPlay Premium Admin object
    var DooPlayPremiumAdmin = {
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.initComponents();
        },
        
        // Bind events
        bindEvents: function() {
            // Plan management
            $(document).on('click', '#add-new-plan', this.showPlanModal);
            $(document).on('click', '.edit-plan', this.editPlan);
            $(document).on('click', '.delete-plan', this.deletePlan);
            
            // User management
            $(document).on('click', '.view-user', this.viewUser);
            $(document).on('input', '.search-input', this.searchUsers);
            $(document).on('change', '.filter-select', this.filterUsers);
            
            // Content management
            $(document).on('click', '#apply-bulk-action', this.applyBulkAction);
            
            // Gateway management
            $(document).on('change', '.gateway-toggle input', this.toggleGateway);
            $(document).on('click', '.configure-gateway', this.configureGateway);
            
            // General
            $(document).on('click', '.dooplay-action-card', this.handleActionCard);
        },
        
        // Initialize components
        initComponents: function() {
            this.initTooltips();
            this.initCharts();
            this.loadDashboardData();
            this.initAdvancedFeatures();
        },
        
        // Show plan modal
        showPlanModal: function(e) {
            e.preventDefault();
            
            var modal = $('<div class="dooplay-modal-overlay">' +
                         '<div class="dooplay-modal">' +
                         '<div class="dooplay-modal-header">' +
                         '<h3>Add New Plan</h3>' +
                         '<button class="dooplay-modal-close">&times;</button>' +
                         '</div>' +
                         '<div class="dooplay-modal-body">' +
                         '<form id="plan-form">' +
                         '<div class="form-group">' +
                         '<label>Plan Name</label>' +
                         '<input type="text" name="plan_name" required>' +
                         '</div>' +
                         '<div class="form-group">' +
                         '<label>Price (৳)</label>' +
                         '<input type="number" name="plan_price" step="0.01" required>' +
                         '</div>' +
                         '<div class="form-group">' +
                         '<label>Duration (days)</label>' +
                         '<input type="number" name="plan_duration" required>' +
                         '</div>' +
                         '<div class="form-group">' +
                         '<label>Features</label>' +
                         '<div class="feature-checkboxes">' +
                         '<label><input type="checkbox" name="features[]" value="hd_quality"> HD Quality</label>' +
                         '<label><input type="checkbox" name="features[]" value="ad_free"> Ad Free</label>' +
                         '<label><input type="checkbox" name="features[]" value="download_links"> Download Links</label>' +
                         '<label><input type="checkbox" name="features[]" value="multiple_servers"> Multiple Servers</label>' +
                         '<label><input type="checkbox" name="features[]" value="early_access"> Early Access</label>' +
                         '</div>' +
                         '</div>' +
                         '</form>' +
                         '</div>' +
                         '<div class="dooplay-modal-footer">' +
                         '<button class="button button-secondary" id="cancel-plan">Cancel</button>' +
                         '<button class="button button-primary" id="save-plan">Save Plan</button>' +
                         '</div>' +
                         '</div>' +
                         '</div>');
            
            $('body').append(modal);
            modal.fadeIn();
            
            // Handle modal events
            modal.on('click', '.dooplay-modal-close, #cancel-plan', function() {
                modal.fadeOut(function() {
                    modal.remove();
                });
            });
            
            modal.on('click', '#save-plan', function() {
                DooPlayPremiumAdmin.savePlan(modal);
            });
        },
        
        // Edit plan
        editPlan: function(e) {
            e.preventDefault();
            var planId = $(this).data('plan-id');
            
            DooPlayPremiumAdmin.showNotification('Edit plan functionality coming soon!', 'info');
        },
        
        // Delete plan
        deletePlan: function(e) {
            e.preventDefault();
            var planId = $(this).data('plan-id');
            
            if (confirm('Are you sure you want to delete this plan?')) {
                DooPlayPremiumAdmin.showNotification('Delete plan functionality coming soon!', 'info');
            }
        },
        
        // Save plan
        savePlan: function(modal) {
            var formData = modal.find('#plan-form').serialize();
            
            // Simulate save
            DooPlayPremiumAdmin.showNotification('Plan saved successfully!', 'success');
            
            modal.fadeOut(function() {
                modal.remove();
            });
            
            // Reload plans
            setTimeout(function() {
                location.reload();
            }, 1000);
        },
        
        // View user
        viewUser: function(e) {
            e.preventDefault();
            var userId = $(this).data('user-id');
            
            var modal = $('<div class="dooplay-modal-overlay">' +
                         '<div class="dooplay-modal dooplay-modal-large">' +
                         '<div class="dooplay-modal-header">' +
                         '<h3>User Details</h3>' +
                         '<button class="dooplay-modal-close">&times;</button>' +
                         '</div>' +
                         '<div class="dooplay-modal-body">' +
                         '<div class="user-details-loading">' +
                         '<div class="loading-spinner"></div>' +
                         '<p>Loading user details...</p>' +
                         '</div>' +
                         '</div>' +
                         '</div>' +
                         '</div>');
            
            $('body').append(modal);
            modal.fadeIn();
            
            // Load user details
            setTimeout(function() {
                var userDetails = '<div class="user-details-grid">' +
                                 '<div class="user-info-card">' +
                                 '<h4>User Information</h4>' +
                                 '<p><strong>User ID:</strong> ' + userId + '</p>' +
                                 '<p><strong>Status:</strong> <span class="status-badge status-active">Active</span></p>' +
                                 '<p><strong>Plan:</strong> Standard Premium</p>' +
                                 '<p><strong>Expires:</strong> Dec 31, 2025</p>' +
                                 '</div>' +
                                 '<div class="user-activity-card">' +
                                 '<h4>Recent Activity</h4>' +
                                 '<ul class="activity-list">' +
                                 '<li>Watched premium content - 2 hours ago</li>' +
                                 '<li>Downloaded movie - 1 day ago</li>' +
                                 '<li>Subscription renewed - 5 days ago</li>' +
                                 '</ul>' +
                                 '</div>' +
                                 '</div>';
                
                modal.find('.dooplay-modal-body').html(userDetails);
            }, 1000);
            
            // Handle modal close
            modal.on('click', '.dooplay-modal-close', function() {
                modal.fadeOut(function() {
                    modal.remove();
                });
            });
        },
        
        // Search users
        searchUsers: function() {
            var searchTerm = $(this).val().toLowerCase();
            
            $('.dooplay-table tbody tr').each(function() {
                var userText = $(this).text().toLowerCase();
                if (userText.indexOf(searchTerm) > -1) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        },
        
        // Filter users
        filterUsers: function() {
            var filterValue = $(this).val();
            
            $('.dooplay-table tbody tr').each(function() {
                if (!filterValue) {
                    $(this).show();
                } else {
                    var statusBadge = $(this).find('.status-badge');
                    if (statusBadge.hasClass('status-' + filterValue)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                }
            });
        },
        
        // Apply bulk action
        applyBulkAction: function(e) {
            e.preventDefault();
            var action = $('.bulk-action-select').val();
            
            if (!action) {
                DooPlayPremiumAdmin.showNotification('Please select an action', 'warning');
                return;
            }
            
            DooPlayPremiumAdmin.showNotification('Bulk action "' + action + '" applied!', 'success');
        },
        
        // Toggle gateway
        toggleGateway: function() {
            var $toggle = $(this);
            var gateway = $toggle.data('gateway');
            var enabled = $toggle.is(':checked');
            var $card = $toggle.closest('.dooplay-gateway-card');
            
            if (enabled) {
                $card.removeClass('disabled').addClass('enabled');
                $card.find('.status-indicator').removeClass('inactive').addClass('active').text('Active');
            } else {
                $card.removeClass('enabled').addClass('disabled');
                $card.find('.status-indicator').removeClass('active').addClass('inactive').text('Inactive');
            }
            
            DooPlayPremiumAdmin.showNotification(gateway + ' gateway ' + (enabled ? 'enabled' : 'disabled'), 'success');
        },
        
        // Configure gateway
        configureGateway: function(e) {
            e.preventDefault();
            var gateway = $(this).data('gateway');
            
            // Redirect to settings tab with gateway focus
            window.location.href = 'admin.php?page=dooplay-premium&tab=settings&gateway=' + gateway;
        },
        
        // Handle action card clicks
        handleActionCard: function(e) {
            // Add click animation
            var $card = $(this);
            $card.addClass('clicked');
            
            setTimeout(function() {
                $card.removeClass('clicked');
            }, 200);
        },
        
        // Initialize tooltips
        initTooltips: function() {
            $('[data-tooltip]').each(function() {
                var $element = $(this);
                var tooltipText = $element.data('tooltip');
                
                $element.on('mouseenter', function() {
                    var tooltip = $('<div class="dooplay-tooltip">' + tooltipText + '</div>');
                    $('body').append(tooltip);
                    
                    var offset = $element.offset();
                    tooltip.css({
                        top: offset.top - tooltip.outerHeight() - 10,
                        left: offset.left + ($element.outerWidth() / 2) - (tooltip.outerWidth() / 2)
                    });
                    
                    tooltip.fadeIn();
                });
                
                $element.on('mouseleave', function() {
                    $('.dooplay-tooltip').fadeOut(function() {
                        $(this).remove();
                    });
                });
            });
        },
        
        // Initialize charts
        initCharts: function() {
            this.initMiniCharts();
            this.initMainCharts();
            this.bindChartEvents();
        },

        // Initialize mini charts in stat cards
        initMiniCharts: function() {
            // Revenue mini chart
            this.createMiniChart('revenue-mini-chart', [10, 15, 12, 18, 22, 20, 25], '#46b450');

            // Users mini chart
            this.createMiniChart('users-mini-chart', [5, 8, 6, 12, 15, 18, 20], '#667eea');

            // Conversion mini chart
            this.createMiniChart('conversion-mini-chart', [2, 3, 2.5, 4, 3.8, 4.2, 4.5], '#ffb900');

            // Churn mini chart
            this.createMiniChart('churn-mini-chart', [8, 7, 6, 5, 4, 3, 2], '#dc3545');
        },

        // Create mini chart
        createMiniChart: function(canvasId, data, color) {
            var canvas = document.getElementById(canvasId);
            if (!canvas) return;

            var ctx = canvas.getContext('2d');
            var width = canvas.width;
            var height = canvas.height;

            // Clear canvas
            ctx.clearRect(0, 0, width, height);

            // Calculate points
            var max = Math.max(...data);
            var min = Math.min(...data);
            var range = max - min || 1;

            var points = data.map(function(value, index) {
                return {
                    x: (index / (data.length - 1)) * width,
                    y: height - ((value - min) / range) * height
                };
            });

            // Draw line
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();

            points.forEach(function(point, index) {
                if (index === 0) {
                    ctx.moveTo(point.x, point.y);
                } else {
                    ctx.lineTo(point.x, point.y);
                }
            });

            ctx.stroke();

            // Draw area under curve
            ctx.globalAlpha = 0.1;
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(points[0].x, height);

            points.forEach(function(point) {
                ctx.lineTo(point.x, point.y);
            });

            ctx.lineTo(points[points.length - 1].x, height);
            ctx.closePath();
            ctx.fill();
            ctx.globalAlpha = 1;
        },

        // Initialize main charts
        initMainCharts: function() {
            this.initMainRevenueChart();
            this.initPlanDistributionChart();
        },

        // Initialize main revenue chart
        initMainRevenueChart: function() {
            var canvas = document.getElementById('main-revenue-chart');
            if (!canvas) return;

            // Show loading state
            this.showChartLoading(canvas.parentElement);

            // Simulate data loading
            setTimeout(function() {
                DooPlayPremiumAdmin.renderMainRevenueChart(canvas);
            }, 1000);
        },

        // Render main revenue chart
        renderMainRevenueChart: function(canvas) {
            var ctx = canvas.getContext('2d');
            var width = canvas.width;
            var height = canvas.height;

            // Sample data
            var revenueData = [1200, 1800, 1500, 2200, 2800, 2400, 3200, 2900, 3500, 3800, 4200, 4500];
            var subscriptionData = [45, 52, 48, 65, 72, 68, 85, 78, 92, 98, 105, 112];

            // Clear canvas
            ctx.clearRect(0, 0, width, height);

            // Draw grid
            this.drawChartGrid(ctx, width, height);

            // Draw revenue line
            this.drawChartLine(ctx, revenueData, width, height, '#46b450', 3);

            // Draw subscription line (scaled)
            var scaledSubscriptions = subscriptionData.map(function(val) { return val * 30; });
            this.drawChartLine(ctx, scaledSubscriptions, width, height, '#667eea', 2);

            // Hide loading state
            this.hideChartLoading(canvas.parentElement);
        },

        // Initialize plan distribution chart
        initPlanDistributionChart: function() {
            var canvas = document.getElementById('plan-distribution-chart');
            if (!canvas) return;

            this.showChartLoading(canvas.parentElement);

            setTimeout(function() {
                DooPlayPremiumAdmin.renderPlanDistributionChart(canvas);
            }, 1200);
        },

        // Render plan distribution chart
        renderPlanDistributionChart: function(canvas) {
            var ctx = canvas.getContext('2d');
            var centerX = canvas.width / 2;
            var centerY = canvas.height / 2;
            var radius = Math.min(centerX, centerY) - 20;

            // Sample data
            var planData = [
                { label: 'Basic', value: 45, color: '#46b450' },
                { label: 'Standard', value: 35, color: '#ffb900' },
                { label: 'Pro', value: 20, color: '#dc3545' }
            ];

            var total = planData.reduce(function(sum, item) { return sum + item.value; }, 0);
            var currentAngle = -Math.PI / 2;

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw pie slices
            planData.forEach(function(item) {
                var sliceAngle = (item.value / total) * 2 * Math.PI;

                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                ctx.closePath();
                ctx.fillStyle = item.color;
                ctx.fill();

                // Draw label
                var labelAngle = currentAngle + sliceAngle / 2;
                var labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
                var labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);

                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(item.value + '%', labelX, labelY);

                currentAngle += sliceAngle;
            });

            this.hideChartLoading(canvas.parentElement);
        },
        
        // Load dashboard data
        loadDashboardData: function() {
            // Animate stat cards
            $('.dooplay-stat-card').each(function(index) {
                var $card = $(this);
                setTimeout(function() {
                    $card.addClass('loaded');
                }, index * 200);
            });

            // Load real-time data
            this.loadRealTimeData();
        },

        // Load real-time data
        loadRealTimeData: function() {
            // Simulate real-time updates
            setInterval(function() {
                DooPlayPremiumAdmin.updateStatCards();
            }, 30000); // Update every 30 seconds
        },

        // Update stat cards
        updateStatCards: function() {
            $('.stat-value').each(function() {
                var $value = $(this);
                var currentValue = parseFloat($value.text().replace(/[^\d.]/g, ''));
                var change = (Math.random() - 0.5) * 0.1; // Random small change
                var newValue = currentValue * (1 + change);

                // Animate value change
                $({ value: currentValue }).animate({ value: newValue }, {
                    duration: 1000,
                    step: function() {
                        if ($value.text().includes('৳')) {
                            $value.text('৳' + Math.round(this.value).toLocaleString());
                        } else if ($value.text().includes('%')) {
                            $value.text(this.value.toFixed(1) + '%');
                        } else {
                            $value.text(Math.round(this.value).toLocaleString());
                        }
                    }
                });
            });
        },

        // Initialize advanced features
        initAdvancedFeatures: function() {
            this.initKeyboardShortcuts();
            this.initAutoRefresh();
            this.initNotificationSystem();
            this.initSearchFunctionality();
            this.initSettingsTabs();
        },

        // Initialize keyboard shortcuts
        initKeyboardShortcuts: function() {
            $(document).on('keydown', function(e) {
                // Ctrl/Cmd + D for Dashboard
                if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                    e.preventDefault();
                    window.location.href = 'admin.php?page=deshiflix-premium';
                }

                // Ctrl/Cmd + P for Plans
                if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                    e.preventDefault();
                    window.location.href = 'admin.php?page=deshiflix-premium-plans';
                }

                // Ctrl/Cmd + U for Users
                if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                    e.preventDefault();
                    window.location.href = 'admin.php?page=deshiflix-premium-users';
                }

                // Ctrl/Cmd + A for Analytics
                if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
                    e.preventDefault();
                    window.location.href = 'admin.php?page=deshiflix-premium-analytics';
                }
            });
        },

        // Initialize auto refresh
        initAutoRefresh: function() {
            // Auto refresh every 5 minutes
            setInterval(function() {
                if (document.visibilityState === 'visible') {
                    DooPlayPremiumAdmin.refreshDashboardData();
                }
            }, 300000);
        },

        // Refresh dashboard data
        refreshDashboardData: function() {
            this.showNotification('Refreshing data...', 'info');

            // Simulate data refresh
            setTimeout(function() {
                DooPlayPremiumAdmin.updateStatCards();
                DooPlayPremiumAdmin.showNotification('Data refreshed successfully', 'success');
            }, 2000);
        },

        // Initialize notification system
        initNotificationSystem: function() {
            // Check for system notifications
            this.checkSystemNotifications();
        },

        // Check system notifications
        checkSystemNotifications: function() {
            // Simulate checking for important notifications
            setTimeout(function() {
                var notifications = [
                    { type: 'info', message: 'Welcome to DeshiFlix Premium System!' },
                    { type: 'warning', message: 'Remember to backup your data regularly.' }
                ];

                notifications.forEach(function(notification, index) {
                    setTimeout(function() {
                        DooPlayPremiumAdmin.showNotification(notification.message, notification.type);
                    }, index * 3000);
                });
            }, 2000);
        },

        // Initialize search functionality
        initSearchFunctionality: function() {
            // Global search shortcut
            $(document).on('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === '/') {
                    e.preventDefault();
                    DooPlayPremiumAdmin.showGlobalSearch();
                }
            });
        },

        // Show global search
        showGlobalSearch: function() {
            var searchModal = $('<div class="dooplay-search-modal">' +
                              '<div class="search-modal-content">' +
                              '<div class="search-header">' +
                              '<input type="text" placeholder="Search users, plans, content..." class="global-search-input">' +
                              '<button class="search-close">&times;</button>' +
                              '</div>' +
                              '<div class="search-results">' +
                              '<div class="search-suggestions">' +
                              '<h4>Quick Actions</h4>' +
                              '<div class="suggestion-item" data-action="plans">📋 Manage Plans</div>' +
                              '<div class="suggestion-item" data-action="users">👥 View Users</div>' +
                              '<div class="suggestion-item" data-action="analytics">📊 Analytics</div>' +
                              '<div class="suggestion-item" data-action="settings">⚙️ Settings</div>' +
                              '</div>' +
                              '</div>' +
                              '</div>' +
                              '</div>');

            $('body').append(searchModal);
            searchModal.fadeIn();
            searchModal.find('.global-search-input').focus();

            // Handle search
            searchModal.on('input', '.global-search-input', function() {
                var query = $(this).val().toLowerCase();
                // Implement search logic here
            });

            // Handle close
            searchModal.on('click', '.search-close, .dooplay-search-modal', function(e) {
                if (e.target === this) {
                    searchModal.fadeOut(function() {
                        searchModal.remove();
                    });
                }
            });

            // Handle suggestions
            searchModal.on('click', '.suggestion-item', function() {
                var action = $(this).data('action');
                window.location.href = 'admin.php?page=deshiflix-premium-' + action;
            });
        },

        // Bind chart events
        bindChartEvents: function() {
            // Chart period change
            $(document).on('change', '#chart-period', function() {
                var period = $(this).val();
                DooPlayPremiumAdmin.updateChartData(period);
            });
        },

        // Update chart data
        updateChartData: function(period) {
            this.showNotification('Updating chart data for ' + period + ' days...', 'info');

            // Simulate data update
            setTimeout(function() {
                DooPlayPremiumAdmin.initMainCharts();
                DooPlayPremiumAdmin.showNotification('Chart data updated', 'success');
            }, 1500);
        },

        // Chart helper methods
        drawChartGrid: function(ctx, width, height) {
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.lineWidth = 1;

            // Vertical lines
            for (var i = 0; i <= 10; i++) {
                var x = (i / 10) * width;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            // Horizontal lines
            for (var i = 0; i <= 5; i++) {
                var y = (i / 5) * height;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
        },

        // Draw chart line
        drawChartLine: function(ctx, data, width, height, color, lineWidth) {
            var max = Math.max(...data);
            var min = Math.min(...data);
            var range = max - min || 1;

            var points = data.map(function(value, index) {
                return {
                    x: (index / (data.length - 1)) * width,
                    y: height - ((value - min) / range) * height
                };
            });

            ctx.strokeStyle = color;
            ctx.lineWidth = lineWidth;
            ctx.beginPath();

            points.forEach(function(point, index) {
                if (index === 0) {
                    ctx.moveTo(point.x, point.y);
                } else {
                    ctx.lineTo(point.x, point.y);
                }
            });

            ctx.stroke();
        },

        // Show chart loading
        showChartLoading: function(container) {
            var loading = $('<div class="chart-loading">' +
                           '<div class="chart-loading-spinner"></div>' +
                           '<p>Loading chart data...</p>' +
                           '</div>');
            $(container).append(loading);
        },

        // Hide chart loading
        hideChartLoading: function(container) {
            $(container).find('.chart-loading').fadeOut(function() {
                $(this).remove();
            });
        },

        // Initialize settings tabs
        initSettingsTabs: function() {
            // Tab switching
            $(document).on('click', '.tab-btn', function(e) {
                e.preventDefault();

                var targetTab = $(this).data('tab');

                // Update tab buttons
                $('.tab-btn').removeClass('active');
                $(this).addClass('active');

                // Update tab content
                $('.tab-content').removeClass('active');
                $('#' + targetTab + '-tab').addClass('active');

                // Save current tab in localStorage
                localStorage.setItem('dooplay_premium_active_tab', targetTab);

                // Trigger tab change event
                $(document).trigger('dooplay:tab-changed', [targetTab]);
            });

            // Restore last active tab
            var lastTab = localStorage.getItem('dooplay_premium_active_tab');
            if (lastTab && $('.tab-btn[data-tab="' + lastTab + '"]').length) {
                $('.tab-btn[data-tab="' + lastTab + '"]').click();
            }

            // Toggle switches
            $(document).on('change', '.toggle-switch input', function() {
                var $switch = $(this);
                var $card = $switch.closest('.gateway-card');

                if ($switch.is(':checked')) {
                    $card.addClass('enabled').removeClass('disabled');
                    DooPlayPremiumAdmin.showNotification('Gateway enabled', 'success');
                } else {
                    $card.addClass('disabled').removeClass('enabled');
                    DooPlayPremiumAdmin.showNotification('Gateway disabled', 'info');
                }
            });

            // Feature cards interaction
            $(document).on('change', '.feature-card input[type="checkbox"]', function() {
                var $card = $(this).closest('.feature-card');
                var featureName = $(this).closest('.feature-label').find('.feature-title').text();

                if ($(this).is(':checked')) {
                    $card.addClass('enabled');
                    DooPlayPremiumAdmin.showNotification(featureName + ' enabled', 'success');
                } else {
                    $card.removeClass('enabled');
                    DooPlayPremiumAdmin.showNotification(featureName + ' disabled', 'info');
                }
            });

            // Settings form validation
            $(document).on('submit', '.premium-settings-form', function(e) {
                var isValid = DooPlayPremiumAdmin.validateSettingsForm($(this));

                if (!isValid) {
                    e.preventDefault();
                    DooPlayPremiumAdmin.showNotification('Please check your settings and try again', 'error');
                    return false;
                }

                // Show saving state
                var $submitBtn = $(this).find('button[type="submit"]');
                var originalText = $submitBtn.html();

                $submitBtn.html('<span class="dashicons dashicons-update spin"></span> Saving...').prop('disabled', true);

                // Re-enable after 3 seconds (form will submit)
                setTimeout(function() {
                    $submitBtn.html(originalText).prop('disabled', false);
                }, 3000);
            });

            // Reset settings
            $(document).on('click', '#reset-settings', function(e) {
                e.preventDefault();

                if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
                    DooPlayPremiumAdmin.resetSettingsToDefaults();
                }
            });

            // Auto-save draft settings
            var saveTimeout;
            $(document).on('change', '.premium-settings-form input, .premium-settings-form select', function() {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(function() {
                    DooPlayPremiumAdmin.saveDraftSettings();
                }, 2000);
            });
        },

        // Validate settings form
        validateSettingsForm: function($form) {
            var isValid = true;
            var errors = [];

            // Check required fields
            $form.find('input[required], select[required]').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    errors.push($(this).attr('name') + ' is required');
                    $(this).addClass('error');
                } else {
                    $(this).removeClass('error');
                }
            });

            // Validate API keys if gateways are enabled
            if ($form.find('input[name="bkash_enabled"]:checked').length) {
                var apiKey = $form.find('input[name="bkash_api_key"]').val();
                if (!apiKey || apiKey.length < 10) {
                    isValid = false;
                    errors.push('bKash API Key must be at least 10 characters');
                }
            }

            if ($form.find('input[name="nagad_enabled"]:checked').length) {
                var merchantId = $form.find('input[name="nagad_merchant_id"]').val();
                if (!merchantId || merchantId.length < 5) {
                    isValid = false;
                    errors.push('Nagad Merchant ID must be at least 5 characters');
                }
            }

            // Validate numeric fields
            $form.find('input[type="number"]').each(function() {
                var val = parseInt($(this).val());
                var min = parseInt($(this).attr('min'));
                var max = parseInt($(this).attr('max'));

                if (min && val < min) {
                    isValid = false;
                    errors.push($(this).attr('name') + ' must be at least ' + min);
                }

                if (max && val > max) {
                    isValid = false;
                    errors.push($(this).attr('name') + ' must be no more than ' + max);
                }
            });

            if (errors.length > 0) {
                console.log('Validation errors:', errors);
            }

            return isValid;
        },

        // Reset settings to defaults
        resetSettingsToDefaults: function() {
            var $form = $('.premium-settings-form');

            // Reset checkboxes
            $form.find('input[type="checkbox"]').prop('checked', false);

            // Reset default values
            $form.find('input[name="max_devices_per_user"]').val(3);
            $form.find('input[name="trial_period_days"]').val(7);
            $form.find('input[name="cache_duration"]').val(60);
            $form.find('input[name="api_rate_limit"]').val(100);

            // Clear text inputs
            $form.find('input[type="text"], input[type="password"]').val('');

            // Update UI
            $('.gateway-card').removeClass('enabled').addClass('disabled');
            $('.feature-card').removeClass('enabled');

            this.showNotification('Settings reset to defaults', 'success');
        },

        // Save draft settings
        saveDraftSettings: function() {
            var formData = $('.premium-settings-form').serialize();
            localStorage.setItem('dooplay_premium_draft_settings', formData);

            // Show subtle indication
            $('.settings-footer').append('<span class="draft-saved">Draft saved</span>');
            setTimeout(function() {
                $('.draft-saved').fadeOut(function() {
                    $(this).remove();
                });
            }, 2000);
        },

        // Load draft settings
        loadDraftSettings: function() {
            var draftData = localStorage.getItem('dooplay_premium_draft_settings');
            if (draftData) {
                // Parse and apply draft settings
                var params = new URLSearchParams(draftData);
                params.forEach(function(value, key) {
                    var $field = $('.premium-settings-form [name="' + key + '"]');
                    if ($field.attr('type') === 'checkbox') {
                        $field.prop('checked', value === '1');
                    } else {
                        $field.val(value);
                    }
                });
            }
        }
        
        // Show notification
        showNotification: function(message, type) {
            type = type || 'info';
            
            var notification = $('<div class="dooplay-notification dooplay-notification-' + type + '">' +
                               '<span class="notification-message">' + message + '</span>' +
                               '<button class="notification-close">&times;</button>' +
                               '</div>');
            
            $('body').append(notification);
            
            // Position notification
            notification.css({
                position: 'fixed',
                top: '20px',
                right: '20px',
                zIndex: 99999
            });
            
            notification.slideDown();
            
            // Auto hide after 5 seconds
            setTimeout(function() {
                notification.slideUp(function() {
                    notification.remove();
                });
            }, 5000);
            
            // Manual close
            notification.on('click', '.notification-close', function() {
                notification.slideUp(function() {
                    notification.remove();
                });
            });
        },
        
        // Format currency
        formatCurrency: function(amount) {
            return '৳' + parseFloat(amount).toLocaleString('en-BD', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            });
        },
        
        // Format number
        formatNumber: function(num) {
            return new Intl.NumberFormat('en-BD').format(num);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        DooPlayPremiumAdmin.init();
    });
    
    // Make available globally
    window.DooPlayPremiumAdmin = DooPlayPremiumAdmin;
    
})(jQuery);

// Additional CSS for dynamic elements
jQuery(document).ready(function($) {
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .dooplay-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.7);
                z-index: 99999;
                display: none;
            }
            
            .dooplay-modal {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 12px;
                min-width: 400px;
                max-width: 90vw;
                max-height: 90vh;
                overflow: hidden;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            }
            
            .dooplay-modal-large {
                min-width: 600px;
            }
            
            .dooplay-modal-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .dooplay-modal-header h3 {
                margin: 0;
            }
            
            .dooplay-modal-close {
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background 0.3s ease;
            }
            
            .dooplay-modal-close:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            
            .dooplay-modal-body {
                padding: 20px;
                max-height: 60vh;
                overflow-y: auto;
            }
            
            .dooplay-modal-footer {
                padding: 20px;
                border-top: 1px solid #eee;
                display: flex;
                justify-content: flex-end;
                gap: 10px;
            }
            
            .form-group {
                margin-bottom: 20px;
            }
            
            .form-group label {
                display: block;
                margin-bottom: 5px;
                font-weight: 600;
                color: #333;
            }
            
            .form-group input {
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
            }
            
            .feature-checkboxes {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 10px;
            }
            
            .feature-checkboxes label {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: normal;
                margin-bottom: 0;
            }
            
            .user-details-loading {
                text-align: center;
                padding: 40px;
            }
            
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .user-details-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
            }
            
            .user-info-card,
            .user-activity-card {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
            }
            
            .user-info-card h4,
            .user-activity-card h4 {
                margin: 0 0 15px 0;
                color: #333;
            }
            
            .activity-list {
                list-style: none;
                padding: 0;
                margin: 0;
            }
            
            .activity-list li {
                padding: 8px 0;
                border-bottom: 1px solid #eee;
            }
            
            .activity-list li:last-child {
                border-bottom: none;
            }
            
            .dooplay-notification {
                background: white;
                border-radius: 8px;
                padding: 15px 20px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                display: none;
                min-width: 300px;
                border-left: 4px solid #667eea;
            }
            
            .dooplay-notification-success {
                border-left-color: #46b450;
            }
            
            .dooplay-notification-warning {
                border-left-color: #ffb900;
            }
            
            .dooplay-notification-error {
                border-left-color: #dc3545;
            }
            
            .dooplay-notification .notification-message {
                display: inline-block;
                margin-right: 15px;
            }
            
            .dooplay-notification .notification-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                float: right;
                color: #666;
            }
            
            .dooplay-tooltip {
                position: absolute;
                background: #333;
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                z-index: 99999;
                display: none;
            }
            
            .dooplay-action-card.clicked {
                transform: translateY(-5px) scale(0.98);
            }
            
            .dooplay-stat-card.loaded {
                animation: slideInUp 0.6s ease;
            }
            
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `)
        .appendTo('head');
});
