<?php
/**
 * DeshiFlix Premium System - Bulk Actions
 *
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Start output buffering to prevent header issues
ob_start();

/**
 * Premium Bulk Actions Class
 */
class DeshiFlix_Premium_Bulk_Actions {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize bulk actions
     */
    private function init() {
        add_filter('bulk_actions-edit-movies', array($this, 'add_bulk_actions'));
        add_filter('bulk_actions-edit-tvshows', array($this, 'add_bulk_actions'));
        add_filter('bulk_actions-edit-episodes', array($this, 'add_bulk_actions'));
        
        add_filter('handle_bulk_actions-edit-movies', array($this, 'handle_bulk_actions'), 10, 3);
        add_filter('handle_bulk_actions-edit-tvshows', array($this, 'handle_bulk_actions'), 10, 3);
        add_filter('handle_bulk_actions-edit-episodes', array($this, 'handle_bulk_actions'), 10, 3);
        
        add_action('admin_notices', array($this, 'bulk_action_notices'));
    }
    
    /**
     * Add bulk actions
     */
    public function add_bulk_actions($bulk_actions) {
        $bulk_actions['make_premium_basic'] = __('Make Premium (Basic)', 'deshiflix');
        $bulk_actions['make_premium_standard'] = __('Make Premium (Standard)', 'deshiflix');
        $bulk_actions['make_premium_pro'] = __('Make Premium (Pro)', 'deshiflix');
        $bulk_actions['make_free'] = __('Make Free', 'deshiflix');
        $bulk_actions['set_early_access'] = __('Set Early Access', 'deshiflix');
        $bulk_actions['remove_early_access'] = __('Remove Early Access', 'deshiflix');
        
        return $bulk_actions;
    }
    
    /**
     * Handle bulk actions
     */
    public function handle_bulk_actions($redirect_to, $doaction, $post_ids) {
        if (empty($post_ids)) {
            return $redirect_to;
        }
        
        $processed = 0;
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        
        switch ($doaction) {
            case 'make_premium_basic':
                $processed = $this->bulk_make_premium($post_ids, 'basic');
                break;
                
            case 'make_premium_standard':
                $processed = $this->bulk_make_premium($post_ids, 'standard');
                break;
                
            case 'make_premium_pro':
                $processed = $this->bulk_make_premium($post_ids, 'pro');
                break;
                
            case 'make_free':
                $processed = $this->bulk_make_free($post_ids);
                break;
                
            case 'set_early_access':
                $processed = $this->bulk_set_early_access($post_ids);
                break;
                
            case 'remove_early_access':
                $processed = $this->bulk_remove_early_access($post_ids);
                break;
                
            default:
                return $redirect_to;
        }
        
        $redirect_to = add_query_arg(array(
            'bulk_action' => $doaction,
            'processed' => $processed
        ), $redirect_to);
        
        return $redirect_to;
    }
    
    /**
     * Bulk make premium
     */
    private function bulk_make_premium($post_ids, $level) {
        $processed = 0;
        
        foreach ($post_ids as $post_id) {
            if (!current_user_can('edit_post', $post_id)) {
                continue;
            }
            
            // Set premium status
            update_post_meta($post_id, '_is_premium_content', 1);
            update_post_meta($post_id, '_premium_level', $level);
            
            // Set premium features based on level
            $features = $this->get_features_by_level($level);
            update_post_meta($post_id, '_premium_features', $features);
            
            // Update premium content table
            $this->update_premium_content_table($post_id, $level, $features);
            
            $processed++;
        }
        
        return $processed;
    }
    
    /**
     * Bulk make free
     */
    private function bulk_make_free($post_ids) {
        $processed = 0;
        
        foreach ($post_ids as $post_id) {
            if (!current_user_can('edit_post', $post_id)) {
                continue;
            }
            
            // Remove premium status
            update_post_meta($post_id, '_is_premium_content', 0);
            delete_post_meta($post_id, '_premium_level');
            delete_post_meta($post_id, '_premium_features');
            delete_post_meta($post_id, '_premium_unlock_date');
            
            // Remove from premium content table
            $this->remove_from_premium_content_table($post_id);
            
            $processed++;
        }
        
        return $processed;
    }
    
    /**
     * Bulk set early access
     */
    private function bulk_set_early_access($post_ids) {
        $processed = 0;
        $unlock_date = date('Y-m-d H:i:s', strtotime('+30 days')); // 30 days from now
        
        foreach ($post_ids as $post_id) {
            if (!current_user_can('edit_post', $post_id)) {
                continue;
            }
            
            // Set early access
            update_post_meta($post_id, '_premium_unlock_date', $unlock_date);
            
            // Make premium if not already
            if (!get_post_meta($post_id, '_is_premium_content', true)) {
                update_post_meta($post_id, '_is_premium_content', 1);
                update_post_meta($post_id, '_premium_level', 'basic');
                
                $features = $this->get_features_by_level('basic');
                update_post_meta($post_id, '_premium_features', $features);
                
                $this->update_premium_content_table($post_id, 'basic', $features);
            }
            
            $processed++;
        }
        
        return $processed;
    }
    
    /**
     * Bulk remove early access
     */
    private function bulk_remove_early_access($post_ids) {
        $processed = 0;
        
        foreach ($post_ids as $post_id) {
            if (!current_user_can('edit_post', $post_id)) {
                continue;
            }
            
            // Remove early access
            delete_post_meta($post_id, '_premium_unlock_date');
            
            $processed++;
        }
        
        return $processed;
    }
    
    /**
     * Get features by level
     */
    private function get_features_by_level($level) {
        $features = array();
        
        switch ($level) {
            case 'basic':
                $features = array('hd_quality', 'ad_free');
                break;
                
            case 'standard':
                $features = array('hd_quality', 'ad_free', 'download_links', 'multiple_servers');
                break;
                
            case 'pro':
                $features = array('hd_quality', 'ad_free', 'download_links', 'multiple_servers', 'early_access', 'exclusive_content');
                break;
        }
        
        return $features;
    }
    
    /**
     * Update premium content table
     */
    private function update_premium_content_table($post_id, $level, $features) {
        global $wpdb;
        
        $table_content = $wpdb->prefix . 'deshiflix_premium_content';
        
        // Check if entry exists
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_content WHERE post_id = %d",
            $post_id
        ));
        
        $data = array(
            'post_id' => $post_id,
            'is_premium' => 1,
            'premium_level' => $level,
            'features' => json_encode($features),
            'updated_at' => current_time('mysql')
        );
        
        if ($existing) {
            $wpdb->update($table_content, $data, array('post_id' => $post_id));
        } else {
            $data['created_at'] = current_time('mysql');
            $wpdb->insert($table_content, $data);
        }
    }
    
    /**
     * Remove from premium content table
     */
    private function remove_from_premium_content_table($post_id) {
        global $wpdb;
        
        $table_content = $wpdb->prefix . 'deshiflix_premium_content';
        
        $wpdb->delete($table_content, array('post_id' => $post_id));
    }
    
    /**
     * Bulk action notices
     */
    public function bulk_action_notices() {
        if (!isset($_REQUEST['bulk_action']) || !isset($_REQUEST['processed'])) {
            return;
        }
        
        $action = sanitize_text_field($_REQUEST['bulk_action']);
        $processed = intval($_REQUEST['processed']);
        
        if ($processed === 0) {
            return;
        }
        
        $message = '';
        
        switch ($action) {
            case 'make_premium_basic':
                $message = sprintf(
                    _n('%d item made premium (basic).', '%d items made premium (basic).', $processed, 'deshiflix'),
                    $processed
                );
                break;
                
            case 'make_premium_standard':
                $message = sprintf(
                    _n('%d item made premium (standard).', '%d items made premium (standard).', $processed, 'deshiflix'),
                    $processed
                );
                break;
                
            case 'make_premium_pro':
                $message = sprintf(
                    _n('%d item made premium (pro).', '%d items made premium (pro).', $processed, 'deshiflix'),
                    $processed
                );
                break;
                
            case 'make_free':
                $message = sprintf(
                    _n('%d item made free.', '%d items made free.', $processed, 'deshiflix'),
                    $processed
                );
                break;
                
            case 'set_early_access':
                $message = sprintf(
                    _n('%d item set to early access.', '%d items set to early access.', $processed, 'deshiflix'),
                    $processed
                );
                break;
                
            case 'remove_early_access':
                $message = sprintf(
                    _n('%d item removed from early access.', '%d items removed from early access.', $processed, 'deshiflix'),
                    $processed
                );
                break;
        }
        
        if ($message) {
            ?>
            <div class="notice notice-success is-dismissible">
                <p><?php echo esc_html($message); ?></p>
            </div>
            <?php
        }
    }
}

// Initialize bulk actions
DeshiFlix_Premium_Bulk_Actions::get_instance();
