<?php
/**
 * Test Admin Menu Structure
 * 
 * This file shows what the admin menu should look like
 */

if (!defined('ABSPATH')) {
    exit;
}

echo '<h1>DeshiFlix Premium Admin Menu Structure</h1>';

echo '<h2>Expected Admin Sidebar Menu:</h2>';
echo '<ul>';
echo '<li><strong>Premium System</strong> (Main Menu)';
echo '<ul>';
echo '<li>Dashboard (Default page)</li>';
echo '<li>Plans (Subscription management)</li>';
echo '<li>Users (Premium subscribers)</li>';
echo '<li>Content (Premium content management)</li>';
echo '<li>Analytics (Reports and insights)</li>';
echo '<li>Settings (System configuration)</li>';
echo '</ul>';
echo '</li>';
echo '</ul>';

echo '<h2>Current Page Detection:</h2>';
echo '<ul>';
echo '<li>Current Page: ' . (isset($_GET['page']) ? $_GET['page'] : 'Not set') . '</li>';
echo '<li>Current Tab: ' . (isset($_GET['tab']) ? $_GET['tab'] : 'Not set') . '</li>';
echo '<li>Admin URL: ' . admin_url() . '</li>';
echo '</ul>';

echo '<h2>Menu Links:</h2>';
echo '<ul>';
echo '<li><a href="' . admin_url('admin.php?page=deshiflix-premium') . '">Dashboard</a></li>';
echo '<li><a href="' . admin_url('admin.php?page=deshiflix-premium-plans') . '">Plans</a></li>';
echo '<li><a href="' . admin_url('admin.php?page=deshiflix-premium-users') . '">Users</a></li>';
echo '<li><a href="' . admin_url('admin.php?page=deshiflix-premium-content') . '">Content</a></li>';
echo '<li><a href="' . admin_url('admin.php?page=deshiflix-premium-analytics') . '">Analytics</a></li>';
echo '<li><a href="' . admin_url('admin.php?page=deshiflix-premium-settings') . '">Settings</a></li>';
echo '</ul>';

echo '<h2>WordPress Admin Menu Debug:</h2>';
global $menu, $submenu;

echo '<h3>Main Menu Items:</h3>';
if (isset($menu)) {
    foreach ($menu as $item) {
        if (isset($item[0]) && strpos($item[0], 'Premium') !== false) {
            echo '<li>' . $item[0] . ' - ' . $item[2] . '</li>';
        }
    }
}

echo '<h3>Premium Submenu Items:</h3>';
if (isset($submenu['deshiflix-premium'])) {
    foreach ($submenu['deshiflix-premium'] as $item) {
        echo '<li>' . $item[0] . ' - ' . $item[2] . '</li>';
    }
} else {
    echo '<li>No submenu found for deshiflix-premium</li>';
}

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
a { color: #0073aa; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>';
?>
