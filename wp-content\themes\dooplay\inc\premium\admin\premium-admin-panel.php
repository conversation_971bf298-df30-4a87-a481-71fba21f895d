<?php
/**
 * DeshiFlix Premium System - Admin Panel
 *
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Start output buffering to prevent header issues
ob_start();

class DeshiFlix_Premium_Admin_Panel {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize admin panel
     */
    private function init() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_premium_dashboard_stats', array($this, 'ajax_dashboard_stats'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Main premium menu
        add_menu_page(
            __('DeshiFlix Premium', 'deshiflix'),
            __('Premium System', 'deshiflix'),
            'manage_options',
            'deshiflix-premium',
            array($this, 'dashboard_page'),
            'dashicons-star-filled',
            30
        );
        
        // Dashboard submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Dashboard', 'deshiflix'),
            __('Dashboard', 'deshiflix'),
            'manage_options',
            'deshiflix-premium',
            array($this, 'dashboard_page')
        );
        
        // Users submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Users', 'deshiflix'),
            __('Users', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-users',
            array($this, 'users_page')
        );
        
        // Plans submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Plans', 'deshiflix'),
            __('Plans', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-plans',
            array($this, 'plans_page')
        );
        
        // Content submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Content', 'deshiflix'),
            __('Content', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-content',
            array($this, 'content_page')
        );
        
        // Transactions submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Transactions', 'deshiflix'),
            __('Transactions', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-transactions',
            array($this, 'transactions_page')
        );
        
        // Analytics submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Analytics', 'deshiflix'),
            __('Analytics', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-analytics',
            array($this, 'analytics_page')
        );
        
        // Settings submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Settings', 'deshiflix'),
            __('Settings', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'deshiflix-premium') === false) {
            return;
        }
        
        wp_enqueue_style('deshiflix-premium-admin', 
                        DESHIFLIX_PREMIUM_ASSETS_URL . 'css/premium-admin.css', 
                        array(), DESHIFLIX_PREMIUM_VERSION);
        
        wp_enqueue_script('deshiflix-premium-admin', 
                         DESHIFLIX_PREMIUM_ASSETS_URL . 'js/premium-admin.js', 
                         array('jquery', 'wp-util', 'chart-js'), DESHIFLIX_PREMIUM_VERSION, true);
        
        // Enqueue Chart.js for analytics
        wp_enqueue_script('chart-js', 
                         'https://cdn.jsdelivr.net/npm/chart.js', 
                         array(), '3.9.1', true);
        
        // Localize script
        wp_localize_script('deshiflix-premium-admin', 'deshiflix_premium_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('deshiflix_premium_admin_nonce'),
            'messages' => array(
                'confirm_delete' => __('Are you sure you want to delete this item?', 'deshiflix'),
                'loading' => __('Loading...', 'deshiflix'),
                'success' => __('Operation completed successfully', 'deshiflix'),
                'error' => __('An error occurred', 'deshiflix')
            )
        ));
    }
    
    /**
     * Dashboard page
     */
    public function dashboard_page() {
        $stats = $this->get_dashboard_stats();
        ?>
        <div class="wrap premium-admin-wrap">
            <h1 class="wp-heading-inline">
                <span class="dashicons dashicons-star-filled"></span>
                <?php _e('DeshiFlix Premium Dashboard', 'deshiflix'); ?>
            </h1>
            
            <div class="premium-dashboard-stats">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-content">
                            <h3><?php echo number_format($stats['total_premium_users']); ?></h3>
                            <p><?php _e('Premium Users', 'deshiflix'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <h3>৳<?php echo number_format($stats['total_revenue'], 2); ?></h3>
                            <p><?php _e('Total Revenue', 'deshiflix'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <h3><?php echo number_format($stats['monthly_revenue'], 2); ?></h3>
                            <p><?php _e('This Month', 'deshiflix'); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🎬</div>
                        <div class="stat-content">
                            <h3><?php echo number_format($stats['premium_content']); ?></h3>
                            <p><?php _e('Premium Content', 'deshiflix'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="premium-dashboard-charts">
                <div class="chart-container">
                    <h3><?php _e('Revenue Chart (Last 30 Days)', 'deshiflix'); ?></h3>
                    <canvas id="revenueChart" width="400" height="200"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3><?php _e('User Growth (Last 30 Days)', 'deshiflix'); ?></h3>
                    <canvas id="userGrowthChart" width="400" height="200"></canvas>
                </div>
            </div>
            
            <div class="premium-dashboard-tables">
                <div class="table-container">
                    <h3><?php _e('Recent Transactions', 'deshiflix'); ?></h3>
                    <?php $this->render_recent_transactions(); ?>
                </div>
                
                <div class="table-container">
                    <h3><?php _e('Top Premium Content', 'deshiflix'); ?></h3>
                    <?php $this->render_top_content(); ?>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Load dashboard charts
            loadDashboardCharts();
        });
        
        function loadDashboardCharts() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            const revenueChart = new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($stats['revenue_chart_labels']); ?>,
                    datasets: [{
                        label: 'Revenue (৳)',
                        data: <?php echo json_encode($stats['revenue_chart_data']); ?>,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // User Growth Chart
            const userCtx = document.getElementById('userGrowthChart').getContext('2d');
            const userChart = new Chart(userCtx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($stats['user_chart_labels']); ?>,
                    datasets: [{
                        label: 'New Premium Users',
                        data: <?php echo json_encode($stats['user_chart_data']); ?>,
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        </script>
        <?php
    }
    
    /**
     * Users page
     */
    public function users_page() {
        ?>
        <div class="wrap premium-admin-wrap">
            <h1 class="wp-heading-inline">
                <?php _e('Premium Users Management', 'deshiflix'); ?>
            </h1>
            <a href="#" class="page-title-action" id="add-premium-user">
                <?php _e('Add Premium User', 'deshiflix'); ?>
            </a>
            
            <div class="premium-users-filters">
                <select id="filter-plan">
                    <option value=""><?php _e('All Plans', 'deshiflix'); ?></option>
                    <?php $this->render_plan_options(); ?>
                </select>
                
                <select id="filter-status">
                    <option value=""><?php _e('All Status', 'deshiflix'); ?></option>
                    <option value="active"><?php _e('Active', 'deshiflix'); ?></option>
                    <option value="expired"><?php _e('Expired', 'deshiflix'); ?></option>
                    <option value="cancelled"><?php _e('Cancelled', 'deshiflix'); ?></option>
                </select>
                
                <input type="text" id="search-users" placeholder="<?php _e('Search users...', 'deshiflix'); ?>">
                <button class="button" id="filter-users"><?php _e('Filter', 'deshiflix'); ?></button>
            </div>
            
            <div id="premium-users-table">
                <?php $this->render_users_table(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Plans page
     */
    public function plans_page() {
        ?>
        <div class="wrap premium-admin-wrap">
            <h1 class="wp-heading-inline">
                <?php _e('Premium Plans Management', 'deshiflix'); ?>
            </h1>
            <a href="#" class="page-title-action" id="add-premium-plan">
                <?php _e('Add New Plan', 'deshiflix'); ?>
            </a>
            
            <div id="premium-plans-table">
                <?php $this->render_plans_table(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Content page
     */
    public function content_page() {
        ?>
        <div class="wrap premium-admin-wrap">
            <h1 class="wp-heading-inline">
                <?php _e('Premium Content Management', 'deshiflix'); ?>
            </h1>
            
            <div class="premium-content-actions">
                <button class="button button-primary" id="bulk-make-premium">
                    <?php _e('Bulk Make Premium', 'deshiflix'); ?>
                </button>
                <button class="button" id="bulk-make-free">
                    <?php _e('Bulk Make Free', 'deshiflix'); ?>
                </button>
            </div>
            
            <div class="premium-content-filters">
                <select id="filter-post-type">
                    <option value=""><?php _e('All Types', 'deshiflix'); ?></option>
                    <option value="movies"><?php _e('Movies', 'deshiflix'); ?></option>
                    <option value="tvshows"><?php _e('TV Shows', 'deshiflix'); ?></option>
                    <option value="episodes"><?php _e('Episodes', 'deshiflix'); ?></option>
                </select>
                
                <select id="filter-premium-level">
                    <option value=""><?php _e('All Levels', 'deshiflix'); ?></option>
                    <option value="basic"><?php _e('Basic', 'deshiflix'); ?></option>
                    <option value="standard"><?php _e('Standard', 'deshiflix'); ?></option>
                    <option value="premium"><?php _e('Premium', 'deshiflix'); ?></option>
                </select>
                
                <input type="text" id="search-content" placeholder="<?php _e('Search content...', 'deshiflix'); ?>">
                <button class="button" id="filter-content"><?php _e('Filter', 'deshiflix'); ?></button>
            </div>
            
            <div id="premium-content-table">
                <?php $this->render_content_table(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Transactions page
     */
    public function transactions_page() {
        ?>
        <div class="wrap premium-admin-wrap">
            <h1 class="wp-heading-inline">
                <?php _e('Premium Transactions', 'deshiflix'); ?>
            </h1>
            
            <div class="transactions-filters">
                <select id="filter-payment-method">
                    <option value=""><?php _e('All Payment Methods', 'deshiflix'); ?></option>
                    <option value="bkash"><?php _e('bKash', 'deshiflix'); ?></option>
                    <option value="nagad"><?php _e('Nagad', 'deshiflix'); ?></option>
                    <option value="rocket"><?php _e('Rocket', 'deshiflix'); ?></option>
                    <option value="sslcommerz"><?php _e('SSLCommerz', 'deshiflix'); ?></option>
                </select>
                
                <select id="filter-transaction-status">
                    <option value=""><?php _e('All Status', 'deshiflix'); ?></option>
                    <option value="completed"><?php _e('Completed', 'deshiflix'); ?></option>
                    <option value="pending"><?php _e('Pending', 'deshiflix'); ?></option>
                    <option value="failed"><?php _e('Failed', 'deshiflix'); ?></option>
                    <option value="refunded"><?php _e('Refunded', 'deshiflix'); ?></option>
                </select>
                
                <input type="date" id="filter-date-from">
                <input type="date" id="filter-date-to">
                <button class="button" id="filter-transactions"><?php _e('Filter', 'deshiflix'); ?></button>
            </div>
            
            <div id="transactions-table">
                <?php $this->render_transactions_table(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Analytics page
     */
    public function analytics_page() {
        include DESHIFLIX_PREMIUM_PATH . 'admin/premium-analytics.php';
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        include DESHIFLIX_PREMIUM_PATH . 'admin/premium-settings.php';
    }
    
    /**
     * Get dashboard stats
     */
    private function get_dashboard_stats() {
        global $wpdb;
        
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        $table_content = $wpdb->prefix . 'deshiflix_premium_content';
        
        // Total premium users
        $total_premium_users = $wpdb->get_var(
            "SELECT COUNT(DISTINCT user_id) FROM $table_users WHERE status = 'active'"
        );
        
        // Total revenue
        $total_revenue = $wpdb->get_var(
            "SELECT SUM(amount) FROM $table_transactions WHERE status = 'completed'"
        );
        
        // Monthly revenue
        $monthly_revenue = $wpdb->get_var(
            "SELECT SUM(amount) FROM $table_transactions 
             WHERE status = 'completed' AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())"
        );
        
        // Premium content count
        $premium_content = $wpdb->get_var(
            "SELECT COUNT(*) FROM $table_content WHERE is_premium = 1"
        );
        
        // Chart data (last 30 days)
        $revenue_chart_data = $this->get_revenue_chart_data();
        $user_chart_data = $this->get_user_chart_data();
        
        return array(
            'total_premium_users' => $total_premium_users ?: 0,
            'total_revenue' => $total_revenue ?: 0,
            'monthly_revenue' => $monthly_revenue ?: 0,
            'premium_content' => $premium_content ?: 0,
            'revenue_chart_labels' => $revenue_chart_data['labels'],
            'revenue_chart_data' => $revenue_chart_data['data'],
            'user_chart_labels' => $user_chart_data['labels'],
            'user_chart_data' => $user_chart_data['data']
        );
    }
    
    /**
     * Get revenue chart data
     */
    private function get_revenue_chart_data() {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        $results = $wpdb->get_results(
            "SELECT DATE(created_at) as date, SUM(amount) as revenue 
             FROM $table_transactions 
             WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
             GROUP BY DATE(created_at) 
             ORDER BY date ASC"
        );
        
        $labels = array();
        $data = array();
        
        foreach ($results as $result) {
            $labels[] = date('M j', strtotime($result->date));
            $data[] = floatval($result->revenue);
        }
        
        return array('labels' => $labels, 'data' => $data);
    }
    
    /**
     * Get user chart data
     */
    private function get_user_chart_data() {
        global $wpdb;
        
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        
        $results = $wpdb->get_results(
            "SELECT DATE(created_at) as date, COUNT(*) as users 
             FROM $table_users 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
             GROUP BY DATE(created_at) 
             ORDER BY date ASC"
        );
        
        $labels = array();
        $data = array();
        
        foreach ($results as $result) {
            $labels[] = date('M j', strtotime($result->date));
            $data[] = intval($result->users);
        }
        
        return array('labels' => $labels, 'data' => $data);
    }

    /**
     * Render recent transactions
     */
    private function render_recent_transactions() {
        global $wpdb;

        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';

        $transactions = $wpdb->get_results(
            "SELECT t.*, p.name as plan_name, u.display_name
             FROM $table_transactions t
             LEFT JOIN $table_plans p ON t.plan_id = p.id
             LEFT JOIN {$wpdb->users} u ON t.user_id = u.ID
             ORDER BY t.created_at DESC
             LIMIT 10"
        );

        if (empty($transactions)) {
            echo '<p>' . __('No recent transactions found.', 'deshiflix') . '</p>';
            return;
        }
        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Transaction ID', 'deshiflix'); ?></th>
                    <th><?php _e('User', 'deshiflix'); ?></th>
                    <th><?php _e('Plan', 'deshiflix'); ?></th>
                    <th><?php _e('Amount', 'deshiflix'); ?></th>
                    <th><?php _e('Status', 'deshiflix'); ?></th>
                    <th><?php _e('Date', 'deshiflix'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($transactions as $transaction): ?>
                <tr>
                    <td><code><?php echo esc_html($transaction->transaction_id); ?></code></td>
                    <td><?php echo esc_html($transaction->display_name); ?></td>
                    <td><?php echo esc_html($transaction->plan_name); ?></td>
                    <td>৳<?php echo number_format($transaction->amount, 2); ?></td>
                    <td>
                        <span class="status-badge status-<?php echo esc_attr($transaction->status); ?>">
                            <?php echo ucfirst($transaction->status); ?>
                        </span>
                    </td>
                    <td><?php echo date('M j, Y', strtotime($transaction->created_at)); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * Render top content
     */
    private function render_top_content() {
        global $wpdb;

        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';

        $top_content = $wpdb->get_results(
            "SELECT p.ID, p.post_title, COUNT(a.id) as views
             FROM {$wpdb->posts} p
             LEFT JOIN $table_analytics a ON p.ID = a.post_id
             WHERE p.post_type IN ('movies', 'tvshows', 'episodes')
             AND EXISTS (
                 SELECT 1 FROM {$wpdb->postmeta} pm
                 WHERE pm.post_id = p.ID AND pm.meta_key = '_is_premium_content' AND pm.meta_value = '1'
             )
             GROUP BY p.ID
             ORDER BY views DESC
             LIMIT 10"
        );

        if (empty($top_content)) {
            echo '<p>' . __('No premium content analytics found.', 'deshiflix') . '</p>';
            return;
        }
        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Content', 'deshiflix'); ?></th>
                    <th><?php _e('Views', 'deshiflix'); ?></th>
                    <th><?php _e('Actions', 'deshiflix'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($top_content as $content): ?>
                <tr>
                    <td>
                        <strong><?php echo esc_html($content->post_title); ?></strong>
                        <div class="row-actions">
                            <span class="edit">
                                <a href="<?php echo get_edit_post_link($content->ID); ?>">
                                    <?php _e('Edit', 'deshiflix'); ?>
                                </a>
                            </span>
                        </div>
                    </td>
                    <td><?php echo number_format($content->views); ?></td>
                    <td>
                        <a href="<?php echo get_permalink($content->ID); ?>" target="_blank" class="button button-small">
                            <?php _e('View', 'deshiflix'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * Render plan options
     */
    private function render_plan_options() {
        global $wpdb;

        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plans = $wpdb->get_results("SELECT id, name FROM $table_plans WHERE status = 'active'");

        foreach ($plans as $plan) {
            echo '<option value="' . esc_attr($plan->id) . '">' . esc_html($plan->name) . '</option>';
        }
    }

    /**
     * Render users table
     */
    private function render_users_table() {
        global $wpdb;

        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';

        $users = $wpdb->get_results(
            "SELECT pu.*, p.name as plan_name, u.display_name, u.user_email
             FROM $table_users pu
             LEFT JOIN $table_plans p ON pu.plan_id = p.id
             LEFT JOIN {$wpdb->users} u ON pu.user_id = u.ID
             ORDER BY pu.created_at DESC
             LIMIT 50"
        );

        if (empty($users)) {
            echo '<p>' . __('No premium users found.', 'deshiflix') . '</p>';
            return;
        }
        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><input type="checkbox" id="select-all-users"></th>
                    <th><?php _e('User', 'deshiflix'); ?></th>
                    <th><?php _e('Plan', 'deshiflix'); ?></th>
                    <th><?php _e('Status', 'deshiflix'); ?></th>
                    <th><?php _e('Start Date', 'deshiflix'); ?></th>
                    <th><?php _e('End Date', 'deshiflix'); ?></th>
                    <th><?php _e('Actions', 'deshiflix'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users as $user): ?>
                <tr>
                    <td><input type="checkbox" name="user_ids[]" value="<?php echo esc_attr($user->id); ?>"></td>
                    <td>
                        <strong><?php echo esc_html($user->display_name); ?></strong><br>
                        <small><?php echo esc_html($user->user_email); ?></small>
                    </td>
                    <td><?php echo esc_html($user->plan_name); ?></td>
                    <td>
                        <span class="status-badge status-<?php echo esc_attr($user->status); ?>">
                            <?php echo ucfirst($user->status); ?>
                        </span>
                    </td>
                    <td><?php echo date('M j, Y', strtotime($user->start_date)); ?></td>
                    <td><?php echo date('M j, Y', strtotime($user->end_date)); ?></td>
                    <td>
                        <button class="button button-small edit-user" data-user-id="<?php echo esc_attr($user->id); ?>">
                            <?php _e('Edit', 'deshiflix'); ?>
                        </button>
                        <button class="button button-small cancel-subscription" data-user-id="<?php echo esc_attr($user->user_id); ?>">
                            <?php _e('Cancel', 'deshiflix'); ?>
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * Render plans table
     */
    private function render_plans_table() {
        global $wpdb;

        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plans = $wpdb->get_results("SELECT * FROM $table_plans ORDER BY sort_order ASC");

        if (empty($plans)) {
            echo '<p>' . __('No premium plans found.', 'deshiflix') . '</p>';
            return;
        }
        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Plan Name', 'deshiflix'); ?></th>
                    <th><?php _e('Price', 'deshiflix'); ?></th>
                    <th><?php _e('Duration', 'deshiflix'); ?></th>
                    <th><?php _e('Max Devices', 'deshiflix'); ?></th>
                    <th><?php _e('Status', 'deshiflix'); ?></th>
                    <th><?php _e('Actions', 'deshiflix'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($plans as $plan): ?>
                <tr>
                    <td>
                        <strong><?php echo esc_html($plan->name); ?></strong><br>
                        <small><?php echo esc_html($plan->description); ?></small>
                    </td>
                    <td>
                        ৳<?php echo number_format($plan->price, 2); ?>
                        <?php if ($plan->original_price && $plan->original_price > $plan->price): ?>
                            <br><small><del>৳<?php echo number_format($plan->original_price, 2); ?></del></small>
                        <?php endif; ?>
                    </td>
                    <td><?php echo $plan->duration_days; ?> <?php _e('days', 'deshiflix'); ?></td>
                    <td><?php echo $plan->max_devices; ?></td>
                    <td>
                        <span class="status-badge status-<?php echo esc_attr($plan->status); ?>">
                            <?php echo ucfirst($plan->status); ?>
                        </span>
                    </td>
                    <td>
                        <button class="button button-small edit-plan" data-plan-id="<?php echo esc_attr($plan->id); ?>">
                            <?php _e('Edit', 'deshiflix'); ?>
                        </button>
                        <button class="button button-small toggle-plan-status" data-plan-id="<?php echo esc_attr($plan->id); ?>">
                            <?php echo $plan->status === 'active' ? __('Deactivate', 'deshiflix') : __('Activate', 'deshiflix'); ?>
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * Render content table
     */
    private function render_content_table() {
        global $wpdb;

        $premium_content = $wpdb->get_results(
            "SELECT p.ID, p.post_title, p.post_type, pm.meta_value as premium_level
             FROM {$wpdb->posts} p
             INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
             WHERE pm.meta_key = '_is_premium_content' AND pm.meta_value = '1'
             AND p.post_type IN ('movies', 'tvshows', 'episodes')
             ORDER BY p.post_date DESC
             LIMIT 50"
        );

        if (empty($premium_content)) {
            echo '<p>' . __('No premium content found.', 'deshiflix') . '</p>';
            return;
        }
        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><input type="checkbox" id="select-all-content"></th>
                    <th><?php _e('Title', 'deshiflix'); ?></th>
                    <th><?php _e('Type', 'deshiflix'); ?></th>
                    <th><?php _e('Premium Level', 'deshiflix'); ?></th>
                    <th><?php _e('Actions', 'deshiflix'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($premium_content as $content): ?>
                <tr>
                    <td><input type="checkbox" name="content_ids[]" value="<?php echo esc_attr($content->ID); ?>"></td>
                    <td>
                        <strong><?php echo esc_html($content->post_title); ?></strong>
                        <div class="row-actions">
                            <span class="edit">
                                <a href="<?php echo get_edit_post_link($content->ID); ?>">
                                    <?php _e('Edit', 'deshiflix'); ?>
                                </a>
                            </span>
                        </div>
                    </td>
                    <td><?php echo ucfirst($content->post_type); ?></td>
                    <td>
                        <?php
                        $level = get_post_meta($content->ID, '_premium_level', true);
                        echo '<span class="premium-level-badge level-' . esc_attr($level) . '">' . ucfirst($level) . '</span>';
                        ?>
                    </td>
                    <td>
                        <button class="button button-small toggle-premium" data-post-id="<?php echo esc_attr($content->ID); ?>">
                            <?php _e('Make Free', 'deshiflix'); ?>
                        </button>
                        <a href="<?php echo get_permalink($content->ID); ?>" target="_blank" class="button button-small">
                            <?php _e('View', 'deshiflix'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * Render transactions table
     */
    private function render_transactions_table() {
        global $wpdb;

        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';

        $transactions = $wpdb->get_results(
            "SELECT t.*, p.name as plan_name, u.display_name
             FROM $table_transactions t
             LEFT JOIN $table_plans p ON t.plan_id = p.id
             LEFT JOIN {$wpdb->users} u ON t.user_id = u.ID
             ORDER BY t.created_at DESC
             LIMIT 100"
        );

        if (empty($transactions)) {
            echo '<p>' . __('No transactions found.', 'deshiflix') . '</p>';
            return;
        }
        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Transaction ID', 'deshiflix'); ?></th>
                    <th><?php _e('User', 'deshiflix'); ?></th>
                    <th><?php _e('Plan', 'deshiflix'); ?></th>
                    <th><?php _e('Amount', 'deshiflix'); ?></th>
                    <th><?php _e('Payment Method', 'deshiflix'); ?></th>
                    <th><?php _e('Status', 'deshiflix'); ?></th>
                    <th><?php _e('Date', 'deshiflix'); ?></th>
                    <th><?php _e('Actions', 'deshiflix'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($transactions as $transaction): ?>
                <tr>
                    <td><code><?php echo esc_html($transaction->transaction_id); ?></code></td>
                    <td><?php echo esc_html($transaction->display_name); ?></td>
                    <td><?php echo esc_html($transaction->plan_name); ?></td>
                    <td>৳<?php echo number_format($transaction->amount, 2); ?></td>
                    <td><?php echo ucfirst($transaction->payment_method); ?></td>
                    <td>
                        <span class="status-badge status-<?php echo esc_attr($transaction->status); ?>">
                            <?php echo ucfirst($transaction->status); ?>
                        </span>
                    </td>
                    <td><?php echo date('M j, Y H:i', strtotime($transaction->created_at)); ?></td>
                    <td>
                        <button class="button button-small view-transaction" data-transaction-id="<?php echo esc_attr($transaction->id); ?>">
                            <?php _e('View', 'deshiflix'); ?>
                        </button>
                        <?php if ($transaction->status === 'completed'): ?>
                        <button class="button button-small refund-transaction" data-transaction-id="<?php echo esc_attr($transaction->id); ?>">
                            <?php _e('Refund', 'deshiflix'); ?>
                        </button>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * AJAX dashboard stats
     */
    public function ajax_dashboard_stats() {
        check_ajax_referer('deshiflix_premium_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied', 'deshiflix')));
        }

        $stats = $this->get_dashboard_stats();
        wp_send_json_success($stats);
    }
}

// Initialize admin panel
if (is_admin()) {
    DeshiFlix_Premium_Admin_Panel::get_instance();
}
