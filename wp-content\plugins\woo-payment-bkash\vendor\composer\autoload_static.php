<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit6f1567cb8907bf59187b056ab5a35f54
{
    public static $files = array (
        'a2d018554d96b84645661425a707773f' => __DIR__ . '/../..' . '/includes/functions.php',
    );

    public static $prefixLengthsPsr4 = array (
        'D' => 
        array (
            'DCoders\\Bkash\\' => 14,
        ),
        'A' => 
        array (
            'Appsero\\' => 8,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'DCoders\\Bkash\\' => 
        array (
            0 => __DIR__ . '/../..' . '/includes',
        ),
        'Appsero\\' => 
        array (
            0 => __DIR__ . '/..' . '/appsero/client/src',
        ),
    );

    public static $classMap = array (
        'Appsero\\Client' => __DIR__ . '/..' . '/appsero/client/src/Client.php',
        '<PERSON><PERSON>ero\\Insights' => __DIR__ . '/..' . '/appsero/client/src/Insights.php',
        'A<PERSON>ero\\License' => __DIR__ . '/..' . '/appsero/client/src/License.php',
        'Appsero\\Updater' => __DIR__ . '/..' . '/appsero/client/src/Updater.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'DCoders\\Bkash\\API' => __DIR__ . '/../..' . '/includes/API.php',
        'DCoders\\Bkash\\API\\BkashBaseRestController' => __DIR__ . '/../..' . '/includes/API/BkashBaseRestController.php',
        'DCoders\\Bkash\\API\\Payment' => __DIR__ . '/../..' . '/includes/API/Payment.php',
        'DCoders\\Bkash\\API\\Settings' => __DIR__ . '/../..' . '/includes/API/Settings.php',
        'DCoders\\Bkash\\API\\Transaction' => __DIR__ . '/../..' . '/includes/API/Transaction.php',
        'DCoders\\Bkash\\API\\Upgrade' => __DIR__ . '/../..' . '/includes/API/Upgrade.php',
        'DCoders\\Bkash\\Abstracts\\BkashProcessor' => __DIR__ . '/../..' . '/includes/Abstracts/BkashProcessor.php',
        'DCoders\\Bkash\\Abstracts\\DcBkashUpgrader' => __DIR__ . '/../..' . '/includes/Abstracts/DcBkashUpgrader.php',
        'DCoders\\Bkash\\Admin' => __DIR__ . '/../..' . '/includes/Admin.php',
        'DCoders\\Bkash\\Admin\\Menu' => __DIR__ . '/../..' . '/includes/Admin/Menu.php',
        'DCoders\\Bkash\\Admin\\Settings' => __DIR__ . '/../..' . '/includes/Admin/Settings.php',
        'DCoders\\Bkash\\Ajax' => __DIR__ . '/../..' . '/includes/Ajax.php',
        'DCoders\\Bkash\\Assets' => __DIR__ . '/../..' . '/includes/Assets.php',
        'DCoders\\Bkash\\Frontend' => __DIR__ . '/../..' . '/includes/Frontend.php',
        'DCoders\\Bkash\\Frontend\\Shortcode' => __DIR__ . '/../..' . '/includes/Frontend/Shortcode.php',
        'DCoders\\Bkash\\Gateway\\Bkash' => __DIR__ . '/../..' . '/includes/Gateway/Bkash.php',
        'DCoders\\Bkash\\Gateway\\IntegrationTypes\\Checkout' => __DIR__ . '/../..' . '/includes/Gateway/IntegrationTypes/Checkout.php',
        'DCoders\\Bkash\\Gateway\\IntegrationTypes\\CheckoutUrl' => __DIR__ . '/../..' . '/includes/Gateway/IntegrationTypes/CheckoutUrl.php',
        'DCoders\\Bkash\\Gateway\\Manager' => __DIR__ . '/../..' . '/includes/Gateway/Manager.php',
        'DCoders\\Bkash\\Gateway\\Processor' => __DIR__ . '/../..' . '/includes/Gateway/Processor.php',
        'DCoders\\Bkash\\Installer' => __DIR__ . '/../..' . '/includes/Installer.php',
        'DCoders\\Bkash\\Upgrade\\AdminNotice' => __DIR__ . '/../..' . '/includes/Upgrade/AdminNotice.php',
        'DCoders\\Bkash\\Upgrade\\Manager' => __DIR__ . '/../..' . '/includes/Upgrade/Manager.php',
        'DCoders\\Bkash\\Upgrade\\Upgrades' => __DIR__ . '/../..' . '/includes/Upgrade/Upgrades.php',
        'DCoders\\Bkash\\Upgrade\\Upgrades\\V_2_0_0' => __DIR__ . '/../..' . '/includes/Upgrade/Upgrades/V_2_0_0.php',
        'DCoders\\Bkash\\Upgrade\\Upgrades\\V_2_1_0' => __DIR__ . '/../..' . '/includes/Upgrade/Upgrades/V_2_1_0.php',
        'DCoders\\Bkash\\Upgrade\\Upgrades\\V_3_0_0' => __DIR__ . '/../..' . '/includes/Upgrade/Upgrades/V_3_0_0.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit6f1567cb8907bf59187b056ab5a35f54::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit6f1567cb8907bf59187b056ab5a35f54::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit6f1567cb8907bf59187b056ab5a35f54::$classMap;

        }, null, ClassLoader::class);
    }
}
