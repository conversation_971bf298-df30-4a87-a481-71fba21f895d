<?php
/**
 * Premium System Testing Framework
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Premium_Tests {
    
    private static $instance = null;
    private $test_results = array();
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Add admin menu for testing
        add_action('admin_menu', array($this, 'add_testing_menu'));
        
        // AJAX handlers for tests
        add_action('wp_ajax_run_premium_tests', array($this, 'run_tests_ajax'));
        add_action('wp_ajax_test_payment_flow', array($this, 'test_payment_flow_ajax'));
    }
    
    /**
     * Add testing menu
     */
    public function add_testing_menu() {
        add_submenu_page(
            'deshiflix-premium',
            'System Tests',
            'System Tests',
            'manage_options',
            'premium-tests',
            array($this, 'testing_page')
        );
    }
    
    /**
     * Testing page
     */
    public function testing_page() {
        ?>
        <div class="wrap">
            <h1>🧪 Premium System Tests</h1>
            
            <div class="premium-testing-dashboard">
                <div class="test-categories">
                    <div class="test-category">
                        <h3>🔧 Core System Tests</h3>
                        <button class="button button-primary" onclick="runTestCategory('core')">
                            Run Core Tests
                        </button>
                        <div id="core-test-results" class="test-results"></div>
                    </div>
                    
                    <div class="test-category">
                        <h3>💳 Payment System Tests</h3>
                        <button class="button button-primary" onclick="runTestCategory('payment')">
                            Run Payment Tests
                        </button>
                        <div id="payment-test-results" class="test-results"></div>
                    </div>
                    
                    <div class="test-category">
                        <h3>🎬 Content Protection Tests</h3>
                        <button class="button button-primary" onclick="runTestCategory('content')">
                            Run Content Tests
                        </button>
                        <div id="content-test-results" class="test-results"></div>
                    </div>
                    
                    <div class="test-category">
                        <h3>📱 API Tests</h3>
                        <button class="button button-primary" onclick="runTestCategory('api')">
                            Run API Tests
                        </button>
                        <div id="api-test-results" class="test-results"></div>
                    </div>
                </div>
                
                <div class="test-actions">
                    <button class="button button-secondary" onclick="runAllTests()">
                        🚀 Run All Tests
                    </button>
                    <button class="button button-secondary" onclick="clearTestResults()">
                        🗑️ Clear Results
                    </button>
                </div>
                
                <div id="overall-test-results" class="overall-results"></div>
            </div>
        </div>
        
        <style>
        .premium-testing-dashboard {
            max-width: 1200px;
            margin: 20px 0;
        }
        
        .test-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-category {
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-category h3 {
            margin-top: 0;
            color: #333;
        }
        
        .test-results {
            margin-top: 15px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            min-height: 100px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .test-actions {
            text-align: center;
            margin: 30px 0;
        }
        
        .test-actions .button {
            margin: 0 10px;
            padding: 10px 20px;
        }
        
        .overall-results {
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .test-pass {
            color: #4CAF50;
        }
        
        .test-fail {
            color: #f44336;
        }
        
        .test-warning {
            color: #ff9800;
        }
        </style>
        
        <script>
        function runTestCategory(category) {
            var resultsDiv = document.getElementById(category + '-test-results');
            resultsDiv.innerHTML = 'Running ' + category + ' tests...';
            
            jQuery.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'run_premium_tests',
                    category: category,
                    nonce: '<?php echo wp_create_nonce('premium_tests'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        resultsDiv.innerHTML = response.data.results;
                    } else {
                        resultsDiv.innerHTML = 'Error: ' + response.data;
                    }
                },
                error: function() {
                    resultsDiv.innerHTML = 'Network error occurred';
                }
            });
        }
        
        function runAllTests() {
            var categories = ['core', 'payment', 'content', 'api'];
            categories.forEach(function(category) {
                runTestCategory(category);
            });
        }
        
        function clearTestResults() {
            var resultDivs = document.querySelectorAll('.test-results');
            resultDivs.forEach(function(div) {
                div.innerHTML = '';
            });
            document.getElementById('overall-test-results').innerHTML = '';
        }
        </script>
        <?php
    }
    
    /**
     * Run tests via AJAX
     */
    public function run_tests_ajax() {
        if (!wp_verify_nonce($_POST['nonce'], 'premium_tests')) {
            wp_send_json_error('Security check failed');
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $category = sanitize_text_field($_POST['category']);
        
        switch ($category) {
            case 'core':
                $results = $this->run_core_tests();
                break;
            case 'payment':
                $results = $this->run_payment_tests();
                break;
            case 'content':
                $results = $this->run_content_tests();
                break;
            case 'api':
                $results = $this->run_api_tests();
                break;
            default:
                wp_send_json_error('Invalid test category');
        }
        
        wp_send_json_success(array('results' => $results));
    }
    
    /**
     * Run core system tests
     */
    private function run_core_tests() {
        $results = "=== CORE SYSTEM TESTS ===\n\n";
        
        // Test 1: Database tables
        $results .= $this->test_database_tables();
        
        // Test 2: Premium core class
        $results .= $this->test_premium_core_class();
        
        // Test 3: User management
        $results .= $this->test_user_management();
        
        // Test 4: Plan management
        $results .= $this->test_plan_management();
        
        return $results;
    }
    
    /**
     * Test database tables
     */
    private function test_database_tables() {
        global $wpdb;
        
        $result = "1. Database Tables Test:\n";
        
        $required_tables = array(
            'deshiflix_premium_users',
            'deshiflix_premium_plans',
            'deshiflix_premium_content',
            'deshiflix_premium_transactions',
            'deshiflix_premium_analytics',
            'deshiflix_premium_devices'
        );
        
        $all_exist = true;
        
        foreach ($required_tables as $table) {
            $full_table_name = $wpdb->prefix . $table;
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$full_table_name'") === $full_table_name;
            
            if ($exists) {
                $result .= "   ✅ $table - EXISTS\n";
            } else {
                $result .= "   ❌ $table - MISSING\n";
                $all_exist = false;
            }
        }
        
        $result .= $all_exist ? "   🎉 All tables exist!\n\n" : "   ⚠️ Some tables are missing!\n\n";
        
        return $result;
    }
    
    /**
     * Test premium core class
     */
    private function test_premium_core_class() {
        $result = "2. Premium Core Class Test:\n";
        
        if (function_exists('deshiflix_premium')) {
            $result .= "   ✅ deshiflix_premium() function exists\n";
            
            $premium_core = deshiflix_premium();
            if ($premium_core) {
                $result .= "   ✅ Premium core instance created\n";
                
                // Test methods
                if (method_exists($premium_core, 'is_user_premium')) {
                    $result .= "   ✅ is_user_premium() method exists\n";
                } else {
                    $result .= "   ❌ is_user_premium() method missing\n";
                }
                
                if (method_exists($premium_core, 'get_user_premium_details')) {
                    $result .= "   ✅ get_user_premium_details() method exists\n";
                } else {
                    $result .= "   ❌ get_user_premium_details() method missing\n";
                }
            } else {
                $result .= "   ❌ Failed to create premium core instance\n";
            }
        } else {
            $result .= "   ❌ deshiflix_premium() function not found\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test user management
     */
    private function test_user_management() {
        $result = "3. User Management Test:\n";
        
        if (class_exists('DeshiFlix_Premium_User')) {
            $result .= "   ✅ DeshiFlix_Premium_User class exists\n";
            
            $user_manager = DeshiFlix_Premium_User::get_instance();
            if ($user_manager) {
                $result .= "   ✅ User manager instance created\n";
                
                // Test with admin user
                $admin_users = get_users(array('role' => 'administrator', 'number' => 1));
                if (!empty($admin_users)) {
                    $test_user_id = $admin_users[0]->ID;
                    
                    // Test premium check
                    $is_premium = $user_manager->is_user_premium($test_user_id);
                    $result .= "   ✅ Premium check completed (User $test_user_id: " . ($is_premium ? 'Premium' : 'Free') . ")\n";
                }
            } else {
                $result .= "   ❌ Failed to create user manager instance\n";
            }
        } else {
            $result .= "   ❌ DeshiFlix_Premium_User class not found\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test plan management
     */
    private function test_plan_management() {
        $result = "4. Plan Management Test:\n";
        
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $plans_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_plans WHERE status = 'active'");
        
        if ($plans_count > 0) {
            $result .= "   ✅ Found $plans_count active plans\n";
            
            $sample_plan = $wpdb->get_row("SELECT * FROM $table_plans WHERE status = 'active' LIMIT 1");
            if ($sample_plan) {
                $result .= "   ✅ Sample plan: {$sample_plan->name} - {$sample_plan->price}৳\n";
            }
        } else {
            $result .= "   ⚠️ No active plans found\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Run payment system tests
     */
    private function run_payment_tests() {
        $results = "=== PAYMENT SYSTEM TESTS ===\n\n";
        
        // Test 1: bKash integration
        $results .= $this->test_bkash_integration();
        
        // Test 2: Payment callbacks
        $results .= $this->test_payment_callbacks();
        
        // Test 3: Transaction handling
        $results .= $this->test_transaction_handling();
        
        return $results;
    }
    
    /**
     * Test bKash integration
     */
    private function test_bkash_integration() {
        $result = "1. bKash Integration Test:\n";
        
        if (function_exists('dc_bkash')) {
            $result .= "   ✅ bKash plugin is active\n";
            
            if (class_exists('DeshiFlix_BKash_Premium_Integration')) {
                $result .= "   ✅ Premium bKash integration class exists\n";
            } else {
                $result .= "   ❌ Premium bKash integration class missing\n";
            }
        } else {
            $result .= "   ⚠️ bKash plugin not active\n";
        }
        
        // Test bKash settings
        $bkash_settings = get_option('dc_bkash_settings', array());
        if (!empty($bkash_settings['app_key'])) {
            $result .= "   ✅ bKash app key configured\n";
        } else {
            $result .= "   ⚠️ bKash app key not configured\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test payment callbacks
     */
    private function test_payment_callbacks() {
        $result = "2. Payment Callbacks Test:\n";
        
        if (class_exists('DeshiFlix_Payment_Callbacks')) {
            $result .= "   ✅ Payment callbacks class exists\n";
            
            // Test callback URLs
            $callback_urls = array(
                'success' => home_url('/premium-payment-success/'),
                'failed' => home_url('/premium-payment-failed/'),
                'cancelled' => home_url('/premium-payment-cancelled/')
            );
            
            foreach ($callback_urls as $type => $url) {
                $response = wp_remote_get($url, array('timeout' => 10));
                if (!is_wp_error($response)) {
                    $result .= "   ✅ $type callback URL accessible\n";
                } else {
                    $result .= "   ⚠️ $type callback URL issue: " . $response->get_error_message() . "\n";
                }
            }
        } else {
            $result .= "   ❌ Payment callbacks class missing\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test transaction handling
     */
    private function test_transaction_handling() {
        $result = "3. Transaction Handling Test:\n";
        
        global $wpdb;
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        $transactions_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_transactions");
        $result .= "   ℹ️ Total transactions in database: $transactions_count\n";
        
        // Test transaction statuses
        $statuses = $wpdb->get_results("SELECT status, COUNT(*) as count FROM $table_transactions GROUP BY status");
        foreach ($statuses as $status) {
            $result .= "   ℹ️ {$status->status}: {$status->count} transactions\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Run content protection tests
     */
    private function run_content_tests() {
        $results = "=== CONTENT PROTECTION TESTS ===\n\n";
        
        // Test 1: Premium content detection
        $results .= $this->test_premium_content_detection();
        
        // Test 2: Access control
        $results .= $this->test_access_control();
        
        // Test 3: Download protection
        $results .= $this->test_download_protection();
        
        return $results;
    }
    
    /**
     * Test premium content detection
     */
    private function test_premium_content_detection() {
        $result = "1. Premium Content Detection Test:\n";
        
        if (class_exists('DeshiFlix_Premium_Content')) {
            $result .= "   ✅ Premium content class exists\n";
            
            $content_manager = DeshiFlix_Premium_Content::get_instance();
            
            // Get a sample post
            $posts = get_posts(array('post_type' => array('movies', 'tvshows'), 'numberposts' => 1));
            if (!empty($posts)) {
                $post_id = $posts[0]->ID;
                $is_premium = $content_manager->is_premium_content($post_id);
                $result .= "   ✅ Content check completed (Post $post_id: " . ($is_premium ? 'Premium' : 'Free') . ")\n";
            } else {
                $result .= "   ⚠️ No posts found for testing\n";
            }
        } else {
            $result .= "   ❌ Premium content class missing\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test access control
     */
    private function test_access_control() {
        $result = "2. Access Control Test:\n";
        
        if (class_exists('DeshiFlix_Premium_Features')) {
            $result .= "   ✅ Premium features class exists\n";
            
            $features = DeshiFlix_Premium_Features::get_instance();
            
            // Test with admin user
            $admin_users = get_users(array('role' => 'administrator', 'number' => 1));
            if (!empty($admin_users)) {
                $test_user_id = $admin_users[0]->ID;
                
                $test_features = array('hd_quality', 'download_links', 'ad_free');
                foreach ($test_features as $feature) {
                    $has_access = $features->user_has_feature_access($feature, $test_user_id);
                    $result .= "   ✅ $feature access: " . ($has_access ? 'Granted' : 'Denied') . "\n";
                }
            }
        } else {
            $result .= "   ❌ Premium features class missing\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test download protection
     */
    private function test_download_protection() {
        $result = "3. Download Protection Test:\n";
        
        if (class_exists('DeshiFlix_Download_Protection')) {
            $result .= "   ✅ Download protection class exists\n";
        } else {
            $result .= "   ❌ Download protection class missing\n";
        }
        
        // Test download filters
        if (has_filter('dooplay_download_links')) {
            $result .= "   ✅ Download links filter is active\n";
        } else {
            $result .= "   ⚠️ Download links filter not found\n";
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Run API tests
     */
    private function run_api_tests() {
        $results = "=== API TESTS ===\n\n";
        
        // Test 1: REST API endpoints
        $results .= $this->test_rest_api_endpoints();
        
        // Test 2: Mobile API
        $results .= $this->test_mobile_api();
        
        return $results;
    }
    
    /**
     * Test REST API endpoints
     */
    private function test_rest_api_endpoints() {
        $result = "1. REST API Endpoints Test:\n";
        
        $endpoints = array(
            '/wp-json/deshiflix/v1/premium/status',
            '/wp-json/deshiflix/v1/premium/plans'
        );
        
        foreach ($endpoints as $endpoint) {
            $url = home_url($endpoint);
            $response = wp_remote_get($url, array('timeout' => 10));
            
            if (!is_wp_error($response)) {
                $status_code = wp_remote_retrieve_response_code($response);
                if ($status_code === 200 || $status_code === 401) { // 401 is expected for protected endpoints
                    $result .= "   ✅ $endpoint - Accessible (Status: $status_code)\n";
                } else {
                    $result .= "   ⚠️ $endpoint - Unexpected status: $status_code\n";
                }
            } else {
                $result .= "   ❌ $endpoint - Error: " . $response->get_error_message() . "\n";
            }
        }
        
        $result .= "\n";
        return $result;
    }
    
    /**
     * Test mobile API
     */
    private function test_mobile_api() {
        $result = "2. Mobile API Test:\n";
        
        if (class_exists('DeshiFlix_Mobile_Premium_API')) {
            $result .= "   ✅ Mobile API class exists\n";
        } else {
            $result .= "   ❌ Mobile API class missing\n";
        }
        
        $result .= "\n";
        return $result;
    }
}

// Initialize testing framework
DeshiFlix_Premium_Tests::get_instance();
