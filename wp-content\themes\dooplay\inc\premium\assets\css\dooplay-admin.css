/**
 * DeshiFlix Premium - DooPlay Style Admin CSS
 *
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

/* Force load styles */
body.wp-admin {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON>o, sans-serif;
}

/* Ensure our styles take priority */
.wrap.dooplay-admin-wrap {
    margin: 20px 0 !important;
    background: #f1f1f1 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

/* DooPlay Admin Wrapper */
.dooplay-admin-wrap {
    margin: 20px 0;
    background: #f1f1f1;
    border-radius: 8px;
    overflow: hidden;
}

/* Breadcrumb - Force styles */
.dooplay-admin-breadcrumb {
    background: white !important;
    padding: 15px 30px !important;
    border-bottom: 1px solid #e1e1e1 !important;
    font-size: 14px !important;
    margin: 0 !important;
}

.dooplay-admin-breadcrumb a {
    color: #667eea !important;
    text-decoration: none !important;
    font-weight: 500 !important;
}

.dooplay-admin-breadcrumb a:hover {
    text-decoration: underline !important;
}

.breadcrumb-separator {
    margin: 0 10px !important;
    color: #999 !important;
}

.breadcrumb-current {
    color: #333 !important;
    font-weight: 600 !important;
}

/* Header Section - Force styles */
.dooplay-admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 30px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin: 0 !important;
    position: relative !important;
}

.dooplay-admin-title h1 {
    margin: 0 !important;
    font-size: 2rem !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    color: white !important;
}

.dooplay-admin-title .dashicons {
    color: #ffd700 !important;
    font-size: 2rem !important;
}

.dooplay-admin-title .description {
    margin: 5px 0 0 0 !important;
    opacity: 0.9 !important;
    font-size: 1rem !important;
    color: white !important;
}

.dooplay-admin-actions {
    display: flex;
    gap: 10px;
}

.dooplay-admin-actions .button {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dooplay-admin-actions .button-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.dooplay-admin-actions .button-primary {
    background: #ffd700;
    color: #333;
    border: none;
}

.dooplay-admin-actions .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Navigation */
.dooplay-admin-nav {
    background: white;
    border-bottom: 1px solid #e1e1e1;
}

.dooplay-admin-nav .nav-tab-wrapper {
    border: none;
    margin: 0;
    padding: 0 30px;
}

.dooplay-admin-nav .nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    margin: 0;
    border: none;
    border-bottom: 3px solid transparent;
    background: none;
    color: #666;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dooplay-admin-nav .nav-tab:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.dooplay-admin-nav .nav-tab.nav-tab-active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.dooplay-admin-nav .nav-tab .dashicons {
    font-size: 16px;
}

/* Content Area - Force styles */
.dooplay-admin-content {
    background: white !important;
    min-height: 500px !important;
    padding: 30px !important;
    margin: 0 !important;
}

/* Welcome Banner */
.dooplay-welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    margin-bottom: 30px;
    overflow: hidden;
    position: relative;
}

.dooplay-welcome-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.welcome-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40px;
    position: relative;
    z-index: 1;
}

.welcome-text h2 {
    color: white;
    font-size: 2.2rem;
    margin: 0 0 15px 0;
    font-weight: 700;
}

.welcome-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin: 0 0 25px 0;
    line-height: 1.6;
}

.welcome-actions {
    display: flex;
    gap: 15px;
}

.welcome-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.welcome-btn.primary {
    background: #ffd700;
    color: #333;
}

.welcome-btn.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.welcome-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    text-decoration: none;
}

.welcome-btn.primary:hover {
    background: #ffed4e;
    color: #333;
}

.welcome-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

.welcome-illustration {
    display: flex;
    align-items: center;
    justify-content: center;
}

.illustration-icon {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.illustration-icon .dashicons {
    font-size: 4rem;
    color: #ffd700;
}

.dooplay-admin-section {
    padding: 30px;
}

.dooplay-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f1f1f1;
}

.dooplay-section-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.8rem;
    font-weight: 600;
}

.dooplay-section-header p {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 1rem;
}

.section-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Advanced Stats Grid */
.dooplay-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.dooplay-stat-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    position: relative;
}

.dooplay-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.dooplay-stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px 0;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.2), transparent);
}

.stat-icon .dashicons {
    font-size: 20px;
    color: white;
    position: relative;
    z-index: 1;
}

.stat-actions {
    display: flex;
    gap: 8px;
}

.stat-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #667eea;
}

.stat-action-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.1);
}

.stat-content {
    padding: 15px 25px;
}

.stat-value {
    font-size: 2.2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 10px;
    font-weight: 500;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    width: fit-content;
}

.stat-change.positive {
    background: rgba(70, 180, 80, 0.1);
    color: #46b450;
}

.stat-change.negative {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.stat-change.neutral {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.stat-change.warning {
    background: rgba(255, 185, 0, 0.1);
    color: #ffb900;
}

.change-icon {
    font-size: 1rem;
}

.stat-chart {
    padding: 0 25px 20px;
    height: 40px;
    display: flex;
    align-items: center;
}

.stat-chart canvas {
    width: 100%;
    height: 30px;
}

/* Charts Section */
.dooplay-charts-section {
    margin-top: 40px;
}

.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 25px;
}

.chart-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.chart-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.chart-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-select:hover {
    border-color: #667eea;
}

.chart-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.chart-content {
    padding: 25px;
    position: relative;
}

.main-chart .chart-content {
    height: 350px;
}

.side-chart .chart-content {
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Chart Loading State */
.chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #666;
}

.chart-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Plans Overview Grid - Force styles */
.plans-overview-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 40px !important;
}

.overview-card {
    background: white !important;
    border-radius: 12px !important;
    padding: 25px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
    display: flex !important;
    align-items: center !important;
    gap: 20px !important;
    transition: all 0.3s ease !important;
    border-left: 4px solid #667eea !important;
    margin: 0 !important;
}

.overview-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.overview-card.total-plans {
    border-left-color: #667eea;
}

.overview-card.active-subscriptions {
    border-left-color: #46b450;
}

.overview-card.monthly-revenue {
    border-left-color: #ffb900;
}

.overview-card.popular-plan {
    border-left-color: #dc3545;
}

.overview-card .card-icon {
    width: 50px !important;
    height: 50px !important;
    border-radius: 12px !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
}

.overview-card .card-icon .dashicons {
    font-size: 20px !important;
    color: white !important;
}

.overview-card .card-content h3 {
    margin: 0 0 5px 0 !important;
    font-size: 1.8rem !important;
    font-weight: 700 !important;
    color: #333 !important;
}

.overview-card .card-content p {
    margin: 0 !important;
    color: #666 !important;
    font-size: 0.9rem !important;
}

/* Plans Toolbar */
.plans-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
}

.toolbar-left {
    display: flex;
    gap: 15px;
    align-items: center;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box .dashicons {
    position: absolute;
    left: 12px;
    color: #666;
    font-size: 16px;
}

.plans-search {
    padding: 10px 12px 10px 35px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    min-width: 250px;
    transition: all 0.3s ease;
}

.plans-search:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.plans-filter {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    cursor: pointer;
}

.toolbar-right {
    display: flex;
    align-items: center;
}

.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
}

.view-btn {
    padding: 8px 12px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
}

.view-btn.active {
    background: white;
    color: #667eea;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.view-btn:hover {
    color: #667eea;
}

/* Enhanced Plans Grid */
.dooplay-plans-container {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.dooplay-plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 25px;
}

.dooplay-plans-grid.list-view {
    grid-template-columns: 1fr;
}

.dooplay-plan-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
}

.dooplay-plan-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.dooplay-plan-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.dooplay-plan-card.featured {
    border-color: #ffd700;
    position: relative;
}

.dooplay-plan-card.featured::after {
    content: '⭐ Popular';
    position: absolute;
    top: 15px;
    right: 15px;
    background: #ffd700;
    color: #333;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.plan-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.plan-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.plan-header h3 {
    margin: 0 0 15px 0;
    font-size: 1.6rem;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.plan-price {
    font-size: 2.5rem;
    font-weight: 800;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.plan-price .currency {
    font-size: 1.2rem;
    opacity: 0.8;
}

.plan-price .period {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

.plan-features {
    padding: 25px;
}

.plan-features h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    color: #555;
    font-size: 0.9rem;
}

.features-list li::before {
    content: '✓';
    color: #46b450;
    font-weight: bold;
    font-size: 1rem;
}

.plan-stats {
    padding: 0 25px 20px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item .stat-number {
    display: block;
    font-size: 1.4rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-item .stat-label {
    font-size: 0.8rem;
    color: #666;
}

.plan-actions {
    padding: 25px;
    border-top: 1px solid #f1f1f1;
    display: flex;
    gap: 10px;
}

.plan-actions .button {
    flex: 1;
    text-align: center;
    padding: 12px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.plan-actions .button-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.plan-actions .button-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.plan-actions .button-secondary {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.plan-actions .button-secondary:hover {
    background: #667eea;
    color: white;
}

.dooplay-stat-card.revenue {
    border-left-color: #46b450;
}

.dooplay-stat-card.users {
    border-left-color: #667eea;
}

.dooplay-stat-card.conversion {
    border-left-color: #ffb900;
}

.dooplay-stat-card.churn {
    border-left-color: #dc3545;
}

.dooplay-stat-card .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.dooplay-stat-card .stat-icon .dashicons {
    font-size: 24px;
    color: #667eea;
}

.dooplay-stat-card.revenue .stat-icon {
    background: rgba(70, 180, 80, 0.1);
}

.dooplay-stat-card.revenue .stat-icon .dashicons {
    color: #46b450;
}

.dooplay-stat-card .stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.dooplay-stat-card .stat-content p {
    margin: 5px 0;
    color: #666;
    font-size: 0.9rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
}

.stat-change.positive {
    background: rgba(70, 180, 80, 0.1);
    color: #46b450;
}

.stat-change.negative {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.stat-change.neutral {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* Quick Actions */
.dooplay-quick-actions h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.4rem;
}

.dooplay-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.dooplay-action-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.dooplay-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
    text-decoration: none;
    color: inherit;
}

.dooplay-action-card .dashicons {
    font-size: 32px;
    color: #667eea;
    margin-bottom: 15px;
}

.dooplay-action-card h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.2rem;
}

.dooplay-action-card p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Plans Grid */
.dooplay-plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.dooplay-plan-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.dooplay-plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dooplay-plan-card .plan-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.dooplay-plan-card .plan-header h3 {
    margin: 0 0 10px 0;
    font-size: 1.4rem;
}

.dooplay-plan-card .plan-price {
    font-size: 2rem;
    font-weight: 700;
}

.dooplay-plan-card .plan-features {
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.feature-tag {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.dooplay-plan-card .plan-actions {
    padding: 20px;
    border-top: 1px solid #f1f1f1;
    display: flex;
    gap: 10px;
}

/* Users Table */
.dooplay-users-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.dooplay-table {
    width: 100%;
    border-collapse: collapse;
}

.dooplay-table th {
    background: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e9ecef;
}

.dooplay-table td {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}

.dooplay-table tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.status-active {
    background: rgba(70, 180, 80, 0.1);
    color: #46b450;
}

.status-badge.status-expired {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.status-badge.status-cancelled {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* Content Grid */
.dooplay-content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.dooplay-content-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dooplay-content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dooplay-content-card .content-thumbnail {
    height: 150px;
    overflow: hidden;
}

.dooplay-content-card .content-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dooplay-content-card .content-info {
    padding: 15px;
}

.dooplay-content-card .content-info h4 {
    margin: 0 0 5px 0;
    color: #333;
}

.dooplay-content-card .content-info p {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 0.9rem;
}

.premium-level {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.premium-level.level-basic {
    background: rgba(70, 180, 80, 0.1);
    color: #46b450;
}

.premium-level.level-standard {
    background: rgba(255, 185, 0, 0.1);
    color: #ffb900;
}

.premium-level.level-pro {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.dooplay-content-card .content-actions {
    padding: 15px;
    border-top: 1px solid #f1f1f1;
}

/* Payment Gateways */
.dooplay-gateways-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.dooplay-gateway-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.dooplay-gateway-card.enabled {
    border-color: #46b450;
}

.dooplay-gateway-card.disabled {
    border-color: #dc3545;
}

.dooplay-gateway-card .gateway-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.dooplay-gateway-card .gateway-icon {
    font-size: 2rem;
}

.dooplay-gateway-card .gateway-header h3 {
    margin: 0;
    flex: 1;
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #46b450;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.status-indicator {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-indicator.active {
    background: rgba(70, 180, 80, 0.1);
    color: #46b450;
}

.status-indicator.inactive {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* Empty State */
.dooplay-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.dooplay-empty-state .dashicons {
    font-size: 4rem;
    color: #ccc;
    margin-bottom: 20px;
}

.dooplay-empty-state h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.dooplay-empty-state p {
    margin: 0 0 20px 0;
}

/* Form Elements */
.search-input,
.filter-select,
.bulk-action-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.search-input {
    min-width: 200px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dooplay-admin-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .dooplay-admin-nav .nav-tab-wrapper {
        padding: 0 15px;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .dooplay-admin-section {
        padding: 20px 15px;
    }
    
    .dooplay-section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .dooplay-stats-grid,
    .dooplay-actions-grid,
    .dooplay-plans-grid,
    .dooplay-content-grid,
    .dooplay-gateways-grid {
        grid-template-columns: 1fr;
    }
    
    .section-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .search-input {
        min-width: 100%;
    }
}

/* Settings Page Advanced Styles */
.settings-overview-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 40px !important;
}

.settings-form-container {
    background: white !important;
    border-radius: 15px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
}

.settings-tabs {
    display: flex !important;
    flex-direction: column !important;
}

.tab-nav {
    display: flex !important;
    background: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 0 !important;
    margin: 0 !important;
}

.tab-btn {
    flex: 1 !important;
    padding: 20px 25px !important;
    border: none !important;
    background: transparent !important;
    color: #666 !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 10px !important;
    border-bottom: 3px solid transparent !important;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    color: #667eea !important;
}

.tab-btn.active {
    background: white !important;
    color: #667eea !important;
    border-bottom-color: #667eea !important;
}

.tab-btn .dashicons {
    font-size: 18px !important;
}

.tab-content {
    display: none !important;
    padding: 40px !important;
}

.tab-content.active {
    display: block !important;
}

.settings-section h3 {
    margin: 0 0 30px 0 !important;
    font-size: 1.8rem !important;
    font-weight: 700 !important;
    color: #333 !important;
    border-bottom: 2px solid #f1f1f1 !important;
    padding-bottom: 15px !important;
}

.settings-grid {
    display: grid !important;
    gap: 25px !important;
}

.setting-item {
    background: #f8f9fa !important;
    border-radius: 12px !important;
    padding: 25px !important;
    border-left: 4px solid #667eea !important;
    transition: all 0.3s ease !important;
}

.setting-item:hover {
    background: #f1f3f4 !important;
    transform: translateX(5px) !important;
}

.setting-label {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
    cursor: pointer !important;
}

.setting-title {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: #333 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.setting-description {
    color: #666 !important;
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
}

.setting-input {
    padding: 12px 15px !important;
    border: 2px solid #dee2e6 !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
    max-width: 300px !important;
}

.setting-input:focus {
    outline: none !important;
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* Features Grid */
.features-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 25px !important;
}

.feature-card {
    background: white !important;
    border-radius: 15px !important;
    padding: 25px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
    border: 2px solid transparent !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    gap: 20px !important;
}

.feature-card:hover {
    border-color: #667eea !important;
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.feature-icon {
    width: 60px !important;
    height: 60px !important;
    border-radius: 15px !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
}

.feature-icon .dashicons {
    font-size: 24px !important;
    color: white !important;
}

.feature-content {
    flex: 1 !important;
}

.feature-label {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
    cursor: pointer !important;
}

.feature-title {
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    color: #333 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.feature-description {
    color: #666 !important;
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
}

/* Settings Footer */
.settings-footer {
    padding: 30px 40px !important;
    background: #f8f9fa !important;
    border-top: 1px solid #dee2e6 !important;
    display: flex !important;
    gap: 15px !important;
    justify-content: flex-end !important;
}

.button-large {
    padding: 15px 30px !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.button-large:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}
