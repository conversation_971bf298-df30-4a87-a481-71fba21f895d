# Live TV Player Troubleshooting Guide

## Common Issues and Solutions

### 🚫 Stream Not Playing

#### Possible Causes:
1. **Invalid Stream URL**
2. **Stream is Offline**
3. **CORS (Cross-Origin) Issues**
4. **Browser Compatibility**
5. **Network/Firewall Restrictions**

#### Solutions:

##### 1. Test Stream URL
- Go to **Admin Panel → Live TV → Stream Tester**
- Enter your stream URL and click "Test Stream"
- Check the results for detailed information

##### 2. Verify Stream Format
**Supported Formats:**
- HLS (.m3u8) - **Recommended**
- MP4 (direct video files)
- DASH (.mpd)
- WebM

**Example Valid URLs:**
```
https://example.com/stream/playlist.m3u8
https://example.com/video.mp4
https://example.com/manifest.mpd
```

##### 3. Check Stream URL Structure
**Good HLS URL:**
```
https://domain.com/live/channel1/playlist.m3u8
```

**Bad URLs:**
```
http://domain.com/stream (no file extension)
rtmp://domain.com/live/stream (RTMP not supported in browsers)
```

### 🔧 Browser Compatibility Issues

#### Chrome/Edge:
- Usually works best with HLS streams
- Requires HTTPS for many features

#### Firefox:
- May need HLS.js library (automatically loaded)
- Check console for errors

#### Safari:
- Native HLS support
- May have stricter CORS policies

#### Android TV Browser:
- Limited codec support
- Prefer H.264 video with AAC audio

### 🌐 CORS (Cross-Origin) Issues

#### Symptoms:
- Stream URL tests successful but video doesn't play
- Console errors mentioning "CORS" or "Cross-Origin"

#### Solutions:
1. **Server-side:** Add CORS headers to your streaming server
2. **Use HTTPS:** Ensure both your site and stream use HTTPS
3. **Proxy streams:** Route streams through your server

#### CORS Headers (for stream server):
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, HEAD, OPTIONS
Access-Control-Allow-Headers: Range
```

### 📱 Mobile/TV Specific Issues

#### Android TV:
- Use H.264 video codec
- AAC audio codec recommended
- Avoid high bitrates (>5Mbps)

#### iOS/Safari:
- Must use HTTPS for autoplay
- HLS is natively supported
- MP4 works well

### 🔍 Debugging Steps

#### 1. Check Browser Console
1. Press F12 to open Developer Tools
2. Go to Console tab
3. Look for error messages
4. Common errors:
   - "Failed to load resource"
   - "CORS error"
   - "Format error"

#### 2. Network Tab Analysis
1. Open Developer Tools → Network tab
2. Try to play the stream
3. Look for failed requests (red entries)
4. Check response codes:
   - 200: OK
   - 404: Not Found
   - 403: Forbidden
   - 500: Server Error

#### 3. Test Direct URL
1. Copy stream URL
2. Open in new browser tab
3. Should download or play directly

### 🛠️ Stream URL Formats

#### HLS (HTTP Live Streaming) - .m3u8
**Best for:** Live streams, adaptive quality
```
https://example.com/live/stream/playlist.m3u8
```

#### MP4 (Direct Video)
**Best for:** Video files, recorded content
```
https://example.com/videos/movie.mp4
```

#### DASH (Dynamic Adaptive Streaming)
**Best for:** High-quality adaptive streaming
```
https://example.com/stream/manifest.mpd
```

### 🔧 Common Fixes

#### Fix 1: Update Stream URL Format
**Before:**
```
http://example.com/stream
```
**After:**
```
https://example.com/stream/playlist.m3u8
```

#### Fix 2: Add Backup URLs
In channel settings, add multiple stream URLs:
- Primary: Main stream URL
- Backup: Alternative stream URL

#### Fix 3: Check Stream Server
Ensure your streaming server:
- Is online and accessible
- Supports CORS
- Uses HTTPS
- Has proper MIME types configured

### 📊 Stream Testing Checklist

- [ ] URL is accessible (returns 200 status)
- [ ] Correct MIME type (application/vnd.apple.mpegurl for HLS)
- [ ] HTTPS protocol (recommended)
- [ ] CORS headers present
- [ ] Stream is currently live/available
- [ ] Compatible video/audio codecs

### 🎯 Optimization Tips

#### For Better Performance:
1. **Use CDN:** Content Delivery Network for streams
2. **Optimize Bitrate:** 2-5 Mbps for most users
3. **Multiple Qualities:** Provide different quality options
4. **Proper Encoding:** H.264 video + AAC audio

#### For Android TV:
1. **Lower Bitrates:** 1-3 Mbps recommended
2. **Simple Codecs:** Avoid complex encoding
3. **Stable URLs:** Avoid frequently changing URLs

### 🚨 Emergency Fixes

#### If All Streams Stop Working:
1. Check if your streaming server is online
2. Verify DNS settings
3. Test with a known working stream URL
4. Clear browser cache and cookies
5. Try different browser

#### Quick Test URLs:
```
# Test HLS stream (if available)
https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8

# Test MP4 (sample video)
https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4
```

### 📞 Getting Help

#### Information to Provide:
1. **Stream URL** (you can mask domain)
2. **Browser and version**
3. **Device type** (PC, Android TV, etc.)
4. **Error messages** from console
5. **Stream test results** from admin panel

#### Where to Check:
1. **Browser Console:** F12 → Console
2. **Network Tab:** F12 → Network
3. **Stream Tester:** Admin → Live TV → Stream Tester

### 🔄 Regular Maintenance

#### Weekly:
- Test random channels to ensure they're working
- Check for broken stream URLs
- Monitor server performance

#### Monthly:
- Update stream URLs if needed
- Clean up offline channels
- Review analytics for popular channels

#### As Needed:
- Update player code for new features
- Add new stream sources
- Optimize for new devices

---

## Quick Reference

### Supported Stream Types:
✅ HLS (.m3u8) - **Best Choice**
✅ MP4 (direct video)
✅ WebM
✅ DASH (.mpd)
❌ RTMP (not supported in browsers)
❌ Flash-based streams

### Required Headers for Streams:
```
Content-Type: application/vnd.apple.mpegurl (for HLS)
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, HEAD, OPTIONS
```

### Browser Support:
- **Chrome/Edge:** Excellent
- **Firefox:** Good (with HLS.js)
- **Safari:** Excellent (native HLS)
- **Android TV:** Good (optimized)

Remember: When in doubt, use the **Stream Tester** in the admin panel to diagnose issues!
