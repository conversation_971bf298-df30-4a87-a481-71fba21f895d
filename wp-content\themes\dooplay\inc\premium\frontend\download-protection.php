<?php
/**
 * Download Protection for Premium Content
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Download_Protection {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Filter download links
        add_filter('dooplay_download_links', array($this, 'filter_download_links'), 10, 2);
        add_filter('dt_links_download', array($this, 'filter_dt_links'), 10, 2);
        
        // Add premium download protection
        add_action('template_redirect', array($this, 'protect_download_access'));
        
        // Add download tracking
        add_action('wp_ajax_track_premium_download', array($this, 'track_download'));
        add_action('wp_ajax_nopriv_track_premium_download', array($this, 'track_download'));
    }
    
    /**
     * Filter download links based on premium status
     */
    public function filter_download_links($links, $post_id) {
        if (!function_exists('deshiflix_premium') || !deshiflix_premium()) {
            return $links;
        }
        
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        
        // Check if content is premium
        if (!$content_manager->is_premium_content($post_id)) {
            return $links;
        }
        
        $user_id = get_current_user_id();
        
        // Check if user has access
        if (!$content_manager->user_has_content_access($post_id, $user_id)) {
            return $this->get_premium_download_message($post_id);
        }
        
        // Check if user's plan includes downloads
        $features = DeshiFlix_Premium_Features::get_instance();
        if (!$features->user_has_feature_access('download_links', $user_id)) {
            return $this->get_upgrade_download_message($post_id);
        }
        
        // Add premium protection to links
        return $this->protect_download_links($links, $post_id);
    }
    
    /**
     * Filter DT Links download
     */
    public function filter_dt_links($links, $post_id) {
        return $this->filter_download_links($links, $post_id);
    }
    
    /**
     * Get premium download message
     */
    private function get_premium_download_message($post_id) {
        ob_start();
        ?>
        <div class="premium-download-lock">
            <div class="lock-icon">🔒</div>
            <h3><?php _e('Premium Content', 'deshiflix'); ?></h3>
            <p><?php _e('This content requires a premium subscription to download.', 'deshiflix'); ?></p>
            <a href="<?php echo home_url('/premium-plans/'); ?>" class="btn btn-premium">
                <?php _e('Upgrade to Premium', 'deshiflix'); ?>
            </a>
        </div>
        
        <style>
        .premium-download-lock {
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            margin: 20px 0;
        }
        
        .premium-download-lock .lock-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .premium-download-lock h3 {
            margin: 0 0 15px 0;
            font-size: 24px;
        }
        
        .premium-download-lock p {
            margin: 0 0 25px 0;
            opacity: 0.9;
        }
        
        .btn-premium {
            display: inline-block;
            padding: 12px 30px;
            background: #FFD700;
            color: #000;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-premium:hover {
            background: #FFA500;
            transform: translateY(-2px);
        }
        </style>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get upgrade download message
     */
    private function get_upgrade_download_message($post_id) {
        ob_start();
        ?>
        <div class="premium-download-upgrade">
            <div class="upgrade-icon">⬆️</div>
            <h3><?php _e('Upgrade Required', 'deshiflix'); ?></h3>
            <p><?php _e('Your current plan does not include download access. Upgrade to unlock downloads.', 'deshiflix'); ?></p>
            <a href="<?php echo home_url('/premium-plans/'); ?>" class="btn btn-upgrade">
                <?php _e('Upgrade Plan', 'deshiflix'); ?>
            </a>
        </div>
        
        <style>
        .premium-download-upgrade {
            text-align: center;
            padding: 30px 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 12px;
            margin: 20px 0;
        }
        
        .premium-download-upgrade .upgrade-icon {
            font-size: 36px;
            margin-bottom: 15px;
        }
        
        .premium-download-upgrade h3 {
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        
        .premium-download-upgrade p {
            margin: 0 0 20px 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .btn-upgrade {
            display: inline-block;
            padding: 10px 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 20px;
            font-weight: bold;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        
        .btn-upgrade:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
        </style>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Protect download links
     */
    private function protect_download_links($links, $post_id) {
        if (!is_array($links)) {
            return $links;
        }
        
        $protected_links = array();
        
        foreach ($links as $link) {
            // Add premium protection parameters
            if (is_array($link)) {
                $link['premium_protected'] = true;
                $link['post_id'] = $post_id;
                $link['user_id'] = get_current_user_id();
                $link['access_token'] = $this->generate_access_token($post_id, get_current_user_id());
            }
            
            $protected_links[] = $link;
        }
        
        return $protected_links;
    }
    
    /**
     * Generate access token for download
     */
    private function generate_access_token($post_id, $user_id) {
        $data = array(
            'post_id' => $post_id,
            'user_id' => $user_id,
            'timestamp' => time(),
            'expires' => time() + (24 * 60 * 60) // 24 hours
        );
        
        return base64_encode(json_encode($data));
    }
    
    /**
     * Protect download access
     */
    public function protect_download_access() {
        if (!isset($_GET['premium_download']) || !isset($_GET['token'])) {
            return;
        }
        
        $token = sanitize_text_field($_GET['token']);
        $data = json_decode(base64_decode($token), true);
        
        if (!$data || !isset($data['expires']) || $data['expires'] < time()) {
            wp_die(__('Download link has expired.', 'deshiflix'));
        }
        
        $post_id = intval($data['post_id']);
        $user_id = intval($data['user_id']);
        
        // Verify user still has access
        if (!function_exists('deshiflix_premium') || !deshiflix_premium()) {
            wp_die(__('Premium system not available.', 'deshiflix'));
        }
        
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        if (!$content_manager->user_has_content_access($post_id, $user_id)) {
            wp_die(__('Access denied. Premium subscription required.', 'deshiflix'));
        }
        
        // Track download
        $this->track_premium_download($post_id, $user_id);
        
        // Redirect to actual download
        $download_url = sanitize_url($_GET['url']);
        wp_redirect($download_url);
        exit;
    }
    
    /**
     * Track premium download
     */
    public function track_download() {
        if (!wp_verify_nonce($_POST['nonce'], 'premium_download_nonce')) {
            wp_die('Security check failed');
        }
        
        $post_id = intval($_POST['post_id']);
        $user_id = get_current_user_id();
        
        $this->track_premium_download($post_id, $user_id);
        
        wp_send_json_success();
    }
    
    /**
     * Track premium download in database
     */
    private function track_premium_download($post_id, $user_id) {
        global $wpdb;
        
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        $wpdb->insert(
            $table_analytics,
            array(
                'user_id' => $user_id,
                'event_type' => 'premium_download',
                'event_data' => json_encode(array(
                    'post_id' => $post_id,
                    'post_title' => get_the_title($post_id),
                    'download_time' => current_time('mysql')
                )),
                'post_id' => $post_id,
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT']
            )
        );
    }
}

// Initialize download protection
DeshiFlix_Download_Protection::get_instance();
