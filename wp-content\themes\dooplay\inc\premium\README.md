# DeshiFlix Premium System

A comprehensive premium subscription system designed specifically for Bangladeshi OTT platforms, built for the DooPlay WordPress theme.

## 🚀 Features

### 💳 Payment Integration
- **bKash** - Mobile banking integration
- **Nagad** - Digital payment system
- **Rocket** - Mobile financial service
- **SSLCommerz** - Card payment gateway
- **aamarPay** - Online payment solution

### 🎬 Content Management
- Premium content locking system
- HD/4K quality restrictions
- Download link protection
- Early access for new content
- Multiple server access control

### 👥 User Management
- Subscription plan management
- Device tracking and limits
- User dashboard with billing history
- Auto-renewal system
- Bulk user actions

### 📊 Analytics & Reporting
- Revenue tracking and analytics
- User behavior analysis
- Content performance metrics
- Conversion rate monitoring
- Automated daily/monthly reports

### 🔒 Security Features
- Content protection (right-click disable, watermarks)
- Anti-piracy measures
- Secure download tokens
- IP and location restrictions
- Device fingerprinting

### ⚙️ Admin Features
- Comprehensive admin dashboard
- Payment gateway management
- Feature toggle system
- Bulk content actions
- Real-time statistics

## 📁 File Structure

```
wp-content/themes/dooplay/inc/premium/
├── core/
│   ├── class-premium-core.php           # Main system core
│   ├── class-premium-user.php           # User management
│   ├── class-premium-payment.php        # Payment processing
│   ├── class-premium-content.php        # Content protection
│   ├── class-premium-analytics.php      # Analytics system
│   ├── class-premium-features.php       # Feature management
│   ├── class-premium-security.php       # Security features
│   └── class-premium-user-management.php # User administration
├── admin/
│   ├── premium-admin-panel.php          # Admin dashboard
│   ├── premium-settings.php             # Settings page
│   ├── premium-analytics.php            # Analytics admin
│   └── premium-bulk-actions.php         # Bulk operations
├── templates/
│   ├── premium-content-overlay.php      # Content lock overlay
│   ├── premium-dashboard.php            # User dashboard
│   ├── premium-plans.php                # Subscription plans
│   └── payment-form.php                 # Payment form
├── assets/
│   ├── css/
│   │   ├── premium-frontend.css         # Frontend styles
│   │   └── premium-admin.css            # Admin styles
│   └── js/
│       ├── premium-frontend.js          # Frontend scripts
│       ├── premium-admin.js             # Admin scripts
│       └── premium-features.js          # Feature scripts
└── README.md                            # This file
```

## 🛠️ Installation

1. **Upload Files**: Copy the entire `premium` folder to your DooPlay theme's `inc` directory.

2. **Database Setup**: The system will automatically create required database tables on activation.

3. **Include in Theme**: Add this line to your theme's `functions.php`:
   ```php
   require_once get_template_directory() . '/inc/premium/core/class-premium-core.php';
   ```

4. **Configure Payment Gateways**: Go to WordPress Admin → Premium System → Settings → Payment Gateways and configure your payment methods.

## ⚙️ Configuration

### Payment Gateway Setup

#### bKash Configuration
1. Get API credentials from bKash merchant portal
2. Add credentials in Premium Settings → Payment Gateways
3. Enable test mode for development

#### Nagad Configuration
1. Register as Nagad merchant
2. Get API keys and configure in settings
3. Test with sandbox environment

#### SSLCommerz Configuration
1. Create SSLCommerz merchant account
2. Get store ID and password
3. Configure in payment settings

### Premium Plans Setup
1. Go to Premium System → Plans
2. Create subscription plans with features
3. Set pricing and duration
4. Configure plan features (HD access, downloads, etc.)

### Content Protection
1. Go to Premium System → Features
2. Enable desired protection features
3. Configure security settings
4. Set content access rules

## 🎯 Usage

### Making Content Premium
1. Edit any movie, TV show, or episode
2. In the Premium Content meta box:
   - Check "Make this premium content"
   - Select premium level (Basic/Standard/Pro)
   - Choose features (HD, Downloads, etc.)
   - Set early access date (optional)

### Bulk Operations
1. Go to Movies/TV Shows/Episodes list
2. Select multiple items
3. Choose bulk action:
   - Make Premium (Basic/Standard/Pro)
   - Make Free
   - Set/Remove Early Access

### User Management
1. Go to Premium System → User Management
2. View all premium users
3. Manage subscriptions
4. Track user devices
5. View payment history

### Analytics
1. Go to Premium System → Analytics
2. View revenue and subscription metrics
3. Analyze user behavior
4. Export data for external analysis
5. Set up automated reports

## 🔧 Customization

### Adding New Payment Gateways
1. Extend the `DeshiFlix_Premium_Payment` class
2. Add gateway configuration in settings
3. Implement payment processing methods
4. Add webhook handling

### Custom Features
1. Extend `DeshiFlix_Premium_Features` class
2. Define new feature in `define_features()` method
3. Add feature logic in content filters
4. Update admin interface

### Styling
- Modify CSS files in `assets/css/` directory
- Override templates by copying to your theme
- Use WordPress hooks for custom functionality

## 🛡️ Security

### Content Protection
- Right-click disabled on premium content
- Developer tools detection
- Video watermarking
- Download link encryption

### Payment Security
- SSL/TLS encryption for all transactions
- Secure token generation
- Webhook signature verification
- PCI DSS compliance ready

### User Security
- Failed login attempt tracking
- Device fingerprinting
- IP-based restrictions
- Session management

## 📱 Mobile Optimization

- Responsive design for all screen sizes
- Touch-friendly interfaces
- Mobile payment optimization
- Progressive web app ready

## 🌐 Localization

The system is fully translatable and includes:
- Bengali (Bangladesh) translations
- RTL support ready
- Currency formatting for BDT
- Date/time localization

## 🔄 Updates

### Version 1.0.0
- Initial release
- Full payment gateway integration
- Complete admin system
- Security features
- Analytics dashboard

## 📞 Support

For support and customization:
- Check WordPress admin for system status
- Review error logs in WordPress debug
- Test payment gateways in sandbox mode
- Monitor analytics for system health

## 📄 License

This premium system is designed for DeshiFlix and follows WordPress coding standards and GPL licensing.

## 🤝 Contributing

When contributing:
1. Follow WordPress coding standards
2. Test with all payment gateways
3. Ensure mobile compatibility
4. Update documentation
5. Test security features

---

**Note**: This system requires WordPress 5.0+ and PHP 7.4+ for optimal performance. Always test in a staging environment before deploying to production.
