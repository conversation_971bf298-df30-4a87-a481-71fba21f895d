<?php
/**
 * Premium Content Lock Overlay Template
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

global $post;

$premium_level = get_post_meta($post->ID, '_premium_level', true);
$premium_features = get_post_meta($post->ID, '_premium_features', true);
$unlock_date = get_post_meta($post->ID, '_premium_unlock_date', true);

// Get available plans
$premium_user = DeshiFlix_Premium_User::get_instance();
$plans = $premium_user->get_premium_plans();

$is_logged_in = is_user_logged_in();
$current_user_id = get_current_user_id();
?>

<div class="premium-content-lock-overlay">
    <div class="premium-lock-container">
        <div class="premium-lock-background">
            <?php if (has_post_thumbnail($post->ID)): ?>
                <div class="premium-lock-backdrop" style="background-image: url('<?php echo get_the_post_thumbnail_url($post->ID, 'full'); ?>');">
                    <div class="premium-lock-backdrop-overlay"></div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="premium-lock-content">
            <div class="premium-lock-icon">
                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 8H20C20.5523 8 21 8.44772 21 9V21C21 21.5523 20.5523 22 20 22H4C3.44772 22 3 21.5523 3 21V9C3 8.44772 3.44772 8 4 8H6V7C6 3.68629 8.68629 1 12 1C15.3137 1 18 3.68629 18 7V8ZM16 8V7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7V8H16ZM12 14C13.1046 14 14 13.1046 14 12C14 10.8954 13.1046 10 12 10C10.8954 10 10 10.8954 10 12C10 13.1046 10.8954 14 12 14Z" fill="currentColor"/>
                </svg>
            </div>
            
            <div class="premium-lock-header">
                <h2 class="premium-lock-title">
                    <?php _e('🔒 Premium Content', 'deshiflix'); ?>
                </h2>
                <p class="premium-lock-subtitle">
                    <?php 
                    if ($unlock_date && strtotime($unlock_date) > time()) {
                        printf(__('This content will be available for free on %s', 'deshiflix'), 
                               date('F j, Y', strtotime($unlock_date)));
                    } else {
                        printf(__('This %s content requires a premium subscription', 'deshiflix'), 
                               ucfirst($premium_level ?: 'premium'));
                    }
                    ?>
                </p>
            </div>
            
            <div class="premium-lock-features">
                <h3><?php _e('Premium Benefits', 'deshiflix'); ?></h3>
                <div class="premium-features-grid">
                    <div class="premium-feature-item">
                        <span class="feature-icon">🎬</span>
                        <span class="feature-text"><?php _e('HD/4K Quality', 'deshiflix'); ?></span>
                    </div>
                    <div class="premium-feature-item">
                        <span class="feature-icon">🚫</span>
                        <span class="feature-text"><?php _e('Ad-Free Experience', 'deshiflix'); ?></span>
                    </div>
                    <div class="premium-feature-item">
                        <span class="feature-icon">⬇️</span>
                        <span class="feature-text"><?php _e('Download Links', 'deshiflix'); ?></span>
                    </div>
                    <div class="premium-feature-item">
                        <span class="feature-icon">🌐</span>
                        <span class="feature-text"><?php _e('Multiple Servers', 'deshiflix'); ?></span>
                    </div>
                    <div class="premium-feature-item">
                        <span class="feature-icon">⚡</span>
                        <span class="feature-text"><?php _e('Early Access', 'deshiflix'); ?></span>
                    </div>
                    <div class="premium-feature-item">
                        <span class="feature-icon">📱</span>
                        <span class="feature-text"><?php _e('Mobile App Access', 'deshiflix'); ?></span>
                    </div>
                </div>
            </div>
            
            <?php if (!$is_logged_in): ?>
                <div class="premium-lock-auth">
                    <p class="auth-message"><?php _e('Please login to access premium content', 'deshiflix'); ?></p>
                    <div class="auth-buttons">
                        <a href="<?php echo wp_login_url(get_permalink()); ?>" class="btn btn-login">
                            <?php _e('Login', 'deshiflix'); ?>
                        </a>
                        <a href="<?php echo wp_registration_url(); ?>" class="btn btn-register">
                            <?php _e('Register', 'deshiflix'); ?>
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="premium-lock-plans">
                    <h3><?php _e('Choose Your Plan', 'deshiflix'); ?></h3>
                    <div class="premium-plans-grid">
                        <?php foreach ($plans as $plan): 
                            $features = json_decode($plan->features, true);
                            $is_popular = $plan->name === 'স্ট্যান্ডার্ড প্ল্যান';
                        ?>
                            <div class="premium-plan-card <?php echo $is_popular ? 'popular' : ''; ?>">
                                <?php if ($is_popular): ?>
                                    <div class="plan-badge"><?php _e('Most Popular', 'deshiflix'); ?></div>
                                <?php endif; ?>
                                
                                <div class="plan-header">
                                    <h4 class="plan-name"><?php echo esc_html($plan->name); ?></h4>
                                    <div class="plan-price">
                                        <span class="price-amount">৳<?php echo number_format($plan->price, 0); ?></span>
                                        <?php if ($plan->original_price && $plan->original_price > $plan->price): ?>
                                            <span class="price-original">৳<?php echo number_format($plan->original_price, 0); ?></span>
                                        <?php endif; ?>
                                        <span class="price-period">
                                            /<?php echo $plan->duration_days > 30 ? __('year', 'deshiflix') : __('month', 'deshiflix'); ?>
                                        </span>
                                    </div>
                                    <?php if ($plan->original_price && $plan->original_price > $plan->price): ?>
                                        <div class="plan-discount">
                                            <?php 
                                            $discount = round((($plan->original_price - $plan->price) / $plan->original_price) * 100);
                                            printf(__('%d%% OFF', 'deshiflix'), $discount);
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="plan-features">
                                    <ul>
                                        <?php if (isset($features['hd_quality']) && $features['hd_quality']): ?>
                                            <li>✅ <?php _e('HD Quality', 'deshiflix'); ?></li>
                                        <?php endif; ?>
                                        <?php if (isset($features['ad_free']) && $features['ad_free']): ?>
                                            <li>✅ <?php _e('Ad-Free', 'deshiflix'); ?></li>
                                        <?php endif; ?>
                                        <?php if (isset($features['download_links']) && $features['download_links']): ?>
                                            <li>✅ <?php _e('Download Links', 'deshiflix'); ?></li>
                                        <?php endif; ?>
                                        <?php if (isset($features['early_access']) && $features['early_access']): ?>
                                            <li>✅ <?php _e('Early Access', 'deshiflix'); ?></li>
                                        <?php endif; ?>
                                        <?php if (isset($features['multiple_servers']) && $features['multiple_servers']): ?>
                                            <li>✅ <?php _e('Multiple Servers', 'deshiflix'); ?></li>
                                        <?php endif; ?>
                                        <li>📱 <?php printf(__('Up to %d devices', 'deshiflix'), $plan->max_devices); ?></li>
                                        <?php if ($plan->download_limit > 0): ?>
                                            <li>⬇️ <?php printf(__('%d downloads/month', 'deshiflix'), $plan->download_limit); ?></li>
                                        <?php elseif ($plan->download_limit == 0 && isset($features['download_links']) && $features['download_links']): ?>
                                            <li>⬇️ <?php _e('Unlimited downloads', 'deshiflix'); ?></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                                
                                <div class="plan-action">
                                    <button class="btn btn-premium-upgrade" 
                                            data-plan-id="<?php echo esc_attr($plan->id); ?>"
                                            data-plan-name="<?php echo esc_attr($plan->name); ?>"
                                            data-plan-price="<?php echo esc_attr($plan->price); ?>">
                                        <?php _e('Choose Plan', 'deshiflix'); ?>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="premium-lock-payment-methods">
                    <h4><?php _e('Supported Payment Methods', 'deshiflix'); ?></h4>
                    <div class="payment-methods-list">
                        <div class="payment-method">
                            <img src="<?php echo DESHIFLIX_PREMIUM_ASSETS_URL; ?>images/bkash-logo.png" alt="bKash" />
                        </div>
                        <div class="payment-method">
                            <img src="<?php echo DESHIFLIX_PREMIUM_ASSETS_URL; ?>images/nagad-logo.png" alt="Nagad" />
                        </div>
                        <div class="payment-method">
                            <img src="<?php echo DESHIFLIX_PREMIUM_ASSETS_URL; ?>images/rocket-logo.png" alt="Rocket" />
                        </div>
                        <div class="payment-method">
                            <img src="<?php echo DESHIFLIX_PREMIUM_ASSETS_URL; ?>images/sslcommerz-logo.png" alt="SSLCommerz" />
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="premium-lock-footer">
                <p class="premium-guarantee">
                    <span class="guarantee-icon">🛡️</span>
                    <?php _e('30-day money-back guarantee', 'deshiflix'); ?>
                </p>
                <p class="premium-support">
                    <?php _e('Need help?', 'deshiflix'); ?> 
                    <a href="mailto:<EMAIL>"><?php _e('Contact Support', 'deshiflix'); ?></a>
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.premium-content-lock-overlay {
    position: relative;
    min-height: 600px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    overflow: hidden;
    margin: 20px 0;
}

.premium-lock-container {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

.premium-lock-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.premium-lock-backdrop {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    filter: blur(8px);
}

.premium-lock-backdrop-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
}

.premium-lock-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    width: 100%;
    text-align: center;
    color: white;
}

.premium-lock-icon {
    margin-bottom: 20px;
    color: #ffd700;
}

.premium-lock-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.premium-lock-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.premium-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.premium-feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.feature-icon {
    font-size: 1.5rem;
}

.premium-plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.premium-plan-card {
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    border-radius: 12px;
    padding: 30px 20px;
    position: relative;
    transition: transform 0.3s ease;
}

.premium-plan-card:hover {
    transform: translateY(-5px);
}

.premium-plan-card.popular {
    border: 3px solid #ffd700;
    transform: scale(1.05);
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #ffd700;
    color: #333;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.plan-name {
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.plan-price {
    margin-bottom: 15px;
}

.price-amount {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.price-original {
    text-decoration: line-through;
    color: #999;
    margin-left: 10px;
}

.price-period {
    color: #666;
    font-size: 0.9rem;
}

.plan-discount {
    background: #e74c3c;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-block;
    margin-top: 5px;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.plan-features li {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-premium-upgrade {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 100%;
}

.btn-premium-upgrade:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-login, .btn-register {
    background: #3498db;
    color: white;
    margin: 0 10px;
}

.payment-methods-list {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
}

.payment-method img {
    height: 40px;
    width: auto;
    border-radius: 4px;
}

.premium-lock-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.premium-guarantee {
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.guarantee-icon {
    margin-right: 8px;
}

@media (max-width: 768px) {
    .premium-lock-title {
        font-size: 2rem;
    }
    
    .premium-plans-grid {
        grid-template-columns: 1fr;
    }
    
    .premium-plan-card.popular {
        transform: none;
    }
    
    .premium-features-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    $('.btn-premium-upgrade').click(function() {
        var planId = $(this).data('plan-id');
        var planName = $(this).data('plan-name');
        var planPrice = $(this).data('plan-price');
        
        // Show payment modal or redirect to payment page
        showPaymentModal(planId, planName, planPrice);
    });
    
    function showPaymentModal(planId, planName, planPrice) {
        // This will be implemented in the payment system
        alert('Payment system will be implemented here for plan: ' + planName);
    }
});
</script>
