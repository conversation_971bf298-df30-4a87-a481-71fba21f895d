<?php
/**
 * DeshiFlix Premium System - User Management Class
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Premium_User_Management {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Premium user roles
     */
    private $premium_roles = array();
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize user management
     */
    private function init() {
        $this->define_premium_roles();
        $this->init_hooks();
    }
    
    /**
     * Define premium user roles
     */
    private function define_premium_roles() {
        $this->premium_roles = array(
            'premium_user' => array(
                'display_name' => __('Premium User', 'deshiflix'),
                'capabilities' => array(
                    'read' => true,
                    'premium_content_access' => true,
                    'hd_quality_access' => true,
                    'download_access' => true,
                    'ad_free_experience' => true,
                    'multiple_servers_access' => true
                )
            ),
            'premium_basic' => array(
                'display_name' => __('Premium Basic', 'deshiflix'),
                'capabilities' => array(
                    'read' => true,
                    'premium_content_access' => true,
                    'hd_quality_access' => true,
                    'ad_free_experience' => true
                )
            ),
            'premium_standard' => array(
                'display_name' => __('Premium Standard', 'deshiflix'),
                'capabilities' => array(
                    'read' => true,
                    'premium_content_access' => true,
                    'hd_quality_access' => true,
                    'download_access' => true,
                    'ad_free_experience' => true,
                    'multiple_servers_access' => true
                )
            ),
            'premium_pro' => array(
                'display_name' => __('Premium Pro', 'deshiflix'),
                'capabilities' => array(
                    'read' => true,
                    'premium_content_access' => true,
                    'hd_quality_access' => true,
                    'download_access' => true,
                    'ad_free_experience' => true,
                    'multiple_servers_access' => true,
                    'early_access' => true,
                    'exclusive_content_access' => true,
                    'priority_support' => true
                )
            )
        );
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // User registration and login hooks
        add_action('user_register', array($this, 'setup_new_user'));
        add_action('wp_login', array($this, 'update_user_login_data'), 10, 2);
        add_action('wp_logout', array($this, 'update_user_logout_data'));
        
        // User profile hooks
        add_action('show_user_profile', array($this, 'add_premium_profile_section'));
        add_action('edit_user_profile', array($this, 'add_premium_profile_section'));
        add_action('personal_options_update', array($this, 'save_premium_profile_data'));
        add_action('edit_user_profile_update', array($this, 'save_premium_profile_data'));
        
        // Admin hooks
        add_action('admin_menu', array($this, 'add_user_management_menu'));
        add_filter('manage_users_columns', array($this, 'add_premium_user_columns'));
        add_filter('manage_users_custom_column', array($this, 'display_premium_user_columns'), 10, 3);
        add_filter('users_list_table_query_args', array($this, 'filter_users_by_premium_status'));
        
        // AJAX hooks
        add_action('wp_ajax_premium_user_action', array($this, 'ajax_premium_user_action'));
        add_action('wp_ajax_bulk_premium_user_action', array($this, 'ajax_bulk_premium_user_action'));
        add_action('wp_ajax_get_user_premium_details', array($this, 'ajax_get_user_premium_details'));
        
        // Subscription management hooks
        add_action('deshiflix_premium_subscription_activated', array($this, 'handle_subscription_activation'), 10, 2);
        add_action('deshiflix_premium_subscription_expired', array($this, 'handle_subscription_expiration'), 10, 1);
        add_action('deshiflix_premium_subscription_cancelled', array($this, 'handle_subscription_cancellation'), 10, 1);
        
        // Device management hooks
        add_action('wp_login', array($this, 'track_user_device'), 10, 2);
        add_action('wp_ajax_remove_user_device', array($this, 'ajax_remove_user_device'));
        add_action('wp_ajax_get_user_devices', array($this, 'ajax_get_user_devices'));
        
        // Cron hooks
        add_action('deshiflix_check_expired_subscriptions', array($this, 'check_expired_subscriptions'));
        add_action('deshiflix_send_expiry_reminders', array($this, 'send_expiry_reminders'));
    }
    
    /**
     * Create premium user roles
     */
    public function create_premium_roles() {
        foreach ($this->premium_roles as $role_key => $role_data) {
            if (!get_role($role_key)) {
                add_role($role_key, $role_data['display_name'], $role_data['capabilities']);
            }
        }
    }
    
    /**
     * Remove premium user roles
     */
    public function remove_premium_roles() {
        foreach ($this->premium_roles as $role_key => $role_data) {
            remove_role($role_key);
        }
    }
    
    /**
     * Setup new user
     */
    public function setup_new_user($user_id) {
        // Initialize user premium data
        $premium_data = array(
            'status' => 'inactive',
            'plan_id' => null,
            'start_date' => null,
            'end_date' => null,
            'payment_method' => null,
            'auto_renew' => false,
            'devices' => array(),
            'download_count' => 0,
            'last_activity' => current_time('mysql'),
            'created_at' => current_time('mysql')
        );
        
        update_user_meta($user_id, '_deshiflix_premium_data', $premium_data);
        
        // Track user registration
        $this->track_user_activity($user_id, 'user_registered');
    }
    
    /**
     * Update user login data
     */
    public function update_user_login_data($user_login, $user) {
        $premium_data = get_user_meta($user->ID, '_deshiflix_premium_data', true);
        
        if (!$premium_data) {
            $this->setup_new_user($user->ID);
            $premium_data = get_user_meta($user->ID, '_deshiflix_premium_data', true);
        }
        
        $premium_data['last_activity'] = current_time('mysql');
        $premium_data['last_login'] = current_time('mysql');
        
        update_user_meta($user->ID, '_deshiflix_premium_data', $premium_data);
        
        // Check subscription status
        $this->check_user_subscription_status($user->ID);
        
        // Track login
        $this->track_user_activity($user->ID, 'user_login');
    }
    
    /**
     * Update user logout data
     */
    public function update_user_logout_data() {
        $user_id = get_current_user_id();
        
        if ($user_id) {
            $premium_data = get_user_meta($user_id, '_deshiflix_premium_data', true);
            
            if ($premium_data) {
                $premium_data['last_logout'] = current_time('mysql');
                update_user_meta($user_id, '_deshiflix_premium_data', $premium_data);
            }
            
            // Track logout
            $this->track_user_activity($user_id, 'user_logout');
        }
    }
    
    /**
     * Check user subscription status
     */
    public function check_user_subscription_status($user_id) {
        global $wpdb;
        
        $table_premium_users = $wpdb->prefix . 'deshiflix_premium_users';
        
        $subscription = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_premium_users 
             WHERE user_id = %d AND status = 'active' 
             ORDER BY created_at DESC LIMIT 1",
            $user_id
        ));
        
        if ($subscription) {
            // Check if subscription has expired
            if (strtotime($subscription->end_date) <= time()) {
                $this->expire_user_subscription($user_id);
            } else {
                // Update user role based on plan
                $this->update_user_premium_role($user_id, $subscription->plan_id);
            }
        }
    }
    
    /**
     * Update user premium role
     */
    public function update_user_premium_role($user_id, $plan_id) {
        $user = get_user_by('ID', $user_id);
        
        if (!$user) {
            return false;
        }
        
        // Get plan details
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_plans WHERE id = %d",
            $plan_id
        ));
        
        if (!$plan) {
            return false;
        }
        
        // Remove existing premium roles
        foreach ($this->premium_roles as $role_key => $role_data) {
            $user->remove_role($role_key);
        }
        
        // Assign appropriate premium role based on plan
        $plan_features = json_decode($plan->features, true);
        
        if (isset($plan_features['exclusive_content']) && $plan_features['exclusive_content']) {
            $user->add_role('premium_pro');
        } elseif (isset($plan_features['download_links']) && $plan_features['download_links']) {
            $user->add_role('premium_standard');
        } else {
            $user->add_role('premium_basic');
        }
        
        return true;
    }
    
    /**
     * Expire user subscription
     */
    public function expire_user_subscription($user_id) {
        global $wpdb;
        
        // Update database
        $table_premium_users = $wpdb->prefix . 'deshiflix_premium_users';
        $wpdb->update(
            $table_premium_users,
            array('status' => 'expired'),
            array('user_id' => $user_id, 'status' => 'active')
        );
        
        // Update user meta
        $premium_data = get_user_meta($user_id, '_deshiflix_premium_data', true);
        if ($premium_data) {
            $premium_data['status'] = 'expired';
            update_user_meta($user_id, '_deshiflix_premium_data', $premium_data);
        }
        
        // Remove premium roles
        $user = get_user_by('ID', $user_id);
        if ($user) {
            foreach ($this->premium_roles as $role_key => $role_data) {
                $user->remove_role($role_key);
            }
        }
        
        // Track expiration
        $this->track_user_activity($user_id, 'subscription_expired');
        
        // Send expiration notification
        $this->send_expiration_notification($user_id);
        
        // Trigger action
        do_action('deshiflix_premium_subscription_expired', $user_id);
    }
    
    /**
     * Track user activity
     */
    public function track_user_activity($user_id, $activity_type, $additional_data = array()) {
        global $wpdb;
        
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        $activity_data = array_merge(array(
            'timestamp' => current_time('mysql'),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ), $additional_data);
        
        $wpdb->insert($table_analytics, array(
            'user_id' => $user_id,
            'event_type' => $activity_type,
            'event_data' => json_encode($activity_data),
            'session_id' => session_id(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ));
    }
    
    /**
     * Track user device
     */
    public function track_user_device($user_login, $user) {
        $device_id = $this->generate_device_id();
        $device_info = $this->get_device_info();
        
        global $wpdb;
        $table_devices = $wpdb->prefix . 'deshiflix_premium_devices';
        
        // Check if device already exists
        $existing_device = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_devices WHERE user_id = %d AND device_id = %s",
            $user->ID,
            $device_id
        ));
        
        if ($existing_device) {
            // Update last active
            $wpdb->update(
                $table_devices,
                array('last_active' => current_time('mysql')),
                array('id' => $existing_device->id)
            );
        } else {
            // Check device limit
            $device_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_devices WHERE user_id = %d AND status = 'active'",
                $user->ID
            ));
            
            $max_devices = $this->get_user_max_devices($user->ID);
            
            if ($device_count >= $max_devices) {
                // Remove oldest device
                $oldest_device = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $table_devices 
                     WHERE user_id = %d AND status = 'active' 
                     ORDER BY last_active ASC LIMIT 1",
                    $user->ID
                ));
                
                if ($oldest_device) {
                    $wpdb->update(
                        $table_devices,
                        array('status' => 'inactive'),
                        array('id' => $oldest_device->id)
                    );
                }
            }
            
            // Add new device
            $wpdb->insert($table_devices, array(
                'user_id' => $user->ID,
                'device_id' => $device_id,
                'device_name' => $device_info['name'],
                'device_type' => $device_info['type'],
                'browser' => $device_info['browser'],
                'os' => $device_info['os'],
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'status' => 'active'
            ));
        }
        
        // Store device ID in session
        if (!session_id()) {
            session_start();
        }
        $_SESSION['deshiflix_device_id'] = $device_id;
    }
    
    /**
     * Generate device ID
     */
    private function generate_device_id() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        
        return md5($user_agent . $ip_address . time());
    }
    
    /**
     * Get device info
     */
    private function get_device_info() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Simple device detection
        $device_type = 'desktop';
        if (preg_match('/Mobile|Android|iPhone|iPad/', $user_agent)) {
            $device_type = 'mobile';
        } elseif (preg_match('/Tablet|iPad/', $user_agent)) {
            $device_type = 'tablet';
        }
        
        // Simple browser detection
        $browser = 'Unknown';
        if (preg_match('/Chrome/', $user_agent)) {
            $browser = 'Chrome';
        } elseif (preg_match('/Firefox/', $user_agent)) {
            $browser = 'Firefox';
        } elseif (preg_match('/Safari/', $user_agent)) {
            $browser = 'Safari';
        } elseif (preg_match('/Edge/', $user_agent)) {
            $browser = 'Edge';
        }
        
        // Simple OS detection
        $os = 'Unknown';
        if (preg_match('/Windows/', $user_agent)) {
            $os = 'Windows';
        } elseif (preg_match('/Mac/', $user_agent)) {
            $os = 'macOS';
        } elseif (preg_match('/Linux/', $user_agent)) {
            $os = 'Linux';
        } elseif (preg_match('/Android/', $user_agent)) {
            $os = 'Android';
        } elseif (preg_match('/iOS/', $user_agent)) {
            $os = 'iOS';
        }
        
        return array(
            'name' => $browser . ' on ' . $os,
            'type' => $device_type,
            'browser' => $browser,
            'os' => $os
        );
    }
    
    /**
     * Get user max devices
     */
    private function get_user_max_devices($user_id) {
        $user_plan_id = deshiflix_premium()->get_user_premium_plan($user_id);
        
        if (!$user_plan_id) {
            return 1; // Default for free users
        }
        
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT max_devices FROM $table_plans WHERE id = %d",
            $user_plan_id
        ));
        
        return $plan ? $plan->max_devices : 1;
    }
    
    /**
     * Add premium profile section
     */
    public function add_premium_profile_section($user) {
        if (!current_user_can('edit_user', $user->ID)) {
            return;
        }
        
        $premium_data = get_user_meta($user->ID, '_deshiflix_premium_data', true);
        $is_premium = deshiflix_premium()->is_user_premium($user->ID);
        
        ?>
        <h3><?php _e('Premium Subscription Details', 'deshiflix'); ?></h3>
        <table class="form-table">
            <tr>
                <th><label><?php _e('Premium Status', 'deshiflix'); ?></label></th>
                <td>
                    <?php if ($is_premium): ?>
                        <span class="premium-status active">✅ <?php _e('Active Premium User', 'deshiflix'); ?></span>
                    <?php else: ?>
                        <span class="premium-status inactive">❌ <?php _e('Free User', 'deshiflix'); ?></span>
                    <?php endif; ?>
                </td>
            </tr>
            
            <?php if ($premium_data): ?>
            <tr>
                <th><label><?php _e('Last Activity', 'deshiflix'); ?></label></th>
                <td><?php echo $premium_data['last_activity'] ? date('F j, Y H:i', strtotime($premium_data['last_activity'])) : __('Never', 'deshiflix'); ?></td>
            </tr>
            
            <tr>
                <th><label><?php _e('Download Count', 'deshiflix'); ?></label></th>
                <td><?php echo intval($premium_data['download_count']); ?> <?php _e('downloads this month', 'deshiflix'); ?></td>
            </tr>
            <?php endif; ?>
            
            <?php if (current_user_can('manage_options')): ?>
            <tr>
                <th><label><?php _e('Admin Actions', 'deshiflix'); ?></label></th>
                <td>
                    <button type="button" class="button" onclick="managePremiumUser(<?php echo $user->ID; ?>)">
                        <?php _e('Manage Premium Status', 'deshiflix'); ?>
                    </button>
                    <button type="button" class="button" onclick="viewUserDevices(<?php echo $user->ID; ?>)">
                        <?php _e('View Devices', 'deshiflix'); ?>
                    </button>
                </td>
            </tr>
            <?php endif; ?>
        </table>
        
        <style>
        .premium-status.active {
            color: #46b450;
            font-weight: bold;
        }
        
        .premium-status.inactive {
            color: #dc3232;
            font-weight: bold;
        }
        </style>
        <?php
    }
    
    /**
     * Save premium profile data
     */
    public function save_premium_profile_data($user_id) {
        if (!current_user_can('edit_user', $user_id)) {
            return;
        }
        
        // Admin can update premium data here if needed
        if (current_user_can('manage_options')) {
            // Handle admin updates
        }
    }
    
    /**
     * Add premium user columns
     */
    public function add_premium_user_columns($columns) {
        $columns['premium_status'] = __('Premium Status', 'deshiflix');
        $columns['premium_plan'] = __('Plan', 'deshiflix');
        $columns['premium_expires'] = __('Expires', 'deshiflix');
        
        return $columns;
    }
    
    /**
     * Display premium user columns
     */
    public function display_premium_user_columns($value, $column_name, $user_id) {
        switch ($column_name) {
            case 'premium_status':
                if (deshiflix_premium()->is_user_premium($user_id)) {
                    return '<span class="premium-badge active">✨ Premium</span>';
                } else {
                    return '<span class="premium-badge inactive">🆓 Free</span>';
                }
                break;
                
            case 'premium_plan':
                $plan_id = deshiflix_premium()->get_user_premium_plan($user_id);
                if ($plan_id) {
                    global $wpdb;
                    $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
                    $plan = $wpdb->get_row($wpdb->prepare(
                        "SELECT name FROM $table_plans WHERE id = %d",
                        $plan_id
                    ));
                    return $plan ? esc_html($plan->name) : '-';
                }
                return '-';
                break;
                
            case 'premium_expires':
                $premium_user = DeshiFlix_Premium_User::get_instance();
                $details = $premium_user->get_user_premium_details($user_id);
                
                if ($details && $details->status === 'active') {
                    $expires = strtotime($details->end_date);
                    $now = time();
                    
                    if ($expires > $now) {
                        $days_left = ceil(($expires - $now) / (24 * 60 * 60));
                        return date('M j, Y', $expires) . '<br><small>(' . $days_left . ' days left)</small>';
                    } else {
                        return '<span style="color: #dc3232;">Expired</span>';
                    }
                }
                return '-';
                break;
        }
        
        return $value;
    }
    
    /**
     * Filter users by premium status
     */
    public function filter_users_by_premium_status($args) {
        if (isset($_GET['premium_status'])) {
            $premium_status = sanitize_text_field($_GET['premium_status']);
            
            if ($premium_status === 'premium') {
                // Show only premium users
                $args['meta_query'] = array(
                    array(
                        'key' => '_deshiflix_premium_data',
                        'value' => '"status":"active"',
                        'compare' => 'LIKE'
                    )
                );
            } elseif ($premium_status === 'free') {
                // Show only free users
                $args['meta_query'] = array(
                    'relation' => 'OR',
                    array(
                        'key' => '_deshiflix_premium_data',
                        'value' => '"status":"inactive"',
                        'compare' => 'LIKE'
                    ),
                    array(
                        'key' => '_deshiflix_premium_data',
                        'compare' => 'NOT EXISTS'
                    )
                );
            }
        }
        
        return $args;
    }
    
    /**
     * Add user management menu
     */
    public function add_user_management_menu() {
        add_submenu_page(
            'deshiflix-premium',
            __('User Management', 'deshiflix'),
            __('User Management', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-user-management',
            array($this, 'user_management_page')
        );
    }
    
    /**
     * User management page
     */
    public function user_management_page() {
        include DESHIFLIX_PREMIUM_PATH . 'admin/premium-user-management.php';
    }
    
    /**
     * Check expired subscriptions (cron job)
     */
    public function check_expired_subscriptions() {
        global $wpdb;
        
        $table_premium_users = $wpdb->prefix . 'deshiflix_premium_users';
        
        $expired_users = $wpdb->get_results(
            "SELECT user_id FROM $table_premium_users 
             WHERE status = 'active' AND end_date < NOW()"
        );
        
        foreach ($expired_users as $expired_user) {
            $this->expire_user_subscription($expired_user->user_id);
        }
    }
    
    /**
     * Send expiry reminders (cron job)
     */
    public function send_expiry_reminders() {
        global $wpdb;
        
        $table_premium_users = $wpdb->prefix . 'deshiflix_premium_users';
        
        // Get users expiring in 3 days
        $expiring_users = $wpdb->get_results(
            "SELECT pu.user_id, pu.end_date, u.user_email, u.display_name 
             FROM $table_premium_users pu 
             JOIN {$wpdb->users} u ON pu.user_id = u.ID 
             WHERE pu.status = 'active' 
             AND pu.end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 3 DAY)"
        );
        
        foreach ($expiring_users as $user) {
            $this->send_expiry_reminder($user);
        }
    }
    
    /**
     * Send expiry reminder
     */
    private function send_expiry_reminder($user_data) {
        $subject = __('Your Premium Subscription Expires Soon - DeshiFlix', 'deshiflix');
        $message = sprintf(
            __('Hi %s,\n\nYour premium subscription will expire on %s. Renew now to continue enjoying premium features.\n\nRenew: %s', 'deshiflix'),
            $user_data->display_name,
            date('F j, Y', strtotime($user_data->end_date)),
            home_url('/premium-plans/')
        );
        
        wp_mail($user_data->user_email, $subject, $message);
    }
    
    /**
     * Send expiration notification
     */
    private function send_expiration_notification($user_id) {
        $user = get_user_by('ID', $user_id);
        if (!$user) return;
        
        $subject = __('Premium Subscription Expired - DeshiFlix', 'deshiflix');
        $message = sprintf(
            __('Hi %s,\n\nYour premium subscription has expired. Renew now to continue enjoying premium content.\n\nRenew: %s', 'deshiflix'),
            $user->display_name,
            home_url('/premium-plans/')
        );
        
        wp_mail($user->user_email, $subject, $message);
    }
    
    /**
     * AJAX premium user action
     */
    public function ajax_premium_user_action() {
        check_ajax_referer('premium_user_management_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied', 'deshiflix')));
        }
        
        $action = sanitize_text_field($_POST['user_action']);
        $user_id = intval($_POST['user_id']);
        
        switch ($action) {
            case 'activate_premium':
                $plan_id = intval($_POST['plan_id']);
                $duration = intval($_POST['duration']);
                
                $premium_user = DeshiFlix_Premium_User::get_instance();
                $result = $premium_user->activate_premium_subscription($user_id, $plan_id, $duration, 'admin');
                
                if ($result) {
                    wp_send_json_success(array('message' => __('Premium activated', 'deshiflix')));
                } else {
                    wp_send_json_error(array('message' => __('Failed to activate premium', 'deshiflix')));
                }
                break;
                
            case 'cancel_premium':
                $premium_user = DeshiFlix_Premium_User::get_instance();
                $result = $premium_user->cancel_premium_subscription($user_id);
                
                if ($result) {
                    wp_send_json_success(array('message' => __('Premium cancelled', 'deshiflix')));
                } else {
                    wp_send_json_error(array('message' => __('Failed to cancel premium', 'deshiflix')));
                }
                break;
                
            default:
                wp_send_json_error(array('message' => __('Invalid action', 'deshiflix')));
        }
    }
    
    /**
     * AJAX get user premium details
     */
    public function ajax_get_user_premium_details() {
        check_ajax_referer('premium_user_management_nonce', 'nonce');
        
        $user_id = intval($_POST['user_id']);
        
        $premium_user = DeshiFlix_Premium_User::get_instance();
        $details = $premium_user->get_user_premium_details($user_id);
        $premium_data = get_user_meta($user_id, '_deshiflix_premium_data', true);
        
        wp_send_json_success(array(
            'details' => $details,
            'premium_data' => $premium_data,
            'is_premium' => deshiflix_premium()->is_user_premium($user_id)
        ));
    }
}

// Initialize user management
DeshiFlix_Premium_User_Management::get_instance();
