<?php
/**
 * DeshiFlix Premium System - Frontend Subscription Handler
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Premium Subscription Frontend Class
 */
class DeshiFlix_Premium_Subscription_Frontend {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize frontend subscription
     */
    private function init() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Shortcodes
        add_shortcode('premium_plans', array($this, 'premium_plans_shortcode'));
        add_shortcode('premium_dashboard', array($this, 'premium_dashboard_shortcode'));
        add_shortcode('premium_payment_form', array($this, 'premium_payment_form_shortcode'));
        add_shortcode('premium_status', array($this, 'premium_status_shortcode'));
        
        // AJAX handlers
        add_action('wp_ajax_process_premium_payment', array($this, 'ajax_process_premium_payment'));
        add_action('wp_ajax_nopriv_process_premium_payment', array($this, 'ajax_process_premium_payment'));
        add_action('wp_ajax_cancel_premium', array($this, 'ajax_cancel_premium'));
        add_action('wp_ajax_check_content_access', array($this, 'ajax_check_content_access'));
        add_action('wp_ajax_nopriv_check_content_access', array($this, 'ajax_check_content_access'));
        
        // Frontend scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        
        // Template redirects
        add_action('template_redirect', array($this, 'handle_premium_pages'));
    }
    
    /**
     * Premium plans shortcode
     */
    public function premium_plans_shortcode($atts) {
        $atts = shortcode_atts(array(
            'show_features' => 'true',
            'highlight_plan' => '',
            'columns' => '3'
        ), $atts);
        
        ob_start();
        include DESHIFLIX_PREMIUM_PATH . 'templates/premium-plans.php';
        return ob_get_clean();
    }
    
    /**
     * Premium dashboard shortcode
     */
    public function premium_dashboard_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please login to view your premium dashboard.', 'deshiflix') . '</p>';
        }
        
        $atts = shortcode_atts(array(
            'show_billing' => 'true',
            'show_devices' => 'true',
            'show_downloads' => 'true'
        ), $atts);
        
        ob_start();
        include DESHIFLIX_PREMIUM_PATH . 'templates/premium-dashboard.php';
        return ob_get_clean();
    }
    
    /**
     * Premium payment form shortcode
     */
    public function premium_payment_form_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please login to subscribe to premium.', 'deshiflix') . '</p>';
        }
        
        $atts = shortcode_atts(array(
            'plan_id' => '',
            'redirect_url' => ''
        ), $atts);
        
        ob_start();
        include DESHIFLIX_PREMIUM_PATH . 'templates/payment-form.php';
        return ob_get_clean();
    }
    
    /**
     * Premium status shortcode
     */
    public function premium_status_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please login to view your premium status.', 'deshiflix') . '</p>';
        }
        
        $user_id = get_current_user_id();
        $is_premium = deshiflix_premium()->is_user_premium($user_id);
        
        if ($is_premium) {
            $premium_user = DeshiFlix_Premium_User::get_instance();
            $details = $premium_user->get_user_premium_details($user_id);
            
            $output = '<div class="premium-status-widget premium-active">';
            $output .= '<h4>✨ ' . __('Premium Active', 'deshiflix') . '</h4>';
            
            if ($details) {
                $days_left = ceil((strtotime($details->end_date) - time()) / (24 * 60 * 60));
                $output .= '<p>' . sprintf(__('Expires in %d days', 'deshiflix'), $days_left) . '</p>';
                $output .= '<p><small>' . sprintf(__('Expires on: %s', 'deshiflix'), date('F j, Y', strtotime($details->end_date))) . '</small></p>';
            }
            
            $output .= '</div>';
        } else {
            $output = '<div class="premium-status-widget premium-inactive">';
            $output .= '<h4>🆓 ' . __('Free User', 'deshiflix') . '</h4>';
            $output .= '<p>' . __('Upgrade to premium for exclusive content and features.', 'deshiflix') . '</p>';
            $output .= '<a href="/premium-plans/" class="btn btn-premium">' . __('Upgrade Now', 'deshiflix') . '</a>';
            $output .= '</div>';
        }
        
        return $output;
    }
    
    /**
     * AJAX process premium payment
     */
    public function ajax_process_premium_payment() {
        check_ajax_referer('premium_payment_nonce', 'nonce');
        
        if (!is_user_logged_in()) {
            wp_send_json_error(array('message' => __('Please login first', 'deshiflix')));
        }
        
        $plan_id = intval($_POST['plan_id']);
        $gateway = sanitize_text_field($_POST['gateway']);
        $user_id = get_current_user_id();
        
        // Validate plan
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_plans WHERE id = %d AND status = 'active'",
            $plan_id
        ));
        
        if (!$plan) {
            wp_send_json_error(array('message' => __('Invalid plan selected', 'deshiflix')));
        }
        
        // Process payment
        $payment_system = DeshiFlix_Premium_Payment::get_instance();
        $result = $payment_system->process_payment($user_id, $plan_id, $gateway);
        
        if ($result['success']) {
            wp_send_json_success(array(
                'payment_url' => $result['payment_url'],
                'transaction_id' => $result['transaction_id']
            ));
        } else {
            wp_send_json_error(array('message' => $result['message']));
        }
    }
    
    /**
     * AJAX cancel premium
     */
    public function ajax_cancel_premium() {
        check_ajax_referer('premium_cancel_nonce', 'nonce');
        
        if (!is_user_logged_in()) {
            wp_send_json_error(array('message' => __('Please login first', 'deshiflix')));
        }
        
        $user_id = get_current_user_id();
        
        // Cancel subscription
        $premium_user = DeshiFlix_Premium_User::get_instance();
        $result = $premium_user->cancel_premium_subscription($user_id);
        
        if ($result) {
            wp_send_json_success(array('message' => __('Subscription cancelled successfully', 'deshiflix')));
        } else {
            wp_send_json_error(array('message' => __('Failed to cancel subscription', 'deshiflix')));
        }
    }
    
    /**
     * AJAX check content access
     */
    public function ajax_check_content_access() {
        check_ajax_referer('premium_content_nonce', 'nonce');
        
        $post_id = intval($_POST['post_id']);
        $user_id = get_current_user_id();
        
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        $has_access = $content_manager->user_has_content_access($post_id, $user_id);
        $is_premium_content = $content_manager->is_premium_content($post_id);
        
        wp_send_json_success(array(
            'has_access' => $has_access,
            'is_premium' => $is_premium_content,
            'user_premium' => deshiflix_premium()->is_user_premium($user_id)
        ));
    }
    
    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        // Only enqueue on relevant pages
        if (!$this->should_enqueue_scripts()) {
            return;
        }
        
        wp_enqueue_style('deshiflix-premium-frontend', 
                        DESHIFLIX_PREMIUM_ASSETS_URL . 'css/premium-frontend.css', 
                        array(), DESHIFLIX_PREMIUM_VERSION);
        
        wp_enqueue_script('deshiflix-premium-frontend', 
                         DESHIFLIX_PREMIUM_ASSETS_URL . 'js/premium-frontend.js', 
                         array('jquery'), DESHIFLIX_PREMIUM_VERSION, true);
        
        wp_localize_script('deshiflix-premium-frontend', 'deshiflix_premium', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('premium_frontend_nonce'),
            'payment_nonce' => wp_create_nonce('premium_payment_nonce'),
            'cancel_nonce' => wp_create_nonce('premium_cancel_nonce'),
            'content_nonce' => wp_create_nonce('premium_content_nonce'),
            'is_user_premium' => is_user_logged_in() ? deshiflix_premium()->is_user_premium() : false,
            'login_url' => wp_login_url(),
            'assets_url' => DESHIFLIX_PREMIUM_ASSETS_URL,
            'messages' => array(
                'login_required' => __('Please login to access premium content', 'deshiflix'),
                'premium_required' => __('Premium subscription required', 'deshiflix'),
                'content_protected' => __('This content is protected', 'deshiflix'),
                'confirm_cancel' => __('Are you sure you want to cancel your subscription?', 'deshiflix'),
                'processing' => __('Processing...', 'deshiflix'),
                'error' => __('An error occurred', 'deshiflix')
            )
        ));
    }
    
    /**
     * Check if scripts should be enqueued
     */
    private function should_enqueue_scripts() {
        global $post;
        
        // Always enqueue on premium-related pages
        if (is_page() && $post) {
            $content = $post->post_content;
            if (has_shortcode($content, 'premium_plans') || 
                has_shortcode($content, 'premium_dashboard') || 
                has_shortcode($content, 'premium_payment_form') ||
                has_shortcode($content, 'premium_status')) {
                return true;
            }
        }
        
        // Enqueue on single posts that might be premium
        if (is_single() && in_array(get_post_type(), array('movies', 'tvshows', 'episodes'))) {
            return true;
        }
        
        // Enqueue if user is premium (for content protection)
        if (is_user_logged_in() && deshiflix_premium()->is_user_premium()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Handle premium pages
     */
    public function handle_premium_pages() {
        // Handle payment success page
        if (isset($_GET['premium_payment_success'])) {
            $this->handle_payment_success();
        }
        
        // Handle payment failure page
        if (isset($_GET['premium_payment_failed'])) {
            $this->handle_payment_failure();
        }
        
        // Handle payment cancellation
        if (isset($_GET['premium_payment_cancelled'])) {
            $this->handle_payment_cancellation();
        }
    }
    
    /**
     * Handle payment success
     */
    private function handle_payment_success() {
        $transaction_id = sanitize_text_field($_GET['transaction_id'] ?? '');
        
        if ($transaction_id) {
            // Verify transaction
            global $wpdb;
            $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
            
            $transaction = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $table_transactions WHERE transaction_id = %s",
                $transaction_id
            ));
            
            if ($transaction && $transaction->status === 'completed') {
                // Show success message
                add_action('wp_head', function() {
                    echo '<script>
                        document.addEventListener("DOMContentLoaded", function() {
                            alert("' . __('Payment successful! Your premium subscription is now active.', 'deshiflix') . '");
                        });
                    </script>';
                });
            }
        }
    }
    
    /**
     * Handle payment failure
     */
    private function handle_payment_failure() {
        add_action('wp_head', function() {
            echo '<script>
                document.addEventListener("DOMContentLoaded", function() {
                    alert("' . __('Payment failed. Please try again.', 'deshiflix') . '");
                });
            </script>';
        });
    }
    
    /**
     * Handle payment cancellation
     */
    private function handle_payment_cancellation() {
        add_action('wp_head', function() {
            echo '<script>
                document.addEventListener("DOMContentLoaded", function() {
                    alert("' . __('Payment was cancelled.', 'deshiflix') . '");
                });
            </script>';
        });
    }
    
    /**
     * Get premium plans for display
     */
    public function get_premium_plans() {
        global $wpdb;
        
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        return $wpdb->get_results(
            "SELECT * FROM $table_plans WHERE status = 'active' ORDER BY price ASC"
        );
    }
    
    /**
     * Get user premium details
     */
    public function get_user_premium_details($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return null;
        }
        
        $premium_user = DeshiFlix_Premium_User::get_instance();
        return $premium_user->get_user_premium_details($user_id);
    }
    
    /**
     * Check if content requires premium
     */
    public function content_requires_premium($post_id) {
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        return $content_manager->is_premium_content($post_id);
    }
    
    /**
     * Get content premium level
     */
    public function get_content_premium_level($post_id) {
        return get_post_meta($post_id, '_premium_level', true);
    }
    
    /**
     * Get content premium features
     */
    public function get_content_premium_features($post_id) {
        $features = get_post_meta($post_id, '_premium_features', true);
        return is_array($features) ? $features : array();
    }
}

// Initialize frontend subscription
DeshiFlix_Premium_Subscription_Frontend::get_instance();
