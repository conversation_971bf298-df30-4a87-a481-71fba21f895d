<?php
/**
 * DeshiFlix Premium System - Content Management Class
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Premium_Content {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize content management
     */
    private function init() {
        add_action('init', array($this, 'init_hooks'));
        add_action('add_meta_boxes', array($this, 'add_premium_meta_boxes'));
        add_action('save_post', array($this, 'save_premium_meta_data'));
    }
    
    /**
     * Initialize hooks
     */
    public function init_hooks() {
        // Content filtering hooks
        add_filter('the_content', array($this, 'filter_premium_content'), 20);
        add_filter('dooplay_player_content', array($this, 'filter_player_content'), 10, 2);
        add_filter('dooplay_download_links', array($this, 'filter_download_links'), 10, 2);
        
        // Admin hooks
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_toggle_premium_content', array($this, 'ajax_toggle_premium_content'));
        add_action('wp_ajax_bulk_premium_content', array($this, 'ajax_bulk_premium_content'));
        
        // Frontend hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('wp_ajax_check_content_access', array($this, 'ajax_check_content_access'));
        add_action('wp_ajax_nopriv_check_content_access', array($this, 'ajax_check_content_access'));
        
        // Post list columns
        add_filter('manage_movies_posts_columns', array($this, 'add_premium_column'));
        add_filter('manage_tvshows_posts_columns', array($this, 'add_premium_column'));
        add_filter('manage_episodes_posts_columns', array($this, 'add_premium_column'));
        add_action('manage_movies_posts_custom_column', array($this, 'display_premium_column'), 10, 2);
        add_action('manage_tvshows_posts_custom_column', array($this, 'display_premium_column'), 10, 2);
        add_action('manage_episodes_posts_custom_column', array($this, 'display_premium_column'), 10, 2);
    }
    
    /**
     * Add premium meta boxes
     */
    public function add_premium_meta_boxes() {
        $post_types = array('movies', 'tvshows', 'episodes', 'seasons');
        
        foreach ($post_types as $post_type) {
            add_meta_box(
                'deshiflix_premium_settings',
                __('Premium Content Settings', 'deshiflix'),
                array($this, 'premium_meta_box_callback'),
                $post_type,
                'side',
                'high'
            );
        }
    }
    
    /**
     * Premium meta box callback
     */
    public function premium_meta_box_callback($post) {
        wp_nonce_field('deshiflix_premium_meta_nonce', 'premium_meta_nonce');
        
        $is_premium = get_post_meta($post->ID, '_is_premium_content', true);
        $premium_level = get_post_meta($post->ID, '_premium_level', true);
        $unlock_date = get_post_meta($post->ID, '_premium_unlock_date', true);
        $premium_features = get_post_meta($post->ID, '_premium_features', true);
        
        if (!$premium_features) {
            $premium_features = array();
        }
        ?>
        <div class="premium-content-settings">
            <p>
                <label>
                    <input type="checkbox" name="is_premium_content" value="1" <?php checked($is_premium, 1); ?>>
                    <?php _e('Premium Content', 'deshiflix'); ?>
                </label>
            </p>
            
            <div class="premium-options" style="<?php echo $is_premium ? '' : 'display:none;'; ?>">
                <p>
                    <label><?php _e('Premium Level:', 'deshiflix'); ?></label>
                    <select name="premium_level">
                        <option value="basic" <?php selected($premium_level, 'basic'); ?>><?php _e('Basic', 'deshiflix'); ?></option>
                        <option value="standard" <?php selected($premium_level, 'standard'); ?>><?php _e('Standard', 'deshiflix'); ?></option>
                        <option value="premium" <?php selected($premium_level, 'premium'); ?>><?php _e('Premium', 'deshiflix'); ?></option>
                    </select>
                </p>
                
                <p>
                    <label><?php _e('Early Access Until:', 'deshiflix'); ?></label>
                    <input type="datetime-local" name="premium_unlock_date" value="<?php echo esc_attr($unlock_date); ?>">
                    <small><?php _e('Leave empty for permanent premium', 'deshiflix'); ?></small>
                </p>
                
                <p>
                    <label><?php _e('Premium Features:', 'deshiflix'); ?></label><br>
                    <label>
                        <input type="checkbox" name="premium_features[]" value="hd_quality" 
                               <?php checked(in_array('hd_quality', $premium_features)); ?>>
                        <?php _e('HD Quality Only', 'deshiflix'); ?>
                    </label><br>
                    <label>
                        <input type="checkbox" name="premium_features[]" value="download_links" 
                               <?php checked(in_array('download_links', $premium_features)); ?>>
                        <?php _e('Download Links', 'deshiflix'); ?>
                    </label><br>
                    <label>
                        <input type="checkbox" name="premium_features[]" value="ad_free" 
                               <?php checked(in_array('ad_free', $premium_features)); ?>>
                        <?php _e('Ad-Free Experience', 'deshiflix'); ?>
                    </label><br>
                    <label>
                        <input type="checkbox" name="premium_features[]" value="multiple_servers" 
                               <?php checked(in_array('multiple_servers', $premium_features)); ?>>
                        <?php _e('Multiple Servers', 'deshiflix'); ?>
                    </label>
                </p>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('input[name="is_premium_content"]').change(function() {
                if ($(this).is(':checked')) {
                    $('.premium-options').show();
                } else {
                    $('.premium-options').hide();
                }
            });
        });
        </script>
        
        <style>
        .premium-content-settings label {
            display: block;
            margin-bottom: 5px;
        }
        .premium-content-settings select,
        .premium-content-settings input[type="datetime-local"] {
            width: 100%;
        }
        .premium-options {
            border-top: 1px solid #ddd;
            padding-top: 10px;
            margin-top: 10px;
        }
        </style>
        <?php
    }
    
    /**
     * Save premium meta data
     */
    public function save_premium_meta_data($post_id) {
        if (!isset($_POST['premium_meta_nonce']) || 
            !wp_verify_nonce($_POST['premium_meta_nonce'], 'deshiflix_premium_meta_nonce')) {
            return;
        }
        
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Save premium content status
        $is_premium = isset($_POST['is_premium_content']) ? 1 : 0;
        update_post_meta($post_id, '_is_premium_content', $is_premium);
        
        if ($is_premium) {
            // Save premium level
            $premium_level = sanitize_text_field($_POST['premium_level']);
            update_post_meta($post_id, '_premium_level', $premium_level);
            
            // Save unlock date
            $unlock_date = sanitize_text_field($_POST['premium_unlock_date']);
            update_post_meta($post_id, '_premium_unlock_date', $unlock_date);
            
            // Save premium features
            $premium_features = isset($_POST['premium_features']) ? 
                                array_map('sanitize_text_field', $_POST['premium_features']) : array();
            update_post_meta($post_id, '_premium_features', $premium_features);
            
            // Update premium content table
            $this->update_premium_content_table($post_id, $premium_level, $unlock_date);
        } else {
            // Remove premium meta data
            delete_post_meta($post_id, '_premium_level');
            delete_post_meta($post_id, '_premium_unlock_date');
            delete_post_meta($post_id, '_premium_features');
            
            // Remove from premium content table
            $this->remove_from_premium_content_table($post_id);
        }
    }
    
    /**
     * Update premium content table
     */
    private function update_premium_content_table($post_id, $premium_level, $unlock_date) {
        global $wpdb;
        
        $table_premium_content = $wpdb->prefix . 'deshiflix_premium_content';
        
        // Check if already exists
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_premium_content WHERE post_id = %d",
            $post_id
        ));
        
        $data = array(
            'post_id' => $post_id,
            'is_premium' => 1,
            'premium_level' => $premium_level,
            'unlock_date' => $unlock_date ? $unlock_date : null
        );
        
        if ($exists) {
            $wpdb->update($table_premium_content, $data, array('post_id' => $post_id));
        } else {
            $wpdb->insert($table_premium_content, $data);
        }
    }
    
    /**
     * Remove from premium content table
     */
    private function remove_from_premium_content_table($post_id) {
        global $wpdb;
        
        $table_premium_content = $wpdb->prefix . 'deshiflix_premium_content';
        $wpdb->delete($table_premium_content, array('post_id' => $post_id));
    }
    
    /**
     * Filter premium content
     */
    public function filter_premium_content($content) {
        global $post;
        
        if (!$post || is_admin() || is_feed()) {
            return $content;
        }
        
        if (!$this->is_premium_content($post->ID)) {
            return $content;
        }
        
        // Check if user has access
        if ($this->user_has_content_access($post->ID)) {
            return $content;
        }
        
        // Show premium content lock
        return $this->get_premium_content_lock_html($post);
    }
    
    /**
     * Filter player content
     */
    public function filter_player_content($content, $post_id) {
        if (!$this->is_premium_content($post_id)) {
            return $content;
        }
        
        if (!$this->user_has_content_access($post_id)) {
            return $this->get_premium_player_lock_html($post_id);
        }
        
        return $content;
    }
    
    /**
     * Filter download links
     */
    public function filter_download_links($links, $post_id) {
        if (!$this->is_premium_content($post_id)) {
            return $links;
        }
        
        $premium_features = get_post_meta($post_id, '_premium_features', true);
        
        if (in_array('download_links', $premium_features) && 
            !$this->user_has_content_access($post_id)) {
            return array(); // No download links for non-premium users
        }
        
        return $links;
    }
    
    /**
     * Check if content is premium
     */
    public function is_premium_content($post_id) {
        $is_premium = get_post_meta($post_id, '_is_premium_content', true);
        
        if (!$is_premium) {
            return false;
        }
        
        // Check if early access period has ended
        $unlock_date = get_post_meta($post_id, '_premium_unlock_date', true);
        
        if ($unlock_date && strtotime($unlock_date) <= time()) {
            return false; // Early access period ended
        }
        
        return true;
    }
    
    /**
     * Check if user has content access
     */
    public function user_has_content_access($post_id, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return false;
        }
        
        // Check if user is premium
        if (!deshiflix_premium()->is_user_premium($user_id)) {
            return false;
        }
        
        // Get user's premium plan
        $user_plan = deshiflix_premium()->get_user_premium_plan($user_id);
        $content_level = get_post_meta($post_id, '_premium_level', true);
        
        // Check if user's plan covers content level
        return $this->plan_covers_content_level($user_plan, $content_level);
    }
    
    /**
     * Check if plan covers content level
     */
    private function plan_covers_content_level($user_plan_id, $content_level) {
        global $wpdb;
        
        $table_premium_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT features FROM $table_premium_plans WHERE id = %d",
            $user_plan_id
        ));
        
        if (!$plan) {
            return false;
        }
        
        $features = json_decode($plan->features, true);
        
        // Basic plan covers basic content, etc.
        $plan_levels = array(
            'basic' => array('basic'),
            'standard' => array('basic', 'standard'),
            'premium' => array('basic', 'standard', 'premium')
        );
        
        // Determine plan type based on features
        $plan_type = 'basic';
        if (isset($features['download_links']) && $features['download_links']) {
            $plan_type = 'standard';
        }
        if (isset($features['early_access']) && $features['early_access']) {
            $plan_type = 'premium';
        }
        
        return in_array($content_level, $plan_levels[$plan_type]);
    }
    
    /**
     * Get premium content lock HTML
     */
    private function get_premium_content_lock_html($post) {
        ob_start();
        include DESHIFLIX_PREMIUM_PATH . 'templates/premium-content-overlay.php';
        return ob_get_clean();
    }
    
    /**
     * Get premium player lock HTML
     */
    private function get_premium_player_lock_html($post_id) {
        $premium_level = get_post_meta($post_id, '_premium_level', true);
        
        ob_start();
        ?>
        <div class="premium-player-lock">
            <div class="lock-overlay">
                <div class="lock-content">
                    <div class="lock-icon">🔒</div>
                    <h3><?php _e('Premium Content', 'deshiflix'); ?></h3>
                    <p><?php printf(__('This %s content requires a premium subscription', 'deshiflix'), $premium_level); ?></p>
                    <div class="premium-benefits">
                        <ul>
                            <li>✅ <?php _e('HD Quality Streaming', 'deshiflix'); ?></li>
                            <li>✅ <?php _e('Ad-Free Experience', 'deshiflix'); ?></li>
                            <li>✅ <?php _e('Download Links', 'deshiflix'); ?></li>
                            <li>✅ <?php _e('Multiple Servers', 'deshiflix'); ?></li>
                        </ul>
                    </div>
                    <button class="btn-upgrade-premium" onclick="showPremiumUpgrade()">
                        <?php _e('Upgrade to Premium', 'deshiflix'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Add premium column to post list
     */
    public function add_premium_column($columns) {
        $columns['premium_status'] = __('Premium', 'deshiflix');
        return $columns;
    }
    
    /**
     * Display premium column content
     */
    public function display_premium_column($column, $post_id) {
        if ($column === 'premium_status') {
            $is_premium = get_post_meta($post_id, '_is_premium_content', true);
            
            if ($is_premium) {
                $premium_level = get_post_meta($post_id, '_premium_level', true);
                echo '<span class="premium-badge premium-' . esc_attr($premium_level) . '">💎 ' . 
                     ucfirst($premium_level) . '</span>';
            } else {
                echo '<span class="free-badge">🆓 Free</span>';
            }
        }
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        global $post_type;
        
        if (!in_array($post_type, array('movies', 'tvshows', 'episodes', 'seasons'))) {
            return;
        }
        
        wp_enqueue_style('deshiflix-premium-admin', 
                        DESHIFLIX_PREMIUM_ASSETS_URL . 'css/premium-admin.css');
        wp_enqueue_script('deshiflix-premium-admin', 
                         DESHIFLIX_PREMIUM_ASSETS_URL . 'js/premium-admin.js', 
                         array('jquery'));
    }
    
    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        wp_enqueue_style('deshiflix-premium-content', 
                        DESHIFLIX_PREMIUM_ASSETS_URL . 'css/premium-content.css');
        wp_enqueue_script('deshiflix-premium-content', 
                         DESHIFLIX_PREMIUM_ASSETS_URL . 'js/premium-content.js', 
                         array('jquery'));
    }
    
    /**
     * AJAX toggle premium content
     */
    public function ajax_toggle_premium_content() {
        check_ajax_referer('deshiflix_premium_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array('message' => __('Permission denied', 'deshiflix')));
        }
        
        $post_id = intval($_POST['post_id']);
        $is_premium = intval($_POST['is_premium']);
        
        update_post_meta($post_id, '_is_premium_content', $is_premium);
        
        if ($is_premium) {
            $this->update_premium_content_table($post_id, 'basic', null);
        } else {
            $this->remove_from_premium_content_table($post_id);
        }
        
        wp_send_json_success(array('message' => __('Premium status updated', 'deshiflix')));
    }
    
    /**
     * AJAX bulk premium content
     */
    public function ajax_bulk_premium_content() {
        check_ajax_referer('deshiflix_premium_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array('message' => __('Permission denied', 'deshiflix')));
        }
        
        $post_ids = array_map('intval', $_POST['post_ids']);
        $action = sanitize_text_field($_POST['bulk_action']);
        $premium_level = sanitize_text_field($_POST['premium_level']);
        
        $updated = 0;
        
        foreach ($post_ids as $post_id) {
            if ($action === 'make_premium') {
                update_post_meta($post_id, '_is_premium_content', 1);
                update_post_meta($post_id, '_premium_level', $premium_level);
                $this->update_premium_content_table($post_id, $premium_level, null);
                $updated++;
            } elseif ($action === 'make_free') {
                update_post_meta($post_id, '_is_premium_content', 0);
                $this->remove_from_premium_content_table($post_id);
                $updated++;
            }
        }
        
        wp_send_json_success(array(
            'message' => sprintf(__('%d posts updated', 'deshiflix'), $updated),
            'updated' => $updated
        ));
    }
    
    /**
     * AJAX check content access
     */
    public function ajax_check_content_access() {
        check_ajax_referer('deshiflix_premium_nonce', 'nonce');
        
        $post_id = intval($_POST['post_id']);
        $user_id = get_current_user_id();
        
        $has_access = $this->user_has_content_access($post_id, $user_id);
        $is_premium_content = $this->is_premium_content($post_id);
        
        wp_send_json_success(array(
            'has_access' => $has_access,
            'is_premium' => $is_premium_content,
            'user_premium' => deshiflix_premium()->is_user_premium($user_id)
        ));
    }

    /**
     * Protect player content based on premium status
     */
    public function protect_player_content($content, $post_id) {
        if (!$this->is_premium_content($post_id)) {
            return $content;
        }

        if (!$this->user_has_content_access($post_id)) {
            return $this->get_premium_player_lock_html($post_id);
        }

        // Add premium watermark or branding
        return $this->add_premium_branding($content, $post_id);
    }

    /**
     * Add premium branding to content
     */
    private function add_premium_branding($content, $post_id) {
        $premium_level = get_post_meta($post_id, '_premium_level', true);

        $branding = '<div class="premium-content-branding">';
        $branding .= '<span class="premium-badge-overlay">✨ Premium ' . ucfirst($premium_level) . '</span>';
        $branding .= '</div>';

        return $branding . $content;
    }

    /**
     * Protect download links
     */
    public function protect_download_links($links, $post_id) {
        if (!$this->is_premium_content($post_id)) {
            return $links;
        }

        $premium_features = get_post_meta($post_id, '_premium_features', true);

        if (in_array('download_links', $premium_features) &&
            !$this->user_has_content_access($post_id)) {

            // Return empty array or premium upgrade message
            return array();
        }

        // Add premium download tracking
        if ($this->user_has_content_access($post_id)) {
            $this->track_premium_download($post_id, get_current_user_id());
        }

        return $links;
    }

    /**
     * Track premium download
     */
    private function track_premium_download($post_id, $user_id) {
        global $wpdb;

        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';

        $wpdb->insert($table_analytics, array(
            'user_id' => $user_id,
            'event_type' => 'premium_download',
            'event_data' => json_encode(array(
                'post_id' => $post_id,
                'post_title' => get_the_title($post_id),
                'download_time' => current_time('mysql')
            )),
            'post_id' => $post_id,
            'session_id' => session_id(),
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ));
    }

    /**
     * Check download limits
     */
    public function check_download_limits($user_id) {
        $user_plan = deshiflix_premium()->get_user_premium_plan($user_id);

        if (!$user_plan) {
            return array('allowed' => false, 'message' => __('Premium subscription required', 'deshiflix'));
        }

        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';

        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT download_limit FROM $table_plans WHERE id = %d",
            $user_plan
        ));

        if (!$plan || $plan->download_limit == 0) {
            return array('allowed' => true, 'remaining' => 'unlimited');
        }

        // Count downloads this month
        $downloads_this_month = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_analytics
             WHERE user_id = %d AND event_type = 'premium_download'
             AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())",
            $user_id
        ));

        $remaining = $plan->download_limit - $downloads_this_month;

        if ($remaining <= 0) {
            return array(
                'allowed' => false,
                'message' => __('Monthly download limit reached', 'deshiflix'),
                'remaining' => 0
            );
        }

        return array('allowed' => true, 'remaining' => $remaining);
    }

    /**
     * Add content protection hooks for different post types
     */
    public function add_content_protection_hooks() {
        // Movies
        add_filter('dooplay_movie_player', array($this, 'protect_movie_player'), 10, 2);
        add_filter('dooplay_movie_download_links', array($this, 'protect_movie_downloads'), 10, 2);

        // TV Shows
        add_filter('dooplay_tvshow_player', array($this, 'protect_tvshow_player'), 10, 2);
        add_filter('dooplay_tvshow_download_links', array($this, 'protect_tvshow_downloads'), 10, 2);

        // Episodes
        add_filter('dooplay_episode_player', array($this, 'protect_episode_player'), 10, 2);
        add_filter('dooplay_episode_download_links', array($this, 'protect_episode_downloads'), 10, 2);

        // General content
        add_filter('the_content', array($this, 'protect_general_content'), 15);
    }

    /**
     * Protect movie player
     */
    public function protect_movie_player($player_content, $post_id) {
        return $this->protect_player_content($player_content, $post_id);
    }

    /**
     * Protect movie downloads
     */
    public function protect_movie_downloads($download_links, $post_id) {
        return $this->protect_download_links($download_links, $post_id);
    }

    /**
     * Protect TV show player
     */
    public function protect_tvshow_player($player_content, $post_id) {
        return $this->protect_player_content($player_content, $post_id);
    }

    /**
     * Protect TV show downloads
     */
    public function protect_tvshow_downloads($download_links, $post_id) {
        return $this->protect_download_links($download_links, $post_id);
    }

    /**
     * Protect episode player
     */
    public function protect_episode_player($player_content, $post_id) {
        return $this->protect_player_content($player_content, $post_id);
    }

    /**
     * Protect episode downloads
     */
    public function protect_episode_downloads($download_links, $post_id) {
        return $this->protect_download_links($download_links, $post_id);
    }

    /**
     * Protect general content
     */
    public function protect_general_content($content) {
        global $post;

        if (!$post || is_admin() || is_feed()) {
            return $content;
        }

        if (!in_array($post->post_type, array('movies', 'tvshows', 'episodes'))) {
            return $content;
        }

        return $this->filter_premium_content($content);
    }

    /**
     * Add premium content indicators
     */
    public function add_premium_content_indicators() {
        add_action('dooplay_movie_meta', array($this, 'show_premium_indicator'));
        add_action('dooplay_tvshow_meta', array($this, 'show_premium_indicator'));
        add_action('dooplay_episode_meta', array($this, 'show_premium_indicator'));
    }

    /**
     * Show premium indicator
     */
    public function show_premium_indicator() {
        global $post;

        if (!$this->is_premium_content($post->ID)) {
            return;
        }

        $premium_level = get_post_meta($post->ID, '_premium_level', true);
        $unlock_date = get_post_meta($post->ID, '_premium_unlock_date', true);

        echo '<div class="premium-content-indicator">';
        echo '<span class="premium-badge level-' . esc_attr($premium_level) . '">';
        echo '💎 ' . sprintf(__('Premium %s', 'deshiflix'), ucfirst($premium_level));
        echo '</span>';

        if ($unlock_date && strtotime($unlock_date) > time()) {
            echo '<span class="early-access-badge">';
            echo '⚡ ' . sprintf(__('Early Access until %s', 'deshiflix'), date('M j', strtotime($unlock_date)));
            echo '</span>';
        }

        echo '</div>';
    }

    /**
     * Add premium content to search results
     */
    public function modify_search_results($query) {
        if (!is_admin() && $query->is_search() && $query->is_main_query()) {
            // Show premium content in search but with indicators
            $meta_query = $query->get('meta_query');
            if (!$meta_query) {
                $meta_query = array();
            }

            // Don't exclude premium content from search
            $query->set('meta_query', $meta_query);
        }
    }

    /**
     * Add premium content to RSS feeds
     */
    public function modify_rss_content($content) {
        global $post;

        if (!$post) {
            return $content;
        }

        if ($this->is_premium_content($post->ID)) {
            $premium_level = get_post_meta($post->ID, '_premium_level', true);
            $content = '[Premium ' . ucfirst($premium_level) . ' Content] ' . $content;
        }

        return $content;
    }

    /**
     * Bulk update premium content
     */
    public function bulk_update_premium_content($post_ids, $action, $premium_level = 'basic') {
        $updated = 0;

        foreach ($post_ids as $post_id) {
            if (!current_user_can('edit_post', $post_id)) {
                continue;
            }

            if ($action === 'make_premium') {
                update_post_meta($post_id, '_is_premium_content', 1);
                update_post_meta($post_id, '_premium_level', $premium_level);
                $this->update_premium_content_table($post_id, $premium_level, null);
                $updated++;
            } elseif ($action === 'make_free') {
                update_post_meta($post_id, '_is_premium_content', 0);
                delete_post_meta($post_id, '_premium_level');
                delete_post_meta($post_id, '_premium_features');
                $this->remove_from_premium_content_table($post_id);
                $updated++;
            }
        }

        return $updated;
    }

    /**
     * Get premium content statistics
     */
    public function get_premium_content_stats() {
        global $wpdb;

        $table_content = $wpdb->prefix . 'deshiflix_premium_content';

        $stats = array();

        // Total premium content
        $stats['total_premium'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM $table_content WHERE is_premium = 1"
        );

        // Premium content by level
        $stats['by_level'] = $wpdb->get_results(
            "SELECT premium_level, COUNT(*) as count
             FROM $table_content
             WHERE is_premium = 1
             GROUP BY premium_level"
        );

        // Premium content by post type
        $stats['by_type'] = $wpdb->get_results(
            "SELECT p.post_type, COUNT(*) as count
             FROM $table_content pc
             JOIN {$wpdb->posts} p ON pc.post_id = p.ID
             WHERE pc.is_premium = 1
             GROUP BY p.post_type"
        );

        return $stats;
    }

    /**
     * Schedule content unlock
     */
    public function schedule_content_unlock($post_id, $unlock_date) {
        if (!$unlock_date) {
            return;
        }

        $timestamp = strtotime($unlock_date);

        if ($timestamp > time()) {
            wp_schedule_single_event($timestamp, 'deshiflix_unlock_premium_content', array($post_id));
        }
    }

    /**
     * Handle scheduled content unlock
     */
    public function handle_scheduled_unlock($post_id) {
        update_post_meta($post_id, '_is_premium_content', 0);
        delete_post_meta($post_id, '_premium_unlock_date');
        $this->remove_from_premium_content_table($post_id);

        // Send notification to admin
        $post_title = get_the_title($post_id);
        $admin_email = get_option('admin_email');

        wp_mail(
            $admin_email,
            sprintf(__('Content Unlocked: %s', 'deshiflix'), $post_title),
            sprintf(__('The premium content "%s" has been automatically unlocked and is now available to all users.', 'deshiflix'), $post_title)
        );
    }
}

// Initialize content protection
add_action('init', function() {
    $content_protection = DeshiFlix_Premium_Content::get_instance();
    $content_protection->add_content_protection_hooks();
    $content_protection->add_premium_content_indicators();

    // Add search modification
    add_action('pre_get_posts', array($content_protection, 'modify_search_results'));

    // Add RSS modification
    add_filter('the_excerpt_rss', array($content_protection, 'modify_rss_content'));
    add_filter('the_content_feed', array($content_protection, 'modify_rss_content'));

    // Add scheduled unlock handler
    add_action('deshiflix_unlock_premium_content', array($content_protection, 'handle_scheduled_unlock'));
});
