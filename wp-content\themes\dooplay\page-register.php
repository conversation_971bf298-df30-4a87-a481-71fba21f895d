<?php
/**
 * Template Name: Register Page
 * 
 * @package DooPlay
 * @subpackage DeshiFlix Premium
 */

get_header(); ?>

<div id="single">
    <div class="content">
        <div class="module">
            <div class="content">
                <div class="register-page-wrapper">
                    <?php echo do_shortcode('[premium_register_form]'); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.register-page-wrapper {
    max-width: 600px;
    margin: 40px auto;
    padding: 0 20px;
}

.premium-register-form .form-container {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-help {
    color: #666;
    font-size: 12px;
    margin-top: 5px;
}

.password-strength {
    margin-top: 5px;
}

.strength-meter {
    height: 4px;
    background: #eee;
    border-radius: 2px;
    overflow: hidden;
}

.strength-bar {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
}

.strength-text {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
    line-height: 1.4;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 3px;
    position: relative;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input:checked + .checkmark {
    background: #FFD700;
    border-color: #FFD700;
}

.checkbox-label input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #000;
    font-weight: bold;
    font-size: 12px;
}

.checkbox-label input {
    display: none;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .register-page-wrapper {
        padding: 0 10px;
    }
    
    .premium-register-form .form-container {
        padding: 30px 20px;
    }
}
</style>

<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Password strength checker
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('register_password');
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.querySelector('.strength-bar');
            const strengthText = document.querySelector('.strength-text');
            
            let strength = 0;
            let text = 'Weak';
            let color = '#ff4444';
            
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            switch (strength) {
                case 0:
                case 1:
                    text = 'Very Weak';
                    color = '#ff4444';
                    break;
                case 2:
                    text = 'Weak';
                    color = '#ff8800';
                    break;
                case 3:
                    text = 'Fair';
                    color = '#ffaa00';
                    break;
                case 4:
                    text = 'Good';
                    color = '#88cc00';
                    break;
                case 5:
                    text = 'Strong';
                    color = '#44aa00';
                    break;
            }
            
            strengthBar.style.width = (strength * 20) + '%';
            strengthBar.style.backgroundColor = color;
            strengthText.textContent = text;
            strengthText.style.color = color;
        });
    }
});
</script>

<?php get_footer(); ?>
