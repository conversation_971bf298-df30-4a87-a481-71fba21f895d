<?php
/**
 * Premium Notification System
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Premium_Notifications {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Notification hooks
        add_action('deshiflix_premium_activated', array($this, 'send_welcome_notification'));
        add_action('deshiflix_premium_expiring', array($this, 'send_expiry_warning'));
        add_action('deshiflix_premium_expired', array($this, 'send_expired_notification'));
        add_action('deshiflix_new_content_added', array($this, 'notify_premium_users'));
        
        // AJAX handlers
        add_action('wp_ajax_get_premium_notifications', array($this, 'get_notifications'));
        add_action('wp_ajax_mark_notification_read', array($this, 'mark_as_read'));
        
        // Scheduled notifications
        add_action('wp', array($this, 'schedule_notification_checks'));
        add_action('deshiflix_check_expiring_subscriptions', array($this, 'check_expiring_subscriptions'));
        
        // Add notification center to admin bar
        add_action('admin_bar_menu', array($this, 'add_notification_center'), 999);
        add_action('wp_footer', array($this, 'add_notification_center_scripts'));
    }
    
    /**
     * Send welcome notification
     */
    public function send_welcome_notification($user_id) {
        $user = get_user_by('ID', $user_id);
        
        // Email notification
        $subject = 'স্বাগতম DeshiFlix Premium এ! 🎉';
        $message = $this->get_welcome_email_template($user);
        wp_mail($user->user_email, $subject, $message, array('Content-Type: text/html; charset=UTF-8'));
        
        // In-app notification
        $this->create_notification($user_id, array(
            'type' => 'welcome',
            'title' => 'Premium সক্রিয় হয়েছে!',
            'message' => 'আপনার Premium subscription সফলভাবে সক্রিয় হয়েছে। এখন সব Premium features উপভোগ করুন!',
            'icon' => '🎉',
            'action_url' => home_url('/premium-dashboard/')
        ));
    }
    
    /**
     * Send expiry warning
     */
    public function send_expiry_warning($user_id) {
        $user = get_user_by('ID', $user_id);
        $premium_details = deshiflix_premium()->get_user_premium_details($user_id);
        
        $days_left = ceil((strtotime($premium_details['expires_at']) - time()) / (24 * 60 * 60));
        
        // Email notification
        $subject = 'আপনার Premium subscription শীঘ্রই শেষ হবে ⏰';
        $message = $this->get_expiry_warning_email_template($user, $days_left);
        wp_mail($user->user_email, $subject, $message, array('Content-Type: text/html; charset=UTF-8'));
        
        // In-app notification
        $this->create_notification($user_id, array(
            'type' => 'warning',
            'title' => 'Subscription শেষ হওয়ার সতর্কতা',
            'message' => "আপনার Premium subscription আর {$days_left} দিন পর শেষ হবে। Renew করুন যাতে service বন্ধ না হয়।",
            'icon' => '⏰',
            'action_url' => home_url('/premium-plans/'),
            'priority' => 'high'
        ));
    }
    
    /**
     * Send expired notification
     */
    public function send_expired_notification($user_id) {
        $user = get_user_by('ID', $user_id);
        
        // Email notification
        $subject = 'আপনার Premium subscription শেষ হয়ে গেছে 😔';
        $message = $this->get_expired_email_template($user);
        wp_mail($user->user_email, $subject, $message, array('Content-Type: text/html; charset=UTF-8'));
        
        // In-app notification
        $this->create_notification($user_id, array(
            'type' => 'expired',
            'title' => 'Premium Subscription শেষ',
            'message' => 'আপনার Premium subscription শেষ হয়ে গেছে। আবার Premium features পেতে renew করুন।',
            'icon' => '😔',
            'action_url' => home_url('/premium-plans/'),
            'priority' => 'high'
        ));
    }
    
    /**
     * Notify premium users about new content
     */
    public function notify_premium_users($post_id) {
        // Only notify for premium content
        if (!function_exists('deshiflix_premium')) {
            return;
        }
        
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        if (!$content_manager->is_premium_content($post_id)) {
            return;
        }
        
        // Get all premium users
        global $wpdb;
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        $premium_users = $wpdb->get_results(
            "SELECT DISTINCT user_id FROM $table_users WHERE status = 'active' AND end_date > NOW()"
        );
        
        $post_title = get_the_title($post_id);
        $post_type = get_post_type($post_id);
        $type_name = $post_type === 'movies' ? 'নতুন মুভি' : 'নতুন এপিসোড';
        
        foreach ($premium_users as $premium_user) {
            $this->create_notification($premium_user->user_id, array(
                'type' => 'new_content',
                'title' => "{$type_name} যোগ হয়েছে!",
                'message' => "নতুন premium content '{$post_title}' এখন উপলব্ধ। এখনই দেখুন!",
                'icon' => '🎬',
                'action_url' => get_permalink($post_id),
                'post_id' => $post_id
            ));
        }
    }
    
    /**
     * Create notification
     */
    private function create_notification($user_id, $data) {
        global $wpdb;
        
        $table_notifications = $wpdb->prefix . 'deshiflix_notifications';
        
        // Create table if not exists
        $this->maybe_create_notifications_table();
        
        $wpdb->insert(
            $table_notifications,
            array(
                'user_id' => $user_id,
                'type' => $data['type'],
                'title' => $data['title'],
                'message' => $data['message'],
                'icon' => $data['icon'] ?? '📢',
                'action_url' => $data['action_url'] ?? '',
                'post_id' => $data['post_id'] ?? null,
                'priority' => $data['priority'] ?? 'normal',
                'is_read' => 0,
                'created_at' => current_time('mysql')
            )
        );
    }
    
    /**
     * Get notifications via AJAX
     */
    public function get_notifications() {
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            wp_send_json_error('User not logged in');
        }
        
        global $wpdb;
        $table_notifications = $wpdb->prefix . 'deshiflix_notifications';
        
        $notifications = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_notifications WHERE user_id = %d ORDER BY created_at DESC LIMIT 20",
            $user_id
        ));
        
        $unread_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_notifications WHERE user_id = %d AND is_read = 0",
            $user_id
        ));
        
        wp_send_json_success(array(
            'notifications' => $notifications,
            'unread_count' => intval($unread_count)
        ));
    }
    
    /**
     * Mark notification as read
     */
    public function mark_as_read() {
        $user_id = get_current_user_id();
        $notification_id = intval($_POST['notification_id']);
        
        if (!$user_id) {
            wp_send_json_error('User not logged in');
        }
        
        global $wpdb;
        $table_notifications = $wpdb->prefix . 'deshiflix_notifications';
        
        $wpdb->update(
            $table_notifications,
            array('is_read' => 1),
            array('id' => $notification_id, 'user_id' => $user_id)
        );
        
        wp_send_json_success();
    }
    
    /**
     * Schedule notification checks
     */
    public function schedule_notification_checks() {
        if (!wp_next_scheduled('deshiflix_check_expiring_subscriptions')) {
            wp_schedule_event(time(), 'daily', 'deshiflix_check_expiring_subscriptions');
        }
    }
    
    /**
     * Check expiring subscriptions
     */
    public function check_expiring_subscriptions() {
        global $wpdb;
        
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        
        // Users expiring in 3 days
        $expiring_users = $wpdb->get_results(
            "SELECT user_id FROM $table_users 
             WHERE status = 'active' 
             AND end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 3 DAY)"
        );
        
        foreach ($expiring_users as $user) {
            $this->send_expiry_warning($user->user_id);
        }
        
        // Users expired today
        $expired_users = $wpdb->get_results(
            "SELECT user_id FROM $table_users 
             WHERE status = 'active' 
             AND end_date < NOW()"
        );
        
        foreach ($expired_users as $user) {
            $this->send_expired_notification($user->user_id);
            
            // Update status to expired
            $wpdb->update(
                $table_users,
                array('status' => 'expired'),
                array('user_id' => $user->user_id)
            );
        }
    }
    
    /**
     * Maybe create notifications table
     */
    private function maybe_create_notifications_table() {
        global $wpdb;
        
        $table_notifications = $wpdb->prefix . 'deshiflix_notifications';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_notifications (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11) NOT NULL,
            type varchar(50) NOT NULL,
            title varchar(255) NOT NULL,
            message text NOT NULL,
            icon varchar(10) DEFAULT '📢',
            action_url varchar(500),
            post_id int(11),
            priority enum('low','normal','high') DEFAULT 'normal',
            is_read tinyint(1) DEFAULT 0,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY is_read (is_read),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Get welcome email template
     */
    private function get_welcome_email_template($user) {
        return "
        <h2>স্বাগতম {$user->display_name}! 🎉</h2>
        <p>আপনার DeshiFlix Premium subscription সফলভাবে সক্রিয় হয়েছে!</p>
        <h3>এখন আপনি পাবেন:</h3>
        <ul>
            <li>🎬 সব Premium content</li>
            <li>📱 HD/4K quality</li>
            <li>⬇️ Download links</li>
            <li>🚫 Ad-free experience</li>
            <li>⚡ Instant downloads</li>
        </ul>
        <p><a href='" . home_url('/premium-dashboard/') . "'>Premium Dashboard দেখুন</a></p>
        ";
    }
    
    /**
     * Get expiry warning email template
     */
    private function get_expiry_warning_email_template($user, $days_left) {
        return "
        <h2>সতর্কতা: Subscription শেষ হওয়ার {$days_left} দিন বাকি! ⏰</h2>
        <p>প্রিয় {$user->display_name},</p>
        <p>আপনার Premium subscription আর {$days_left} দিন পর শেষ হবে।</p>
        <p>Service বন্ধ না হওয়ার জন্য এখনই renew করুন।</p>
        <p><a href='" . home_url('/premium-plans/') . "'>এখনই Renew করুন</a></p>
        ";
    }
    
    /**
     * Get expired email template
     */
    private function get_expired_email_template($user) {
        return "
        <h2>আপনার Premium Subscription শেষ হয়ে গেছে 😔</h2>
        <p>প্রিয় {$user->display_name},</p>
        <p>আপনার Premium subscription শেষ হয়ে গেছে।</p>
        <p>আবার Premium features পেতে নতুন subscription নিন।</p>
        <p><a href='" . home_url('/premium-plans/') . "'>নতুন Plan কিনুন</a></p>
        ";
    }
    
    /**
     * Add notification center to admin bar
     */
    public function add_notification_center($wp_admin_bar) {
        if (!is_user_logged_in()) {
            return;
        }
        
        global $wpdb;
        $table_notifications = $wpdb->prefix . 'deshiflix_notifications';
        $user_id = get_current_user_id();
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_notifications'");
        if (!$table_exists) {
            return;
        }
        
        $unread_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_notifications WHERE user_id = %d AND is_read = 0",
            $user_id
        ));
        
        $badge = $unread_count > 0 ? " <span class='notification-badge'>{$unread_count}</span>" : '';
        
        $wp_admin_bar->add_node(array(
            'id' => 'premium-notifications',
            'title' => '🔔 Notifications' . $badge,
            'href' => '#',
            'meta' => array('class' => 'premium-notification-center')
        ));
    }
    
    /**
     * Add notification center scripts
     */
    public function add_notification_center_scripts() {
        if (!is_user_logged_in()) {
            return;
        }
        ?>
        <style>
        .notification-badge {
            background: #ff4444;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 11px;
            margin-left: 5px;
        }
        </style>
        <?php
    }
}

// Initialize notification system
DeshiFlix_Premium_Notifications::get_instance();
