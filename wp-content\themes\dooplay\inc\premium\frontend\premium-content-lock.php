<?php
/**
 * DeshiFlix Premium System - Content Lock Frontend
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Premium Content Lock Frontend Class
 */
class DeshiFlix_Premium_Content_Lock {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize content lock
     */
    private function init() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Content filtering
        add_filter('the_content', array($this, 'filter_premium_content'), 999);
        add_filter('dooplay_player_content', array($this, 'filter_player_content'), 999);
        add_filter('dooplay_download_links', array($this, 'filter_download_links'), 999, 2);
        
        // Video player hooks
        add_action('wp_head', array($this, 'add_content_protection_styles'));
        add_action('wp_footer', array($this, 'add_content_protection_scripts'));
        
        // AJAX handlers
        add_action('wp_ajax_check_premium_access', array($this, 'ajax_check_premium_access'));
        add_action('wp_ajax_nopriv_check_premium_access', array($this, 'ajax_check_premium_access'));
        add_action('wp_ajax_unlock_premium_content', array($this, 'ajax_unlock_premium_content'));
        add_action('wp_ajax_nopriv_unlock_premium_content', array($this, 'ajax_unlock_premium_content'));
        
        // Early access handling
        add_filter('the_content', array($this, 'handle_early_access_content'), 998);
    }
    
    /**
     * Filter premium content
     */
    public function filter_premium_content($content) {
        global $post;
        
        if (!$post || is_admin() || !in_array($post->post_type, array('movies', 'tvshows', 'episodes'))) {
            return $content;
        }
        
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        
        // Check if content is premium
        if (!$content_manager->is_premium_content($post->ID)) {
            return $content;
        }
        
        // Check user access
        $user_id = get_current_user_id();
        if ($content_manager->user_has_content_access($post->ID, $user_id)) {
            return $this->add_premium_content_wrapper($content, $post->ID);
        }
        
        // Show lock overlay
        return $this->get_premium_lock_overlay($post->ID);
    }
    
    /**
     * Filter player content
     */
    public function filter_player_content($player_content) {
        global $post;
        
        if (!$post) {
            return $player_content;
        }
        
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        
        // Check if content is premium
        if (!$content_manager->is_premium_content($post->ID)) {
            return $player_content;
        }
        
        // Check user access
        $user_id = get_current_user_id();
        if (!$content_manager->user_has_content_access($post->ID, $user_id)) {
            return $this->get_premium_player_overlay($post->ID);
        }
        
        return $this->add_premium_player_protection($player_content, $post->ID);
    }
    
    /**
     * Filter download links
     */
    public function filter_download_links($links, $post_id) {
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        
        // Check if content is premium
        if (!$content_manager->is_premium_content($post_id)) {
            return $links;
        }
        
        // Check user access
        $user_id = get_current_user_id();
        if (!$content_manager->user_has_content_access($post_id, $user_id)) {
            return array(); // No download links for non-premium users
        }
        
        // Check if user has download feature
        $features = DeshiFlix_Premium_Features::get_instance();
        if (!$features->user_has_feature($user_id, 'download_links')) {
            return array(); // User plan doesn't include downloads
        }
        
        return $this->protect_download_links($links, $post_id);
    }
    
    /**
     * Handle early access content
     */
    public function handle_early_access_content($content) {
        global $post;
        
        if (!$post || is_admin()) {
            return $content;
        }
        
        $unlock_date = get_post_meta($post->ID, '_premium_unlock_date', true);
        
        if (!$unlock_date) {
            return $content;
        }
        
        $current_time = current_time('timestamp');
        $unlock_time = strtotime($unlock_date);
        
        // If content is still locked for early access
        if ($current_time < $unlock_time) {
            $user_id = get_current_user_id();
            
            // Check if user has early access feature
            $features = DeshiFlix_Premium_Features::get_instance();
            if (!$features->user_has_feature($user_id, 'early_access')) {
                return $this->get_early_access_overlay($post->ID, $unlock_date);
            }
        }
        
        return $content;
    }
    
    /**
     * Get premium lock overlay
     */
    private function get_premium_lock_overlay($post_id) {
        $premium_level = get_post_meta($post_id, '_premium_level', true);
        $features = get_post_meta($post_id, '_premium_features', true);
        
        ob_start();
        ?>
        <div class="premium-content-lock" data-post-id="<?php echo $post_id; ?>">
            <div class="lock-overlay">
                <div class="lock-content">
                    <div class="lock-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    
                    <h3 class="lock-title">
                        <?php _e('Premium Content', 'deshiflix'); ?>
                    </h3>
                    
                    <p class="lock-description">
                        <?php _e('This content is available for premium subscribers only.', 'deshiflix'); ?>
                    </p>
                    
                    <?php if ($premium_level): ?>
                    <div class="required-plan">
                        <span class="plan-badge plan-<?php echo esc_attr($premium_level); ?>">
                            <?php echo ucfirst($premium_level); ?> <?php _e('Plan Required', 'deshiflix'); ?>
                        </span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (is_array($features) && !empty($features)): ?>
                    <div class="premium-features">
                        <h4><?php _e('Premium Features:', 'deshiflix'); ?></h4>
                        <ul class="features-list">
                            <?php foreach ($features as $feature): ?>
                            <li>
                                <i class="fas fa-check"></i>
                                <?php echo $this->get_feature_label($feature); ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                    
                    <div class="lock-actions">
                        <?php if (is_user_logged_in()): ?>
                            <a href="/premium-plans/" class="btn btn-premium">
                                <i class="fas fa-crown"></i>
                                <?php _e('Upgrade to Premium', 'deshiflix'); ?>
                            </a>
                        <?php else: ?>
                            <a href="<?php echo wp_login_url(get_permalink()); ?>" class="btn btn-login">
                                <i class="fas fa-sign-in-alt"></i>
                                <?php _e('Login to Continue', 'deshiflix'); ?>
                            </a>
                            <a href="/premium-plans/" class="btn btn-premium">
                                <i class="fas fa-crown"></i>
                                <?php _e('Get Premium', 'deshiflix'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .premium-content-lock {
            position: relative;
            min-height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .lock-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }
        
        .lock-content {
            text-align: center;
            color: white;
            max-width: 500px;
        }
        
        .lock-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .lock-title {
            font-size: 2rem;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .lock-description {
            font-size: 1.1rem;
            margin-bottom: 25px;
            opacity: 0.9;
        }
        
        .required-plan {
            margin-bottom: 25px;
        }
        
        .plan-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .plan-badge.plan-basic {
            background: #28a745;
        }
        
        .plan-badge.plan-standard {
            background: #ffc107;
            color: #333;
        }
        
        .plan-badge.plan-pro {
            background: #dc3545;
        }
        
        .premium-features {
            margin-bottom: 30px;
            text-align: left;
        }
        
        .premium-features h4 {
            text-align: center;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .features-list li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .features-list i {
            color: #28a745;
        }
        
        .lock-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-premium {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
        }
        
        .btn-premium:hover {
            background: linear-gradient(45deg, #ffed4e, #ffd700);
            transform: translateY(-2px);
        }
        
        .btn-login {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-login:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }
        
        @media (max-width: 768px) {
            .lock-content {
                padding: 20px;
            }
            
            .lock-icon {
                font-size: 3rem;
            }
            
            .lock-title {
                font-size: 1.5rem;
            }
            
            .lock-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }
        }
        </style>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get premium player overlay
     */
    private function get_premium_player_overlay($post_id) {
        ob_start();
        ?>
        <div class="premium-player-lock" data-post-id="<?php echo $post_id; ?>">
            <div class="player-lock-overlay">
                <div class="player-lock-content">
                    <div class="lock-icon">
                        <i class="fas fa-play-circle"></i>
                        <i class="fas fa-lock lock-badge"></i>
                    </div>
                    
                    <h3><?php _e('Premium Video', 'deshiflix'); ?></h3>
                    <p><?php _e('Upgrade to premium to watch this video', 'deshiflix'); ?></p>
                    
                    <a href="/premium-plans/" class="btn btn-unlock">
                        <i class="fas fa-crown"></i>
                        <?php _e('Unlock Now', 'deshiflix'); ?>
                    </a>
                </div>
            </div>
        </div>
        
        <style>
        .premium-player-lock {
            position: relative;
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .player-lock-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .player-lock-content {
            text-align: center;
            color: white;
        }
        
        .player-lock-content .lock-icon {
            position: relative;
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .lock-badge {
            position: absolute;
            bottom: -5px;
            right: -5px;
            font-size: 1.5rem;
            background: #dc3545;
            border-radius: 50%;
            padding: 5px;
        }
        
        .player-lock-content h3 {
            margin-bottom: 10px;
            font-size: 1.5rem;
        }
        
        .player-lock-content p {
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        .btn-unlock {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-unlock:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }
        </style>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get early access overlay
     */
    private function get_early_access_overlay($post_id, $unlock_date) {
        $unlock_time = strtotime($unlock_date);
        $time_left = $unlock_time - current_time('timestamp');
        
        ob_start();
        ?>
        <div class="early-access-lock" data-post-id="<?php echo $post_id; ?>" data-unlock-time="<?php echo $unlock_time; ?>">
            <div class="early-access-overlay">
                <div class="early-access-content">
                    <div class="early-access-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    
                    <h3><?php _e('Early Access Content', 'deshiflix'); ?></h3>
                    <p><?php _e('This content will be available for all premium users on:', 'deshiflix'); ?></p>
                    
                    <div class="unlock-date">
                        <?php echo date('F j, Y \a\t g:i A', $unlock_time); ?>
                    </div>
                    
                    <div class="countdown-timer" id="countdown-<?php echo $post_id; ?>">
                        <div class="countdown-item">
                            <span class="countdown-number" id="days">0</span>
                            <span class="countdown-label"><?php _e('Days', 'deshiflix'); ?></span>
                        </div>
                        <div class="countdown-item">
                            <span class="countdown-number" id="hours">0</span>
                            <span class="countdown-label"><?php _e('Hours', 'deshiflix'); ?></span>
                        </div>
                        <div class="countdown-item">
                            <span class="countdown-number" id="minutes">0</span>
                            <span class="countdown-label"><?php _e('Minutes', 'deshiflix'); ?></span>
                        </div>
                        <div class="countdown-item">
                            <span class="countdown-number" id="seconds">0</span>
                            <span class="countdown-label"><?php _e('Seconds', 'deshiflix'); ?></span>
                        </div>
                    </div>
                    
                    <div class="early-access-cta">
                        <p><?php _e('Want early access to new content?', 'deshiflix'); ?></p>
                        <a href="/premium-plans/" class="btn btn-early-access">
                            <i class="fas fa-star"></i>
                            <?php _e('Upgrade to Pro', 'deshiflix'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
        (function() {
            var unlockTime = <?php echo $unlock_time; ?> * 1000; // Convert to milliseconds
            var countdownElement = document.getElementById('countdown-<?php echo $post_id; ?>');
            
            function updateCountdown() {
                var now = new Date().getTime();
                var timeLeft = unlockTime - now;
                
                if (timeLeft > 0) {
                    var days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
                    var hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    var minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                    var seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                    
                    countdownElement.querySelector('#days').textContent = days;
                    countdownElement.querySelector('#hours').textContent = hours;
                    countdownElement.querySelector('#minutes').textContent = minutes;
                    countdownElement.querySelector('#seconds').textContent = seconds;
                } else {
                    // Time's up, reload the page
                    location.reload();
                }
            }
            
            updateCountdown();
            setInterval(updateCountdown, 1000);
        })();
        </script>
        
        <style>
        .early-access-lock {
            position: relative;
            min-height: 500px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .early-access-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }
        
        .early-access-content {
            text-align: center;
            color: white;
            max-width: 600px;
        }
        
        .early-access-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #ffd700;
        }
        
        .early-access-content h3 {
            font-size: 2rem;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .early-access-content p {
            font-size: 1.1rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .unlock-date {
            font-size: 1.3rem;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 30px;
        }
        
        .countdown-timer {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .countdown-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            min-width: 80px;
        }
        
        .countdown-number {
            display: block;
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
        }
        
        .countdown-label {
            display: block;
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .early-access-cta p {
            margin-bottom: 15px;
        }
        
        .btn-early-access {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-early-access:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }
        
        @media (max-width: 768px) {
            .countdown-timer {
                flex-wrap: wrap;
                gap: 10px;
            }
            
            .countdown-item {
                min-width: 60px;
                padding: 10px;
            }
            
            .countdown-number {
                font-size: 1.5rem;
            }
        }
        </style>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Add premium content wrapper
     */
    private function add_premium_content_wrapper($content, $post_id) {
        return '<div class="premium-content-wrapper" data-post-id="' . $post_id . '">' . $content . '</div>';
    }
    
    /**
     * Add premium player protection
     */
    private function add_premium_player_protection($player_content, $post_id) {
        return '<div class="premium-player-wrapper" data-post-id="' . $post_id . '">' . $player_content . '</div>';
    }
    
    /**
     * Protect download links
     */
    private function protect_download_links($links, $post_id) {
        $protected_links = array();
        
        foreach ($links as $quality => $url) {
            // Generate secure download token
            $token = wp_create_nonce('download_' . $post_id . '_' . $quality);
            
            // Create protected download URL
            $protected_url = add_query_arg(array(
                'premium_download' => $post_id,
                'quality' => $quality,
                'token' => $token
            ), home_url('/'));
            
            $protected_links[$quality] = $protected_url;
        }
        
        return $protected_links;
    }
    
    /**
     * Get feature label
     */
    private function get_feature_label($feature) {
        $labels = array(
            'hd_quality' => __('HD Quality', 'deshiflix'),
            'ad_free' => __('Ad-Free Experience', 'deshiflix'),
            'download_links' => __('Download Links', 'deshiflix'),
            'multiple_servers' => __('Multiple Servers', 'deshiflix'),
            'early_access' => __('Early Access', 'deshiflix'),
            'exclusive_content' => __('Exclusive Content', 'deshiflix')
        );
        
        return $labels[$feature] ?? ucfirst(str_replace('_', ' ', $feature));
    }
    
    /**
     * Add content protection styles
     */
    public function add_content_protection_styles() {
        if (!$this->should_add_protection()) {
            return;
        }
        
        ?>
        <style>
        /* Premium content protection styles */
        .premium-content-wrapper,
        .premium-player-wrapper {
            position: relative;
        }
        
        .premium-content-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: transparent;
            z-index: 1;
            pointer-events: none;
        }
        
        /* Disable text selection on premium content */
        .premium-content-wrapper,
        .premium-player-wrapper {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        /* Disable image dragging */
        .premium-content-wrapper img,
        .premium-player-wrapper img {
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
            user-drag: none;
        }
        
        /* Video protection */
        .premium-player-wrapper video {
            pointer-events: auto;
        }
        
        .premium-player-wrapper video::-webkit-media-controls-download-button {
            display: none;
        }
        
        .premium-player-wrapper video::-webkit-media-controls-fullscreen-button {
            display: none;
        }
        </style>
        <?php
    }
    
    /**
     * Add content protection scripts
     */
    public function add_content_protection_scripts() {
        if (!$this->should_add_protection()) {
            return;
        }
        
        ?>
        <script>
        (function() {
            'use strict';
            
            // Disable right-click on premium content
            document.addEventListener('contextmenu', function(e) {
                if (e.target.closest('.premium-content-wrapper, .premium-player-wrapper')) {
                    e.preventDefault();
                    showProtectionMessage('Right-click is disabled on premium content');
                    return false;
                }
            });
            
            // Disable keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                var premiumElement = e.target.closest('.premium-content-wrapper, .premium-player-wrapper');
                
                if (premiumElement) {
                    // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
                    if (e.keyCode === 123 || 
                        (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) ||
                        (e.ctrlKey && e.keyCode === 85)) {
                        e.preventDefault();
                        showProtectionMessage('Developer tools are disabled');
                        return false;
                    }
                    
                    // Disable Ctrl+S (save)
                    if (e.ctrlKey && e.keyCode === 83) {
                        e.preventDefault();
                        showProtectionMessage('Saving is disabled');
                        return false;
                    }
                    
                    // Disable Print Screen
                    if (e.keyCode === 44) {
                        showProtectionMessage('Screenshots are not allowed');
                    }
                }
            });
            
            // Disable drag and drop
            document.addEventListener('dragstart', function(e) {
                if (e.target.closest('.premium-content-wrapper, .premium-player-wrapper')) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // Show protection message
            function showProtectionMessage(message) {
                var notification = document.createElement('div');
                notification.className = 'premium-protection-notification';
                notification.textContent = message;
                notification.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #dc3545; color: white; padding: 15px 20px; border-radius: 5px; z-index: 9999; font-size: 14px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
                
                document.body.appendChild(notification);
                
                setTimeout(function() {
                    notification.style.opacity = '0';
                    setTimeout(function() {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }
            
            // Monitor for developer tools
            var devtools = {
                open: false,
                orientation: null
            };
            
            setInterval(function() {
                if (window.outerHeight - window.innerHeight > 200 || 
                    window.outerWidth - window.innerWidth > 200) {
                    if (!devtools.open) {
                        devtools.open = true;
                        console.clear();
                        console.log('%cContent Protection Active!', 'color: red; font-size: 20px; font-weight: bold;');
                        console.log('%cThis content is protected by DeshiFlix Premium', 'color: red; font-size: 14px;');
                    }
                } else {
                    devtools.open = false;
                }
            }, 500);
            
        })();
        </script>
        <?php
    }
    
    /**
     * Check if protection should be added
     */
    private function should_add_protection() {
        global $post;
        
        if (!$post || is_admin()) {
            return false;
        }
        
        // Only add protection on single posts
        if (!is_single()) {
            return false;
        }
        
        // Only for supported post types
        if (!in_array($post->post_type, array('movies', 'tvshows', 'episodes'))) {
            return false;
        }
        
        // Check if content is premium
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        return $content_manager->is_premium_content($post->ID);
    }
    
    /**
     * AJAX check premium access
     */
    public function ajax_check_premium_access() {
        check_ajax_referer('premium_content_nonce', 'nonce');
        
        $post_id = intval($_POST['post_id']);
        $user_id = get_current_user_id();
        
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        $has_access = $content_manager->user_has_content_access($post_id, $user_id);
        $is_premium = $content_manager->is_premium_content($post_id);
        
        wp_send_json_success(array(
            'has_access' => $has_access,
            'is_premium' => $is_premium,
            'user_premium' => deshiflix_premium()->is_user_premium($user_id)
        ));
    }
    
    /**
     * AJAX unlock premium content
     */
    public function ajax_unlock_premium_content() {
        check_ajax_referer('premium_unlock_nonce', 'nonce');
        
        if (!is_user_logged_in()) {
            wp_send_json_error(array('message' => __('Please login first', 'deshiflix')));
        }
        
        $post_id = intval($_POST['post_id']);
        $user_id = get_current_user_id();
        
        // Check if user has premium access
        if (!deshiflix_premium()->is_user_premium($user_id)) {
            wp_send_json_error(array('message' => __('Premium subscription required', 'deshiflix')));
        }
        
        // Verify content access
        $content_manager = DeshiFlix_Premium_Content::get_instance();
        $has_access = $content_manager->user_has_content_access($post_id, $user_id);
        
        if ($has_access) {
            wp_send_json_success(array('message' => __('Content unlocked', 'deshiflix')));
        } else {
            wp_send_json_error(array('message' => __('Access denied', 'deshiflix')));
        }
    }
}

// Initialize content lock
DeshiFlix_Premium_Content_Lock::get_instance();
