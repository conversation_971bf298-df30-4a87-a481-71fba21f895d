<?php
/**
 * Premium Watchlist Features
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Premium_Watchlist {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Enhanced watchlist for premium users
        add_action('wp_ajax_add_to_premium_watchlist', array($this, 'add_to_watchlist'));
        add_action('wp_ajax_remove_from_premium_watchlist', array($this, 'remove_from_watchlist'));
        add_action('wp_ajax_get_premium_watchlist', array($this, 'get_watchlist'));
        
        // Premium watchlist features
        add_action('wp_footer', array($this, 'add_watchlist_scripts'));
        add_filter('dooplay_watchlist_limit', array($this, 'increase_watchlist_limit'), 10, 2);
    }
    
    /**
     * Add to premium watchlist
     */
    public function add_to_watchlist() {
        if (!wp_verify_nonce($_POST['nonce'], 'premium_watchlist_nonce')) {
            wp_die('Security check failed');
        }
        
        $user_id = get_current_user_id();
        $post_id = intval($_POST['post_id']);
        
        if (!$user_id) {
            wp_send_json_error('User not logged in');
        }
        
        // Get current watchlist
        $watchlist = get_user_meta($user_id, '_premium_watchlist', true) ?: array();
        
        // Check limits
        $is_premium = function_exists('deshiflix_premium') && deshiflix_premium()->is_user_premium($user_id);
        $limit = $is_premium ? 500 : 50; // Premium users get 10x more
        
        if (count($watchlist) >= $limit && !in_array($post_id, $watchlist)) {
            wp_send_json_error('Watchlist limit reached. Upgrade to premium for unlimited watchlist.');
        }
        
        // Add to watchlist
        if (!in_array($post_id, $watchlist)) {
            $watchlist[] = $post_id;
            update_user_meta($user_id, '_premium_watchlist', $watchlist);
            
            // Track activity for premium users
            if ($is_premium) {
                $this->track_watchlist_activity($user_id, $post_id, 'added');
            }
        }
        
        wp_send_json_success(array(
            'message' => 'Added to watchlist',
            'count' => count($watchlist),
            'limit' => $limit
        ));
    }
    
    /**
     * Remove from watchlist
     */
    public function remove_from_watchlist() {
        if (!wp_verify_nonce($_POST['nonce'], 'premium_watchlist_nonce')) {
            wp_die('Security check failed');
        }
        
        $user_id = get_current_user_id();
        $post_id = intval($_POST['post_id']);
        
        if (!$user_id) {
            wp_send_json_error('User not logged in');
        }
        
        $watchlist = get_user_meta($user_id, '_premium_watchlist', true) ?: array();
        $watchlist = array_diff($watchlist, array($post_id));
        
        update_user_meta($user_id, '_premium_watchlist', $watchlist);
        
        // Track activity for premium users
        $is_premium = function_exists('deshiflix_premium') && deshiflix_premium()->is_user_premium($user_id);
        if ($is_premium) {
            $this->track_watchlist_activity($user_id, $post_id, 'removed');
        }
        
        wp_send_json_success(array(
            'message' => 'Removed from watchlist',
            'count' => count($watchlist)
        ));
    }
    
    /**
     * Get premium watchlist
     */
    public function get_watchlist() {
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            wp_send_json_error('User not logged in');
        }
        
        $watchlist = get_user_meta($user_id, '_premium_watchlist', true) ?: array();
        $is_premium = function_exists('deshiflix_premium') && deshiflix_premium()->is_user_premium($user_id);
        
        $formatted_watchlist = array();
        
        foreach ($watchlist as $post_id) {
            $post = get_post($post_id);
            if ($post) {
                $formatted_watchlist[] = array(
                    'id' => $post_id,
                    'title' => $post->post_title,
                    'poster' => get_the_post_thumbnail_url($post_id, 'medium'),
                    'permalink' => get_permalink($post_id),
                    'type' => get_post_type($post_id),
                    'added_date' => get_post_meta($post_id, '_watchlist_added_date_' . $user_id, true)
                );
            }
        }
        
        wp_send_json_success(array(
            'watchlist' => $formatted_watchlist,
            'count' => count($formatted_watchlist),
            'is_premium' => $is_premium,
            'limit' => $is_premium ? 500 : 50
        ));
    }
    
    /**
     * Increase watchlist limit for premium users
     */
    public function increase_watchlist_limit($limit, $user_id) {
        $is_premium = function_exists('deshiflix_premium') && deshiflix_premium()->is_user_premium($user_id);
        return $is_premium ? 500 : 50;
    }
    
    /**
     * Track watchlist activity
     */
    private function track_watchlist_activity($user_id, $post_id, $action) {
        global $wpdb;
        
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        $wpdb->insert(
            $table_analytics,
            array(
                'user_id' => $user_id,
                'event_type' => 'watchlist_' . $action,
                'event_data' => json_encode(array(
                    'post_id' => $post_id,
                    'post_title' => get_the_title($post_id),
                    'action' => $action,
                    'timestamp' => current_time('mysql')
                )),
                'post_id' => $post_id,
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT']
            )
        );
    }
    
    /**
     * Add watchlist scripts
     */
    public function add_watchlist_scripts() {
        if (!is_user_logged_in()) {
            return;
        }
        ?>
        <script>
        jQuery(document).ready(function($) {
            // Enhanced watchlist functionality
            $('.premium-watchlist-btn').click(function() {
                var $btn = $(this);
                var postId = $btn.data('post-id');
                var action = $btn.hasClass('in-watchlist') ? 'remove_from_premium_watchlist' : 'add_to_premium_watchlist';
                
                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: {
                        action: action,
                        post_id: postId,
                        nonce: '<?php echo wp_create_nonce('premium_watchlist_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $btn.toggleClass('in-watchlist');
                            $btn.find('.watchlist-text').text(
                                $btn.hasClass('in-watchlist') ? 'Remove from Watchlist' : 'Add to Watchlist'
                            );
                            
                            // Update counter
                            $('.watchlist-counter').text(response.data.count);
                            
                            // Show notification
                            showNotification(response.data.message, 'success');
                        } else {
                            showNotification(response.data, 'error');
                        }
                    }
                });
            });
            
            function showNotification(message, type) {
                var notification = $('<div class="premium-notification ' + type + '">' + message + '</div>');
                $('body').append(notification);
                
                setTimeout(function() {
                    notification.fadeOut(function() {
                        $(this).remove();
                    });
                }, 3000);
            }
        });
        </script>
        
        <style>
        .premium-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            animation: slideIn 0.3s ease;
        }
        
        .premium-notification.success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }
        
        .premium-notification.error {
            background: linear-gradient(45deg, #f44336, #da190b);
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        
        .premium-watchlist-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .premium-watchlist-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        
        .premium-watchlist-btn.in-watchlist {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        </style>
        <?php
    }
}

// Initialize premium watchlist
DeshiFlix_Premium_Watchlist::get_instance();
