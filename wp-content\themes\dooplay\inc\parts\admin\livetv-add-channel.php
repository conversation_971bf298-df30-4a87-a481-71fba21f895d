<?php
// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

global $wpdb;

// Check if editing
$editing = isset($_GET['edit']) ? intval($_GET['edit']) : false;
$channel = null;

if ($editing) {
    $channel = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}doo_livetv_channels WHERE id = %d",
        $editing
    ));
    
    if (!$channel) {
        wp_die('Channel not found.');
    }
}

// Handle form submission
if (isset($_POST['submit'])) {
    $name = sanitize_text_field($_POST['name']);
    $slug = sanitize_title($_POST['slug'] ?: $name);
    $description = sanitize_textarea_field($_POST['description']);
    $stream_url = esc_url_raw($_POST['stream_url']);
    $backup_urls = sanitize_textarea_field($_POST['backup_urls']);
    $logo_url = esc_url_raw($_POST['logo_url']);
    $category_id = intval($_POST['category_id']) ?: null;
    $country = sanitize_text_field($_POST['country']);
    $language = sanitize_text_field($_POST['language']);
    $quality = sanitize_text_field($_POST['quality']);
    $status = sanitize_text_field($_POST['status']);
    $sort_order = intval($_POST['sort_order']);
    
    $data = array(
        'name' => $name,
        'slug' => $slug,
        'description' => $description,
        'stream_url' => $stream_url,
        'backup_urls' => $backup_urls,
        'logo_url' => $logo_url,
        'category_id' => $category_id,
        'country' => $country,
        'language' => $language,
        'quality' => $quality,
        'status' => $status,
        'sort_order' => $sort_order
    );
    
    if ($editing) {
        // Update existing channel
        $result = $wpdb->update(
            $wpdb->prefix . 'doo_livetv_channels',
            $data,
            array('id' => $editing),
            array('%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%d'),
            array('%d')
        );
        
        if ($result !== false) {
            echo '<div class="notice notice-success"><p>Channel updated successfully!</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>Failed to update channel.</p></div>';
        }
    } else {
        // Check if slug already exists
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$wpdb->prefix}doo_livetv_channels WHERE slug = %s",
            $slug
        ));
        
        if ($exists) {
            $slug = $slug . '-' . time();
            $data['slug'] = $slug;
        }
        
        // Insert new channel
        $result = $wpdb->insert(
            $wpdb->prefix . 'doo_livetv_channels',
            $data,
            array('%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%d')
        );
        
        if ($result) {
            echo '<div class="notice notice-success"><p>Channel added successfully!</p></div>';
            $editing = $wpdb->insert_id;
            $channel = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}doo_livetv_channels WHERE id = %d",
                $editing
            ));
        } else {
            echo '<div class="notice notice-error"><p>Failed to add channel.</p></div>';
        }
    }
}

// Get categories
$categories = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}doo_livetv_categories WHERE status = 'active' ORDER BY name ASC");
?>

<div class="wrap">
    <h1><?php echo $editing ? 'Edit Channel' : 'Add New Channel'; ?></h1>
    
    <form method="post" action="">
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="name">Channel Name *</label>
                </th>
                <td>
                    <input type="text" id="name" name="name" class="regular-text" 
                           value="<?php echo esc_attr($channel->name ?? ''); ?>" required>
                    <p class="description">Enter the channel name.</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="slug">Slug</label>
                </th>
                <td>
                    <input type="text" id="slug" name="slug" class="regular-text" 
                           value="<?php echo esc_attr($channel->slug ?? ''); ?>">
                    <p class="description">URL-friendly version of the name. Leave blank to auto-generate.</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="description">Description</label>
                </th>
                <td>
                    <textarea id="description" name="description" rows="3" class="large-text"><?php echo esc_textarea($channel->description ?? ''); ?></textarea>
                    <p class="description">Brief description of the channel.</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="stream_url">Stream URL *</label>
                </th>
                <td>
                    <input type="url" id="stream_url" name="stream_url" class="large-text" 
                           value="<?php echo esc_attr($channel->stream_url ?? ''); ?>" required>
                    <p class="description">Primary streaming URL (M3U8, RTMP, etc.)</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="backup_urls">Backup URLs</label>
                </th>
                <td>
                    <textarea id="backup_urls" name="backup_urls" rows="3" class="large-text"><?php echo esc_textarea($channel->backup_urls ?? ''); ?></textarea>
                    <p class="description">Alternative streaming URLs (one per line).</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="logo_url">Logo URL</label>
                </th>
                <td>
                    <input type="url" id="logo_url" name="logo_url" class="large-text" 
                           value="<?php echo esc_attr($channel->logo_url ?? ''); ?>">
                    <button type="button" class="button" id="upload-logo">Upload Logo</button>
                    <p class="description">Channel logo image URL.</p>
                    <?php if ($channel && $channel->logo_url): ?>
                        <div style="margin-top: 10px;">
                            <img src="<?php echo esc_url($channel->logo_url); ?>" 
                                 alt="Current logo" style="max-width: 100px; height: auto;">
                        </div>
                    <?php endif; ?>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="category_id">Category</label>
                </th>
                <td>
                    <select id="category_id" name="category_id">
                        <option value="">Select Category</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category->id; ?>" 
                                    <?php selected($channel->category_id ?? '', $category->id); ?>>
                                <?php echo esc_html($category->name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="description">Channel category for organization.</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="country">Country</label>
                </th>
                <td>
                    <input type="text" id="country" name="country" class="regular-text" 
                           value="<?php echo esc_attr($channel->country ?? ''); ?>">
                    <p class="description">Country of origin.</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="language">Language</label>
                </th>
                <td>
                    <input type="text" id="language" name="language" class="regular-text" 
                           value="<?php echo esc_attr($channel->language ?? ''); ?>">
                    <p class="description">Primary language of the channel.</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="quality">Quality</label>
                </th>
                <td>
                    <select id="quality" name="quality">
                        <option value="SD" <?php selected($channel->quality ?? 'HD', 'SD'); ?>>SD</option>
                        <option value="HD" <?php selected($channel->quality ?? 'HD', 'HD'); ?>>HD</option>
                        <option value="FHD" <?php selected($channel->quality ?? 'HD', 'FHD'); ?>>Full HD</option>
                        <option value="4K" <?php selected($channel->quality ?? 'HD', '4K'); ?>>4K</option>
                    </select>
                    <p class="description">Stream quality.</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="status">Status</label>
                </th>
                <td>
                    <select id="status" name="status">
                        <option value="active" <?php selected($channel->status ?? 'active', 'active'); ?>>Active</option>
                        <option value="inactive" <?php selected($channel->status ?? 'active', 'inactive'); ?>>Inactive</option>
                    </select>
                    <p class="description">Channel status.</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="sort_order">Sort Order</label>
                </th>
                <td>
                    <input type="number" id="sort_order" name="sort_order" class="small-text" 
                           value="<?php echo esc_attr($channel->sort_order ?? 0); ?>" min="0">
                    <p class="description">Display order (lower numbers appear first).</p>
                </td>
            </tr>
        </table>
        
        <p class="submit">
            <input type="submit" name="submit" class="button-primary" 
                   value="<?php echo $editing ? 'Update Channel' : 'Add Channel'; ?>">
            <a href="<?php echo admin_url('admin.php?page=doo-livetv'); ?>" class="button">Cancel</a>
            
            <?php if ($editing): ?>
                <a href="<?php echo home_url('/live-tv/?channel=' . $channel->slug); ?>"
                   class="button" target="_blank">View Channel</a>
            <?php endif; ?>
        </p>
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    // Auto-generate slug from name
    $('#name').on('input', function() {
        if (!$('#slug').val()) {
            var slug = $(this).val()
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');
            $('#slug').val(slug);
        }
    });
    
    // Media uploader for logo
    $('#upload-logo').on('click', function(e) {
        e.preventDefault();
        
        var mediaUploader = wp.media({
            title: 'Select Channel Logo',
            button: {
                text: 'Use this image'
            },
            multiple: false
        });
        
        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            $('#logo_url').val(attachment.url);
        });
        
        mediaUploader.open();
    });
});
</script>
