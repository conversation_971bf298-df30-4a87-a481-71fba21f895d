<?php
/*
* Auto Categorize Existing Channels
*/

// Security check
if (!defined('ABSPATH')) {
    exit;
}

// Check admin permissions
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

global $wpdb;
$table_channels = $wpdb->prefix . 'doo_livetv_channels';
$table_categories = $wpdb->prefix . 'doo_livetv_categories';

// Handle auto categorize request
if (isset($_POST['action']) && $_POST['action'] === 'auto_categorize') {
    check_ajax_referer('livetv_auto_categorize', 'nonce');
    
    $livetv = new DooLiveTV();
    
    // Get all channels without category or with wrong category
    $channels = $wpdb->get_results("
        SELECT * FROM $table_channels 
        WHERE category_id IS NULL OR category_id = 0
        ORDER BY name ASC
    ");
    
    $updated = 0;
    $results = array();
    
    foreach ($channels as $channel) {
        // Try to detect category from channel name
        $category_name = detect_category_from_name($channel->name);
        $country = detect_country_from_name($channel->name);
        
        if ($category_name) {
            $category_id = get_or_create_category($category_name);
            
            $wpdb->update(
                $table_channels,
                array(
                    'category_id' => $category_id,
                    'country' => $country ?: 'Bangladesh'
                ),
                array('id' => $channel->id),
                array('%d', '%s'),
                array('%d')
            );
            
            $updated++;
            $results[] = array(
                'name' => $channel->name,
                'category' => $category_name,
                'country' => $country ?: 'Bangladesh'
            );
        }
    }
    
    wp_send_json_success(array(
        'updated' => $updated,
        'results' => $results
    ));
}

function detect_category_from_name($name) {
    $name_lower = strtolower($name);
    
    // News channels
    if (preg_match('/\b(news|24|tv|channel|bangla|bangladesh|bd|desh)\b/', $name_lower)) {
        return 'News';
    }
    
    // Entertainment channels
    if (preg_match('/\b(entertainment|drama|serial|movie|cinema|film)\b/', $name_lower)) {
        return 'Entertainment';
    }
    
    // Sports channels
    if (preg_match('/\b(sports|cricket|football|soccer|game|match)\b/', $name_lower)) {
        return 'Sports';
    }
    
    // Music channels
    if (preg_match('/\b(music|song|gaan|sangeet|fm|radio)\b/', $name_lower)) {
        return 'Music';
    }
    
    // Kids channels
    if (preg_match('/\b(kids|cartoon|child|baby|junior)\b/', $name_lower)) {
        return 'Kids';
    }
    
    // Religious channels
    if (preg_match('/\b(islam|quran|hadith|religious|peace|deen)\b/', $name_lower)) {
        return 'Religious';
    }
    
    // Default category
    return 'General';
}

function detect_country_from_name($name) {
    $name_lower = strtolower($name);
    
    if (preg_match('/\b(bangla|bangladesh|bd|desh)\b/', $name_lower)) {
        return 'Bangladesh';
    }
    
    if (preg_match('/\b(india|indian|hindi|bollywood)\b/', $name_lower)) {
        return 'India';
    }
    
    if (preg_match('/\b(uk|british|england)\b/', $name_lower)) {
        return 'United Kingdom';
    }
    
    if (preg_match('/\b(usa|america|american|us)\b/', $name_lower)) {
        return 'United States';
    }
    
    return null;
}

function get_or_create_category($category_name) {
    global $wpdb;
    $table_categories = $wpdb->prefix . 'doo_livetv_categories';
    
    $slug = sanitize_title($category_name);
    
    // Check if category exists
    $category_id = $wpdb->get_var($wpdb->prepare(
        "SELECT id FROM $table_categories WHERE slug = %s",
        $slug
    ));
    
    if (!$category_id) {
        // Create new category
        $wpdb->insert(
            $table_categories,
            array(
                'name' => $category_name,
                'slug' => $slug,
                'description' => 'Auto-created category',
                'color' => '#007cba',
                'created_at' => current_time('mysql')
            )
        );
        $category_id = $wpdb->insert_id;
    }
    
    return $category_id;
}

// Get statistics
$total_channels = $wpdb->get_var("SELECT COUNT(*) FROM $table_channels");
$uncategorized = $wpdb->get_var("SELECT COUNT(*) FROM $table_channels WHERE category_id IS NULL OR category_id = 0");
$categorized = $total_channels - $uncategorized;
?>

<div class="wrap">
    <h1>🏷️ Auto Categorize Channels</h1>
    
    <div class="notice notice-info">
        <p><strong>Auto Categorization:</strong> This feature will automatically assign categories and countries to your channels based on their names.</p>
    </div>
    
    <div class="stats-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0;">
        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; text-align: center;">
            <div style="font-size: 2rem; font-weight: bold; color: #1976d2;"><?php echo $total_channels; ?></div>
            <div style="color: #666;">Total Channels</div>
        </div>
        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; text-align: center;">
            <div style="font-size: 2rem; font-weight: bold; color: #388e3c;"><?php echo $categorized; ?></div>
            <div style="color: #666;">Categorized</div>
        </div>
        <div style="background: #fff3e0; padding: 20px; border-radius: 8px; text-align: center;">
            <div style="font-size: 2rem; font-weight: bold; color: #f57c00;"><?php echo $uncategorized; ?></div>
            <div style="color: #666;">Uncategorized</div>
        </div>
    </div>
    
    <?php if ($uncategorized > 0): ?>
    <form id="auto-categorize-form">
        <?php wp_nonce_field('livetv_auto_categorize', 'nonce'); ?>
        
        <div style="background: #f9f9f9; border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3>🤖 Auto Categorization Rules</h3>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li><strong>News:</strong> Channels with "news", "24", "tv", "bangla", "bangladesh" in name</li>
                <li><strong>Entertainment:</strong> Channels with "entertainment", "drama", "movie", "cinema" in name</li>
                <li><strong>Sports:</strong> Channels with "sports", "cricket", "football", "game" in name</li>
                <li><strong>Music:</strong> Channels with "music", "song", "gaan", "fm", "radio" in name</li>
                <li><strong>Kids:</strong> Channels with "kids", "cartoon", "child", "baby" in name</li>
                <li><strong>Religious:</strong> Channels with "islam", "quran", "religious", "peace" in name</li>
                <li><strong>General:</strong> Default category for unmatched channels</li>
            </ul>
        </div>
        
        <p class="submit">
            <button type="submit" class="button button-primary" id="start-categorize">
                🏷️ Start Auto Categorization
            </button>
        </p>
    </form>
    
    <div id="categorize-progress" style="display: none; margin: 20px 0;">
        <h3>Categorization Progress</h3>
        <div id="progress-log" style="background: #f9f9f9; border: 1px solid #ddd; padding: 15px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
            <div>Starting categorization...</div>
        </div>
    </div>
    
    <div id="categorize-results" style="display: none; margin: 20px 0;">
        <div class="notice notice-success">
            <p><strong>Categorization Completed!</strong></p>
            <p id="results-summary"></p>
            <p>
                <a href="<?php echo admin_url('admin.php?page=doo-livetv'); ?>" class="button button-primary">
                    View Channels
                </a>
                <button type="button" class="button" onclick="location.reload();">
                    Refresh Stats
                </button>
            </p>
        </div>
    </div>
    
    <?php else: ?>
    <div class="notice notice-success">
        <p><strong>🎉 All channels are already categorized!</strong></p>
        <p>All your channels have been assigned to categories. You can manually edit categories if needed.</p>
        <p>
            <a href="<?php echo admin_url('admin.php?page=doo-livetv'); ?>" class="button button-primary">
                View Channels
            </a>
        </p>
    </div>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    $('#auto-categorize-form').submit(function(e) {
        e.preventDefault();
        
        $('#start-categorize').prop('disabled', true).text('🔄 Processing...');
        $('#categorize-progress').show();
        $('#categorize-results').hide();
        
        $('#progress-log').html('<div>Starting auto categorization...</div>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'doo_livetv_auto_categorize',
                nonce: $('input[name="nonce"]').val()
            },
            success: function(response) {
                if (response.success) {
                    showResults(response.data);
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                alert('AJAX error occurred');
            },
            complete: function() {
                $('#start-categorize').prop('disabled', false).text('🏷️ Start Auto Categorization');
            }
        });
    });
    
    function showResults(data) {
        $('#categorize-results').show();
        $('#results-summary').html(
            `Updated <strong>${data.updated}</strong> channels with automatic categories and countries.`
        );
        
        // Show detailed results
        let logHtml = '<div style="color: green;">✅ Categorization completed!</div>';
        logHtml += '<div style="margin: 10px 0; font-weight: bold;">Updated Channels:</div>';
        
        data.results.forEach(function(result) {
            logHtml += `<div>📺 ${result.name} → ${result.category} (${result.country})</div>`;
        });
        
        $('#progress-log').html(logHtml);
    }
});
</script>
