"use strict";(self.webpackChunkpayment_gateway_bkash_for_wc=self.webpackChunkpayment_gateway_bkash_for_wc||[]).push([[629],{1236:(e,t,a)=>{a.d(t,{A:()=>p});var o,r=a(6540),n=a(6645),d=a(6399),c=a(9508),l=a.n(c),s=a(6474),u=a(4040);function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,o=Array(t);a<t;a++)o[a]=e[a];return o}e=a.hmd(e),(o="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&o(e);var m="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default.signature:function(e){return e};function b(){var e,t,a=(e=(0,r.useState)(!1),t=2,function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var o,r,n,d,c=[],l=!0,s=!1;try{if(n=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;l=!1}else for(;!(l=(o=n.call(a)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=a.return&&(d=a.return(),Object(d)!==d))return}finally{if(s)throw r}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?i(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),o=a[0],c=a[1];return r.createElement("div",{id:"dc-bkash-upgrade-notice"},r.createElement("div",{id:"dc-bkash-upgrade-notice-icon"},r.createElement("div",{id:"dc-bkash-upgrade-notice-message"},r.createElement("div",{id:"dc-bkash-upgrade-notice-title"},r.createElement("p",null,r.createElement("strong",null,(0,n.__)("bKash Data Update Required","dc-bkash")))),r.createElement("div",{id:"dc-bkash-upgrade-notice-content"},r.createElement("p",null,(0,n.__)("We need to update your install to the latest version","dc-bkash"))),r.createElement(d.A,{type:"submit",className:"wc-update-now bg-bkash text-white",onClick:function(){return c(!0),void l()({path:u.n.v1.upgrade,method:"POST",data:{}}).then((function(e){c(!1),s.oR.success((0,n.__)("Updated Successfully!","dc-bkash")),document.querySelector(".dc-bkash-notice-info").classList.add("dc-bkash-notice-info-hide")})).catch((function(e){c(!1),s.oR.error(e.data.status+" : "+e.message)}))},isBusy:o,disabled:o},o?(0,n.__)("Updating","dc-bkash"):(0,n.__)("Update","dc-bkash")))))}s.oR.configure({position:"top-right",autoClose:5e3,closeOnClick:!1,pauseOnHover:!1,draggable:!1,closeButton:!1,style:{top:"3em"}}),m(b,"useState{[ isSubmitted, setIsSubmitted ](false)}");var f=b;const p=f;var h,y;(h="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(h.register(b,"Upgrades","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/upgrade/Pages/Upgrades.js"),h.register(f,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/upgrade/Pages/Upgrades.js")),(y="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&y(e)},4040:(e,t,a)=>{var o;a.d(t,{n:()=>c}),e=a.hmd(e),(o="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&o(e),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var r,n,d="/dc-bkash/v1",c={v1:{settings:d+"/settings",transactions:d+"/transactions",transactionSearch:d+"/transactions/?search=",getToken:d+"/payment/get-token",createPayment:d+"/payment/create-payment",queryPayment:d+"/payment/query-payment/",executePayment:d+"/payment/execute-payment/",docSearchPayment:d+"/payment/search-payment/",docRefundPayment:d+"/payment/refund-payment/",refund:d+"/transactions/refund",searchTransaction:d+"/payment/search-transaction/",upgrade:d+"/upgrade"}};(r="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(r.register(d,"v1","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/constants.js"),r.register(c,"API","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/constants.js")),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&n(e)},4093:(e,t,a)=>{a.d(t,{A:()=>s});var o,r=a(6540),n=a(3472),d=a(1236);function c(){return r.createElement(r.Fragment,null,r.createElement(d.A,null))}e=a.hmd(e),(o="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&o(e),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var l=(0,n.K)(c);const s=l;var u,i;(u="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(u.register(c,"App","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/upgrade/App.js"),u.register(l,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/upgrade/App.js")),(i="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&i(e)},4254:(e,t,a)=>{var o,r=a(6540),n=a(9627),d=a(4093);e=a.hmd(e),(o="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&o(e),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var c,l,s=document.getElementById("dc-bkash-upgrade-notice-container");n.render(r.createElement(d.A,null),s),(c="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&c.register(s,"mountNode","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/upgrade/index.js"),(l="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&l(e)},9508:e=>{e.exports=wp.apiFetch}},e=>{e.O(0,[96],(()=>e(e.s=4254))),e.O()}]);