<?php
/**
 * Test Headers - Check if header issues are resolved
 */

// Start output buffering
ob_start();

// Simulate WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/../../../../');
}

// Test basic PHP functionality
echo "Testing header functionality...\n";

// Test if we can set headers
if (!headers_sent()) {
    echo "✅ Headers not sent yet - Good!\n";
} else {
    echo "❌ Headers already sent - Problem!\n";
}

// Clean buffer
$output = ob_get_clean();

// Now we can safely output
echo $output;

echo "Header test completed.\n";
?>
