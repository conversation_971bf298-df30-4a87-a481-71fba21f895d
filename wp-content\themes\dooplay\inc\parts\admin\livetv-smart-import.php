<?php
/*
* Smart Import - Import only working channels
*/

// Security check
if (!defined('ABSPATH')) {
    exit;
}

// Check admin permissions
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

global $wpdb;
$table_channels = $wpdb->prefix . 'doo_livetv_channels';

// Handle smart import request
if (isset($_POST['action']) && $_POST['action'] === 'smart_import') {
    check_ajax_referer('livetv_smart_import', 'nonce');
    
    $playlist_url = sanitize_url($_POST['playlist_url']);
    $check_timeout = intval($_POST['check_timeout']) ?: 10;
    $max_channels = intval($_POST['max_channels']) ?: 50;
    
    if (empty($playlist_url)) {
        wp_send_json_error('Please provide a playlist URL');
    }
    
    // Parse M3U playlist
    $livetv = new DooLiveTV();
    $result = $livetv->parse_m3u_playlist($playlist_url);
    
    if (!$result['success']) {
        wp_send_json_error($result['message']);
    }
    
    $channels = array_slice($result['channels'], 0, $max_channels);
    $working_channels = array();
    $checked_count = 0;
    
    foreach ($channels as $channel) {
        $checked_count++;
        
        // Check if stream is working
        $check_result = check_stream_quick($channel['url'], $check_timeout);
        
        if ($check_result['status'] === 'working') {
            $working_channels[] = array(
                'name' => $channel['name'],
                'url' => $channel['url'],
                'group' => $channel['group'],
                'logo' => $channel['logo'],
                'check_result' => $check_result
            );
        }
        
        // Send progress update
        if ($checked_count % 5 === 0) {
            echo json_encode(array(
                'type' => 'progress',
                'checked' => $checked_count,
                'total' => count($channels),
                'working' => count($working_channels)
            )) . "\n";
            flush();
        }
    }
    
    // Import working channels
    $imported = 0;
    foreach ($working_channels as $channel) {
        $category_id = $livetv->get_or_create_category($channel['group']);
        
        $slug = sanitize_title($channel['name']);
        
        // Check if channel already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_channels WHERE slug = %s",
            $slug
        ));
        
        if (!$existing) {
            $wpdb->insert(
                $table_channels,
                array(
                    'name' => $channel['name'],
                    'slug' => $slug,
                    'stream_url' => $channel['url'],
                    'logo_url' => $channel['logo'],
                    'category_id' => $category_id,
                    'status' => 'active',
                    'views' => 0,
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                )
            );
            $imported++;
        }
    }
    
    wp_send_json_success(array(
        'checked' => $checked_count,
        'working' => count($working_channels),
        'imported' => $imported
    ));
}

function check_stream_quick($url, $timeout = 10) {
    $start_time = microtime(true);
    
    // Basic URL validation
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return array(
            'status' => 'invalid',
            'message' => 'Invalid URL',
            'response_time' => 0
        );
    }
    
    // Quick HEAD request
    $response = wp_remote_head($url, array(
        'timeout' => $timeout,
        'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'headers' => array(
            'Accept' => '*/*',
            'Cache-Control' => 'no-cache'
        )
    ));
    
    $end_time = microtime(true);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    if (is_wp_error($response)) {
        return array(
            'status' => 'error',
            'message' => 'Connection failed',
            'response_time' => $response_time
        );
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    
    if ($response_code >= 200 && $response_code < 400) {
        return array(
            'status' => 'working',
            'message' => 'Stream accessible',
            'response_time' => $response_time
        );
    } else {
        return array(
            'status' => 'error',
            'message' => 'HTTP ' . $response_code,
            'response_time' => $response_time
        );
    }
}
?>

<div class="wrap">
    <h1>🚀 Smart Import - Working Channels Only</h1>
    
    <div class="notice notice-info">
        <p><strong>Smart Import:</strong> This feature will test each channel before importing and only add working streams to your database.</p>
    </div>
    
    <form id="smart-import-form">
        <?php wp_nonce_field('livetv_smart_import', 'nonce'); ?>
        
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="playlist_url">M3U Playlist URL</label>
                </th>
                <td>
                    <input type="url" id="playlist_url" name="playlist_url" class="regular-text" required 
                           placeholder="https://example.com/playlist.m3u8">
                    <p class="description">Enter the URL of the M3U playlist you want to import</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="check_timeout">Check Timeout (seconds)</label>
                </th>
                <td>
                    <input type="number" id="check_timeout" name="check_timeout" value="10" min="5" max="30" class="small-text">
                    <p class="description">How long to wait when testing each stream (5-30 seconds)</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="max_channels">Max Channels to Check</label>
                </th>
                <td>
                    <input type="number" id="max_channels" name="max_channels" value="50" min="10" max="200" class="small-text">
                    <p class="description">Maximum number of channels to test (to avoid timeouts)</p>
                </td>
            </tr>
        </table>
        
        <p class="submit">
            <button type="submit" class="button button-primary" id="start-import">
                🚀 Start Smart Import
            </button>
            <button type="button" class="button" id="cancel-import" style="display: none;">
                ❌ Cancel Import
            </button>
        </p>
    </form>
    
    <div id="import-progress" style="display: none; margin: 20px 0;">
        <h3>Import Progress</h3>
        
        <div style="background: #f0f0f0; border-radius: 4px; padding: 10px; margin: 10px 0;">
            <div id="progress-bar" style="background: #007cba; height: 20px; border-radius: 4px; width: 0%; transition: width 0.3s;"></div>
            <div id="progress-text" style="text-align: center; margin-top: 5px;">Starting import...</div>
        </div>
        
        <div class="import-stats" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 15px 0;">
            <div style="background: #e3f2fd; padding: 15px; border-radius: 4px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #1976d2;" id="checked-count">0</div>
                <div style="color: #666;">Checked</div>
            </div>
            <div style="background: #e8f5e8; padding: 15px; border-radius: 4px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #388e3c;" id="working-count">0</div>
                <div style="color: #666;">Working</div>
            </div>
            <div style="background: #fff3e0; padding: 15px; border-radius: 4px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #f57c00;" id="imported-count">0</div>
                <div style="color: #666;">Imported</div>
            </div>
        </div>
        
        <div id="import-log" style="background: #f9f9f9; border: 1px solid #ddd; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;">
            <div>Import log will appear here...</div>
        </div>
    </div>
    
    <div id="import-results" style="display: none; margin: 20px 0;">
        <div class="notice notice-success">
            <p><strong>Import Completed!</strong></p>
            <p id="results-summary"></p>
            <p>
                <a href="<?php echo admin_url('admin.php?page=doo-livetv'); ?>" class="button button-primary">
                    View Imported Channels
                </a>
                <a href="<?php echo admin_url('admin.php?page=doo-livetv-channel-checker'); ?>" class="button">
                    Check Channel Status
                </a>
            </p>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    let isImporting = false;
    
    $('#smart-import-form').submit(function(e) {
        e.preventDefault();
        
        if (isImporting) return;
        
        isImporting = true;
        $('#start-import').prop('disabled', true).text('🔄 Importing...');
        $('#cancel-import').show();
        $('#import-progress').show();
        $('#import-results').hide();
        
        // Reset counters
        $('#checked-count').text('0');
        $('#working-count').text('0');
        $('#imported-count').text('0');
        $('#progress-bar').css('width', '0%');
        $('#import-log').html('<div>Starting import...</div>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'doo_livetv_smart_import',
                playlist_url: $('#playlist_url').val(),
                check_timeout: $('#check_timeout').val(),
                max_channels: $('#max_channels').val(),
                nonce: $('input[name="nonce"]').val()
            },
            success: function(response) {
                if (response.success) {
                    showResults(response.data);
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                alert('AJAX error occurred');
            },
            complete: function() {
                isImporting = false;
                $('#start-import').prop('disabled', false).text('🚀 Start Smart Import');
                $('#cancel-import').hide();
            }
        });
    });
    
    function showResults(data) {
        $('#import-results').show();
        $('#results-summary').html(
            `Checked <strong>${data.checked}</strong> channels, ` +
            `found <strong>${data.working}</strong> working streams, ` +
            `imported <strong>${data.imported}</strong> new channels.`
        );
        
        // Update final counts
        $('#checked-count').text(data.checked);
        $('#working-count').text(data.working);
        $('#imported-count').text(data.imported);
        $('#progress-bar').css('width', '100%');
        $('#progress-text').text('Import completed!');
        
        // Add to log
        $('#import-log').append(`<div style="color: green;">✅ Import completed successfully!</div>`);
    }
    
    $('#cancel-import').click(function() {
        if (confirm('Are you sure you want to cancel the import?')) {
            location.reload();
        }
    });
});
</script>
