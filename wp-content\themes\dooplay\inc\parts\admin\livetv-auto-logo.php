<?php
/*
* Auto Logo Detection for Channels
*/

// Security check
if (!defined('ABSPATH')) {
    exit;
}

// Check admin permissions
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

global $wpdb;
$table_channels = $wpdb->prefix . 'doo_livetv_channels';

// Handle auto logo request
if (isset($_POST['action']) && $_POST['action'] === 'auto_logo') {
    check_ajax_referer('livetv_auto_logo', 'nonce');
    
    // Get channels without logos
    $channels = $wpdb->get_results("
        SELECT * FROM $table_channels 
        WHERE logo_url IS NULL OR logo_url = '' 
        ORDER BY name ASC
    ");
    
    $updated = 0;
    $results = array();
    
    foreach ($channels as $channel) {
        $logo_url = find_channel_logo($channel->name);
        
        if ($logo_url) {
            $wpdb->update(
                $table_channels,
                array('logo_url' => $logo_url),
                array('id' => $channel->id),
                array('%s'),
                array('%d')
            );
            
            $updated++;
            $results[] = array(
                'name' => $channel->name,
                'logo' => $logo_url
            );
        }
    }
    
    wp_send_json_success(array(
        'updated' => $updated,
        'results' => $results
    ));
}

function find_channel_logo($channel_name) {
    // Common Bangladeshi channel logos
    $logo_mapping = array(
        // News Channels
        'somoy tv' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/78/Somoy_TV_logo.svg/512px-Somoy_TV_logo.svg.png',
        'channel i' => 'https://upload.wikimedia.org/wikipedia/en/thumb/8/88/Channel-i.svg/512px-Channel-i.svg.png',
        'independent tv' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/41/Independent_Television_Logo.svg/512px-Independent_Television_Logo.svg.png',
        'jamuna tv' => 'https://upload.wikimedia.org/wikipedia/en/thumb/3/39/Jamuna_Television_logo.jpg/512px-Jamuna_Television_logo.jpg',
        'news24' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/9a/News24_Logo.png/512px-News24_Logo.png',
        'channel 24' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/91/Channel24logo.svg/512px-Channel24logo.svg.png',
        'ekattor tv' => 'https://upload.wikimedia.org/wikipedia/en/thumb/5/57/Ekattor_TV_logo.svg/512px-Ekattor_TV_logo.svg.png',
        'dbc news' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/94/DBC_News_logo.png/512px-DBC_News_logo.png',
        
        // Entertainment Channels
        'ntv' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/ff/NTV_%28Bangladesh%29_logo.svg/512px-NTV_%28Bangladesh%29_logo.svg.png',
        'rtv' => 'https://upload.wikimedia.org/wikipedia/en/thumb/4/41/Rtv_bangladesh.PNG/512px-Rtv_bangladesh.PNG',
        'banglavision' => 'https://upload.wikimedia.org/wikipedia/en/thumb/8/8c/Banglavision.svg/512px-Banglavision.svg.png',
        'channel 9' => 'https://upload.wikimedia.org/wikipedia/en/thumb/f/ff/Channel9_bd.svg/512px-Channel9_bd.svg.png',
        'satv' => 'https://upload.wikimedia.org/wikipedia/en/thumb/1/15/SA_TV_Logo.png/512px-SA_TV_Logo.png',
        'boishakhi tv' => 'https://upload.wikimedia.org/wikipedia/en/thumb/6/61/Boishakhi_TV_logo.svg/512px-Boishakhi_TV_logo.svg.png',
        'asian tv' => 'https://upload.wikimedia.org/wikipedia/en/thumb/5/5c/Asian_TV_Logo.png/512px-Asian_TV_Logo.png',
        'bangla tv' => 'https://upload.wikimedia.org/wikipedia/en/thumb/8/8c/BanglaTV_Logo.png/512px-BanglaTV_Logo.png',
        
        // Sports Channels
        't sports' => 'https://upload.wikimedia.org/wikipedia/en/thumb/1/11/T_Sports_logo.png/512px-T_Sports_logo.png',
        'gazi tv' => 'https://upload.wikimedia.org/wikipedia/en/thumb/4/4c/Gazi_Television_Logo.png/512px-Gazi_Television_Logo.png',
        
        // Music Channels
        'gaan bangla' => 'https://upload.wikimedia.org/wikipedia/en/thumb/4/4e/Gaan_Bangla.svg/512px-Gaan_Bangla.svg.png',
        
        // Kids Channels
        'duronto tv' => 'https://upload.wikimedia.org/wikipedia/en/thumb/c/c3/Duronto_TV_Logo.png/512px-Duronto_TV_Logo.png',
        
        // Religious Channels
        'peace tv bangla' => 'https://upload.wikimedia.org/wikipedia/en/thumb/1/1b/Peace_TV_Bangla.png/512px-Peace_TV_Bangla.png',
        'iqra bangla' => 'https://upload.wikimedia.org/wikipedia/en/thumb/2/29/Iqra_Bangla_Logo.png/512px-Iqra_Bangla_Logo.png',
        
        // International Channels
        'star jalsha' => 'https://upload.wikimedia.org/wikipedia/en/thumb/d/d0/Star_Jalsha_logo.jpg/512px-Star_Jalsha_logo.jpg',
        'zee bangla' => 'https://upload.wikimedia.org/wikipedia/en/thumb/8/8c/Zee_Bangla.png/512px-Zee_Bangla.png',
        'colors bangla' => 'https://upload.wikimedia.org/wikipedia/en/thumb/8/8c/Colors_Bangla.png/512px-Colors_Bangla.png',
        'sony aath' => 'https://upload.wikimedia.org/wikipedia/en/thumb/5/53/Sony_Aath_new.png/512px-Sony_Aath_new.png',
        'star sports' => 'https://upload.wikimedia.org/wikipedia/en/thumb/5/51/Star_Sports_logo.svg/512px-Star_Sports_logo.svg.png',
        'sony six' => 'https://upload.wikimedia.org/wikipedia/en/thumb/7/7b/Sony_SIX_logo.png/512px-Sony_SIX_logo.png',
        'discovery' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/27/Discovery_Channel_logo.svg/512px-Discovery_Channel_logo.svg.png',
        'national geographic' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/fc/Natgeologo.svg/512px-Natgeologo.svg.png',
        'animal planet' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/20/2018_Animal_Planet_logo.svg/512px-2018_Animal_Planet_logo.svg.png',
        'history' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/01/History_%282021%29.svg/512px-History_%282021%29.svg.png',
        'cnn' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b1/CNN.svg/512px-CNN.svg.png',
        'bbc world' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/BBC_World_News_red_logo.svg/512px-BBC_World_News_red_logo.svg.png',
        'al jazeera' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/71/Al_Jazeera_English_newlogo.svg/512px-Al_Jazeera_English_newlogo.svg.png'
    );
    
    $channel_lower = strtolower(trim($channel_name));
    
    // Direct match
    if (isset($logo_mapping[$channel_lower])) {
        return $logo_mapping[$channel_lower];
    }
    
    // Partial match
    foreach ($logo_mapping as $key => $logo) {
        if (strpos($channel_lower, $key) !== false || strpos($key, $channel_lower) !== false) {
            return $logo;
        }
    }
    
    // Generate placeholder logo based on channel name
    return generate_placeholder_logo($channel_name);
}

function generate_placeholder_logo($channel_name) {
    // Create a simple placeholder logo URL
    $initials = get_channel_initials($channel_name);
    $color = generate_color_from_name($channel_name);
    
    // Use a placeholder service or return null for now
    return "https://via.placeholder.com/200x120/{$color}/ffffff?text=" . urlencode($initials);
}

function get_channel_initials($name) {
    $words = explode(' ', trim($name));
    $initials = '';
    
    foreach ($words as $word) {
        if (!empty($word)) {
            $initials .= strtoupper(substr($word, 0, 1));
            if (strlen($initials) >= 3) break; // Max 3 characters
        }
    }
    
    return $initials ?: strtoupper(substr($name, 0, 2));
}

function generate_color_from_name($name) {
    // Generate a consistent color based on channel name
    $hash = md5($name);
    return substr($hash, 0, 6);
}

// Get statistics
$total_channels = $wpdb->get_var("SELECT COUNT(*) FROM $table_channels");
$with_logos = $wpdb->get_var("SELECT COUNT(*) FROM $table_channels WHERE logo_url IS NOT NULL AND logo_url != ''");
$without_logos = $total_channels - $with_logos;
?>

<div class="wrap">
    <h1>🖼️ Auto Logo Detection</h1>
    
    <div class="notice notice-info">
        <p><strong>Auto Logo Detection:</strong> This feature will automatically find and assign logos to your channels based on their names.</p>
    </div>
    
    <div class="stats-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0;">
        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; text-align: center;">
            <div style="font-size: 2rem; font-weight: bold; color: #1976d2;"><?php echo $total_channels; ?></div>
            <div style="color: #666;">Total Channels</div>
        </div>
        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; text-align: center;">
            <div style="font-size: 2rem; font-weight: bold; color: #388e3c;"><?php echo $with_logos; ?></div>
            <div style="color: #666;">With Logos</div>
        </div>
        <div style="background: #fff3e0; padding: 20px; border-radius: 8px; text-align: center;">
            <div style="font-size: 2rem; font-weight: bold; color: #f57c00;"><?php echo $without_logos; ?></div>
            <div style="color: #666;">Without Logos</div>
        </div>
    </div>
    
    <?php if ($without_logos > 0): ?>
    <form id="auto-logo-form">
        <?php wp_nonce_field('livetv_auto_logo', 'nonce'); ?>
        
        <div style="background: #f9f9f9; border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3>🎯 Logo Detection Features</h3>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li><strong>Database Matching:</strong> Matches channel names with known logo URLs</li>
                <li><strong>Bangladeshi Channels:</strong> Extensive database of local channel logos</li>
                <li><strong>International Channels:</strong> Popular international channel logos</li>
                <li><strong>Placeholder Generation:</strong> Creates placeholder logos for unmatched channels</li>
                <li><strong>Smart Matching:</strong> Uses partial name matching for better results</li>
            </ul>
        </div>
        
        <p class="submit">
            <button type="submit" class="button button-primary" id="start-logo-detection">
                🖼️ Start Logo Detection
            </button>
        </p>
    </form>
    
    <div id="logo-progress" style="display: none; margin: 20px 0;">
        <h3>Logo Detection Progress</h3>
        <div id="progress-log" style="background: #f9f9f9; border: 1px solid #ddd; padding: 15px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
            <div>Starting logo detection...</div>
        </div>
    </div>
    
    <div id="logo-results" style="display: none; margin: 20px 0;">
        <div class="notice notice-success">
            <p><strong>Logo Detection Completed!</strong></p>
            <p id="results-summary"></p>
            <p>
                <a href="<?php echo admin_url('admin.php?page=doo-livetv'); ?>" class="button button-primary">
                    View Channels
                </a>
                <button type="button" class="button" onclick="location.reload();">
                    Refresh Stats
                </button>
            </p>
        </div>
    </div>
    
    <?php else: ?>
    <div class="notice notice-success">
        <p><strong>🎉 All channels have logos!</strong></p>
        <p>All your channels have been assigned logos. You can manually edit logos if needed.</p>
        <p>
            <a href="<?php echo admin_url('admin.php?page=doo-livetv'); ?>" class="button button-primary">
                View Channels
            </a>
        </p>
    </div>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    $('#auto-logo-form').submit(function(e) {
        e.preventDefault();
        
        $('#start-logo-detection').prop('disabled', true).text('🔄 Processing...');
        $('#logo-progress').show();
        $('#logo-results').hide();
        
        $('#progress-log').html('<div>Starting logo detection...</div>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'doo_livetv_auto_logo',
                nonce: $('input[name="nonce"]').val()
            },
            success: function(response) {
                if (response.success) {
                    showResults(response.data);
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                alert('AJAX error occurred');
            },
            complete: function() {
                $('#start-logo-detection').prop('disabled', false).text('🖼️ Start Logo Detection');
            }
        });
    });
    
    function showResults(data) {
        $('#logo-results').show();
        $('#results-summary').html(
            `Found and assigned logos for <strong>${data.updated}</strong> channels.`
        );
        
        // Show detailed results
        let logHtml = '<div style="color: green;">✅ Logo detection completed!</div>';
        logHtml += '<div style="margin: 10px 0; font-weight: bold;">Updated Channels:</div>';
        
        data.results.forEach(function(result) {
            logHtml += `<div>🖼️ ${result.name} → Logo assigned</div>`;
        });
        
        $('#progress-log').html(logHtml);
    }
});
</script>
