/**
 * DeshiFlix Premium Frontend Styles
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

/* Premium Content Lock Styles */
.premium-content-lock-overlay {
    position: relative;
    min-height: 600px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    overflow: hidden;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.premium-lock-container {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

.premium-lock-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    width: 100%;
    text-align: center;
    color: white;
}

.premium-lock-icon {
    margin-bottom: 20px;
    color: #ffd700;
}

.premium-lock-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.premium-lock-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

/* Premium Features Grid */
.premium-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.premium-feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.premium-feature-item:hover {
    transform: translateY(-2px);
}

.feature-icon {
    font-size: 1.5rem;
}

/* Premium Plans Grid */
.premium-plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.premium-plan-card {
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    border-radius: 12px;
    padding: 30px 20px;
    position: relative;
    transition: transform 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.premium-plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.premium-plan-card.popular {
    border: 3px solid #ffd700;
    transform: scale(1.05);
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #ffd700;
    color: #333;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.plan-name {
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.plan-price {
    margin-bottom: 15px;
}

.price-amount {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.price-original {
    text-decoration: line-through;
    color: #999;
    margin-left: 10px;
}

.price-period {
    color: #666;
    font-size: 0.9rem;
}

.plan-discount {
    background: #e74c3c;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-block;
    margin-top: 5px;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.plan-features li {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
}

.btn-premium-upgrade {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 100%;
}

.btn-premium-upgrade:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-login, .btn-register {
    background: #3498db;
    color: white;
    margin: 0 10px;
}

.btn-premium {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* Payment Methods */
.payment-methods-list {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.payment-method img {
    height: 40px;
    width: auto;
    border-radius: 4px;
    transition: transform 0.3s ease;
}

.payment-method img:hover {
    transform: scale(1.1);
}

/* Premium Dashboard Styles */
.premium-dashboard-container {
    min-height: 100vh;
    background: #f8f9fa;
}

.premium-dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
}

.dashboard-title {
    font-size: 2rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.dashboard-user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 10px;
}

.premium-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.premium-badge.active {
    background: #27ae60;
    color: white;
}

.premium-badge.inactive {
    background: #e74c3c;
    color: white;
}

/* Dashboard Layout */
.dashboard-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 30px;
    margin-top: 30px;
}

.dashboard-sidebar {
    background: white;
    border-radius: 8px;
    padding: 20px 0;
    height: fit-content;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dashboard-nav li {
    border-bottom: 1px solid #eee;
}

.dashboard-nav li:last-child {
    border-bottom: none;
}

.dashboard-nav a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.dashboard-nav li.active a,
.dashboard-nav a:hover {
    background: #667eea;
    color: white;
}

.dashboard-main {
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #333;
}

/* Overview Stats */
.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    font-size: 2rem;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0;
}

.stat-value.premium {
    color: #27ae60;
}

.stat-value.free {
    color: #e74c3c;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #27ae60;
}

.feature-item.active {
    background: #d4edda;
}

/* Tables */
.dashboard-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.dashboard-table th,
.dashboard-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.dashboard-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.dashboard-table tr:hover {
    background: #f8f9fa;
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.status-active,
.status-badge.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-badge.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.status-failed,
.status-badge.status-expired,
.status-badge.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 15px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .premium-lock-title {
        font-size: 2rem;
    }
    
    .premium-plans-grid {
        grid-template-columns: 1fr;
    }
    
    .premium-plan-card.popular {
        transform: none;
    }
    
    .premium-features-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-layout {
        grid-template-columns: 1fr;
    }
    
    .dashboard-sidebar {
        order: 2;
    }
    
    .dashboard-main {
        order: 1;
    }
    
    .overview-stats {
        grid-template-columns: 1fr;
    }
    
    .payment-methods-list {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .premium-lock-container {
        padding: 20px 10px;
    }
    
    .premium-lock-title {
        font-size: 1.5rem;
    }
    
    .dashboard-title {
        font-size: 1.5rem;
    }
    
    .dashboard-main {
        padding: 20px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
