<?php
/**
 * Premium Payment Callback Handler
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Payment_Callbacks {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Add rewrite rules for callback URLs
        add_action('init', array($this, 'add_rewrite_rules'));
        add_action('template_redirect', array($this, 'handle_payment_callbacks'));
        
        // AJAX handlers for payment processing
        add_action('wp_ajax_process_premium_payment', array($this, 'process_payment'));
        add_action('wp_ajax_nopriv_process_premium_payment', array($this, 'process_payment'));
        
        // Webhook handlers
        add_action('wp_ajax_bkash_webhook', array($this, 'handle_bkash_webhook'));
        add_action('wp_ajax_nopriv_bkash_webhook', array($this, 'handle_bkash_webhook'));
    }
    
    /**
     * Add rewrite rules for payment callbacks
     */
    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^premium-payment-success/?$',
            'index.php?premium_payment_callback=success',
            'top'
        );
        
        add_rewrite_rule(
            '^premium-payment-failed/?$',
            'index.php?premium_payment_callback=failed',
            'top'
        );
        
        add_rewrite_rule(
            '^premium-payment-cancelled/?$',
            'index.php?premium_payment_callback=cancelled',
            'top'
        );
        
        add_rewrite_rule(
            '^premium-payment-ipn/?$',
            'index.php?premium_payment_callback=ipn',
            'top'
        );
        
        // Add query vars
        add_filter('query_vars', function($vars) {
            $vars[] = 'premium_payment_callback';
            return $vars;
        });
    }
    
    /**
     * Handle payment callbacks
     */
    public function handle_payment_callbacks() {
        $callback_type = get_query_var('premium_payment_callback');
        
        if (!$callback_type) {
            return;
        }
        
        switch ($callback_type) {
            case 'success':
                $this->handle_payment_success();
                break;
            case 'failed':
                $this->handle_payment_failed();
                break;
            case 'cancelled':
                $this->handle_payment_cancelled();
                break;
            case 'ipn':
                $this->handle_payment_ipn();
                break;
        }
    }
    
    /**
     * Process payment initiation
     */
    public function process_payment() {
        if (!wp_verify_nonce($_GET['nonce'], 'premium_payment')) {
            wp_die('Security check failed');
        }
        
        $plan_id = intval($_GET['plan_id']);
        $method = sanitize_text_field($_GET['method']);
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            wp_redirect(wp_login_url());
            exit;
        }
        
        // Get plan details
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_plans WHERE id = %d AND status = 'active'",
            $plan_id
        ));
        
        if (!$plan) {
            wp_die('Invalid plan selected');
        }
        
        // Create transaction record
        $transaction_id = $this->create_transaction($user_id, $plan, $method);
        
        // Redirect to payment gateway
        switch ($method) {
            case 'bkash':
                $this->redirect_to_bkash($transaction_id, $plan);
                break;
            case 'nagad':
                $this->redirect_to_nagad($transaction_id, $plan);
                break;
            case 'rocket':
                $this->redirect_to_rocket($transaction_id, $plan);
                break;
            case 'sslcommerz':
                $this->redirect_to_sslcommerz($transaction_id, $plan);
                break;
            case 'aamarpay':
                $this->redirect_to_aamarpay($transaction_id, $plan);
                break;
            default:
                wp_die('Invalid payment method');
        }
    }
    
    /**
     * Create transaction record
     */
    private function create_transaction($user_id, $plan, $method) {
        global $wpdb;
        
        $transaction_id = 'DFLIX_' . time() . '_' . $user_id;
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        $wpdb->insert(
            $table_transactions,
            array(
                'user_id' => $user_id,
                'plan_id' => $plan->id,
                'amount' => $plan->price,
                'currency' => 'BDT',
                'payment_method' => $method,
                'transaction_id' => $transaction_id,
                'status' => 'pending',
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT']
            )
        );
        
        return $transaction_id;
    }
    
    /**
     * Redirect to bKash payment
     */
    private function redirect_to_bkash($transaction_id, $plan) {
        // Use existing bKash plugin integration
        if (function_exists('dc_bkash')) {
            $bkash_gateway = dc_bkash()->gateway;
            
            // Create a temporary order for bKash processing
            $order_data = array(
                'transaction_id' => $transaction_id,
                'amount' => $plan->price,
                'currency' => 'BDT',
                'customer_name' => wp_get_current_user()->display_name,
                'customer_email' => wp_get_current_user()->user_email,
                'success_url' => home_url('/premium-payment-success/?transaction_id=' . $transaction_id),
                'fail_url' => home_url('/premium-payment-failed/?transaction_id=' . $transaction_id),
                'cancel_url' => home_url('/premium-payment-cancelled/?transaction_id=' . $transaction_id)
            );
            
            // Process with bKash
            $payment_url = $this->create_bkash_payment($order_data);
            
            if ($payment_url) {
                wp_redirect($payment_url);
                exit;
            }
        }
        
        // Fallback to manual bKash integration
        $this->manual_bkash_integration($transaction_id, $plan);
    }
    
    /**
     * Manual bKash integration
     */
    private function manual_bkash_integration($transaction_id, $plan) {
        // Get bKash settings
        $settings = get_option('deshiflix_premium_payment_settings', array());
        $bkash_settings = $settings['bkash'] ?? array();
        
        if (empty($bkash_settings['app_key']) || empty($bkash_settings['app_secret'])) {
            wp_die('bKash payment not configured properly');
        }
        
        // Create payment request
        $payment_data = array(
            'mode' => '0011',
            'payerReference' => $transaction_id,
            'callbackURL' => home_url('/premium-payment-success/?transaction_id=' . $transaction_id),
            'amount' => $plan->price,
            'currency' => 'BDT',
            'intent' => 'sale',
            'merchantInvoiceNumber' => $transaction_id
        );
        
        // Get access token
        $token = $this->get_bkash_token($bkash_settings);
        
        if (!$token) {
            wp_die('Failed to connect to bKash');
        }
        
        // Create payment
        $api_url = $bkash_settings['sandbox'] ? 
            'https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/create' :
            'https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized/checkout/create';
        
        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token,
                'X-APP-Key' => $bkash_settings['app_key']
            ),
            'body' => json_encode($payment_data),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            wp_die('Payment gateway error: ' . $response->get_error_message());
        }
        
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (isset($response_body['bkashURL'])) {
            wp_redirect($response_body['bkashURL']);
            exit;
        } else {
            wp_die('Failed to create payment: ' . ($response_body['errorMessage'] ?? 'Unknown error'));
        }
    }
    
    /**
     * Get bKash access token
     */
    private function get_bkash_token($settings) {
        $token_url = $settings['sandbox'] ? 
            'https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/token/grant' :
            'https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized/checkout/token/grant';
        
        $response = wp_remote_post($token_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'username' => $settings['username'],
                'password' => $settings['password']
            ),
            'body' => json_encode(array(
                'app_key' => $settings['app_key'],
                'app_secret' => $settings['app_secret']
            )),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        return $response_body['id_token'] ?? false;
    }
    
    /**
     * Handle payment success
     */
    private function handle_payment_success() {
        $transaction_id = sanitize_text_field($_GET['transaction_id'] ?? '');
        
        if (!$transaction_id) {
            wp_redirect(home_url('/premium-plans/?error=invalid_transaction'));
            exit;
        }
        
        // Verify and complete transaction
        $this->complete_transaction($transaction_id, 'completed');
        
        // Show success page
        $this->show_payment_success_page($transaction_id);
    }
    
    /**
     * Handle payment failure
     */
    private function handle_payment_failed() {
        $transaction_id = sanitize_text_field($_GET['transaction_id'] ?? '');
        
        if ($transaction_id) {
            $this->complete_transaction($transaction_id, 'failed');
        }
        
        $this->show_payment_failed_page($transaction_id);
    }
    
    /**
     * Handle payment cancellation
     */
    private function handle_payment_cancelled() {
        $transaction_id = sanitize_text_field($_GET['transaction_id'] ?? '');
        
        if ($transaction_id) {
            $this->complete_transaction($transaction_id, 'cancelled');
        }
        
        $this->show_payment_cancelled_page($transaction_id);
    }
    
    /**
     * Complete transaction
     */
    private function complete_transaction($transaction_id, $status) {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        // Get transaction
        $transaction = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_transactions WHERE transaction_id = %s",
            $transaction_id
        ));
        
        if (!$transaction) {
            return false;
        }
        
        // Update transaction status
        $wpdb->update(
            $table_transactions,
            array('status' => $status),
            array('transaction_id' => $transaction_id)
        );
        
        // If successful, activate premium subscription
        if ($status === 'completed') {
            $this->activate_premium_subscription($transaction);
        }
        
        return true;
    }
    
    /**
     * Activate premium subscription
     */
    private function activate_premium_subscription($transaction) {
        // Get plan details
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plan = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_plans WHERE id = %d",
            $transaction->plan_id
        ));
        
        if (!$plan) {
            return false;
        }
        
        // Activate subscription
        if (function_exists('deshiflix_premium')) {
            $premium_user = DeshiFlix_Premium_User::get_instance();
            $premium_user->activate_premium_subscription(
                $transaction->user_id,
                $plan->id,
                $plan->duration_days,
                $transaction->payment_method
            );
            
            // Trigger premium activation hook
            do_action('deshiflix_premium_activated', $transaction->user_id);
        }
        
        return true;
    }
    
    /**
     * Show payment success page
     */
    private function show_payment_success_page($transaction_id) {
        get_header();
        ?>
        <div class="premium-payment-result success">
            <div class="container">
                <div class="result-content">
                    <div class="success-icon">✅</div>
                    <h1>Payment Successful!</h1>
                    <p>আপনার Premium subscription সফলভাবে সক্রিয় হয়েছে!</p>
                    <div class="transaction-info">
                        <p><strong>Transaction ID:</strong> <?php echo esc_html($transaction_id); ?></p>
                    </div>
                    <div class="action-buttons">
                        <a href="<?php echo home_url('/premium-dashboard/'); ?>" class="btn btn-primary">
                            Premium Dashboard
                        </a>
                        <a href="<?php echo home_url(); ?>" class="btn btn-secondary">
                            Home Page
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .premium-payment-result {
            min-height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }
        
        .result-content {
            text-align: center;
            max-width: 500px;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .success-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .premium-payment-result h1 {
            color: #4CAF50;
            margin-bottom: 15px;
        }
        
        .transaction-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #FFD700;
            color: #000;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        </style>
        <?php
        get_footer();
        exit;
    }
    
    /**
     * Show payment failed page
     */
    private function show_payment_failed_page($transaction_id) {
        get_header();
        ?>
        <div class="premium-payment-result failed">
            <div class="container">
                <div class="result-content">
                    <div class="failed-icon">❌</div>
                    <h1>Payment Failed</h1>
                    <p>দুঃখিত, আপনার payment সফল হয়নি। আবার চেষ্টা করুন।</p>
                    <?php if ($transaction_id): ?>
                    <div class="transaction-info">
                        <p><strong>Transaction ID:</strong> <?php echo esc_html($transaction_id); ?></p>
                    </div>
                    <?php endif; ?>
                    <div class="action-buttons">
                        <a href="<?php echo home_url('/premium-plans/'); ?>" class="btn btn-primary">
                            Try Again
                        </a>
                        <a href="<?php echo home_url(); ?>" class="btn btn-secondary">
                            Home Page
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .premium-payment-result.failed h1 {
            color: #f44336;
        }
        
        .failed-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        </style>
        <?php
        get_footer();
        exit;
    }
    
    /**
     * Show payment cancelled page
     */
    private function show_payment_cancelled_page($transaction_id) {
        get_header();
        ?>
        <div class="premium-payment-result cancelled">
            <div class="container">
                <div class="result-content">
                    <div class="cancelled-icon">⚠️</div>
                    <h1>Payment Cancelled</h1>
                    <p>আপনি payment cancel করেছেন। আবার চেষ্টা করতে পারেন।</p>
                    <div class="action-buttons">
                        <a href="<?php echo home_url('/premium-plans/'); ?>" class="btn btn-primary">
                            Try Again
                        </a>
                        <a href="<?php echo home_url(); ?>" class="btn btn-secondary">
                            Home Page
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .premium-payment-result.cancelled h1 {
            color: #ff9800;
        }
        
        .cancelled-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        </style>
        <?php
        get_footer();
        exit;
    }
    
    /**
     * Handle payment IPN
     */
    private function handle_payment_ipn() {
        // Handle instant payment notifications
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            $data = $_POST;
        }
        
        // Log IPN data
        error_log('Premium Payment IPN: ' . json_encode($data));
        
        // Process based on payment method
        if (isset($data['payment_method'])) {
            switch ($data['payment_method']) {
                case 'bkash':
                    $this->process_bkash_ipn($data);
                    break;
                case 'sslcommerz':
                    $this->process_sslcommerz_ipn($data);
                    break;
                // Add other payment methods as needed
            }
        }
        
        http_response_code(200);
        echo 'OK';
        exit;
    }
    
    /**
     * Process bKash IPN
     */
    private function process_bkash_ipn($data) {
        // Verify bKash IPN and update transaction status
        if (isset($data['trxID']) && isset($data['status'])) {
            $transaction_id = sanitize_text_field($data['merchantInvoiceNumber'] ?? '');
            $status = $data['status'] === 'Completed' ? 'completed' : 'failed';
            
            if ($transaction_id) {
                $this->complete_transaction($transaction_id, $status);
            }
        }
    }
    
    /**
     * Handle bKash webhook
     */
    public function handle_bkash_webhook() {
        $this->handle_payment_ipn();
    }
}

// Initialize payment callbacks
DeshiFlix_Payment_Callbacks::get_instance();
