<?php
/**
 * DeshiFlix Premium System - User Dashboard
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Premium_User_Dashboard {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize user dashboard
     */
    private function init() {
        add_action('init', array($this, 'add_rewrite_rules'));
        add_action('template_redirect', array($this, 'handle_dashboard_page'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_shortcode('premium_dashboard', array($this, 'dashboard_shortcode'));
        add_shortcode('premium_subscription_status', array($this, 'subscription_status_shortcode'));
    }
    
    /**
     * Add rewrite rules for dashboard
     */
    public function add_rewrite_rules() {
        add_rewrite_rule('^premium-dashboard/?$', 'index.php?premium_dashboard=1', 'top');
        add_rewrite_rule('^premium-dashboard/([^/]+)/?$', 'index.php?premium_dashboard=1&dashboard_section=$matches[1]', 'top');
        
        add_rewrite_tag('%premium_dashboard%', '([^&]+)');
        add_rewrite_tag('%dashboard_section%', '([^&]+)');
    }
    
    /**
     * Handle dashboard page
     */
    public function handle_dashboard_page() {
        if (get_query_var('premium_dashboard')) {
            if (!is_user_logged_in()) {
                wp_redirect(wp_login_url(home_url('/premium-dashboard/')));
                exit;
            }
            
            $this->render_dashboard_page();
            exit;
        }
    }
    
    /**
     * Enqueue scripts
     */
    public function enqueue_scripts() {
        if (get_query_var('premium_dashboard') || is_page('premium-dashboard')) {
            wp_enqueue_style('deshiflix-premium-dashboard', 
                            DESHIFLIX_PREMIUM_ASSETS_URL . 'css/premium-dashboard.css', 
                            array(), DESHIFLIX_PREMIUM_VERSION);
            
            wp_enqueue_script('deshiflix-premium-dashboard', 
                             DESHIFLIX_PREMIUM_ASSETS_URL . 'js/premium-dashboard.js', 
                             array('jquery'), DESHIFLIX_PREMIUM_VERSION, true);
            
            wp_localize_script('deshiflix-premium-dashboard', 'premium_dashboard', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('premium_dashboard_nonce'),
                'messages' => array(
                    'confirm_cancel' => __('Are you sure you want to cancel your subscription?', 'deshiflix'),
                    'loading' => __('Loading...', 'deshiflix'),
                    'error' => __('An error occurred', 'deshiflix')
                )
            ));
        }
    }
    
    /**
     * Render dashboard page
     */
    private function render_dashboard_page() {
        $user_id = get_current_user_id();
        $section = get_query_var('dashboard_section', 'overview');
        
        // Get user premium details
        $premium_user = DeshiFlix_Premium_User::get_instance();
        $premium_details = $premium_user->get_user_premium_details($user_id);
        $is_premium = deshiflix_premium()->is_user_premium($user_id);
        
        get_header();
        ?>
        
        <div class="premium-dashboard-container">
            <div class="premium-dashboard-header">
                <div class="container">
                    <h1 class="dashboard-title">
                        <span class="dashicons dashicons-star-filled"></span>
                        <?php _e('Premium Dashboard', 'deshiflix'); ?>
                    </h1>
                    <div class="dashboard-user-info">
                        <span class="user-name"><?php echo esc_html(wp_get_current_user()->display_name); ?></span>
                        <?php if ($is_premium): ?>
                            <span class="premium-badge active">✨ <?php _e('Premium Active', 'deshiflix'); ?></span>
                        <?php else: ?>
                            <span class="premium-badge inactive">❌ <?php _e('Not Premium', 'deshiflix'); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="premium-dashboard-content">
                <div class="container">
                    <div class="dashboard-layout">
                        <aside class="dashboard-sidebar">
                            <nav class="dashboard-nav">
                                <ul>
                                    <li class="<?php echo $section === 'overview' ? 'active' : ''; ?>">
                                        <a href="<?php echo home_url('/premium-dashboard/'); ?>">
                                            <span class="dashicons dashicons-dashboard"></span>
                                            <?php _e('Overview', 'deshiflix'); ?>
                                        </a>
                                    </li>
                                    <li class="<?php echo $section === 'subscription' ? 'active' : ''; ?>">
                                        <a href="<?php echo home_url('/premium-dashboard/subscription/'); ?>">
                                            <span class="dashicons dashicons-star-filled"></span>
                                            <?php _e('Subscription', 'deshiflix'); ?>
                                        </a>
                                    </li>
                                    <li class="<?php echo $section === 'billing' ? 'active' : ''; ?>">
                                        <a href="<?php echo home_url('/premium-dashboard/billing/'); ?>">
                                            <span class="dashicons dashicons-money-alt"></span>
                                            <?php _e('Billing History', 'deshiflix'); ?>
                                        </a>
                                    </li>
                                    <li class="<?php echo $section === 'devices' ? 'active' : ''; ?>">
                                        <a href="<?php echo home_url('/premium-dashboard/devices/'); ?>">
                                            <span class="dashicons dashicons-smartphone"></span>
                                            <?php _e('My Devices', 'deshiflix'); ?>
                                        </a>
                                    </li>
                                    <li class="<?php echo $section === 'downloads' ? 'active' : ''; ?>">
                                        <a href="<?php echo home_url('/premium-dashboard/downloads/'); ?>">
                                            <span class="dashicons dashicons-download"></span>
                                            <?php _e('Downloads', 'deshiflix'); ?>
                                        </a>
                                    </li>
                                    <li class="<?php echo $section === 'settings' ? 'active' : ''; ?>">
                                        <a href="<?php echo home_url('/premium-dashboard/settings/'); ?>">
                                            <span class="dashicons dashicons-admin-settings"></span>
                                            <?php _e('Settings', 'deshiflix'); ?>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </aside>
                        
                        <main class="dashboard-main">
                            <?php
                            switch ($section) {
                                case 'overview':
                                    $this->render_overview_section($user_id, $premium_details, $is_premium);
                                    break;
                                case 'subscription':
                                    $this->render_subscription_section($user_id, $premium_details, $is_premium);
                                    break;
                                case 'billing':
                                    $this->render_billing_section($user_id);
                                    break;
                                case 'devices':
                                    $this->render_devices_section($user_id);
                                    break;
                                case 'downloads':
                                    $this->render_downloads_section($user_id);
                                    break;
                                case 'settings':
                                    $this->render_settings_section($user_id);
                                    break;
                                default:
                                    $this->render_overview_section($user_id, $premium_details, $is_premium);
                            }
                            ?>
                        </main>
                    </div>
                </div>
            </div>
        </div>
        
        <?php
        get_footer();
    }
    
    /**
     * Render overview section
     */
    private function render_overview_section($user_id, $premium_details, $is_premium) {
        ?>
        <div class="dashboard-section overview-section">
            <h2 class="section-title"><?php _e('Account Overview', 'deshiflix'); ?></h2>
            
            <div class="overview-stats">
                <div class="stat-card">
                    <div class="stat-icon">👤</div>
                    <div class="stat-content">
                        <h3><?php _e('Account Status', 'deshiflix'); ?></h3>
                        <p class="stat-value <?php echo $is_premium ? 'premium' : 'free'; ?>">
                            <?php echo $is_premium ? __('Premium Active', 'deshiflix') : __('Free Account', 'deshiflix'); ?>
                        </p>
                    </div>
                </div>
                
                <?php if ($is_premium && $premium_details): ?>
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-content">
                        <h3><?php _e('Expires On', 'deshiflix'); ?></h3>
                        <p class="stat-value"><?php echo date('F j, Y', strtotime($premium_details->end_date)); ?></p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💎</div>
                    <div class="stat-content">
                        <h3><?php _e('Current Plan', 'deshiflix'); ?></h3>
                        <p class="stat-value"><?php echo esc_html($premium_details->plan_name); ?></p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <h3><?php _e('Plan Price', 'deshiflix'); ?></h3>
                        <p class="stat-value">৳<?php echo number_format($premium_details->price, 2); ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <?php if ($is_premium): ?>
                <div class="premium-features-overview">
                    <h3><?php _e('Your Premium Features', 'deshiflix'); ?></h3>
                    <div class="features-grid">
                        <div class="feature-item active">
                            <span class="feature-icon">🎬</span>
                            <span class="feature-name"><?php _e('HD/4K Quality', 'deshiflix'); ?></span>
                        </div>
                        <div class="feature-item active">
                            <span class="feature-icon">🚫</span>
                            <span class="feature-name"><?php _e('Ad-Free Experience', 'deshiflix'); ?></span>
                        </div>
                        <div class="feature-item active">
                            <span class="feature-icon">⬇️</span>
                            <span class="feature-name"><?php _e('Download Access', 'deshiflix'); ?></span>
                        </div>
                        <div class="feature-item active">
                            <span class="feature-icon">⚡</span>
                            <span class="feature-name"><?php _e('Early Access', 'deshiflix'); ?></span>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="upgrade-prompt">
                    <h3><?php _e('Upgrade to Premium', 'deshiflix'); ?></h3>
                    <p><?php _e('Unlock exclusive content, HD quality, and ad-free experience', 'deshiflix'); ?></p>
                    <a href="<?php echo home_url('/premium-plans/'); ?>" class="btn btn-premium">
                        <?php _e('View Plans', 'deshiflix'); ?>
                    </a>
                </div>
            <?php endif; ?>
            
            <div class="recent-activity">
                <h3><?php _e('Recent Activity', 'deshiflix'); ?></h3>
                <?php $this->render_recent_activity($user_id); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render subscription section
     */
    private function render_subscription_section($user_id, $premium_details, $is_premium) {
        ?>
        <div class="dashboard-section subscription-section">
            <h2 class="section-title"><?php _e('Subscription Management', 'deshiflix'); ?></h2>
            
            <?php if ($is_premium && $premium_details): ?>
                <div class="current-subscription">
                    <div class="subscription-card">
                        <div class="subscription-header">
                            <h3><?php echo esc_html($premium_details->plan_name); ?></h3>
                            <span class="subscription-status active"><?php _e('Active', 'deshiflix'); ?></span>
                        </div>
                        
                        <div class="subscription-details">
                            <div class="detail-item">
                                <span class="label"><?php _e('Started:', 'deshiflix'); ?></span>
                                <span class="value"><?php echo date('F j, Y', strtotime($premium_details->start_date)); ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="label"><?php _e('Expires:', 'deshiflix'); ?></span>
                                <span class="value"><?php echo date('F j, Y', strtotime($premium_details->end_date)); ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="label"><?php _e('Payment Method:', 'deshiflix'); ?></span>
                                <span class="value"><?php echo ucfirst($premium_details->payment_method); ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="label"><?php _e('Price:', 'deshiflix'); ?></span>
                                <span class="value">৳<?php echo number_format($premium_details->price, 2); ?></span>
                            </div>
                        </div>
                        
                        <div class="subscription-actions">
                            <button class="btn btn-secondary" id="cancel-subscription" data-user-id="<?php echo $user_id; ?>">
                                <?php _e('Cancel Subscription', 'deshiflix'); ?>
                            </button>
                            <a href="<?php echo home_url('/premium-plans/'); ?>" class="btn btn-primary">
                                <?php _e('Upgrade Plan', 'deshiflix'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="no-subscription">
                    <div class="empty-state">
                        <span class="empty-icon">💎</span>
                        <h3><?php _e('No Active Subscription', 'deshiflix'); ?></h3>
                        <p><?php _e('Subscribe to premium to unlock exclusive content and features', 'deshiflix'); ?></p>
                        <a href="<?php echo home_url('/premium-plans/'); ?>" class="btn btn-premium">
                            <?php _e('View Premium Plans', 'deshiflix'); ?>
                        </a>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="subscription-history">
                <h3><?php _e('Subscription History', 'deshiflix'); ?></h3>
                <?php $this->render_subscription_history($user_id); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render billing section
     */
    private function render_billing_section($user_id) {
        ?>
        <div class="dashboard-section billing-section">
            <h2 class="section-title"><?php _e('Billing History', 'deshiflix'); ?></h2>
            
            <div class="billing-filters">
                <select id="billing-filter-year">
                    <option value=""><?php _e('All Years', 'deshiflix'); ?></option>
                    <?php
                    $current_year = date('Y');
                    for ($year = $current_year; $year >= $current_year - 5; $year--) {
                        echo '<option value="' . $year . '">' . $year . '</option>';
                    }
                    ?>
                </select>
                
                <select id="billing-filter-status">
                    <option value=""><?php _e('All Status', 'deshiflix'); ?></option>
                    <option value="completed"><?php _e('Completed', 'deshiflix'); ?></option>
                    <option value="pending"><?php _e('Pending', 'deshiflix'); ?></option>
                    <option value="failed"><?php _e('Failed', 'deshiflix'); ?></option>
                </select>
            </div>
            
            <div class="billing-table-container">
                <?php $this->render_billing_table($user_id); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render recent activity
     */
    private function render_recent_activity($user_id) {
        global $wpdb;
        
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        $activities = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_analytics 
             WHERE user_id = %d 
             ORDER BY created_at DESC 
             LIMIT 10",
            $user_id
        ));
        
        if (empty($activities)) {
            echo '<p>' . __('No recent activity found.', 'deshiflix') . '</p>';
            return;
        }
        
        echo '<div class="activity-list">';
        foreach ($activities as $activity) {
            $event_data = json_decode($activity->event_data, true);
            echo '<div class="activity-item">';
            echo '<span class="activity-type">' . esc_html($activity->event_type) . '</span>';
            echo '<span class="activity-date">' . human_time_diff(strtotime($activity->created_at)) . ' ago</span>';
            echo '</div>';
        }
        echo '</div>';
    }
    
    /**
     * Render subscription history
     */
    private function render_subscription_history($user_id) {
        global $wpdb;
        
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $history = $wpdb->get_results($wpdb->prepare(
            "SELECT pu.*, p.name as plan_name 
             FROM $table_users pu 
             LEFT JOIN $table_plans p ON pu.plan_id = p.id 
             WHERE pu.user_id = %d 
             ORDER BY pu.created_at DESC",
            $user_id
        ));
        
        if (empty($history)) {
            echo '<p>' . __('No subscription history found.', 'deshiflix') . '</p>';
            return;
        }
        
        echo '<div class="subscription-history-table">';
        echo '<table class="dashboard-table">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>' . __('Plan', 'deshiflix') . '</th>';
        echo '<th>' . __('Status', 'deshiflix') . '</th>';
        echo '<th>' . __('Start Date', 'deshiflix') . '</th>';
        echo '<th>' . __('End Date', 'deshiflix') . '</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($history as $item) {
            echo '<tr>';
            echo '<td>' . esc_html($item->plan_name) . '</td>';
            echo '<td><span class="status-badge status-' . esc_attr($item->status) . '">' . ucfirst($item->status) . '</span></td>';
            echo '<td>' . date('M j, Y', strtotime($item->start_date)) . '</td>';
            echo '<td>' . date('M j, Y', strtotime($item->end_date)) . '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
    }
    
    /**
     * Render billing table
     */
    private function render_billing_table($user_id) {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $transactions = $wpdb->get_results($wpdb->prepare(
            "SELECT t.*, p.name as plan_name 
             FROM $table_transactions t 
             LEFT JOIN $table_plans p ON t.plan_id = p.id 
             WHERE t.user_id = %d 
             ORDER BY t.created_at DESC",
            $user_id
        ));
        
        if (empty($transactions)) {
            echo '<div class="empty-state">';
            echo '<span class="empty-icon">💳</span>';
            echo '<h3>' . __('No Billing History', 'deshiflix') . '</h3>';
            echo '<p>' . __('Your payment history will appear here', 'deshiflix') . '</p>';
            echo '</div>';
            return;
        }
        
        echo '<table class="dashboard-table billing-table">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>' . __('Transaction ID', 'deshiflix') . '</th>';
        echo '<th>' . __('Plan', 'deshiflix') . '</th>';
        echo '<th>' . __('Amount', 'deshiflix') . '</th>';
        echo '<th>' . __('Payment Method', 'deshiflix') . '</th>';
        echo '<th>' . __('Status', 'deshiflix') . '</th>';
        echo '<th>' . __('Date', 'deshiflix') . '</th>';
        echo '<th>' . __('Actions', 'deshiflix') . '</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($transactions as $transaction) {
            echo '<tr>';
            echo '<td><code>' . esc_html($transaction->transaction_id) . '</code></td>';
            echo '<td>' . esc_html($transaction->plan_name) . '</td>';
            echo '<td>৳' . number_format($transaction->amount, 2) . '</td>';
            echo '<td>' . ucfirst($transaction->payment_method) . '</td>';
            echo '<td><span class="status-badge status-' . esc_attr($transaction->status) . '">' . ucfirst($transaction->status) . '</span></td>';
            echo '<td>' . date('M j, Y', strtotime($transaction->created_at)) . '</td>';
            echo '<td>';
            if ($transaction->status === 'completed') {
                echo '<button class="btn btn-small download-invoice" data-transaction-id="' . esc_attr($transaction->id) . '">' . __('Download Invoice', 'deshiflix') . '</button>';
            }
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
    }
    
    /**
     * Dashboard shortcode
     */
    public function dashboard_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please login to access your premium dashboard.', 'deshiflix') . '</p>';
        }
        
        ob_start();
        $this->render_dashboard_page();
        return ob_get_clean();
    }
    
    /**
     * Subscription status shortcode
     */
    public function subscription_status_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please login to view subscription status.', 'deshiflix') . '</p>';
        }
        
        $user_id = get_current_user_id();
        $is_premium = deshiflix_premium()->is_user_premium($user_id);
        
        if ($is_premium) {
            $premium_user = DeshiFlix_Premium_User::get_instance();
            $premium_details = $premium_user->get_user_premium_details($user_id);
            
            return '<div class="premium-status-widget active">' .
                   '<span class="status-icon">✨</span>' .
                   '<span class="status-text">' . sprintf(__('Premium Active - %s', 'deshiflix'), $premium_details->plan_name) . '</span>' .
                   '</div>';
        } else {
            return '<div class="premium-status-widget inactive">' .
                   '<span class="status-icon">❌</span>' .
                   '<span class="status-text">' . __('Free Account', 'deshiflix') . '</span>' .
                   '</div>';
        }
    }
}

// Initialize user dashboard
DeshiFlix_Premium_User_Dashboard::get_instance();
