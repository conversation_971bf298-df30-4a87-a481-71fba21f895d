<?php
/**
 * DeshiFlix Premium Admin Menu
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Premium_Admin_Menu {
    
    /**
     * Initialize admin menu
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Main premium menu
        add_menu_page(
            __('DeshiFlix Premium', 'deshiflix'),
            __('DeshiFlix Premium', 'deshiflix'),
            'manage_options',
            'deshiflix-premium',
            array($this, 'admin_page'),
            'dashicons-star-filled',
            30
        );
        
        // Settings submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Settings', 'deshiflix'),
            __('Settings', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-settings',
            array($this, 'settings_page')
        );
        
        // Plans submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Plans', 'deshiflix'),
            __('Plans', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-plans',
            array($this, 'plans_page')
        );
        
        // Users submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Users', 'deshiflix'),
            __('Users', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-users',
            array($this, 'users_page')
        );
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        // Register settings
        register_setting('deshiflix_premium_settings', 'deshiflix_premium_settings');
    }
    
    /**
     * Main admin page
     */
    public function admin_page() {
        // Check if DooPlay admin class exists
        if (class_exists('DeshiFlix_Premium_DooPlay_Admin')) {
            $admin = new DeshiFlix_Premium_DooPlay_Admin();
            $admin->render_dooplay_admin_page();
        } else {
            $this->render_simple_dashboard();
        }
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        $settings_file = get_template_directory() . '/inc/premium/admin/settings-page-simple.php';
        if (file_exists($settings_file)) {
            include $settings_file;
        } else {
            $this->render_simple_settings();
        }
    }
    
    /**
     * Plans page
     */
    public function plans_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Premium Plans', 'deshiflix'); ?></h1>
            <div class="notice notice-info">
                <p><?php _e('Premium plans management coming soon...', 'deshiflix'); ?></p>
            </div>
        </div>
        <?php
    }
    
    /**
     * Users page
     */
    public function users_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Premium Users', 'deshiflix'); ?></h1>
            <div class="notice notice-info">
                <p><?php _e('Premium users management coming soon...', 'deshiflix'); ?></p>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render simple dashboard
     */
    private function render_simple_dashboard() {
        ?>
        <div class="wrap">
            <h1><?php _e('DeshiFlix Premium System', 'deshiflix'); ?></h1>
            
            <div class="notice notice-success">
                <p><strong><?php _e('Premium System Active!', 'deshiflix'); ?></strong></p>
            </div>
            
            <div class="card" style="max-width: 600px;">
                <h2><?php _e('Premium Features Status', 'deshiflix'); ?></h2>
                <table class="widefat">
                    <thead>
                        <tr>
                            <th><?php _e('Feature', 'deshiflix'); ?></th>
                            <th><?php _e('Status', 'deshiflix'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><?php _e('Instant Downloads', 'deshiflix'); ?></td>
                            <td><span class="dashicons dashicons-yes-alt" style="color: green;"></span> <?php _e('Active', 'deshiflix'); ?></td>
                        </tr>
                        <tr>
                            <td><?php _e('Ad-Free Experience', 'deshiflix'); ?></td>
                            <td><span class="dashicons dashicons-yes-alt" style="color: green;"></span> <?php _e('Active', 'deshiflix'); ?></td>
                        </tr>
                        <tr>
                            <td><?php _e('Per-Link Premium Control', 'deshiflix'); ?></td>
                            <td><span class="dashicons dashicons-yes-alt" style="color: green;"></span> <?php _e('Active', 'deshiflix'); ?></td>
                        </tr>
                        <tr>
                            <td><?php _e('Premium Indicators', 'deshiflix'); ?></td>
                            <td><span class="dashicons dashicons-yes-alt" style="color: green;"></span> <?php _e('Active', 'deshiflix'); ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="card" style="max-width: 600px; margin-top: 20px;">
                <h2><?php _e('Quick Actions', 'deshiflix'); ?></h2>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=deshiflix-premium-settings'); ?>" class="button button-primary">
                        <?php _e('Premium Settings', 'deshiflix'); ?>
                    </a>
                    <a href="<?php echo admin_url('edit.php?post_type=dt_links'); ?>" class="button button-secondary">
                        <?php _e('Manage Links', 'deshiflix'); ?>
                    </a>
                </p>
            </div>
            
            <div class="card" style="max-width: 600px; margin-top: 20px;">
                <h2><?php _e('Test Premium Features', 'deshiflix'); ?></h2>
                <p><?php _e('Use test account to verify premium features:', 'deshiflix'); ?></p>
                <p>
                    <strong><?php _e('Username:', 'deshiflix'); ?></strong> <code>premium_test_user</code><br>
                    <strong><?php _e('Password:', 'deshiflix'); ?></strong> <code>premium123</code>
                </p>
                <p>
                    <a href="<?php echo wp_logout_url(wp_login_url()); ?>" class="button button-secondary">
                        <?php _e('Switch to Test User', 'deshiflix'); ?>
                    </a>
                </p>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render simple settings
     */
    private function render_simple_settings() {
        ?>
        <div class="wrap">
            <h1><?php _e('Premium Settings', 'deshiflix'); ?></h1>
            <div class="notice notice-info">
                <p><?php _e('Premium settings interface loading...', 'deshiflix'); ?></p>
            </div>
        </div>
        <?php
    }
}

// Initialize admin menu - temporarily disabled
// new DeshiFlix_Premium_Admin_Menu();
