jQuery(document).ready(function($) {

    // Test function for debugging
    window.testEpisodeAjax = function() {
        $.ajax({
            url: (typeof ajaxurl !== 'undefined') ? ajaxurl : '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'save_episode_links',
                post_id: $('#post_ID').val(),
                episodes: [{episode: '1', url: 'http://test.com', type: 'Download', lang: 'English', quality: 'HD', size: '1GB'}],
                nonce: 'test'
            },
            success: function(response) {
                console.log('Success:', response);
            },
            error: function(xhr, status, error) {
                console.log('Error:', error);
                console.log('Response:', xhr.responseText);
            }
        });
    };
    
    // Generate Episode Fields
    $('#generate_episode_fields').on('click', function(e) {
        e.preventDefault();
        
        var episodeCount = $('#episode_count').val();
        var episodeType = $('#episode_type').val();
        var episodeLang = $('#episode_lang').val();
        var episodeQuality = $('#episode_quality').val();
        var episodeSize = $('#episode_size').val();
        var episodePremium = $('#episode_premium').is(':checked');
        
        if (!episodeCount || episodeCount < 1) {
            alert('Please select number of episodes');
            return;
        }
        
        var container = $('#episode_fields_container');
        container.empty();
        
        // Create header
        var html = '<div class="episode-links-form" style="background: #fff; border: 1px solid #ddd; border-radius: 5px; padding: 15px;">';
        html += '<h4 style="margin-top: 0; color: #0073aa;">Episode Links (Total: ' + episodeCount + ')</h4>';
        html += '<div class="episode-links-grid" style="display: grid; gap: 15px;">';
        
        // Generate fields for each episode
        for (var i = 1; i <= episodeCount; i++) {
            html += '<div class="episode-row" style="border: 1px solid #e1e1e1; padding: 10px; border-radius: 3px; background: #f9f9f9;">';
            html += '<div style="display: grid; grid-template-columns: 80px 1fr 120px 60px 100px; gap: 10px; align-items: center;">';

            // Episode label
            html += '<label style="font-weight: bold; color: #333;">EP-' + i + ':</label>';

            // URL input
            html += '<input type="text" name="episode_url_' + i + '" id="episode_url_' + i + '" ';
            html += 'placeholder="Enter download link for Episode ' + i + '" ';
            html += 'style="width: 100%; padding: 5px;" />';

            // Size display field (editable)
            html += '<input type="text" name="episode_size_display_' + i + '" id="episode_size_display_' + i + '" ';
            html += 'placeholder="Auto-detecting..." ';
            html += 'title="File size will be auto-detected from URL. You can edit manually if needed." ';
            html += 'style="width: 100%; padding: 5px; background: #f0f8ff; border: 1px solid #ddd; font-size: 12px; text-align: center;" />';

            // Premium checkbox
            html += '<label style="display: flex; align-items: center; justify-content: center; gap: 3px;">';
            html += '<input type="checkbox" name="episode_premium_' + i + '" id="episode_premium_' + i + '" ';
            html += 'value="1"' + (episodePremium ? ' checked' : '') + '>';
            html += '<span style="font-size: 12px; color: #FFD700;" title="Premium Only">👑</span>';
            html += '</label>';

            // Hidden fields for metadata
            html += '<input type="hidden" name="episode_type_' + i + '" value="' + episodeType + '" />';
            html += '<input type="hidden" name="episode_lang_' + i + '" value="' + episodeLang + '" />';
            html += '<input type="hidden" name="episode_quality_' + i + '" value="' + episodeQuality + '" />';
            html += '<input type="hidden" name="episode_size_' + i + '" value="' + episodeSize + '" />';

            // Status indicator
            html += '<span class="episode-status" id="status_' + i + '" style="color: #999;">Empty</span>';

            html += '</div></div>';
        }
        
        // Add action buttons
        html += '</div>';
        html += '<div style="margin-top: 15px; text-align: right;">';
        html += '<button type="button" id="save_all_episodes" class="button button-primary" style="margin-right: 10px;">Save All Episodes</button>';
        html += '<button type="button" id="preview_episodes" class="button button-secondary">Preview Links</button>';
        html += '</div>';
        html += '</div>';
        
        container.html(html);
        
        // Add event listeners for URL inputs
        $('input[name^="episode_url_"]').on('input', function() {
            var episodeNum = $(this).attr('name').split('_')[2];
            var status = $('#status_' + episodeNum);
            var url = $(this).val().trim();

            if (url !== '') {
                status.text('Checking...').css('color', '#ff9800');

                // Auto-detect file size
                detectFileSize(url, episodeNum);
            } else {
                status.text('Empty').css('color', '#999');
            }
        });

        // Add paste event listener for better UX
        $('input[name^="episode_url_"]').on('paste', function() {
            var $this = $(this);
            setTimeout(function() {
                $this.trigger('input');
            }, 100);
        });

        // Add event listener for manual size editing
        $('input[name^="episode_size_display_"]').on('input', function() {
            var episodeNum = $(this).attr('name').split('_')[3];
            var manualSize = $(this).val().trim();

            // Update the hidden size field with manual value
            $('input[name="episode_size_' + episodeNum + '"]').val(manualSize);

            // Change background to indicate manual edit
            $(this).css('background', '#e7f3ff');
        });
        
        // Scroll to generated fields
        $('html, body').animate({
            scrollTop: container.offset().top - 50
        }, 500);
    });

    // File size detection function
    function detectFileSize(url, episodeNum) {
        // First try HEAD request to get Content-Length
        $.ajax({
            url: url,
            type: 'HEAD',
            timeout: 10000,
            success: function(data, status, xhr) {
                var contentLength = xhr.getResponseHeader('Content-Length');
                if (contentLength) {
                    var sizeInBytes = parseInt(contentLength);
                    var formattedSize = formatFileSize(sizeInBytes);

                    // Update both hidden and display size fields
                    $('input[name="episode_size_' + episodeNum + '"]').val(formattedSize);
                    $('#episode_size_display_' + episodeNum).val(formattedSize).css('background', '#d4edda');

                    // Update status
                    $('#status_' + episodeNum).text('Ready').css('color', '#46b450');
                } else {
                    // Try alternative method
                    tryAlternativeDetection(url, episodeNum);
                }
            },
            error: function() {
                // Try alternative method
                tryAlternativeDetection(url, episodeNum);
            }
        });
    }

    // Alternative detection method using fetch API
    function tryAlternativeDetection(url, episodeNum) {
        if (typeof fetch !== 'undefined') {
            fetch(url, { method: 'HEAD' })
                .then(response => {
                    var contentLength = response.headers.get('Content-Length');
                    if (contentLength) {
                        var sizeInBytes = parseInt(contentLength);
                        var formattedSize = formatFileSize(sizeInBytes);

                        // Update both hidden and display size fields
                        $('input[name="episode_size_' + episodeNum + '"]').val(formattedSize);
                        $('#episode_size_display_' + episodeNum).val(formattedSize).css('background', '#d4edda');

                        // Update status
                        $('#status_' + episodeNum).text('Ready').css('color', '#46b450');
                    } else {
                        // Fallback: Try to extract size from URL or filename
                        extractSizeFromUrl(url, episodeNum);
                    }
                })
                .catch(() => {
                    // Fallback: Try to extract size from URL or filename
                    extractSizeFromUrl(url, episodeNum);
                });
        } else {
            // Fallback: Try to extract size from URL or filename
            extractSizeFromUrl(url, episodeNum);
        }
    }

    // Extract size from URL patterns
    function extractSizeFromUrl(url, episodeNum) {
        // Common patterns in URLs/filenames
        var sizePatterns = [
            /(\d+(?:\.\d+)?)\s*GB/i,
            /(\d+(?:\.\d+)?)\s*MB/i,
            /(\d+(?:\.\d+)?)\s*KB/i,
            /(\d+(?:\.\d+)?)\s*TB/i,
            /(\d+(?:\.\d+)?)\s*g/i,
            /(\d+(?:\.\d+)?)\s*m/i
        ];

        var detectedSize = null;

        for (var i = 0; i < sizePatterns.length; i++) {
            var match = url.match(sizePatterns[i]);
            if (match) {
                var size = parseFloat(match[1]);
                var unit = match[0].replace(match[1], '').trim().toUpperCase();

                // Normalize unit
                if (unit.includes('G') || unit === 'G') {
                    detectedSize = size + ' GB';
                } else if (unit.includes('M') || unit === 'M') {
                    detectedSize = size + ' MB';
                } else if (unit.includes('K')) {
                    detectedSize = size + ' KB';
                } else if (unit.includes('T')) {
                    detectedSize = size + ' TB';
                }
                break;
            }
        }

        if (detectedSize) {
            // Update both hidden and display size fields
            $('input[name="episode_size_' + episodeNum + '"]').val(detectedSize);
            $('#episode_size_display_' + episodeNum).val(detectedSize).css('background', '#fff3cd');

            // Update status
            $('#status_' + episodeNum).text('Ready').css('color', '#46b450');
        } else {
            // No size detected
            $('#episode_size_display_' + episodeNum).val('Unknown').css('background', '#f8d7da');
            $('#status_' + episodeNum).text('Ready').css('color', '#46b450');
        }
    }

    // Format file size in bytes to human readable format
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Clear Episode Fields
    $('#clear_episode_fields').on('click', function(e) {
        e.preventDefault();
        
        if (confirm('Are you sure you want to clear all episode fields?')) {
            $('#episode_fields_container').empty();
            $('#episode_count').val('');
        }
    });
    
    // Save All Episodes
    $(document).on('click', '#save_all_episodes', function(e) {
        e.preventDefault();
        
        var episodeData = [];
        var hasEmptyFields = false;
        
        // Collect all episode data
        $('input[name^="episode_url_"]').each(function() {
            var episodeNum = $(this).attr('name').split('_')[2];
            var url = $(this).val().trim();
            
            if (url === '') {
                hasEmptyFields = true;
                return;
            }
            
            // Get size from display field (which might be manually edited)
            var finalSize = $('#episode_size_display_' + episodeNum).val() || $('input[name="episode_size_' + episodeNum + '"]').val();

            episodeData.push({
                episode: episodeNum,
                url: url,
                type: $('input[name="episode_type_' + episodeNum + '"]').val(),
                lang: $('input[name="episode_lang_' + episodeNum + '"]').val(),
                quality: $('input[name="episode_quality_' + episodeNum + '"]').val(),
                size: finalSize
            });
        });
        
        if (episodeData.length === 0) {
            alert('Please add at least one episode link');
            return;
        }
        
        if (hasEmptyFields && !confirm('Some episodes have empty links. Do you want to save only the filled ones?')) {
            return;
        }
        
        // Show loading
        $(this).text('Saving...').prop('disabled', true);

        // Get nonce from various possible sources
        var nonceValue = '';
        if ($('#_wpnonce').length) {
            nonceValue = $('#_wpnonce').val();
        } else if ($('input[name="_wpnonce"]').length) {
            nonceValue = $('input[name="_wpnonce"]').val();
        } else if ($('#links_nonce').length) {
            nonceValue = $('#links_nonce').val();
        }

        console.log('Nonce value:', nonceValue);
        console.log('Post ID:', $('#post_ID').val());
        console.log('Episodes data:', episodeData);

        // AJAX call to save episodes
        $.ajax({
            url: (typeof ajaxurl !== 'undefined') ? ajaxurl : '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'save_episode_links',
                post_id: $('#post_ID').val(),
                episodes: episodeData,
                nonce: nonceValue
            },
            success: function(response) {
                console.log('Raw response:', response);

                // Parse response if it's a string
                if (typeof response === 'string') {
                    try {
                        response = JSON.parse(response);
                    } catch (e) {
                        console.error('Failed to parse response:', e);
                        alert('Error: Invalid response from server');
                        return;
                    }
                }

                if (response.success) {
                    alert('Episodes saved successfully!');
                    // Reload the links table
                    $('#dooplay_anchor_reloadllist').trigger('click');
                    // Clear the form
                    $('#episode_fields_container').empty();
                    $('#episode_count').val('');
                } else {
                    alert('Error saving episodes: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error saving episodes. Please try again.');
            },
            complete: function() {
                $('#save_all_episodes').text('Save All Episodes').prop('disabled', false);
            }
        });
    });
    
    // Preview Episodes
    $(document).on('click', '#preview_episodes', function(e) {
        e.preventDefault();
        
        var previewHtml = '<div style="background: #fff; border: 1px solid #ddd; padding: 15px; margin-top: 10px; border-radius: 5px;">';
        previewHtml += '<h4>Episode Links Preview:</h4>';
        previewHtml += '<table style="width: 100%; border-collapse: collapse;">';
        previewHtml += '<thead><tr style="background: #f1f1f1;">';
        previewHtml += '<th style="border: 1px solid #ddd; padding: 8px;">Episode</th>';
        previewHtml += '<th style="border: 1px solid #ddd; padding: 8px;">Type</th>';
        previewHtml += '<th style="border: 1px solid #ddd; padding: 8px;">Language</th>';
        previewHtml += '<th style="border: 1px solid #ddd; padding: 8px;">Quality</th>';
        previewHtml += '<th style="border: 1px solid #ddd; padding: 8px;">Size</th>';
        previewHtml += '<th style="border: 1px solid #ddd; padding: 8px;">URL</th>';
        previewHtml += '</tr></thead><tbody>';
        
        var hasData = false;
        
        $('input[name^="episode_url_"]').each(function() {
            var episodeNum = $(this).attr('name').split('_')[2];
            var url = $(this).val().trim();

            if (url !== '') {
                hasData = true;
                var finalSize = $('#episode_size_display_' + episodeNum).val() || $('input[name="episode_size_' + episodeNum + '"]').val() || 'Unknown';

                previewHtml += '<tr>';
                previewHtml += '<td style="border: 1px solid #ddd; padding: 8px;">EP-' + episodeNum + '</td>';
                previewHtml += '<td style="border: 1px solid #ddd; padding: 8px;">' + $('input[name="episode_type_' + episodeNum + '"]').val() + '</td>';
                previewHtml += '<td style="border: 1px solid #ddd; padding: 8px;">' + $('input[name="episode_lang_' + episodeNum + '"]').val() + '</td>';
                previewHtml += '<td style="border: 1px solid #ddd; padding: 8px;">' + $('input[name="episode_quality_' + episodeNum + '"]').val() + '</td>';
                previewHtml += '<td style="border: 1px solid #ddd; padding: 8px;">' + finalSize + '</td>';
                previewHtml += '<td style="border: 1px solid #ddd; padding: 8px; word-break: break-all;">' + url.substring(0, 50) + (url.length > 50 ? '...' : '') + '</td>';
                previewHtml += '</tr>';
            }
        });
        
        previewHtml += '</tbody></table>';
        
        if (!hasData) {
            previewHtml += '<p style="color: #999; text-align: center;">No episode links to preview</p>';
        }
        
        previewHtml += '<button type="button" onclick="$(this).parent().remove();" class="button button-secondary" style="margin-top: 10px;">Close Preview</button>';
        previewHtml += '</div>';
        
        // Remove existing preview
        $('.episode-links-form').find('div[style*="background: #fff"]').last().after(previewHtml);
    });
    
});
