<?php
/**
 * Premium SEO Optimization
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Premium_SEO {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // SEO meta tags
        add_action('wp_head', array($this, 'add_premium_meta_tags'));
        
        // Open Graph tags
        add_action('wp_head', array($this, 'add_open_graph_tags'));
        
        // Twitter Card tags
        add_action('wp_head', array($this, 'add_twitter_card_tags'));
        
        // JSON-LD structured data
        add_action('wp_head', array($this, 'add_structured_data'));
        
        // Modify page titles
        add_filter('wp_title', array($this, 'modify_premium_page_titles'), 10, 2);
        add_filter('document_title_parts', array($this, 'modify_document_title_parts'));
        
        // Add canonical URLs
        add_action('wp_head', array($this, 'add_canonical_urls'));
        
        // Add robots meta
        add_action('wp_head', array($this, 'add_robots_meta'));
    }
    
    /**
     * Add premium meta tags
     */
    public function add_premium_meta_tags() {
        global $post;
        
        if (is_page()) {
            $page_slug = $post->post_name;
            
            switch ($page_slug) {
                case 'premium-plans':
                    $this->output_premium_plans_meta();
                    break;
                case 'premium-dashboard':
                    $this->output_premium_dashboard_meta();
                    break;
                case 'login':
                    $this->output_login_meta();
                    break;
                case 'register':
                    $this->output_register_meta();
                    break;
                case 'referral-program':
                    $this->output_referral_meta();
                    break;
            }
        }
        
        // Premium content meta
        if (is_single() && function_exists('deshiflix_premium')) {
            $content_manager = DeshiFlix_Premium_Content::get_instance();
            if ($content_manager->is_premium_content(get_the_ID())) {
                $this->output_premium_content_meta();
            }
        }
    }
    
    /**
     * Premium plans meta tags
     */
    private function output_premium_plans_meta() {
        ?>
        <meta name="description" content="Choose from our affordable premium subscription plans. Get unlimited access to HD movies, TV shows, and exclusive content. Starting from ৳299/month with ad-free streaming.">
        <meta name="keywords" content="premium plans, subscription, HD movies, TV shows, streaming, Bangladesh, DeshiFlix, affordable plans">
        <meta name="robots" content="index, follow">
        <meta name="author" content="DeshiFlix">
        
        <!-- Pricing Schema -->
        <meta property="product:price:amount" content="299">
        <meta property="product:price:currency" content="BDT">
        <?php
    }
    
    /**
     * Premium dashboard meta tags
     */
    private function output_premium_dashboard_meta() {
        ?>
        <meta name="description" content="Manage your DeshiFlix premium subscription, view billing history, download content, and access exclusive features from your personal dashboard.">
        <meta name="keywords" content="premium dashboard, account management, subscription, billing, downloads, DeshiFlix">
        <meta name="robots" content="noindex, nofollow">
        <?php
    }
    
    /**
     * Login meta tags
     */
    private function output_login_meta() {
        ?>
        <meta name="description" content="Sign in to your DeshiFlix account to access premium content, manage your subscription, and enjoy unlimited streaming of movies and TV shows.">
        <meta name="keywords" content="login, sign in, DeshiFlix account, premium access, streaming">
        <meta name="robots" content="noindex, follow">
        <?php
    }
    
    /**
     * Register meta tags
     */
    private function output_register_meta() {
        ?>
        <meta name="description" content="Create your free DeshiFlix account and start watching thousands of movies and TV shows. Upgrade to premium for HD quality and ad-free experience.">
        <meta name="keywords" content="register, sign up, create account, free movies, premium subscription, DeshiFlix">
        <meta name="robots" content="index, follow">
        <?php
    }
    
    /**
     * Referral meta tags
     */
    private function output_referral_meta() {
        ?>
        <meta name="description" content="Earn free premium days by referring friends to DeshiFlix. Get rewards for every successful referral and enjoy unlimited streaming together.">
        <meta name="keywords" content="referral program, earn rewards, free premium, invite friends, DeshiFlix rewards">
        <meta name="robots" content="index, follow">
        <?php
    }
    
    /**
     * Premium content meta tags
     */
    private function output_premium_content_meta() {
        ?>
        <meta name="premium-content" content="true">
        <meta name="subscription-required" content="true">
        <?php
    }
    
    /**
     * Add Open Graph tags
     */
    public function add_open_graph_tags() {
        global $post;
        
        if (is_page()) {
            $page_slug = $post->post_name;
            
            switch ($page_slug) {
                case 'premium-plans':
                    ?>
                    <meta property="og:title" content="DeshiFlix Premium Plans - Unlimited Streaming from ৳299/month">
                    <meta property="og:description" content="Choose from our affordable premium plans and enjoy unlimited HD streaming, ad-free experience, and exclusive content access.">
                    <meta property="og:type" content="website">
                    <meta property="og:url" content="<?php echo get_permalink(); ?>">
                    <meta property="og:image" content="<?php echo get_template_directory_uri(); ?>/inc/premium/assets/images/premium-plans-og.jpg">
                    <meta property="og:site_name" content="DeshiFlix">
                    <meta property="og:locale" content="bn_BD">
                    <?php
                    break;
                    
                case 'register':
                    ?>
                    <meta property="og:title" content="Join DeshiFlix - Free Movies & TV Shows">
                    <meta property="og:description" content="Create your free account and start watching thousands of movies and TV shows. Upgrade to premium for the ultimate experience.">
                    <meta property="og:type" content="website">
                    <meta property="og:url" content="<?php echo get_permalink(); ?>">
                    <meta property="og:image" content="<?php echo get_template_directory_uri(); ?>/inc/premium/assets/images/register-og.jpg">
                    <?php
                    break;
            }
        }
    }
    
    /**
     * Add Twitter Card tags
     */
    public function add_twitter_card_tags() {
        global $post;
        
        if (is_page()) {
            $page_slug = $post->post_name;
            
            switch ($page_slug) {
                case 'premium-plans':
                    ?>
                    <meta name="twitter:card" content="summary_large_image">
                    <meta name="twitter:title" content="DeshiFlix Premium Plans - Unlimited Streaming">
                    <meta name="twitter:description" content="Get unlimited access to HD movies and TV shows starting from ৳299/month. Ad-free streaming with exclusive content.">
                    <meta name="twitter:image" content="<?php echo get_template_directory_uri(); ?>/inc/premium/assets/images/premium-plans-twitter.jpg">
                    <meta name="twitter:site" content="@DeshiFlix">
                    <?php
                    break;
            }
        }
    }
    
    /**
     * Add structured data
     */
    public function add_structured_data() {
        global $post;
        
        if (is_page() && $post->post_name === 'premium-plans') {
            $this->output_subscription_schema();
        }
        
        if (is_single() && function_exists('deshiflix_premium')) {
            $content_manager = DeshiFlix_Premium_Content::get_instance();
            if ($content_manager->is_premium_content(get_the_ID())) {
                $this->output_premium_content_schema();
            }
        }
    }
    
    /**
     * Output subscription schema
     */
    private function output_subscription_schema() {
        global $wpdb;
        
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $plans = $wpdb->get_results("SELECT * FROM $table_plans WHERE status = 'active' ORDER BY price ASC");
        
        if (empty($plans)) {
            return;
        }
        
        $offers = array();
        
        foreach ($plans as $plan) {
            $offers[] = array(
                '@type' => 'Offer',
                'name' => $plan->name,
                'description' => $plan->description,
                'price' => $plan->price,
                'priceCurrency' => 'BDT',
                'availability' => 'https://schema.org/InStock',
                'validFrom' => date('Y-m-d'),
                'priceValidUntil' => date('Y-m-d', strtotime('+1 year')),
                'url' => home_url('/premium-plans/')
            );
        }
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Service',
            'name' => 'DeshiFlix Premium Subscription',
            'description' => 'Premium streaming service with unlimited access to movies and TV shows',
            'provider' => array(
                '@type' => 'Organization',
                'name' => 'DeshiFlix',
                'url' => home_url()
            ),
            'offers' => $offers,
            'category' => 'Entertainment',
            'audience' => array(
                '@type' => 'Audience',
                'geographicArea' => 'Bangladesh'
            )
        );
        
        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
    
    /**
     * Output premium content schema
     */
    private function output_premium_content_schema() {
        global $post;
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Movie', // or TVSeries based on post type
            'name' => get_the_title(),
            'description' => get_the_excerpt(),
            'url' => get_permalink(),
            'image' => get_the_post_thumbnail_url($post->ID, 'large'),
            'isAccessibleForFree' => false,
            'hasPart' => array(
                '@type' => 'VideoObject',
                'requiresSubscription' => true,
                'isAccessibleForFree' => false
            ),
            'offers' => array(
                '@type' => 'Offer',
                'category' => 'subscription',
                'eligibleRegion' => 'BD'
            )
        );
        
        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
    
    /**
     * Modify premium page titles
     */
    public function modify_premium_page_titles($title, $sep) {
        global $post;
        
        if (is_page()) {
            $page_slug = $post->post_name;
            
            switch ($page_slug) {
                case 'premium-plans':
                    return 'Premium Plans - Unlimited Streaming from ৳299/month ' . $sep . ' DeshiFlix';
                case 'premium-dashboard':
                    return 'Premium Dashboard - Manage Your Subscription ' . $sep . ' DeshiFlix';
                case 'login':
                    return 'Login - Access Your Account ' . $sep . ' DeshiFlix';
                case 'register':
                    return 'Register - Join DeshiFlix Today ' . $sep . ' DeshiFlix';
                case 'referral-program':
                    return 'Referral Program - Earn Free Premium Days ' . $sep . ' DeshiFlix';
            }
        }
        
        return $title;
    }
    
    /**
     * Modify document title parts
     */
    public function modify_document_title_parts($title_parts) {
        global $post;
        
        if (is_page()) {
            $page_slug = $post->post_name;
            
            switch ($page_slug) {
                case 'premium-plans':
                    $title_parts['title'] = 'Premium Plans - Unlimited Streaming from ৳299/month';
                    break;
                case 'premium-dashboard':
                    $title_parts['title'] = 'Premium Dashboard - Manage Your Subscription';
                    break;
                case 'login':
                    $title_parts['title'] = 'Login - Access Your Account';
                    break;
                case 'register':
                    $title_parts['title'] = 'Register - Join DeshiFlix Today';
                    break;
            }
        }
        
        return $title_parts;
    }
    
    /**
     * Add canonical URLs
     */
    public function add_canonical_urls() {
        global $post;
        
        if (is_page()) {
            $canonical_url = get_permalink();
            echo '<link rel="canonical" href="' . esc_url($canonical_url) . '">' . "\n";
        }
    }
    
    /**
     * Add robots meta
     */
    public function add_robots_meta() {
        global $post;
        
        if (is_page()) {
            $page_slug = $post->post_name;
            
            // Pages that should not be indexed
            $noindex_pages = array('premium-dashboard', 'login');
            
            if (in_array($page_slug, $noindex_pages)) {
                echo '<meta name="robots" content="noindex, nofollow">' . "\n";
            } else {
                echo '<meta name="robots" content="index, follow">' . "\n";
            }
        }
    }
}

// Initialize premium SEO
DeshiFlix_Premium_SEO::get_instance();
