<?php
/**
 * DeshiFlix Premium System - Analytics & Reporting Class
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Premium_Analytics {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Analytics data cache
     */
    private $cache = array();
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize analytics system
     */
    private function init() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Admin hooks
        add_action('admin_menu', array($this, 'add_analytics_menu'));
        add_action('wp_ajax_get_analytics_data', array($this, 'ajax_get_analytics_data'));
        add_action('wp_ajax_export_analytics_data', array($this, 'ajax_export_analytics_data'));
        
        // Tracking hooks
        add_action('deshiflix_premium_subscription_activated', array($this, 'track_subscription_activation'), 10, 2);
        add_action('deshiflix_premium_payment_completed', array($this, 'track_payment_completion'), 10, 2);
        add_action('deshiflix_premium_content_viewed', array($this, 'track_content_view'), 10, 2);
        add_action('deshiflix_premium_download_started', array($this, 'track_download'), 10, 2);
        
        // Cron hooks
        add_action('deshiflix_generate_daily_reports', array($this, 'generate_daily_reports'));
        add_action('deshiflix_generate_monthly_reports', array($this, 'generate_monthly_reports'));
        
        // Dashboard widgets
        add_action('wp_dashboard_setup', array($this, 'add_dashboard_widgets'));
    }
    
    /**
     * Add analytics menu
     */
    public function add_analytics_menu() {
        add_submenu_page(
            'deshiflix-premium',
            __('Analytics & Reports', 'deshiflix'),
            __('Analytics', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-analytics',
            array($this, 'analytics_page')
        );
    }
    
    /**
     * Analytics page
     */
    public function analytics_page() {
        ?>
        <div class="wrap premium-analytics-wrap">
            <h1 class="wp-heading-inline">
                <span class="dashicons dashicons-chart-bar"></span>
                <?php _e('Premium Analytics & Reports', 'deshiflix'); ?>
            </h1>
            
            <div class="analytics-filters">
                <div class="filter-group">
                    <label for="analytics-period"><?php _e('Period:', 'deshiflix'); ?></label>
                    <select id="analytics-period">
                        <option value="7"><?php _e('Last 7 days', 'deshiflix'); ?></option>
                        <option value="30" selected><?php _e('Last 30 days', 'deshiflix'); ?></option>
                        <option value="90"><?php _e('Last 90 days', 'deshiflix'); ?></option>
                        <option value="365"><?php _e('Last year', 'deshiflix'); ?></option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="analytics-metric"><?php _e('Metric:', 'deshiflix'); ?></label>
                    <select id="analytics-metric">
                        <option value="revenue"><?php _e('Revenue', 'deshiflix'); ?></option>
                        <option value="subscriptions"><?php _e('Subscriptions', 'deshiflix'); ?></option>
                        <option value="content_views"><?php _e('Content Views', 'deshiflix'); ?></option>
                        <option value="downloads"><?php _e('Downloads', 'deshiflix'); ?></option>
                    </select>
                </div>
                
                <button class="button button-primary" id="update-analytics">
                    <?php _e('Update', 'deshiflix'); ?>
                </button>
                
                <button class="button" id="export-analytics">
                    <?php _e('Export Data', 'deshiflix'); ?>
                </button>
            </div>
            
            <!-- Key Metrics -->
            <div class="analytics-overview">
                <h2><?php _e('Key Metrics', 'deshiflix'); ?></h2>
                <div class="metrics-grid">
                    <div class="metric-card revenue">
                        <div class="metric-icon">💰</div>
                        <div class="metric-content">
                            <h3 id="total-revenue">৳0</h3>
                            <p><?php _e('Total Revenue', 'deshiflix'); ?></p>
                            <span class="metric-change" id="revenue-change">+0%</span>
                        </div>
                    </div>
                    
                    <div class="metric-card subscriptions">
                        <div class="metric-icon">👥</div>
                        <div class="metric-content">
                            <h3 id="total-subscriptions">0</h3>
                            <p><?php _e('Active Subscriptions', 'deshiflix'); ?></p>
                            <span class="metric-change" id="subscriptions-change">+0%</span>
                        </div>
                    </div>
                    
                    <div class="metric-card conversion">
                        <div class="metric-icon">📈</div>
                        <div class="metric-content">
                            <h3 id="conversion-rate">0%</h3>
                            <p><?php _e('Conversion Rate', 'deshiflix'); ?></p>
                            <span class="metric-change" id="conversion-change">+0%</span>
                        </div>
                    </div>
                    
                    <div class="metric-card churn">
                        <div class="metric-icon">📉</div>
                        <div class="metric-content">
                            <h3 id="churn-rate">0%</h3>
                            <p><?php _e('Churn Rate', 'deshiflix'); ?></p>
                            <span class="metric-change" id="churn-change">+0%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts -->
            <div class="analytics-charts">
                <div class="chart-container">
                    <h3><?php _e('Revenue Trend', 'deshiflix'); ?></h3>
                    <canvas id="revenueChart" width="400" height="200"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3><?php _e('Subscription Growth', 'deshiflix'); ?></h3>
                    <canvas id="subscriptionChart" width="400" height="200"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3><?php _e('Payment Methods Distribution', 'deshiflix'); ?></h3>
                    <canvas id="paymentMethodsChart" width="400" height="200"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3><?php _e('Plan Distribution', 'deshiflix'); ?></h3>
                    <canvas id="planDistributionChart" width="400" height="200"></canvas>
                </div>
            </div>
            
            <!-- Detailed Reports -->
            <div class="analytics-reports">
                <div class="report-tabs">
                    <nav class="nav-tab-wrapper">
                        <a href="#revenue-report" class="nav-tab nav-tab-active"><?php _e('Revenue Report', 'deshiflix'); ?></a>
                        <a href="#user-report" class="nav-tab"><?php _e('User Report', 'deshiflix'); ?></a>
                        <a href="#content-report" class="nav-tab"><?php _e('Content Report', 'deshiflix'); ?></a>
                        <a href="#conversion-report" class="nav-tab"><?php _e('Conversion Report', 'deshiflix'); ?></a>
                    </nav>
                    
                    <div id="revenue-report" class="tab-content active">
                        <h3><?php _e('Revenue Analysis', 'deshiflix'); ?></h3>
                        <div id="revenue-report-content">
                            <?php $this->render_revenue_report(); ?>
                        </div>
                    </div>
                    
                    <div id="user-report" class="tab-content">
                        <h3><?php _e('User Analytics', 'deshiflix'); ?></h3>
                        <div id="user-report-content">
                            <?php $this->render_user_report(); ?>
                        </div>
                    </div>
                    
                    <div id="content-report" class="tab-content">
                        <h3><?php _e('Content Performance', 'deshiflix'); ?></h3>
                        <div id="content-report-content">
                            <?php $this->render_content_report(); ?>
                        </div>
                    </div>
                    
                    <div id="conversion-report" class="tab-content">
                        <h3><?php _e('Conversion Funnel', 'deshiflix'); ?></h3>
                        <div id="conversion-report-content">
                            <?php $this->render_conversion_report(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .premium-analytics-wrap {
            margin: 20px 0;
        }
        
        .analytics-filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 20px;
            align-items: end;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .filter-group label {
            font-weight: bold;
            color: #333;
        }
        
        .filter-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .metric-icon {
            font-size: 2.5rem;
        }
        
        .metric-content h3 {
            margin: 0;
            font-size: 2rem;
            color: #333;
        }
        
        .metric-content p {
            margin: 5px 0;
            color: #666;
        }
        
        .metric-change {
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .metric-change.positive {
            color: #46b450;
        }
        
        .metric-change.negative {
            color: #dc3232;
        }
        
        .analytics-charts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .chart-container h3 {
            margin-top: 0;
            color: #333;
        }
        
        .analytics-reports {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .report-tabs .nav-tab-wrapper {
            margin: 0;
            border-bottom: 1px solid #ddd;
        }
        
        .tab-content {
            display: none;
            padding: 20px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .report-table th,
        .report-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .report-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .report-table tr:hover {
            background: #f8f9fa;
        }
        
        @media (max-width: 768px) {
            .analytics-filters {
                flex-direction: column;
                align-items: stretch;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .analytics-charts {
                grid-template-columns: 1fr;
            }
        }
        </style>

        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
        jQuery(document).ready(function($) {
            // Initialize charts
            loadAnalyticsData();
            
            // Update analytics
            $('#update-analytics').click(function() {
                loadAnalyticsData();
            });
            
            // Export data
            $('#export-analytics').click(function() {
                exportAnalyticsData();
            });
            
            // Tab switching
            $('.nav-tab').click(function(e) {
                e.preventDefault();
                
                $('.nav-tab').removeClass('nav-tab-active');
                $('.tab-content').removeClass('active');
                
                $(this).addClass('nav-tab-active');
                var target = $(this).attr('href');
                $(target).addClass('active');
            });
            
            function loadAnalyticsData() {
                var period = $('#analytics-period').val();
                var metric = $('#analytics-metric').val();
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'get_analytics_data',
                        period: period,
                        metric: metric,
                        nonce: '<?php echo wp_create_nonce('premium_analytics_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            updateMetrics(response.data.metrics);
                            updateCharts(response.data.charts);
                        }
                    }
                });
            }
            
            function updateMetrics(metrics) {
                $('#total-revenue').text('৳' + formatNumber(metrics.total_revenue));
                $('#total-subscriptions').text(formatNumber(metrics.total_subscriptions));
                $('#conversion-rate').text(metrics.conversion_rate + '%');
                $('#churn-rate').text(metrics.churn_rate + '%');
                
                // Update changes
                updateMetricChange('#revenue-change', metrics.revenue_change);
                updateMetricChange('#subscriptions-change', metrics.subscriptions_change);
                updateMetricChange('#conversion-change', metrics.conversion_change);
                updateMetricChange('#churn-change', metrics.churn_change);
            }
            
            function updateMetricChange(selector, change) {
                var $element = $(selector);
                var prefix = change >= 0 ? '+' : '';
                $element.text(prefix + change + '%');
                $element.removeClass('positive negative');
                $element.addClass(change >= 0 ? 'positive' : 'negative');
            }
            
            function updateCharts(chartData) {
                // Update revenue chart
                updateRevenueChart(chartData.revenue);
                
                // Update subscription chart
                updateSubscriptionChart(chartData.subscriptions);
                
                // Update payment methods chart
                updatePaymentMethodsChart(chartData.payment_methods);
                
                // Update plan distribution chart
                updatePlanDistributionChart(chartData.plan_distribution);
            }
            
            function updateRevenueChart(data) {
                var ctx = document.getElementById('revenueChart').getContext('2d');
                
                if (window.revenueChart) {
                    window.revenueChart.destroy();
                }
                
                window.revenueChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            label: 'Revenue (৳)',
                            data: data.values,
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            function updateSubscriptionChart(data) {
                var ctx = document.getElementById('subscriptionChart').getContext('2d');
                
                if (window.subscriptionChart) {
                    window.subscriptionChart.destroy();
                }
                
                window.subscriptionChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            label: 'New Subscriptions',
                            data: data.values,
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            function updatePaymentMethodsChart(data) {
                var ctx = document.getElementById('paymentMethodsChart').getContext('2d');
                
                if (window.paymentMethodsChart) {
                    window.paymentMethodsChart.destroy();
                }
                
                window.paymentMethodsChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            data: data.values,
                            backgroundColor: [
                                '#FF6384',
                                '#36A2EB',
                                '#FFCE56',
                                '#4BC0C0',
                                '#9966FF'
                            ]
                        }]
                    },
                    options: {
                        responsive: true
                    }
                });
            }
            
            function updatePlanDistributionChart(data) {
                var ctx = document.getElementById('planDistributionChart').getContext('2d');
                
                if (window.planDistributionChart) {
                    window.planDistributionChart.destroy();
                }
                
                window.planDistributionChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            data: data.values,
                            backgroundColor: [
                                '#FF6384',
                                '#36A2EB',
                                '#FFCE56',
                                '#4BC0C0'
                            ]
                        }]
                    },
                    options: {
                        responsive: true
                    }
                });
            }
            
            function formatNumber(num) {
                return new Intl.NumberFormat('en-BD').format(num);
            }
            
            function exportAnalyticsData() {
                var period = $('#analytics-period').val();
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'export_analytics_data',
                        period: period,
                        nonce: '<?php echo wp_create_nonce('premium_analytics_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Create download link
                            var blob = new Blob([response.data.csv], { type: 'text/csv' });
                            var url = window.URL.createObjectURL(blob);
                            var a = document.createElement('a');
                            a.href = url;
                            a.download = 'premium-analytics-' + new Date().toISOString().split('T')[0] + '.csv';
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            window.URL.revokeObjectURL(url);
                        }
                    }
                });
            }
        });
        </script>
        <?php
    }
    
    /**
     * Get analytics data
     */
    public function get_analytics_data($period = 30, $metric = 'revenue') {
        $cache_key = "analytics_data_{$period}_{$metric}";
        
        if (isset($this->cache[$cache_key])) {
            return $this->cache[$cache_key];
        }
        
        global $wpdb;
        
        $data = array(
            'metrics' => $this->get_key_metrics($period),
            'charts' => $this->get_chart_data($period)
        );
        
        $this->cache[$cache_key] = $data;
        
        return $data;
    }
    
    /**
     * Get key metrics
     */
    private function get_key_metrics($period) {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        
        $date_condition = "DATE(created_at) >= DATE_SUB(NOW(), INTERVAL $period DAY)";
        
        // Total revenue
        $total_revenue = $wpdb->get_var(
            "SELECT SUM(amount) FROM $table_transactions 
             WHERE status = 'completed' AND $date_condition"
        ) ?: 0;
        
        // Previous period revenue for comparison
        $prev_revenue = $wpdb->get_var(
            "SELECT SUM(amount) FROM $table_transactions 
             WHERE status = 'completed' 
             AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL " . ($period * 2) . " DAY)
             AND DATE(created_at) < DATE_SUB(NOW(), INTERVAL $period DAY)"
        ) ?: 0;
        
        $revenue_change = $prev_revenue > 0 ? 
                         round((($total_revenue - $prev_revenue) / $prev_revenue) * 100, 1) : 0;
        
        // Active subscriptions
        $total_subscriptions = $wpdb->get_var(
            "SELECT COUNT(*) FROM $table_users WHERE status = 'active'"
        ) ?: 0;
        
        // New subscriptions in period
        $new_subscriptions = $wpdb->get_var(
            "SELECT COUNT(*) FROM $table_users WHERE $date_condition"
        ) ?: 0;
        
        $prev_subscriptions = $wpdb->get_var(
            "SELECT COUNT(*) FROM $table_users 
             WHERE DATE(created_at) >= DATE_SUB(NOW(), INTERVAL " . ($period * 2) . " DAY)
             AND DATE(created_at) < DATE_SUB(NOW(), INTERVAL $period DAY)"
        ) ?: 0;
        
        $subscriptions_change = $prev_subscriptions > 0 ? 
                               round((($new_subscriptions - $prev_subscriptions) / $prev_subscriptions) * 100, 1) : 0;
        
        // Conversion rate (simplified)
        $total_visitors = $this->get_total_visitors($period);
        $conversion_rate = $total_visitors > 0 ? 
                          round(($new_subscriptions / $total_visitors) * 100, 2) : 0;
        
        // Churn rate
        $expired_subscriptions = $wpdb->get_var(
            "SELECT COUNT(*) FROM $table_users 
             WHERE status = 'expired' AND $date_condition"
        ) ?: 0;
        
        $churn_rate = $total_subscriptions > 0 ? 
                     round(($expired_subscriptions / $total_subscriptions) * 100, 2) : 0;
        
        return array(
            'total_revenue' => $total_revenue,
            'revenue_change' => $revenue_change,
            'total_subscriptions' => $total_subscriptions,
            'subscriptions_change' => $subscriptions_change,
            'conversion_rate' => $conversion_rate,
            'conversion_change' => 0, // Would need more complex calculation
            'churn_rate' => $churn_rate,
            'churn_change' => 0 // Would need more complex calculation
        );
    }
    
    /**
     * Get chart data
     */
    private function get_chart_data($period) {
        return array(
            'revenue' => $this->get_revenue_chart_data($period),
            'subscriptions' => $this->get_subscription_chart_data($period),
            'payment_methods' => $this->get_payment_methods_data($period),
            'plan_distribution' => $this->get_plan_distribution_data($period)
        );
    }
    
    /**
     * Get revenue chart data
     */
    private function get_revenue_chart_data($period) {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        $results = $wpdb->get_results(
            "SELECT DATE(created_at) as date, SUM(amount) as revenue 
             FROM $table_transactions 
             WHERE status = 'completed' 
             AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL $period DAY)
             GROUP BY DATE(created_at) 
             ORDER BY date ASC"
        );
        
        $labels = array();
        $values = array();
        
        foreach ($results as $result) {
            $labels[] = date('M j', strtotime($result->date));
            $values[] = floatval($result->revenue);
        }
        
        return array('labels' => $labels, 'values' => $values);
    }
    
    /**
     * Get subscription chart data
     */
    private function get_subscription_chart_data($period) {
        global $wpdb;
        
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        
        $results = $wpdb->get_results(
            "SELECT DATE(created_at) as date, COUNT(*) as subscriptions 
             FROM $table_users 
             WHERE DATE(created_at) >= DATE_SUB(NOW(), INTERVAL $period DAY)
             GROUP BY DATE(created_at) 
             ORDER BY date ASC"
        );
        
        $labels = array();
        $values = array();
        
        foreach ($results as $result) {
            $labels[] = date('M j', strtotime($result->date));
            $values[] = intval($result->subscriptions);
        }
        
        return array('labels' => $labels, 'values' => $values);
    }
    
    /**
     * Get payment methods data
     */
    private function get_payment_methods_data($period) {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        
        $results = $wpdb->get_results(
            "SELECT payment_method, COUNT(*) as count 
             FROM $table_transactions 
             WHERE status = 'completed' 
             AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL $period DAY)
             GROUP BY payment_method 
             ORDER BY count DESC"
        );
        
        $labels = array();
        $values = array();
        
        foreach ($results as $result) {
            $labels[] = ucfirst($result->payment_method);
            $values[] = intval($result->count);
        }
        
        return array('labels' => $labels, 'values' => $values);
    }
    
    /**
     * Get plan distribution data
     */
    private function get_plan_distribution_data($period) {
        global $wpdb;
        
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $results = $wpdb->get_results(
            "SELECT p.name, COUNT(pu.id) as count 
             FROM $table_users pu 
             JOIN $table_plans p ON pu.plan_id = p.id 
             WHERE pu.status = 'active'
             GROUP BY p.id 
             ORDER BY count DESC"
        );
        
        $labels = array();
        $values = array();
        
        foreach ($results as $result) {
            $labels[] = $result->name;
            $values[] = intval($result->count);
        }
        
        return array('labels' => $labels, 'values' => $values);
    }
    
    /**
     * Get total visitors (simplified)
     */
    private function get_total_visitors($period) {
        global $wpdb;
        
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        return $wpdb->get_var(
            "SELECT COUNT(DISTINCT session_id) FROM $table_analytics 
             WHERE DATE(created_at) >= DATE_SUB(NOW(), INTERVAL $period DAY)"
        ) ?: 100; // Fallback value
    }
    
    /**
     * Render revenue report
     */
    private function render_revenue_report() {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $revenue_by_plan = $wpdb->get_results(
            "SELECT p.name, SUM(t.amount) as revenue, COUNT(t.id) as transactions 
             FROM $table_transactions t 
             JOIN $table_plans p ON t.plan_id = p.id 
             WHERE t.status = 'completed' 
             AND DATE(t.created_at) >= DATE_SUB(NOW(), INTERVAL 30 DAY)
             GROUP BY p.id 
             ORDER BY revenue DESC"
        );
        
        ?>
        <table class="report-table">
            <thead>
                <tr>
                    <th><?php _e('Plan', 'deshiflix'); ?></th>
                    <th><?php _e('Revenue', 'deshiflix'); ?></th>
                    <th><?php _e('Transactions', 'deshiflix'); ?></th>
                    <th><?php _e('Avg. per Transaction', 'deshiflix'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($revenue_by_plan as $plan): ?>
                <tr>
                    <td><?php echo esc_html($plan->name); ?></td>
                    <td>৳<?php echo number_format($plan->revenue, 2); ?></td>
                    <td><?php echo number_format($plan->transactions); ?></td>
                    <td>৳<?php echo number_format($plan->revenue / $plan->transactions, 2); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * Render user report
     */
    private function render_user_report() {
        global $wpdb;
        
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $user_stats = $wpdb->get_results(
            "SELECT p.name, COUNT(pu.id) as users, 
             AVG(DATEDIFF(pu.end_date, pu.start_date)) as avg_duration 
             FROM $table_users pu 
             JOIN $table_plans p ON pu.plan_id = p.id 
             WHERE pu.status = 'active'
             GROUP BY p.id 
             ORDER BY users DESC"
        );
        
        ?>
        <table class="report-table">
            <thead>
                <tr>
                    <th><?php _e('Plan', 'deshiflix'); ?></th>
                    <th><?php _e('Active Users', 'deshiflix'); ?></th>
                    <th><?php _e('Avg. Duration (Days)', 'deshiflix'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($user_stats as $stat): ?>
                <tr>
                    <td><?php echo esc_html($stat->name); ?></td>
                    <td><?php echo number_format($stat->users); ?></td>
                    <td><?php echo round($stat->avg_duration); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * Render content report
     */
    private function render_content_report() {
        global $wpdb;
        
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        $content_stats = $wpdb->get_results(
            "SELECT p.post_title, COUNT(a.id) as views 
             FROM $table_analytics a 
             JOIN {$wpdb->posts} p ON a.post_id = p.ID 
             WHERE a.event_type = 'premium_content_view' 
             AND DATE(a.created_at) >= DATE_SUB(NOW(), INTERVAL 30 DAY)
             GROUP BY a.post_id 
             ORDER BY views DESC 
             LIMIT 10"
        );
        
        ?>
        <table class="report-table">
            <thead>
                <tr>
                    <th><?php _e('Content', 'deshiflix'); ?></th>
                    <th><?php _e('Premium Views', 'deshiflix'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($content_stats as $content): ?>
                <tr>
                    <td><?php echo esc_html($content->post_title); ?></td>
                    <td><?php echo number_format($content->views); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * Render conversion report
     */
    private function render_conversion_report() {
        ?>
        <div class="conversion-funnel">
            <div class="funnel-step">
                <h4><?php _e('Visitors', 'deshiflix'); ?></h4>
                <div class="funnel-number" id="funnel-visitors">0</div>
            </div>
            <div class="funnel-arrow">↓</div>
            <div class="funnel-step">
                <h4><?php _e('Premium Page Views', 'deshiflix'); ?></h4>
                <div class="funnel-number" id="funnel-premium-views">0</div>
            </div>
            <div class="funnel-arrow">↓</div>
            <div class="funnel-step">
                <h4><?php _e('Payment Initiated', 'deshiflix'); ?></h4>
                <div class="funnel-number" id="funnel-payment-initiated">0</div>
            </div>
            <div class="funnel-arrow">↓</div>
            <div class="funnel-step">
                <h4><?php _e('Subscriptions', 'deshiflix'); ?></h4>
                <div class="funnel-number" id="funnel-subscriptions">0</div>
            </div>
        </div>
        
        <style>
        .conversion-funnel {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 40px 0;
        }
        
        .funnel-step {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            min-width: 150px;
        }
        
        .funnel-step h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .funnel-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .funnel-arrow {
            font-size: 2rem;
            color: #667eea;
        }
        
        @media (max-width: 768px) {
            .conversion-funnel {
                flex-direction: column;
            }
            
            .funnel-arrow {
                transform: rotate(90deg);
            }
        }
        </style>
        <?php
    }
    
    /**
     * Track subscription activation
     */
    public function track_subscription_activation($user_id, $plan_id) {
        $this->track_event('subscription_activated', array(
            'user_id' => $user_id,
            'plan_id' => $plan_id
        ));
    }
    
    /**
     * Track payment completion
     */
    public function track_payment_completion($transaction_id, $amount) {
        $this->track_event('payment_completed', array(
            'transaction_id' => $transaction_id,
            'amount' => $amount
        ));
    }
    
    /**
     * Track content view
     */
    public function track_content_view($post_id, $user_id) {
        $this->track_event('premium_content_view', array(
            'post_id' => $post_id,
            'user_id' => $user_id
        ));
    }
    
    /**
     * Track download
     */
    public function track_download($post_id, $user_id) {
        $this->track_event('premium_download', array(
            'post_id' => $post_id,
            'user_id' => $user_id
        ));
    }
    
    /**
     * Track event
     */
    private function track_event($event_type, $data) {
        global $wpdb;
        
        $table_analytics = $wpdb->prefix . 'deshiflix_premium_analytics';
        
        $wpdb->insert($table_analytics, array(
            'user_id' => $data['user_id'] ?? null,
            'event_type' => $event_type,
            'event_data' => json_encode($data),
            'post_id' => $data['post_id'] ?? null,
            'session_id' => session_id(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ));
    }
    
    /**
     * AJAX get analytics data
     */
    public function ajax_get_analytics_data() {
        check_ajax_referer('premium_analytics_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied', 'deshiflix')));
        }
        
        $period = intval($_POST['period']);
        $metric = sanitize_text_field($_POST['metric']);
        
        $data = $this->get_analytics_data($period, $metric);
        
        wp_send_json_success($data);
    }
    
    /**
     * AJAX export analytics data
     */
    public function ajax_export_analytics_data() {
        check_ajax_referer('premium_analytics_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied', 'deshiflix')));
        }
        
        $period = intval($_POST['period']);
        $csv_data = $this->generate_csv_export($period);
        
        wp_send_json_success(array('csv' => $csv_data));
    }
    
    /**
     * Generate CSV export
     */
    private function generate_csv_export($period) {
        global $wpdb;
        
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $transactions = $wpdb->get_results(
            "SELECT t.*, p.name as plan_name, u.display_name 
             FROM $table_transactions t 
             LEFT JOIN $table_plans p ON t.plan_id = p.id 
             LEFT JOIN {$wpdb->users} u ON t.user_id = u.ID 
             WHERE DATE(t.created_at) >= DATE_SUB(NOW(), INTERVAL $period DAY)
             ORDER BY t.created_at DESC"
        );
        
        $csv = "Date,User,Plan,Amount,Payment Method,Status\n";
        
        foreach ($transactions as $transaction) {
            $csv .= sprintf(
                "%s,%s,%s,%.2f,%s,%s\n",
                $transaction->created_at,
                $transaction->display_name,
                $transaction->plan_name,
                $transaction->amount,
                $transaction->payment_method,
                $transaction->status
            );
        }
        
        return $csv;
    }
    
    /**
     * Generate daily reports
     */
    public function generate_daily_reports() {
        $data = $this->get_analytics_data(1, 'revenue');
        
        // Store daily report
        update_option('deshiflix_daily_report_' . date('Y-m-d'), $data);
        
        // Send to admin if configured
        $settings = get_option('deshiflix_premium_settings', array());
        
        if (isset($settings['daily_reports']) && $settings['daily_reports']) {
            $this->send_daily_report($data);
        }
    }
    
    /**
     * Generate monthly reports
     */
    public function generate_monthly_reports() {
        $data = $this->get_analytics_data(30, 'revenue');
        
        // Store monthly report
        update_option('deshiflix_monthly_report_' . date('Y-m'), $data);
        
        // Send to admin
        $this->send_monthly_report($data);
    }
    
    /**
     * Send daily report
     */
    private function send_daily_report($data) {
        $admin_email = get_option('admin_email');
        $subject = sprintf(__('DeshiFlix Daily Report - %s', 'deshiflix'), date('F j, Y'));
        
        $message = sprintf(
            __("Daily Premium Report\n\nRevenue: ৳%.2f\nNew Subscriptions: %d\nActive Users: %d", 'deshiflix'),
            $data['metrics']['total_revenue'],
            $data['metrics']['total_subscriptions'],
            $data['metrics']['total_subscriptions']
        );
        
        wp_mail($admin_email, $subject, $message);
    }
    
    /**
     * Send monthly report
     */
    private function send_monthly_report($data) {
        $admin_email = get_option('admin_email');
        $subject = sprintf(__('DeshiFlix Monthly Report - %s', 'deshiflix'), date('F Y'));
        
        $message = sprintf(
            __("Monthly Premium Report\n\nTotal Revenue: ৳%.2f\nTotal Subscriptions: %d\nConversion Rate: %.2f%%\nChurn Rate: %.2f%%", 'deshiflix'),
            $data['metrics']['total_revenue'],
            $data['metrics']['total_subscriptions'],
            $data['metrics']['conversion_rate'],
            $data['metrics']['churn_rate']
        );
        
        wp_mail($admin_email, $subject, $message);
    }
    
    /**
     * Add dashboard widgets
     */
    public function add_dashboard_widgets() {
        if (current_user_can('manage_options')) {
            wp_add_dashboard_widget(
                'deshiflix_premium_stats',
                __('Premium Statistics', 'deshiflix'),
                array($this, 'dashboard_widget_content')
            );
        }
    }
    
    /**
     * Dashboard widget content
     */
    public function dashboard_widget_content() {
        $data = $this->get_analytics_data(7, 'revenue');
        $metrics = $data['metrics'];
        
        ?>
        <div class="premium-dashboard-widget">
            <div class="widget-stats">
                <div class="stat-item">
                    <span class="stat-label"><?php _e('Weekly Revenue:', 'deshiflix'); ?></span>
                    <span class="stat-value">৳<?php echo number_format($metrics['total_revenue'], 2); ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><?php _e('Active Subscriptions:', 'deshiflix'); ?></span>
                    <span class="stat-value"><?php echo number_format($metrics['total_subscriptions']); ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><?php _e('Conversion Rate:', 'deshiflix'); ?></span>
                    <span class="stat-value"><?php echo $metrics['conversion_rate']; ?>%</span>
                </div>
            </div>
            <p>
                <a href="<?php echo admin_url('admin.php?page=deshiflix-premium-analytics'); ?>" class="button button-primary">
                    <?php _e('View Full Analytics', 'deshiflix'); ?>
                </a>
            </p>
        </div>
        
        <style>
        .premium-dashboard-widget .widget-stats {
            margin-bottom: 15px;
        }
        
        .premium-dashboard-widget .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .premium-dashboard-widget .stat-label {
            color: #666;
        }
        
        .premium-dashboard-widget .stat-value {
            font-weight: bold;
            color: #333;
        }
        </style>
        <?php
    }
}

// Initialize analytics
DeshiFlix_Premium_Analytics::get_instance();
