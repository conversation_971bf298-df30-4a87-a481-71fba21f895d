<?php
/**
 * Premium Marketing System
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Premium_Marketing {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Add marketing banners
        add_action('wp_footer', array($this, 'add_premium_promotion_banner'));
        add_action('dooplay_single_content_after', array($this, 'add_content_upgrade_prompt'));
        
        // Add premium badges
        add_filter('the_title', array($this, 'add_premium_badge_to_title'), 10, 2);
        add_action('dooplay_archive_item_after', array($this, 'add_premium_overlay'));
        
        // Add marketing widgets
        add_action('widgets_init', array($this, 'register_marketing_widgets'));
        
        // Add promotional popups
        add_action('wp_footer', array($this, 'add_promotional_popup'));
        
        // Add premium teasers
        add_action('wp_head', array($this, 'add_marketing_styles'));
        
        // AJAX handlers
        add_action('wp_ajax_dismiss_promotion', array($this, 'dismiss_promotion'));
        add_action('wp_ajax_nopriv_dismiss_promotion', array($this, 'dismiss_promotion'));
    }
    
    /**
     * Add premium promotion banner
     */
    public function add_premium_promotion_banner() {
        if (is_user_logged_in() && function_exists('deshiflix_premium')) {
            $premium_core = deshiflix_premium();
            if ($premium_core->is_user_premium(get_current_user_id())) {
                return; // Don't show to premium users
            }
        }
        
        // Check if user dismissed the banner
        if (isset($_COOKIE['deshiflix_banner_dismissed'])) {
            return;
        }
        ?>
        <div id="premium-promotion-banner" class="premium-promotion-banner">
            <div class="banner-content">
                <div class="banner-text">
                    <h3>🎬 Upgrade to Premium Today!</h3>
                    <p>Get unlimited access to HD movies, ad-free streaming, and exclusive content starting from ৳299/month</p>
                </div>
                <div class="banner-actions">
                    <a href="/premium-plans/" class="btn btn-premium">
                        <i class="fas fa-crown"></i>
                        View Plans
                    </a>
                    <button class="btn btn-dismiss" onclick="dismissPromotionBanner()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Add content upgrade prompt
     */
    public function add_content_upgrade_prompt() {
        if (!is_single()) {
            return;
        }
        
        global $post;
        
        // Check if content is premium
        if (function_exists('deshiflix_premium')) {
            $content_manager = DeshiFlix_Premium_Content::get_instance();
            if ($content_manager->is_premium_content($post->ID)) {
                $user_id = get_current_user_id();
                
                if (!$user_id || !$content_manager->user_has_content_access($post->ID, $user_id)) {
                    ?>
                    <div class="premium-upgrade-prompt">
                        <div class="upgrade-content">
                            <div class="upgrade-icon">
                                <i class="fas fa-crown"></i>
                            </div>
                            <div class="upgrade-text">
                                <h3>This is Premium Content</h3>
                                <p>Upgrade to premium to watch this content in HD quality with no ads and download for offline viewing.</p>
                            </div>
                            <div class="upgrade-actions">
                                <a href="/premium-plans/" class="btn btn-premium">
                                    Upgrade Now
                                </a>
                                <?php if (!is_user_logged_in()): ?>
                                <a href="/login/" class="btn btn-secondary">
                                    Login
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="upgrade-features">
                            <h4>Premium Benefits:</h4>
                            <ul>
                                <li><i class="fas fa-check"></i> HD & 4K Quality</li>
                                <li><i class="fas fa-check"></i> Ad-Free Experience</li>
                                <li><i class="fas fa-check"></i> Download for Offline</li>
                                <li><i class="fas fa-check"></i> Early Access</li>
                                <li><i class="fas fa-check"></i> Multiple Devices</li>
                            </ul>
                        </div>
                    </div>
                    <?php
                }
            }
        }
    }
    
    /**
     * Add premium badge to title
     */
    public function add_premium_badge_to_title($title, $post_id) {
        if (is_admin() || !$post_id) {
            return $title;
        }
        
        if (function_exists('deshiflix_premium')) {
            $content_manager = DeshiFlix_Premium_Content::get_instance();
            if ($content_manager->is_premium_content($post_id)) {
                $title .= ' <span class="premium-badge"><i class="fas fa-crown"></i> Premium</span>';
            }
        }
        
        return $title;
    }
    
    /**
     * Add premium overlay to archive items
     */
    public function add_premium_overlay() {
        global $post;
        
        if (function_exists('deshiflix_premium')) {
            $content_manager = DeshiFlix_Premium_Content::get_instance();
            if ($content_manager->is_premium_content($post->ID)) {
                ?>
                <div class="premium-overlay">
                    <div class="premium-overlay-content">
                        <i class="fas fa-crown"></i>
                        <span>Premium</span>
                    </div>
                </div>
                <?php
            }
        }
    }
    
    /**
     * Register marketing widgets
     */
    public function register_marketing_widgets() {
        register_widget('DeshiFlix_Premium_Widget');
    }
    
    /**
     * Add promotional popup
     */
    public function add_promotional_popup() {
        if (is_user_logged_in() && function_exists('deshiflix_premium')) {
            $premium_core = deshiflix_premium();
            if ($premium_core->is_user_premium(get_current_user_id())) {
                return; // Don't show to premium users
            }
        }
        
        // Check if user dismissed popup recently
        if (isset($_COOKIE['deshiflix_popup_dismissed'])) {
            return;
        }
        ?>
        <div id="premium-popup" class="premium-popup" style="display: none;">
            <div class="popup-overlay" onclick="closePromotionPopup()"></div>
            <div class="popup-content">
                <button class="popup-close" onclick="closePromotionPopup()">
                    <i class="fas fa-times"></i>
                </button>
                
                <div class="popup-header">
                    <h2>🎉 Special Offer!</h2>
                    <p>Get 50% off your first month of Premium</p>
                </div>
                
                <div class="popup-body">
                    <div class="offer-details">
                        <div class="price-comparison">
                            <div class="old-price">৳599</div>
                            <div class="new-price">৳299</div>
                            <div class="savings">Save ৳300</div>
                        </div>
                        
                        <div class="offer-features">
                            <ul>
                                <li><i class="fas fa-check"></i> Unlimited HD Streaming</li>
                                <li><i class="fas fa-check"></i> Ad-Free Experience</li>
                                <li><i class="fas fa-check"></i> Download Content</li>
                                <li><i class="fas fa-check"></i> Multiple Devices</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="popup-footer">
                    <a href="/premium-plans/" class="btn btn-premium btn-large">
                        Claim Offer Now
                    </a>
                    <p class="offer-expires">Offer expires in 24 hours</p>
                </div>
            </div>
        </div>
        
        <script>
        // Show popup after 30 seconds
        setTimeout(function() {
            if (!getCookie('deshiflix_popup_dismissed')) {
                document.getElementById('premium-popup').style.display = 'flex';
            }
        }, 30000);
        </script>
        <?php
    }
    
    /**
     * Add marketing styles
     */
    public function add_marketing_styles() {
        ?>
        <style>
        /* Premium Promotion Banner */
        .premium-promotion-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            padding: 15px 20px;
            z-index: 9999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            animation: slideDown 0.5s ease;
        }
        
        .banner-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .banner-text h3 {
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .banner-text p {
            margin: 0;
            font-size: 14px;
        }
        
        .banner-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn-premium {
            background: #000;
            color: #FFD700;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-premium:hover {
            background: #333;
            transform: translateY(-2px);
        }
        
        .btn-dismiss {
            background: transparent;
            border: none;
            color: #000;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
        }
        
        /* Premium Upgrade Prompt */
        .premium-upgrade-prompt {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            margin: 30px 0;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            align-items: center;
        }
        
        .upgrade-content {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .upgrade-icon {
            font-size: 3rem;
            color: #FFD700;
        }
        
        .upgrade-text h3 {
            margin: 0 0 10px 0;
            font-size: 1.5rem;
        }
        
        .upgrade-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .upgrade-features ul {
            list-style: none;
            padding: 0;
        }
        
        .upgrade-features li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .upgrade-features i {
            color: #4CAF50;
        }
        
        /* Premium Badge */
        .premium-badge {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        /* Premium Overlay */
        .premium-overlay {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        /* Premium Popup */
        .premium-popup {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .popup-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
        }
        
        .popup-content {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            position: relative;
            text-align: center;
            animation: popupSlideIn 0.5s ease;
        }
        
        .popup-close {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }
        
        .popup-header h2 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .price-comparison {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin: 30px 0;
        }
        
        .old-price {
            font-size: 2rem;
            color: #999;
            text-decoration: line-through;
        }
        
        .new-price {
            font-size: 3rem;
            color: #FFD700;
            font-weight: bold;
        }
        
        .savings {
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
        }
        
        .offer-features ul {
            list-style: none;
            padding: 0;
            text-align: left;
        }
        
        .btn-large {
            padding: 15px 30px;
            font-size: 18px;
        }
        
        .offer-expires {
            color: #ff4444;
            font-size: 12px;
            margin-top: 10px;
        }
        
        /* Animations */
        @keyframes slideDown {
            from { transform: translateY(-100%); }
            to { transform: translateY(0); }
        }
        
        @keyframes popupSlideIn {
            from { 
                opacity: 0;
                transform: scale(0.8) translateY(-50px);
            }
            to { 
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .banner-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .premium-upgrade-prompt {
                grid-template-columns: 1fr;
                padding: 30px 20px;
            }
            
            .upgrade-content {
                flex-direction: column;
                text-align: center;
            }
            
            .popup-content {
                padding: 30px 20px;
            }
            
            .price-comparison {
                flex-direction: column;
                gap: 10px;
            }
        }
        </style>
        <?php
    }
    
    /**
     * Dismiss promotion AJAX handler
     */
    public function dismiss_promotion() {
        $type = sanitize_text_field($_POST['type'] ?? '');
        
        switch ($type) {
            case 'banner':
                setcookie('deshiflix_banner_dismissed', '1', time() + (24 * 60 * 60), '/'); // 24 hours
                break;
            case 'popup':
                setcookie('deshiflix_popup_dismissed', '1', time() + (7 * 24 * 60 * 60), '/'); // 7 days
                break;
        }
        
        wp_send_json_success();
    }
}

/**
 * Premium Widget Class
 */
class DeshiFlix_Premium_Widget extends WP_Widget {
    
    public function __construct() {
        parent::__construct(
            'deshiflix_premium_widget',
            'DeshiFlix Premium Promotion',
            array('description' => 'Promote premium subscription in sidebar')
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        ?>
        <div class="premium-widget">
            <div class="widget-content">
                <div class="premium-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <h4>Upgrade to Premium</h4>
                <p>Get unlimited access to HD content, ad-free streaming, and exclusive features.</p>
                <a href="/premium-plans/" class="btn btn-premium btn-full">
                    View Plans
                </a>
            </div>
        </div>
        
        <style>
        .premium-widget {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .premium-icon {
            font-size: 2rem;
            color: #FFD700;
            margin-bottom: 15px;
        }
        
        .premium-widget h4 {
            margin: 0 0 10px 0;
            color: white;
        }
        
        .premium-widget p {
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .btn-full {
            width: 100%;
            display: block;
            text-align: center;
        }
        </style>
        <?php
        
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'Premium Subscription';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">Title:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" 
                   name="<?php echo $this->get_field_name('title'); ?>" type="text" 
                   value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';
        return $instance;
    }
}

// Initialize premium marketing
DeshiFlix_Premium_Marketing::get_instance();

// Add JavaScript functions
add_action('wp_footer', function() {
    ?>
    <script>
    function dismissPromotionBanner() {
        document.getElementById('premium-promotion-banner').style.display = 'none';
        
        // Set cookie
        setCookie('deshiflix_banner_dismissed', '1', 1);
        
        // AJAX call
        jQuery.post('<?php echo admin_url('admin-ajax.php'); ?>', {
            action: 'dismiss_promotion',
            type: 'banner'
        });
    }
    
    function closePromotionPopup() {
        document.getElementById('premium-popup').style.display = 'none';
        
        // Set cookie
        setCookie('deshiflix_popup_dismissed', '1', 7);
        
        // AJAX call
        jQuery.post('<?php echo admin_url('admin-ajax.php'); ?>', {
            action: 'dismiss_promotion',
            type: 'popup'
        });
    }
    
    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }
    
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for(var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
    </script>
    <?php
});
