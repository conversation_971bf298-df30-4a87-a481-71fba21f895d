<?php
/**
 * Simple Settings Page - WordPress Standard Structure
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get current settings
$settings = get_option('deshiflix_premium_settings', array());

// Handle form submission
if (isset($_POST['save_premium_settings']) && wp_verify_nonce($_POST['premium_settings_nonce'], 'save_premium_settings')) {
    $new_settings = array(
        'enable_premium' => isset($_POST['enable_premium']) ? 1 : 0,
        'enable_content_lock' => isset($_POST['enable_content_lock']) ? 1 : 0,
        'enable_download_protection' => isset($_POST['enable_download_protection']) ? 1 : 0,
        'enable_ad_free' => isset($_POST['enable_ad_free']) ? 1 : 0,
        'enable_instant_download' => isset($_POST['enable_instant_download']) ? 1 : 0,
        'enable_hd_quality' => isset($_POST['enable_hd_quality']) ? 1 : 0,
        'enable_early_access' => isset($_POST['enable_early_access']) ? 1 : 0,
        'max_devices_per_user' => intval($_POST['max_devices_per_user']),
        'trial_period_days' => intval($_POST['trial_period_days']),
        'auto_expire_check' => isset($_POST['auto_expire_check']) ? 1 : 0,
        'bkash_enabled' => isset($_POST['bkash_enabled']) ? 1 : 0,
        'bkash_test_mode' => isset($_POST['bkash_test_mode']) ? 1 : 0,
        'bkash_api_key' => sanitize_text_field($_POST['bkash_api_key']),
        'bkash_api_secret' => sanitize_text_field($_POST['bkash_api_secret']),
        'nagad_enabled' => isset($_POST['nagad_enabled']) ? 1 : 0,
        'nagad_test_mode' => isset($_POST['nagad_test_mode']) ? 1 : 0,
        'nagad_merchant_id' => sanitize_text_field($_POST['nagad_merchant_id']),
        'nagad_merchant_key' => sanitize_text_field($_POST['nagad_merchant_key']),
    );
    
    update_option('deshiflix_premium_settings', $new_settings);
    echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'deshiflix') . '</p></div>';
    $settings = $new_settings;
}
?>

<div class="wrap">
    <h1><?php _e('Premium System Settings', 'deshiflix'); ?></h1>
    
    <style>
    .premium-settings-wrap {
        max-width: 1200px;
        margin: 20px 0;
    }
    
    .settings-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .settings-card {
        background: white;
        border: 1px solid #ccd0d4;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .settings-card h3 {
        margin-top: 0;
        color: #23282d;
        border-bottom: 1px solid #e1e1e1;
        padding-bottom: 10px;
    }
    
    .form-table th {
        width: 200px;
        padding: 15px 10px 15px 0;
    }
    
    .form-table td {
        padding: 15px 10px;
    }
    
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }
    
    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 24px;
    }
    
    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    
    input:checked + .toggle-slider {
        background-color: #0073aa;
    }
    
    input:checked + .toggle-slider:before {
        transform: translateX(26px);
    }
    </style>
    
    <div class="premium-settings-wrap">
        <!-- Overview Cards -->
        <div class="settings-cards">
            <div class="settings-card">
                <h3><?php _e('System Status', 'deshiflix'); ?></h3>
                <p><strong><?php echo isset($settings['enable_premium']) && $settings['enable_premium'] ? __('Active', 'deshiflix') : __('Inactive', 'deshiflix'); ?></strong></p>
                <p><?php _e('Premium system is currently', 'deshiflix'); ?> <?php echo isset($settings['enable_premium']) && $settings['enable_premium'] ? __('enabled', 'deshiflix') : __('disabled', 'deshiflix'); ?></p>
            </div>
            
            <div class="settings-card">
                <h3><?php _e('Payment Gateways', 'deshiflix'); ?></h3>
                <p><strong><?php 
                $active_gateways = 0;
                if (isset($settings['bkash_enabled']) && $settings['bkash_enabled']) $active_gateways++;
                if (isset($settings['nagad_enabled']) && $settings['nagad_enabled']) $active_gateways++;
                echo $active_gateways;
                ?></strong></p>
                <p><?php _e('Active payment methods', 'deshiflix'); ?></p>
            </div>
            
            <div class="settings-card">
                <h3><?php _e('Premium Features', 'deshiflix'); ?></h3>
                <p><strong><?php 
                $features = array('enable_content_lock', 'enable_download_protection', 'enable_ad_free', 'enable_instant_download', 'enable_hd_quality', 'enable_early_access');
                $enabled_features = 0;
                foreach ($features as $feature) {
                    if (isset($settings[$feature]) && $settings[$feature]) $enabled_features++;
                }
                echo $enabled_features . '/' . count($features);
                ?></strong></p>
                <p><?php _e('Features enabled', 'deshiflix'); ?></p>
            </div>
        </div>
        
        <!-- Settings Form -->
        <form method="post" action="">
            <?php wp_nonce_field('save_premium_settings', 'premium_settings_nonce'); ?>
            
            <div class="settings-card">
                <h3><?php _e('General Settings', 'deshiflix'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable Premium System', 'deshiflix'); ?></th>
                        <td>
                            <label class="toggle-switch">
                                <input type="checkbox" name="enable_premium" value="1" <?php checked(isset($settings['enable_premium']) ? $settings['enable_premium'] : 0, 1); ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <p class="description"><?php _e('Turn on the premium subscription system', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Max Devices Per User', 'deshiflix'); ?></th>
                        <td>
                            <input type="number" name="max_devices_per_user" value="<?php echo isset($settings['max_devices_per_user']) ? $settings['max_devices_per_user'] : 3; ?>" min="1" max="10" class="small-text">
                            <p class="description"><?php _e('Maximum number of devices a user can use simultaneously', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Trial Period (Days)', 'deshiflix'); ?></th>
                        <td>
                            <input type="number" name="trial_period_days" value="<?php echo isset($settings['trial_period_days']) ? $settings['trial_period_days'] : 7; ?>" min="0" max="30" class="small-text">
                            <p class="description"><?php _e('Number of free trial days for new users', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="settings-card">
                <h3><?php _e('Premium Features', 'deshiflix'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Content Lock', 'deshiflix'); ?></th>
                        <td>
                            <label class="toggle-switch">
                                <input type="checkbox" name="enable_content_lock" value="1" <?php checked(isset($settings['enable_content_lock']) ? $settings['enable_content_lock'] : 0, 1); ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <p class="description"><?php _e('Lock premium content for non-subscribers', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Download Protection', 'deshiflix'); ?></th>
                        <td>
                            <label class="toggle-switch">
                                <input type="checkbox" name="enable_download_protection" value="1" <?php checked(isset($settings['enable_download_protection']) ? $settings['enable_download_protection'] : 0, 1); ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <p class="description"><?php _e('Protect downloads for premium users only', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Ad-Free Experience', 'deshiflix'); ?></th>
                        <td>
                            <label class="toggle-switch">
                                <input type="checkbox" name="enable_ad_free" value="1" <?php checked(isset($settings['enable_ad_free']) ? $settings['enable_ad_free'] : 0, 1); ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <p class="description"><?php _e('Remove ads for premium subscribers', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Instant Downloads', 'deshiflix'); ?></th>
                        <td>
                            <label class="toggle-switch">
                                <input type="checkbox" name="enable_instant_download" value="1" <?php checked(isset($settings['enable_instant_download']) ? $settings['enable_instant_download'] : 0, 1); ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <p class="description"><?php _e('Skip download wait time for premium users', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('HD Quality', 'deshiflix'); ?></th>
                        <td>
                            <label class="toggle-switch">
                                <input type="checkbox" name="enable_hd_quality" value="1" <?php checked(isset($settings['enable_hd_quality']) ? $settings['enable_hd_quality'] : 0, 1); ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <p class="description"><?php _e('Enable HD video quality for premium users', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="settings-card">
                <h3><?php _e('Payment Gateways', 'deshiflix'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('bKash', 'deshiflix'); ?></th>
                        <td>
                            <label class="toggle-switch">
                                <input type="checkbox" name="bkash_enabled" value="1" <?php checked(isset($settings['bkash_enabled']) ? $settings['bkash_enabled'] : 0, 1); ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <p class="description"><?php _e('Enable bKash payment gateway', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('bKash API Key', 'deshiflix'); ?></th>
                        <td>
                            <input type="text" name="bkash_api_key" value="<?php echo isset($settings['bkash_api_key']) ? esc_attr($settings['bkash_api_key']) : ''; ?>" class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Nagad', 'deshiflix'); ?></th>
                        <td>
                            <label class="toggle-switch">
                                <input type="checkbox" name="nagad_enabled" value="1" <?php checked(isset($settings['nagad_enabled']) ? $settings['nagad_enabled'] : 0, 1); ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <p class="description"><?php _e('Enable Nagad payment gateway', 'deshiflix'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Nagad Merchant ID', 'deshiflix'); ?></th>
                        <td>
                            <input type="text" name="nagad_merchant_id" value="<?php echo isset($settings['nagad_merchant_id']) ? esc_attr($settings['nagad_merchant_id']) : ''; ?>" class="regular-text">
                        </td>
                    </tr>
                </table>
            </div>
            
            <?php submit_button(__('Save Settings', 'deshiflix'), 'primary', 'save_premium_settings'); ?>
        </form>
    </div>
</div>
