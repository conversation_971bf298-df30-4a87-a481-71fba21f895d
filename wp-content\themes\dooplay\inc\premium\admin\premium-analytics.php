<?php
/**
 * DeshiFlix Premium System - Analytics Admin Page
 *
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DeshiFlix_Premium_Analytics {

    /**
     * Render analytics page
     */
    public static function render_analytics_page() {
        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        ?>
        <div class="wrap">
            <h1><?php _e('Premium Analytics Dashboard', 'deshiflix'); ?></h1>

            <!-- Analytics Overview Cards -->
            <div class="premium-analytics-cards" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">

                <!-- Total Premium Users -->
                <div class="analytics-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <h3 style="margin: 0 0 10px 0; font-size: 16px;"><?php _e('Premium Users', 'deshiflix'); ?></h3>
                    <div style="font-size: 32px; font-weight: bold; margin: 10px 0;"><?php echo self::get_premium_users_count(); ?></div>
                    <p style="margin: 0; opacity: 0.9; font-size: 14px;"><?php _e('Active subscribers', 'deshiflix'); ?></p>
                </div>

                <!-- Monthly Revenue -->
                <div class="analytics-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <h3 style="margin: 0 0 10px 0; font-size: 16px;"><?php _e('Monthly Revenue', 'deshiflix'); ?></h3>
                    <div style="font-size: 32px; font-weight: bold; margin: 10px 0;">৳<?php echo number_format(self::get_monthly_revenue()); ?></div>
                    <p style="margin: 0; opacity: 0.9; font-size: 14px;"><?php _e('This month', 'deshiflix'); ?></p>
                </div>

                <!-- Premium Downloads -->
                <div class="analytics-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <h3 style="margin: 0 0 10px 0; font-size: 16px;"><?php _e('Premium Downloads', 'deshiflix'); ?></h3>
                    <div style="font-size: 32px; font-weight: bold; margin: 10px 0;"><?php echo number_format(self::get_premium_downloads()); ?></div>
                    <p style="margin: 0; opacity: 0.9; font-size: 14px;"><?php _e('This month', 'deshiflix'); ?></p>
                </div>

                <!-- Conversion Rate -->
                <div class="analytics-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <h3 style="margin: 0 0 10px 0; font-size: 16px;"><?php _e('Conversion Rate', 'deshiflix'); ?></h3>
                    <div style="font-size: 32px; font-weight: bold; margin: 10px 0;"><?php echo self::get_conversion_rate(); ?>%</div>
                    <p style="margin: 0; opacity: 0.9; font-size: 14px;"><?php _e('Free to premium', 'deshiflix'); ?></p>
                </div>

            </div>

            <!-- Recent Premium Activities -->
            <div class="card" style="margin-top: 30px;">
                <h2><?php _e('Recent Premium Activities', 'deshiflix'); ?></h2>
                <table class="widefat">
                    <thead>
                        <tr>
                            <th><?php _e('User', 'deshiflix'); ?></th>
                            <th><?php _e('Action', 'deshiflix'); ?></th>
                            <th><?php _e('Plan', 'deshiflix'); ?></th>
                            <th><?php _e('Amount', 'deshiflix'); ?></th>
                            <th><?php _e('Date', 'deshiflix'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php self::render_recent_activities(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Premium Content Stats -->
            <div class="card" style="margin-top: 30px;">
                <h2><?php _e('Premium Content Statistics', 'deshiflix'); ?></h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">

                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <h3 style="color: #667eea; font-size: 24px; margin: 0;"><?php echo self::get_premium_links_count(); ?></h3>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Premium Links', 'deshiflix'); ?></p>
                    </div>

                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <h3 style="color: #f5576c; font-size: 24px; margin: 0;"><?php echo self::get_premium_movies_count(); ?></h3>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Premium Movies', 'deshiflix'); ?></p>
                    </div>

                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <h3 style="color: #00f2fe; font-size: 24px; margin: 0;"><?php echo self::get_premium_shows_count(); ?></h3>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Premium Shows', 'deshiflix'); ?></p>
                    </div>

                    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <h3 style="color: #fee140; font-size: 24px; margin: 0;"><?php echo self::get_blocked_attempts(); ?></h3>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Blocked Attempts', 'deshiflix'); ?></p>
                    </div>

                </div>
            </div>

        </div>
        <?php
    }

    /**
     * Get premium users count
     */
    private static function get_premium_users_count() {
        return 1; // Demo count (test user)
    }

    /**
     * Get monthly revenue
     */
    private static function get_monthly_revenue() {
        return 2500; // Demo revenue
    }

    /**
     * Get premium downloads count
     */
    private static function get_premium_downloads() {
        return 1250; // Demo downloads
    }

    /**
     * Get conversion rate
     */
    private static function get_conversion_rate() {
        return 12.5; // Demo conversion rate
    }

    /**
     * Get premium links count
     */
    private static function get_premium_links_count() {
        global $wpdb;
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->postmeta} WHERE meta_key = '_dool_premium' AND meta_value = '1'");
        return $count ? $count : 0;
    }

    /**
     * Get premium movies count
     */
    private static function get_premium_movies_count() {
        return 5; // Demo count
    }

    /**
     * Get premium shows count
     */
    private static function get_premium_shows_count() {
        return 3; // Demo count
    }

    /**
     * Get blocked attempts
     */
    private static function get_blocked_attempts() {
        return 45; // Demo blocked attempts
    }

    /**
     * Render recent activities
     */
    private static function render_recent_activities() {
        $activities = array(
            array(
                'user' => 'premium_test_user',
                'action' => 'Subscription Activated',
                'plan' => 'Test Premium Plan',
                'amount' => '৳199',
                'date' => date('Y-m-d H:i:s')
            ),
            array(
                'user' => 'demo_user',
                'action' => 'Premium Download',
                'plan' => 'Monthly Plan',
                'amount' => '-',
                'date' => date('Y-m-d H:i:s', strtotime('-1 hour'))
            ),
            array(
                'user' => 'test_user_2',
                'action' => 'Plan Upgraded',
                'plan' => 'Yearly Plan',
                'amount' => '৳999',
                'date' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            )
        );

        foreach ($activities as $activity) {
            echo '<tr>';
            echo '<td><strong>' . esc_html($activity['user']) . '</strong></td>';
            echo '<td>' . esc_html($activity['action']) . '</td>';
            echo '<td>' . esc_html($activity['plan']) . '</td>';
            echo '<td>' . esc_html($activity['amount']) . '</td>';
            echo '<td>' . esc_html($activity['date']) . '</td>';
            echo '</tr>';
        }
    }
}
