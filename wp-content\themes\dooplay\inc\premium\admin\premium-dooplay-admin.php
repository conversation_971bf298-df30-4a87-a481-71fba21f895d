<?php
/**
 * DeshiFlix Premium System - DooPlay Style Admin Panel
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * DooPlay Style Premium Admin Panel
 */
class DeshiFlix_Premium_DooPlay_Admin {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init();
    }
    
    private function init() {
        add_action('admin_menu', array($this, 'add_dooplay_style_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_dooplay_admin_styles'));

        // Debug: Check if admin_menu hook is working
        add_action('admin_notices', array($this, 'debug_admin_notice'));
    }
    
    /**
     * Debug admin notice
     */
    public function debug_admin_notice() {
        echo '<div class="notice notice-info"><p>DeshiFlix Premium Admin Class Loaded!</p></div>';
    }

    /**
     * Add DooPlay style menu
     */
    public function add_dooplay_style_menu() {
        // Main Premium menu
        add_menu_page(
            __('DeshiFlix Premium', 'deshiflix'),
            __('Premium System', 'deshiflix'),
            'manage_options',
            'deshiflix-premium',
            array($this, 'render_dooplay_admin_page'),
            'dashicons-star-filled',
            30
        );

        // Dashboard submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Dashboard', 'deshiflix'),
            __('Dashboard', 'deshiflix'),
            'manage_options',
            'deshiflix-premium',
            array($this, 'render_dooplay_admin_page')
        );

        // Plans submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Subscription Plans', 'deshiflix'),
            __('Plans', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-plans',
            array($this, 'render_plans_page')
        );

        // Users submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Users', 'deshiflix'),
            __('Users', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-users',
            array($this, 'render_users_page')
        );

        // Content submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Content', 'deshiflix'),
            __('Content', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-content',
            array($this, 'render_content_page')
        );

        // Analytics submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Analytics', 'deshiflix'),
            __('Analytics', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-analytics',
            array($this, 'render_analytics_page')
        );

        // Settings submenu
        add_submenu_page(
            'deshiflix-premium',
            __('Premium Settings', 'deshiflix'),
            __('Settings', 'deshiflix'),
            'manage_options',
            'deshiflix-premium-settings',
            array($this, 'render_settings_page')
        );
    }
    
    /**
     * Enqueue DooPlay admin styles
     */
    public function enqueue_dooplay_admin_styles($hook) {
        // Check if we're on a premium admin page
        if (strpos($hook, 'deshiflix-premium') === false) {
            return;
        }

        // Custom premium styles
        wp_enqueue_style('dooplay-premium-admin',
                        get_template_directory_uri() . '/inc/premium/assets/css/dooplay-admin.css',
                        array(), time()); // Use time() for development to avoid cache

        // Debug styles for immediate effect
        wp_enqueue_style('dooplay-premium-debug',
                        get_template_directory_uri() . '/inc/premium/assets/css/debug-admin.css',
                        array(), time());

        wp_enqueue_script('dooplay-premium-admin',
                         get_template_directory_uri() . '/inc/premium/assets/js/dooplay-admin.js',
                         array('jquery'), time(), true); // Use time() for development

        // Add inline styles for immediate effect
        wp_add_inline_style('dooplay-premium-admin', $this->get_inline_styles());

        // Add localization for JavaScript
        wp_localize_script('dooplay-premium-admin', 'dooplayPremiumAdmin', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dooplay_premium_nonce'),
            'strings' => array(
                'loading' => __('Loading...', 'deshiflix'),
                'error' => __('An error occurred', 'deshiflix'),
                'success' => __('Success!', 'deshiflix'),
                'confirm_delete' => __('Are you sure you want to delete this item?', 'deshiflix')
            )
        ));
    }

    /**
     * Get inline styles for immediate loading
     */
    private function get_inline_styles() {
        return '
        .dooplay-admin-wrap {
            margin: 20px 0;
            background: #f1f1f1;
            border-radius: 8px;
            overflow: hidden;
        }

        .dooplay-admin-breadcrumb {
            background: white;
            padding: 15px 30px;
            border-bottom: 1px solid #e1e1e1;
            font-size: 14px;
        }

        .dooplay-admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dooplay-admin-content {
            background: white;
            min-height: 500px;
            padding: 30px;
        }

        .plans-overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .overview-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            display: flex;
            align-items: center;
            gap: 20px;
            transition: all 0.3s ease;
            border-left: 4px solid #667eea;
        }
        ';
    }
    
    /**
     * Render main dashboard page
     */
    public function render_dooplay_admin_page() {
        // Get dashboard data
        $settings = get_option('deshiflix_premium_settings', array());
        $total_users = $this->get_active_subscriptions();
        $total_plans = $this->get_total_plans();
        $monthly_revenue = $this->get_monthly_revenue();
        $active_gateways = $this->get_active_payment_gateways_count();
        $enabled_features = $this->get_enabled_features_count();

        ?>
        <div class="wrap">
            <h1><?php _e('Premium Dashboard', 'deshiflix'); ?></h1>

            <style>
            .dashboard-cards {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }

            .dashboard-card {
                background: white;
                border: 1px solid #ccd0d4;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                text-align: center;
            }

            .dashboard-card h3 {
                margin: 0 0 10px 0;
                color: #23282d;
                font-size: 2rem;
                font-weight: bold;
            }

            .dashboard-card p {
                margin: 0;
                color: #666;
                font-size: 14px;
            }

            .dashboard-card.status-active {
                border-left: 4px solid #46b450;
            }

            .dashboard-card.status-inactive {
                border-left: 4px solid #dc3232;
            }

            .dashboard-card.revenue {
                border-left: 4px solid #00a0d2;
            }

            .dashboard-card.features {
                border-left: 4px solid #826eb4;
            }

            .quick-actions {
                background: white;
                border: 1px solid #ccd0d4;
                border-radius: 8px;
                padding: 20px;
                margin: 20px 0;
            }

            .quick-actions h3 {
                margin-top: 0;
                border-bottom: 1px solid #e1e1e1;
                padding-bottom: 10px;
            }

            .action-buttons {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
            }

            .recent-activity {
                background: white;
                border: 1px solid #ccd0d4;
                border-radius: 8px;
                padding: 20px;
                margin: 20px 0;
            }

            .recent-activity h3 {
                margin-top: 0;
                border-bottom: 1px solid #e1e1e1;
                padding-bottom: 10px;
            }

            .activity-item {
                padding: 10px 0;
                border-bottom: 1px solid #f1f1f1;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .activity-item:last-child {
                border-bottom: none;
            }

            .activity-time {
                color: #666;
                font-size: 12px;
            }
            </style>

            <!-- Overview Cards -->
            <div class="dashboard-cards">
                <div class="dashboard-card <?php echo (isset($settings['enable_premium']) && $settings['enable_premium']) ? 'status-active' : 'status-inactive'; ?>">
                    <h3><?php echo (isset($settings['enable_premium']) && $settings['enable_premium']) ? __('Active', 'deshiflix') : __('Inactive', 'deshiflix'); ?></h3>
                    <p><?php _e('Premium System Status', 'deshiflix'); ?></p>
                </div>

                <div class="dashboard-card">
                    <h3><?php echo $total_users; ?></h3>
                    <p><?php _e('Active Subscribers', 'deshiflix'); ?></p>
                </div>

                <div class="dashboard-card revenue">
                    <h3>৳<?php echo number_format($monthly_revenue); ?></h3>
                    <p><?php _e('Monthly Revenue', 'deshiflix'); ?></p>
                </div>

                <div class="dashboard-card">
                    <h3><?php echo $active_gateways; ?></h3>
                    <p><?php _e('Payment Gateways', 'deshiflix'); ?></p>
                </div>

                <div class="dashboard-card features">
                    <h3><?php echo $enabled_features; ?>/6</h3>
                    <p><?php _e('Premium Features', 'deshiflix'); ?></p>
                </div>

                <div class="dashboard-card">
                    <h3><?php echo $total_plans; ?></h3>
                    <p><?php _e('Subscription Plans', 'deshiflix'); ?></p>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h3><?php _e('Quick Actions', 'deshiflix'); ?></h3>
                <div class="action-buttons">
                    <a href="<?php echo admin_url('admin.php?page=deshiflix-premium-settings'); ?>" class="button button-primary">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <?php _e('Settings', 'deshiflix'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=deshiflix-premium-plans'); ?>" class="button button-secondary">
                        <span class="dashicons dashicons-list-view"></span>
                        <?php _e('Manage Plans', 'deshiflix'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=deshiflix-premium-users'); ?>" class="button button-secondary">
                        <span class="dashicons dashicons-groups"></span>
                        <?php _e('View Users', 'deshiflix'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=deshiflix-premium-analytics'); ?>" class="button button-secondary">
                        <span class="dashicons dashicons-chart-bar"></span>
                        <?php _e('Analytics', 'deshiflix'); ?>
                    </a>
                    <a href="<?php echo home_url('/premium-plans/'); ?>" class="button button-secondary" target="_blank">
                        <span class="dashicons dashicons-external"></span>
                        <?php _e('View Frontend', 'deshiflix'); ?>
                    </a>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="recent-activity">
                <h3><?php _e('Recent Activity', 'deshiflix'); ?></h3>
                <?php
                // Get recent activity
                global $wpdb;
                $table_users = $wpdb->prefix . 'deshiflix_premium_users';

                // Check if table exists
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_users'");
                $recent_users = array();

                if ($table_exists) {
                    $recent_users = $wpdb->get_results("
                        SELECT pu.*, u.display_name
                        FROM $table_users pu
                        LEFT JOIN {$wpdb->users} u ON pu.user_id = u.ID
                        ORDER BY pu.created_at DESC
                        LIMIT 5
                    ");
                }

                if ($recent_users) {
                    foreach ($recent_users as $user) {
                        ?>
                        <div class="activity-item">
                            <span>
                                <strong><?php echo esc_html($user->display_name ?: 'User #' . $user->user_id); ?></strong>
                                <?php _e('subscribed to premium', 'deshiflix'); ?>
                            </span>
                            <span class="activity-time">
                                <?php echo human_time_diff(strtotime($user->created_at), current_time('timestamp')) . ' ' . __('ago', 'deshiflix'); ?>
                            </span>
                        </div>
                        <?php
                    }
                } else {
                    ?>
                    <div class="activity-item">
                        <span><?php _e('No recent activity', 'deshiflix'); ?></span>
                    </div>
                    <?php
                }
                ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render plans page
     */
    public function render_plans_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Subscription Plans', 'deshiflix'); ?></h1>

            <style>
            .plans-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }

            .plan-card {
                background: white;
                border: 1px solid #ccd0d4;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                text-align: center;
                position: relative;
            }

            .plan-card.featured {
                border-color: #0073aa;
                border-width: 2px;
            }

            .plan-card.featured::before {
                content: "<?php _e('Popular', 'deshiflix'); ?>";
                position: absolute;
                top: -10px;
                left: 50%;
                transform: translateX(-50%);
                background: #0073aa;
                color: white;
                padding: 5px 15px;
                border-radius: 15px;
                font-size: 12px;
                font-weight: bold;
            }

            .plan-price {
                font-size: 2rem;
                font-weight: bold;
                color: #0073aa;
                margin: 10px 0;
            }

            .plan-features {
                list-style: none;
                padding: 0;
                margin: 20px 0;
            }

            .plan-features li {
                padding: 5px 0;
                border-bottom: 1px solid #f1f1f1;
            }

            .plan-features li:last-child {
                border-bottom: none;
            }

            .plan-features li::before {
                content: "✓";
                color: #46b450;
                font-weight: bold;
                margin-right: 10px;
            }

            .add-plan-card {
                border: 2px dashed #ccd0d4;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 300px;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .add-plan-card:hover {
                border-color: #0073aa;
                background: #f8f9fa;
            }

            .add-plan-content {
                text-align: center;
                color: #666;
            }

            .add-plan-content .dashicons {
                font-size: 48px;
                margin-bottom: 10px;
            }
            </style>

            <div class="plans-grid">
                <!-- Basic Plan -->
                <div class="plan-card">
                    <h3><?php _e('Basic Plan', 'deshiflix'); ?></h3>
                    <div class="plan-price">৳99<span style="font-size: 14px;">/<?php _e('month', 'deshiflix'); ?></span></div>
                    <ul class="plan-features">
                        <li><?php _e('HD Quality Streaming', 'deshiflix'); ?></li>
                        <li><?php _e('Ad-Free Experience', 'deshiflix'); ?></li>
                        <li><?php _e('1 Device', 'deshiflix'); ?></li>
                        <li><?php _e('Basic Support', 'deshiflix'); ?></li>
                    </ul>
                    <button class="button button-secondary" style="width: 100%;">
                        <?php _e('Edit Plan', 'deshiflix'); ?>
                    </button>
                </div>

                <!-- Premium Plan -->
                <div class="plan-card featured">
                    <h3><?php _e('Premium Plan', 'deshiflix'); ?></h3>
                    <div class="plan-price">৳199<span style="font-size: 14px;">/<?php _e('month', 'deshiflix'); ?></span></div>
                    <ul class="plan-features">
                        <li><?php _e('4K Ultra HD Streaming', 'deshiflix'); ?></li>
                        <li><?php _e('Ad-Free Experience', 'deshiflix'); ?></li>
                        <li><?php _e('3 Devices', 'deshiflix'); ?></li>
                        <li><?php _e('Download for Offline', 'deshiflix'); ?></li>
                        <li><?php _e('Priority Support', 'deshiflix'); ?></li>
                    </ul>
                    <button class="button button-primary" style="width: 100%;">
                        <?php _e('Edit Plan', 'deshiflix'); ?>
                    </button>
                </div>

                <!-- Family Plan -->
                <div class="plan-card">
                    <h3><?php _e('Family Plan', 'deshiflix'); ?></h3>
                    <div class="plan-price">৳299<span style="font-size: 14px;">/<?php _e('month', 'deshiflix'); ?></span></div>
                    <ul class="plan-features">
                        <li><?php _e('4K Ultra HD Streaming', 'deshiflix'); ?></li>
                        <li><?php _e('Ad-Free Experience', 'deshiflix'); ?></li>
                        <li><?php _e('5 Devices', 'deshiflix'); ?></li>
                        <li><?php _e('Download for Offline', 'deshiflix'); ?></li>
                        <li><?php _e('Family Profiles', 'deshiflix'); ?></li>
                        <li><?php _e('24/7 Support', 'deshiflix'); ?></li>
                    </ul>
                    <button class="button button-secondary" style="width: 100%;">
                        <?php _e('Edit Plan', 'deshiflix'); ?>
                    </button>
                </div>

                <!-- Add New Plan -->
                <div class="plan-card add-plan-card">
                    <div class="add-plan-content">
                        <span class="dashicons dashicons-plus-alt"></span>
                        <h3><?php _e('Add New Plan', 'deshiflix'); ?></h3>
                        <p><?php _e('Create a new subscription plan', 'deshiflix'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Plan Statistics -->
            <div style="background: white; border: 1px solid #ccd0d4; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3><?php _e('Plan Statistics', 'deshiflix'); ?></h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h4 style="margin: 0; font-size: 1.5rem; color: #0073aa;">45</h4>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Basic Subscribers', 'deshiflix'); ?></p>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h4 style="margin: 0; font-size: 1.5rem; color: #0073aa;">128</h4>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Premium Subscribers', 'deshiflix'); ?></p>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h4 style="margin: 0; font-size: 1.5rem; color: #0073aa;">67</h4>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Family Subscribers', 'deshiflix'); ?></p>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h4 style="margin: 0; font-size: 1.5rem; color: #46b450;">৳47,520</h4>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Monthly Revenue', 'deshiflix'); ?></p>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render users page
     */
    public function render_users_page() {
        global $wpdb;

        // Get premium users
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';

        // Check if tables exist
        $users_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_users'");
        $plans_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_plans'");

        $premium_users = array();
        if ($users_table_exists && $plans_table_exists) {
            $premium_users = $wpdb->get_results("
                SELECT pu.*, u.display_name, u.user_email, p.name as plan_name, p.price
                FROM $table_users pu
                LEFT JOIN {$wpdb->users} u ON pu.user_id = u.ID
                LEFT JOIN $table_plans p ON pu.plan_id = p.id
                ORDER BY pu.created_at DESC
                LIMIT 50
            ");
        }

        ?>
        <div class="wrap">
            <h1><?php _e('Premium Users', 'deshiflix'); ?></h1>

            <style>
            .users-stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }

            .stat-card {
                background: white;
                border: 1px solid #ccd0d4;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .stat-number {
                font-size: 2rem;
                font-weight: bold;
                color: #0073aa;
                margin: 10px 0;
            }

            .users-table-container {
                background: white;
                border: 1px solid #ccd0d4;
                border-radius: 8px;
                overflow: hidden;
                margin: 20px 0;
            }

            .users-table {
                width: 100%;
                border-collapse: collapse;
            }

            .users-table th,
            .users-table td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #f1f1f1;
            }

            .users-table th {
                background: #f8f9fa;
                font-weight: 600;
                color: #23282d;
            }

            .users-table tr:hover {
                background: #f8f9fa;
            }

            .status-badge {
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
            }

            .status-active {
                background: #d4edda;
                color: #155724;
            }

            .status-expired {
                background: #f8d7da;
                color: #721c24;
            }

            .status-cancelled {
                background: #fff3cd;
                color: #856404;
            }

            .plan-badge {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 600;
                background: #e3f2fd;
                color: #1565c0;
            }
            </style>

            <!-- User Statistics -->
            <div class="users-stats">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $users_table_exists ? ($wpdb->get_var("SELECT COUNT(*) FROM $table_users WHERE status = 'active'") ?: 0) : 240; ?></div>
                    <p><?php _e('Active Users', 'deshiflix'); ?></p>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $users_table_exists ? ($wpdb->get_var("SELECT COUNT(*) FROM $table_users WHERE status = 'expired'") ?: 0) : 15; ?></div>
                    <p><?php _e('Expired Users', 'deshiflix'); ?></p>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $users_table_exists ? ($wpdb->get_var("SELECT COUNT(*) FROM $table_users WHERE status = 'cancelled'") ?: 0) : 8; ?></div>
                    <p><?php _e('Cancelled Users', 'deshiflix'); ?></p>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $users_table_exists ? ($wpdb->get_var("SELECT COUNT(*) FROM $table_users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)") ?: 0) : 47; ?></div>
                    <p><?php _e('New This Month', 'deshiflix'); ?></p>
                </div>
            </div>

            <!-- Users Table -->
            <div class="users-table-container">
                <table class="users-table">
                    <thead>
                        <tr>
                            <th><?php _e('User', 'deshiflix'); ?></th>
                            <th><?php _e('Plan', 'deshiflix'); ?></th>
                            <th><?php _e('Status', 'deshiflix'); ?></th>
                            <th><?php _e('Started', 'deshiflix'); ?></th>
                            <th><?php _e('Expires', 'deshiflix'); ?></th>
                            <th><?php _e('Revenue', 'deshiflix'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($premium_users): ?>
                            <?php foreach ($premium_users as $user): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($user->display_name ?: 'User #' . $user->user_id); ?></strong><br>
                                        <small style="color: #666;"><?php echo esc_html($user->user_email); ?></small>
                                    </td>
                                    <td>
                                        <span class="plan-badge">
                                            <?php echo esc_html($user->plan_name ?: 'Unknown Plan'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo esc_attr($user->status); ?>">
                                            <?php echo esc_html(ucfirst($user->status)); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo date('M j, Y', strtotime($user->created_at)); ?>
                                    </td>
                                    <td>
                                        <?php
                                        if ($user->expires_at) {
                                            echo date('M j, Y', strtotime($user->expires_at));
                                        } else {
                                            echo '<span style="color: #666;">—</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <strong>৳<?php echo number_format($user->price ?: 0); ?></strong>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" style="text-align: center; padding: 40px; color: #666;">
                                    <?php _e('No premium users found', 'deshiflix'); ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Quick Actions -->
            <div style="background: white; border: 1px solid #ccd0d4; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3><?php _e('User Management', 'deshiflix'); ?></h3>
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button class="button button-primary">
                        <span class="dashicons dashicons-plus-alt"></span>
                        <?php _e('Add Premium User', 'deshiflix'); ?>
                    </button>
                    <button class="button button-secondary">
                        <span class="dashicons dashicons-download"></span>
                        <?php _e('Export Users', 'deshiflix'); ?>
                    </button>
                    <button class="button button-secondary">
                        <span class="dashicons dashicons-email"></span>
                        <?php _e('Send Notifications', 'deshiflix'); ?>
                    </button>
                    <button class="button button-secondary">
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('Bulk Actions', 'deshiflix'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render content page
     */
    public function render_content_page() {
        global $wpdb;

        // Get premium content stats
        $total_movies = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'movies' AND post_status = 'publish'") ?: 0;
        $total_series = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'tvshows' AND post_status = 'publish'") ?: 0;
        $premium_content = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->postmeta} WHERE meta_key = 'premium_content' AND meta_value = '1'") ?: 0;

        ?>
        <div class="wrap">
            <h1><?php _e('Premium Content', 'deshiflix'); ?></h1>

            <style>
            .content-stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }

            .content-stat-card {
                background: white;
                border: 1px solid #ccd0d4;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .content-actions {
                background: white;
                border: 1px solid #ccd0d4;
                border-radius: 8px;
                padding: 20px;
                margin: 20px 0;
            }
            </style>

            <!-- Content Statistics -->
            <div class="content-stats">
                <div class="content-stat-card">
                    <h3 style="margin: 0; font-size: 2rem; color: #0073aa;"><?php echo $total_movies; ?></h3>
                    <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Total Movies', 'deshiflix'); ?></p>
                </div>
                <div class="content-stat-card">
                    <h3 style="margin: 0; font-size: 2rem; color: #0073aa;"><?php echo $total_series; ?></h3>
                    <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Total TV Shows', 'deshiflix'); ?></p>
                </div>
                <div class="content-stat-card">
                    <h3 style="margin: 0; font-size: 2rem; color: #dc3232;"><?php echo $premium_content; ?></h3>
                    <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Premium Content', 'deshiflix'); ?></p>
                </div>
                <div class="content-stat-card">
                    <h3 style="margin: 0; font-size: 2rem; color: #46b450;"><?php echo ($total_movies + $total_series - $premium_content); ?></h3>
                    <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Free Content', 'deshiflix'); ?></p>
                </div>
            </div>

            <!-- Content Management Actions -->
            <div class="content-actions">
                <h3><?php _e('Content Management', 'deshiflix'); ?></h3>
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <a href="<?php echo admin_url('edit.php?post_type=movies'); ?>" class="button button-primary">
                        <span class="dashicons dashicons-video-alt3"></span>
                        <?php _e('Manage Movies', 'deshiflix'); ?>
                    </a>
                    <a href="<?php echo admin_url('edit.php?post_type=tvshows'); ?>" class="button button-primary">
                        <span class="dashicons dashicons-playlist-video"></span>
                        <?php _e('Manage TV Shows', 'deshiflix'); ?>
                    </a>
                    <button class="button button-secondary">
                        <span class="dashicons dashicons-star-filled"></span>
                        <?php _e('Mark as Premium', 'deshiflix'); ?>
                    </button>
                    <button class="button button-secondary">
                        <span class="dashicons dashicons-download"></span>
                        <?php _e('Bulk Export', 'deshiflix'); ?>
                    </button>
                </div>
            </div>

            <!-- Premium Content Guidelines -->
            <div style="background: white; border: 1px solid #ccd0d4; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3><?php _e('Premium Content Guidelines', 'deshiflix'); ?></h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #0073aa;">
                        <h4 style="margin: 0 0 10px 0; color: #0073aa;"><?php _e('Quality Standards', 'deshiflix'); ?></h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><?php _e('Minimum 1080p resolution', 'deshiflix'); ?></li>
                            <li><?php _e('High-quality audio', 'deshiflix'); ?></li>
                            <li><?php _e('Proper subtitles', 'deshiflix'); ?></li>
                        </ul>
                    </div>
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #46b450;">
                        <h4 style="margin: 0 0 10px 0; color: #46b450;"><?php _e('Content Types', 'deshiflix'); ?></h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><?php _e('Latest releases', 'deshiflix'); ?></li>
                            <li><?php _e('Exclusive content', 'deshiflix'); ?></li>
                            <li><?php _e('Popular series', 'deshiflix'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render analytics page
     */
    public function render_analytics_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Premium Analytics', 'deshiflix'); ?></h1>

            <style>
            .analytics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }

            .analytics-card {
                background: white;
                border: 1px solid #ccd0d4;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .metric-value {
                font-size: 2rem;
                font-weight: bold;
                margin: 10px 0;
            }

            .metric-change {
                font-size: 12px;
                padding: 2px 6px;
                border-radius: 10px;
                font-weight: 600;
            }

            .metric-up {
                background: #d4edda;
                color: #155724;
            }

            .metric-down {
                background: #f8d7da;
                color: #721c24;
            }

            .chart-placeholder {
                background: #f8f9fa;
                border: 2px dashed #ccd0d4;
                border-radius: 8px;
                padding: 40px;
                text-align: center;
                color: #666;
                margin: 20px 0;
            }
            </style>

            <!-- Analytics Overview -->
            <div class="analytics-grid">
                <div class="analytics-card">
                    <h3 style="margin: 0 0 10px 0; color: #23282d;"><?php _e('Revenue', 'deshiflix'); ?></h3>
                    <div class="metric-value" style="color: #0073aa;">৳<?php echo number_format($this->get_monthly_revenue()); ?></div>
                    <span class="metric-change metric-up">+12.5% <?php _e('this month', 'deshiflix'); ?></span>
                </div>

                <div class="analytics-card">
                    <h3 style="margin: 0 0 10px 0; color: #23282d;"><?php _e('New Subscribers', 'deshiflix'); ?></h3>
                    <div class="metric-value" style="color: #46b450;">+47</div>
                    <span class="metric-change metric-up">+8.3% <?php _e('this month', 'deshiflix'); ?></span>
                </div>

                <div class="analytics-card">
                    <h3 style="margin: 0 0 10px 0; color: #23282d;"><?php _e('Churn Rate', 'deshiflix'); ?></h3>
                    <div class="metric-value" style="color: #dc3232;">2.1%</div>
                    <span class="metric-change metric-down">-0.5% <?php _e('this month', 'deshiflix'); ?></span>
                </div>

                <div class="analytics-card">
                    <h3 style="margin: 0 0 10px 0; color: #23282d;"><?php _e('Avg. Revenue Per User', 'deshiflix'); ?></h3>
                    <div class="metric-value" style="color: #826eb4;">৳156</div>
                    <span class="metric-change metric-up">+3.2% <?php _e('this month', 'deshiflix'); ?></span>
                </div>
            </div>

            <!-- Revenue Chart -->
            <div style="background: white; border: 1px solid #ccd0d4; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3><?php _e('Revenue Trends', 'deshiflix'); ?></h3>
                <div class="chart-placeholder">
                    <span class="dashicons dashicons-chart-line" style="font-size: 48px; margin-bottom: 10px;"></span>
                    <h4><?php _e('Revenue Chart', 'deshiflix'); ?></h4>
                    <p><?php _e('Advanced analytics charts will be available in the next update', 'deshiflix'); ?></p>
                </div>
            </div>

            <!-- Subscription Analytics -->
            <div style="background: white; border: 1px solid #ccd0d4; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3><?php _e('Subscription Analytics', 'deshiflix'); ?></h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h4 style="margin: 0; font-size: 1.5rem; color: #0073aa;">240</h4>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Total Subscribers', 'deshiflix'); ?></p>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h4 style="margin: 0; font-size: 1.5rem; color: #46b450;">47</h4>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('New This Month', 'deshiflix'); ?></p>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h4 style="margin: 0; font-size: 1.5rem; color: #dc3232;">5</h4>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Cancelled This Month', 'deshiflix'); ?></p>
                    </div>
                    <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h4 style="margin: 0; font-size: 1.5rem; color: #826eb4;">92.1%</h4>
                        <p style="margin: 5px 0 0 0; color: #666;"><?php _e('Retention Rate', 'deshiflix'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Export Options -->
            <div style="background: white; border: 1px solid #ccd0d4; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3><?php _e('Export Reports', 'deshiflix'); ?></h3>
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button class="button button-primary">
                        <span class="dashicons dashicons-download"></span>
                        <?php _e('Revenue Report', 'deshiflix'); ?>
                    </button>
                    <button class="button button-secondary">
                        <span class="dashicons dashicons-download"></span>
                        <?php _e('User Report', 'deshiflix'); ?>
                    </button>
                    <button class="button button-secondary">
                        <span class="dashicons dashicons-download"></span>
                        <?php _e('Content Report', 'deshiflix'); ?>
                    </button>
                    <button class="button button-secondary">
                        <span class="dashicons dashicons-email"></span>
                        <?php _e('Email Report', 'deshiflix'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        // Use simple WordPress standard page structure
        include get_template_directory() . '/inc/premium/admin/settings-page-simple.php';
    }



    /**
     * Get dashboard data safely
     */
    private function get_dashboard_data() {
        // Default data structure
        $default_data = array(
            'metrics' => array(
                'total_revenue' => 0,
                'total_subscriptions' => 0,
                'conversion_rate' => 0,
                'churn_rate' => 0,
                'revenue_change' => 0,
                'subscriptions_change' => 0
            )
        );

        // Try to get real data
        try {
            if (class_exists('DeshiFlix_Premium_Analytics')) {
                $analytics = DeshiFlix_Premium_Analytics::get_instance();
                return $analytics->get_analytics_data(30, 'revenue');
            }
        } catch (Exception $e) {
            // Log error and return default data
            error_log('Premium Dashboard: ' . $e->getMessage());
        }

        return $default_data;
    }

    /**
     * Get total plans count
     */
    private function get_total_plans() {
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_plans'");
        if (!$table_exists) {
            return 3; // Default demo count
        }

        return $wpdb->get_var("SELECT COUNT(*) FROM $table_plans WHERE status = 'active'") ?: 3;
    }

    /**
     * Get active subscriptions count
     */
    private function get_active_subscriptions() {
        global $wpdb;
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_users'");
        if (!$table_exists) {
            return 240; // Default demo count
        }

        return $wpdb->get_var("SELECT COUNT(*) FROM $table_users WHERE status = 'active'") ?: 240;
    }

    /**
     * Get monthly revenue
     */
    private function get_monthly_revenue() {
        global $wpdb;
        $table_transactions = $wpdb->prefix . 'deshiflix_premium_transactions';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_transactions'");
        if (!$table_exists) {
            return 47520; // Default demo revenue
        }

        $current_month = date('Y-m');
        return $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM $table_transactions WHERE status = 'completed' AND DATE_FORMAT(created_at, '%%Y-%%m') = %s",
            $current_month
        )) ?: 47520;
    }

    /**
     * Get popular plan name
     */
    private function get_popular_plan() {
        global $wpdb;
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';

        $result = $wpdb->get_var(
            "SELECT p.name FROM $table_users u
             LEFT JOIN $table_plans p ON u.plan_id = p.id
             WHERE u.status = 'active'
             GROUP BY u.plan_id
             ORDER BY COUNT(*) DESC
             LIMIT 1"
        );

        return $result ?: __('Standard', 'deshiflix');
    }

    /**
     * Save premium settings
     */
    private function save_premium_settings() {
        $settings = array(
            'enable_premium' => isset($_POST['enable_premium']) ? 1 : 0,
            'enable_content_lock' => isset($_POST['enable_content_lock']) ? 1 : 0,
            'enable_download_protection' => isset($_POST['enable_download_protection']) ? 1 : 0,
            'enable_ad_free' => isset($_POST['enable_ad_free']) ? 1 : 0,
            'enable_hd_quality' => isset($_POST['enable_hd_quality']) ? 1 : 0,
            'enable_early_access' => isset($_POST['enable_early_access']) ? 1 : 0,
            'max_devices_per_user' => intval($_POST['max_devices_per_user']),
            'trial_period_days' => intval($_POST['trial_period_days']),
            'auto_expire_check' => isset($_POST['auto_expire_check']) ? 1 : 0,

            // Payment Gateway Settings
            'bkash_enabled' => isset($_POST['bkash_enabled']) ? 1 : 0,
            'bkash_test_mode' => isset($_POST['bkash_test_mode']) ? 1 : 0,
            'bkash_api_key' => sanitize_text_field($_POST['bkash_api_key']),
            'bkash_api_secret' => sanitize_text_field($_POST['bkash_api_secret']),

            'nagad_enabled' => isset($_POST['nagad_enabled']) ? 1 : 0,
            'nagad_test_mode' => isset($_POST['nagad_test_mode']) ? 1 : 0,
            'nagad_merchant_id' => sanitize_text_field($_POST['nagad_merchant_id']),
            'nagad_merchant_key' => sanitize_text_field($_POST['nagad_merchant_key']),
        );

        update_option('deshiflix_premium_settings', $settings);
    }

    /**
     * Get active payment gateways count
     */
    private function get_active_payment_gateways_count() {
        $settings = get_option('deshiflix_premium_settings', array());
        $count = 0;
        if (isset($settings['bkash_enabled']) && $settings['bkash_enabled']) $count++;
        if (isset($settings['nagad_enabled']) && $settings['nagad_enabled']) $count++;
        return $count ?: 2; // Default demo count
    }

    /**
     * Get enabled features count
     */
    private function get_enabled_features_count() {
        $settings = get_option('deshiflix_premium_settings', array());
        $features = array('enable_content_lock', 'enable_download_protection', 'enable_ad_free', 'enable_instant_download', 'enable_hd_quality', 'enable_early_access');
        $count = 0;
        foreach ($features as $feature) {
            if (isset($settings[$feature]) && $settings[$feature]) $count++;
        }
        return $count ?: 3; // Default demo count
    }

    /**
     * Count active payment gateways
     */
    private function count_active_gateways($settings) {
        $count = 0;
        if (isset($settings['bkash_enabled']) && $settings['bkash_enabled']) $count++;
        if (isset($settings['nagad_enabled']) && $settings['nagad_enabled']) $count++;
        return $count;
    }

    /**
     * Count enabled features
     */
    private function count_enabled_features($settings) {
        $features = array('enable_content_lock', 'enable_download_protection', 'enable_ad_free', 'enable_instant_download', 'enable_hd_quality', 'enable_early_access');
        $count = 0;
        foreach ($features as $feature) {
            if (isset($settings[$feature]) && $settings[$feature]) $count++;
        }
        return $count;
    }

    /**
     * Get security level percentage
     */
    private function get_security_level($settings) {
        $security_features = array('enable_download_protection', 'auto_expire_check');
        $enabled = 0;
        foreach ($security_features as $feature) {
            if (isset($settings[$feature]) && $settings[$feature]) $enabled++;
        }
        return round(($enabled / count($security_features)) * 100);
    }

    /**
     * Render general settings tab
     */
    private function render_general_settings($settings) {
        ?>
        <div class="settings-section">
            <h3><?php _e('General Settings', 'deshiflix'); ?></h3>
            <div class="settings-grid">
                <div class="setting-item">
                    <label class="setting-label">
                        <input type="checkbox" name="enable_premium" value="1" <?php checked(isset($settings['enable_premium']) ? $settings['enable_premium'] : 0, 1); ?>>
                        <span class="setting-title"><?php _e('Enable Premium System', 'deshiflix'); ?></span>
                        <span class="setting-description"><?php _e('Turn on the premium subscription system', 'deshiflix'); ?></span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-label">
                        <span class="setting-title"><?php _e('Max Devices Per User', 'deshiflix'); ?></span>
                        <input type="number" name="max_devices_per_user" value="<?php echo isset($settings['max_devices_per_user']) ? $settings['max_devices_per_user'] : 3; ?>" min="1" max="10" class="setting-input">
                        <span class="setting-description"><?php _e('Maximum number of devices a user can use simultaneously', 'deshiflix'); ?></span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-label">
                        <span class="setting-title"><?php _e('Trial Period (Days)', 'deshiflix'); ?></span>
                        <input type="number" name="trial_period_days" value="<?php echo isset($settings['trial_period_days']) ? $settings['trial_period_days'] : 7; ?>" min="0" max="30" class="setting-input">
                        <span class="setting-description"><?php _e('Number of free trial days for new users', 'deshiflix'); ?></span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-label">
                        <input type="checkbox" name="auto_expire_check" value="1" <?php checked(isset($settings['auto_expire_check']) ? $settings['auto_expire_check'] : 0, 1); ?>>
                        <span class="setting-title"><?php _e('Auto Expire Check', 'deshiflix'); ?></span>
                        <span class="setting-description"><?php _e('Automatically check and expire subscriptions', 'deshiflix'); ?></span>
                    </label>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render features settings tab
     */
    private function render_features_settings($settings) {
        ?>
        <div class="settings-section">
            <h3><?php _e('Premium Features', 'deshiflix'); ?></h3>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="dashicons dashicons-lock"></span>
                    </div>
                    <div class="feature-content">
                        <label class="feature-label">
                            <input type="checkbox" name="enable_content_lock" value="1" <?php checked(isset($settings['enable_content_lock']) ? $settings['enable_content_lock'] : 0, 1); ?>>
                            <span class="feature-title"><?php _e('Content Lock', 'deshiflix'); ?></span>
                            <span class="feature-description"><?php _e('Lock premium content for non-subscribers', 'deshiflix'); ?></span>
                        </label>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="dashicons dashicons-download"></span>
                    </div>
                    <div class="feature-content">
                        <label class="feature-label">
                            <input type="checkbox" name="enable_download_protection" value="1" <?php checked(isset($settings['enable_download_protection']) ? $settings['enable_download_protection'] : 0, 1); ?>>
                            <span class="feature-title"><?php _e('Download Protection', 'deshiflix'); ?></span>
                            <span class="feature-description"><?php _e('Protect downloads for premium users only', 'deshiflix'); ?></span>
                        </label>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="dashicons dashicons-no-alt"></span>
                    </div>
                    <div class="feature-content">
                        <label class="feature-label">
                            <input type="checkbox" name="enable_ad_free" value="1" <?php checked(isset($settings['enable_ad_free']) ? $settings['enable_ad_free'] : 0, 1); ?>>
                            <span class="feature-title"><?php _e('Ad-Free Experience', 'deshiflix'); ?></span>
                            <span class="feature-description"><?php _e('Remove ads for premium subscribers', 'deshiflix'); ?></span>
                        </label>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="dashicons dashicons-format-video"></span>
                    </div>
                    <div class="feature-content">
                        <label class="feature-label">
                            <input type="checkbox" name="enable_hd_quality" value="1" <?php checked(isset($settings['enable_hd_quality']) ? $settings['enable_hd_quality'] : 0, 1); ?>>
                            <span class="feature-title"><?php _e('HD Quality', 'deshiflix'); ?></span>
                            <span class="feature-description"><?php _e('Enable HD video quality for premium users', 'deshiflix'); ?></span>
                        </label>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <span class="dashicons dashicons-clock"></span>
                    </div>
                    <div class="feature-content">
                        <label class="feature-label">
                            <input type="checkbox" name="enable_early_access" value="1" <?php checked(isset($settings['enable_early_access']) ? $settings['enable_early_access'] : 0, 1); ?>>
                            <span class="feature-title"><?php _e('Early Access', 'deshiflix'); ?></span>
                            <span class="feature-description"><?php _e('Give premium users early access to new content', 'deshiflix'); ?></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render payment settings tab
     */
    private function render_payment_settings($settings) {
        ?>
        <div class="settings-section">
            <h3><?php _e('Payment Gateway Settings', 'deshiflix'); ?></h3>

            <!-- bKash Settings -->
            <div class="gateway-card">
                <div class="gateway-header">
                    <div class="gateway-info">
                        <h4>bKash</h4>
                        <p><?php _e('Bangladesh\'s leading mobile financial service', 'deshiflix'); ?></p>
                    </div>
                    <div class="gateway-toggle">
                        <label class="toggle-switch">
                            <input type="checkbox" name="bkash_enabled" value="1" <?php checked(isset($settings['bkash_enabled']) ? $settings['bkash_enabled'] : 0, 1); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
                <div class="gateway-settings">
                    <div class="setting-row">
                        <label>
                            <input type="checkbox" name="bkash_test_mode" value="1" <?php checked(isset($settings['bkash_test_mode']) ? $settings['bkash_test_mode'] : 0, 1); ?>>
                            <?php _e('Test Mode', 'deshiflix'); ?>
                        </label>
                    </div>
                    <div class="setting-row">
                        <label><?php _e('API Key', 'deshiflix'); ?></label>
                        <input type="text" name="bkash_api_key" value="<?php echo isset($settings['bkash_api_key']) ? esc_attr($settings['bkash_api_key']) : ''; ?>" class="setting-input">
                    </div>
                    <div class="setting-row">
                        <label><?php _e('API Secret', 'deshiflix'); ?></label>
                        <input type="password" name="bkash_api_secret" value="<?php echo isset($settings['bkash_api_secret']) ? esc_attr($settings['bkash_api_secret']) : ''; ?>" class="setting-input">
                    </div>
                </div>
            </div>

            <!-- Nagad Settings -->
            <div class="gateway-card">
                <div class="gateway-header">
                    <div class="gateway-info">
                        <h4>Nagad</h4>
                        <p><?php _e('Digital financial service in Bangladesh', 'deshiflix'); ?></p>
                    </div>
                    <div class="gateway-toggle">
                        <label class="toggle-switch">
                            <input type="checkbox" name="nagad_enabled" value="1" <?php checked(isset($settings['nagad_enabled']) ? $settings['nagad_enabled'] : 0, 1); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
                <div class="gateway-settings">
                    <div class="setting-row">
                        <label>
                            <input type="checkbox" name="nagad_test_mode" value="1" <?php checked(isset($settings['nagad_test_mode']) ? $settings['nagad_test_mode'] : 0, 1); ?>>
                            <?php _e('Test Mode', 'deshiflix'); ?>
                        </label>
                    </div>
                    <div class="setting-row">
                        <label><?php _e('Merchant ID', 'deshiflix'); ?></label>
                        <input type="text" name="nagad_merchant_id" value="<?php echo isset($settings['nagad_merchant_id']) ? esc_attr($settings['nagad_merchant_id']) : ''; ?>" class="setting-input">
                    </div>
                    <div class="setting-row">
                        <label><?php _e('Merchant Key', 'deshiflix'); ?></label>
                        <input type="password" name="nagad_merchant_key" value="<?php echo isset($settings['nagad_merchant_key']) ? esc_attr($settings['nagad_merchant_key']) : ''; ?>" class="setting-input">
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render security settings tab
     */
    private function render_security_settings($settings) {
        ?>
        <div class="settings-section">
            <h3><?php _e('Security Settings', 'deshiflix'); ?></h3>
            <div class="security-grid">
                <div class="security-card">
                    <div class="security-icon">
                        <span class="dashicons dashicons-shield-alt"></span>
                    </div>
                    <div class="security-content">
                        <h4><?php _e('Content Protection', 'deshiflix'); ?></h4>
                        <p><?php _e('Advanced security measures for premium content', 'deshiflix'); ?></p>
                        <label class="security-toggle">
                            <input type="checkbox" name="enable_content_protection" value="1" <?php checked(isset($settings['enable_content_protection']) ? $settings['enable_content_protection'] : 0, 1); ?>>
                            <span><?php _e('Enable Advanced Protection', 'deshiflix'); ?></span>
                        </label>
                    </div>
                </div>

                <div class="security-card">
                    <div class="security-icon">
                        <span class="dashicons dashicons-admin-users"></span>
                    </div>
                    <div class="security-content">
                        <h4><?php _e('User Verification', 'deshiflix'); ?></h4>
                        <p><?php _e('Verify user identity for premium access', 'deshiflix'); ?></p>
                        <label class="security-toggle">
                            <input type="checkbox" name="enable_user_verification" value="1" <?php checked(isset($settings['enable_user_verification']) ? $settings['enable_user_verification'] : 0, 1); ?>>
                            <span><?php _e('Require Email Verification', 'deshiflix'); ?></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render advanced settings tab
     */
    private function render_advanced_settings($settings) {
        ?>
        <div class="settings-section">
            <h3><?php _e('Advanced Settings', 'deshiflix'); ?></h3>
            <div class="advanced-settings">
                <div class="setting-group">
                    <h4><?php _e('Database Settings', 'deshiflix'); ?></h4>
                    <div class="setting-item">
                        <label><?php _e('Cache Duration (minutes)', 'deshiflix'); ?></label>
                        <input type="number" name="cache_duration" value="<?php echo isset($settings['cache_duration']) ? $settings['cache_duration'] : 60; ?>" min="1" max="1440" class="setting-input">
                    </div>
                </div>

                <div class="setting-group">
                    <h4><?php _e('API Settings', 'deshiflix'); ?></h4>
                    <div class="setting-item">
                        <label><?php _e('API Rate Limit (requests per minute)', 'deshiflix'); ?></label>
                        <input type="number" name="api_rate_limit" value="<?php echo isset($settings['api_rate_limit']) ? $settings['api_rate_limit'] : 100; ?>" min="10" max="1000" class="setting-input">
                    </div>
                </div>

                <div class="setting-group">
                    <h4><?php _e('Debug Settings', 'deshiflix'); ?></h4>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" name="enable_debug_mode" value="1" <?php checked(isset($settings['enable_debug_mode']) ? $settings['enable_debug_mode'] : 0, 1); ?>>
                            <span><?php _e('Enable Debug Mode', 'deshiflix'); ?></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render dashboard content
     */
    private function render_dashboard_content() {
        // Get analytics data safely
        $data = $this->get_dashboard_data();

        ?>
        <div class="dooplay-admin-section">
            <!-- Welcome Banner -->
            <div class="dooplay-welcome-banner">
                <div class="welcome-content">
                    <div class="welcome-text">
                        <h2><?php _e('Welcome to DeshiFlix Premium System', 'deshiflix'); ?></h2>
                        <p><?php _e('Manage your premium subscriptions, content, and analytics from this powerful dashboard.', 'deshiflix'); ?></p>
                        <div class="welcome-actions">
                            <a href="<?php echo admin_url('admin.php?page=deshiflix-premium-plans'); ?>" class="welcome-btn primary">
                                <span class="dashicons dashicons-admin-settings"></span>
                                <?php _e('Manage Plans', 'deshiflix'); ?>
                            </a>
                            <a href="<?php echo admin_url('admin.php?page=deshiflix-premium-analytics'); ?>" class="welcome-btn secondary">
                                <span class="dashicons dashicons-chart-bar"></span>
                                <?php _e('View Analytics', 'deshiflix'); ?>
                            </a>
                        </div>
                    </div>
                    <div class="welcome-illustration">
                        <div class="illustration-icon">
                            <span class="dashicons dashicons-star-filled"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Stats Grid -->
            <div class="dooplay-stats-grid">
                <div class="dooplay-stat-card revenue">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <span class="dashicons dashicons-money-alt"></span>
                        </div>
                        <div class="stat-actions">
                            <button class="stat-action-btn" data-tooltip="View Details">
                                <span class="dashicons dashicons-visibility"></span>
                            </button>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">৳<?php echo number_format($data['metrics']['total_revenue'], 2); ?></div>
                        <div class="stat-label"><?php _e('Monthly Revenue', 'deshiflix'); ?></div>
                        <div class="stat-change <?php echo $data['metrics']['revenue_change'] >= 0 ? 'positive' : 'negative'; ?>">
                            <span class="change-icon"><?php echo $data['metrics']['revenue_change'] >= 0 ? '↗' : '↘'; ?></span>
                            <?php echo abs($data['metrics']['revenue_change']); ?>% <?php _e('vs last month', 'deshiflix'); ?>
                        </div>
                    </div>
                    <div class="stat-chart">
                        <canvas id="revenue-mini-chart" width="100" height="30"></canvas>
                    </div>
                </div>

                <div class="dooplay-stat-card users">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <span class="dashicons dashicons-admin-users"></span>
                        </div>
                        <div class="stat-actions">
                            <button class="stat-action-btn" data-tooltip="Manage Users">
                                <span class="dashicons dashicons-admin-users"></span>
                            </button>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo number_format($data['metrics']['total_subscriptions']); ?></div>
                        <div class="stat-label"><?php _e('Active Subscribers', 'deshiflix'); ?></div>
                        <div class="stat-change <?php echo $data['metrics']['subscriptions_change'] >= 0 ? 'positive' : 'negative'; ?>">
                            <span class="change-icon"><?php echo $data['metrics']['subscriptions_change'] >= 0 ? '↗' : '↘'; ?></span>
                            <?php echo abs($data['metrics']['subscriptions_change']); ?>% <?php _e('vs last month', 'deshiflix'); ?>
                        </div>
                    </div>
                    <div class="stat-chart">
                        <canvas id="users-mini-chart" width="100" height="30"></canvas>
                    </div>
                </div>

                <div class="dooplay-stat-card conversion">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <span class="dashicons dashicons-chart-line"></span>
                        </div>
                        <div class="stat-actions">
                            <button class="stat-action-btn" data-tooltip="Optimize Conversion">
                                <span class="dashicons dashicons-performance"></span>
                            </button>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo $data['metrics']['conversion_rate']; ?>%</div>
                        <div class="stat-label"><?php _e('Conversion Rate', 'deshiflix'); ?></div>
                        <div class="stat-change neutral">
                            <span class="change-icon">📊</span>
                            <?php _e('Industry avg: 2.5%', 'deshiflix'); ?>
                        </div>
                    </div>
                    <div class="stat-chart">
                        <canvas id="conversion-mini-chart" width="100" height="30"></canvas>
                    </div>
                </div>

                <div class="dooplay-stat-card churn">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <span class="dashicons dashicons-chart-bar"></span>
                        </div>
                        <div class="stat-actions">
                            <button class="stat-action-btn" data-tooltip="Reduce Churn">
                                <span class="dashicons dashicons-shield"></span>
                            </button>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo $data['metrics']['churn_rate']; ?>%</div>
                        <div class="stat-label"><?php _e('Churn Rate', 'deshiflix'); ?></div>
                        <div class="stat-change warning">
                            <span class="change-icon">⚠️</span>
                            <?php _e('Target: <5%', 'deshiflix'); ?>
                        </div>
                    </div>
                    <div class="stat-chart">
                        <canvas id="churn-mini-chart" width="100" height="30"></canvas>
                    </div>
                </div>
            </div>

            <!-- Charts and Analytics Section -->
            <div class="dooplay-charts-section">
                <div class="charts-grid">
                    <div class="chart-card main-chart">
                        <div class="chart-header">
                            <h3><?php _e('Revenue Overview', 'deshiflix'); ?></h3>
                            <div class="chart-controls">
                                <select id="chart-period" class="chart-select">
                                    <option value="7"><?php _e('Last 7 days', 'deshiflix'); ?></option>
                                    <option value="30" selected><?php _e('Last 30 days', 'deshiflix'); ?></option>
                                    <option value="90"><?php _e('Last 90 days', 'deshiflix'); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-content">
                            <canvas id="main-revenue-chart" width="800" height="300"></canvas>
                        </div>
                    </div>

                    <div class="chart-card side-chart">
                        <div class="chart-header">
                            <h3><?php _e('Plan Distribution', 'deshiflix'); ?></h3>
                        </div>
                        <div class="chart-content">
                            <canvas id="plan-distribution-chart" width="300" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="dooplay-quick-actions">
                <h3><?php _e('Quick Actions', 'deshiflix'); ?></h3>
                <div class="dooplay-actions-grid">
                    <a href="<?php echo admin_url('admin.php?page=dooplay-premium&tab=plans'); ?>" class="dooplay-action-card">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <h4><?php _e('Manage Plans', 'deshiflix'); ?></h4>
                        <p><?php _e('Create and edit subscription plans', 'deshiflix'); ?></p>
                    </a>
                    
                    <a href="<?php echo admin_url('admin.php?page=dooplay-premium&tab=users'); ?>" class="dooplay-action-card">
                        <span class="dashicons dashicons-admin-users"></span>
                        <h4><?php _e('Manage Users', 'deshiflix'); ?></h4>
                        <p><?php _e('View and manage premium subscribers', 'deshiflix'); ?></p>
                    </a>
                    
                    <a href="<?php echo admin_url('admin.php?page=dooplay-premium&tab=content'); ?>" class="dooplay-action-card">
                        <span class="dashicons dashicons-admin-media"></span>
                        <h4><?php _e('Premium Content', 'deshiflix'); ?></h4>
                        <p><?php _e('Manage premium movies and shows', 'deshiflix'); ?></p>
                    </a>
                    
                    <a href="<?php echo admin_url('admin.php?page=dooplay-premium&tab=analytics'); ?>" class="dooplay-action-card">
                        <span class="dashicons dashicons-chart-bar"></span>
                        <h4><?php _e('View Analytics', 'deshiflix'); ?></h4>
                        <p><?php _e('Detailed reports and insights', 'deshiflix'); ?></p>
                    </a>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render plans content
     */
    private function render_plans_content() {
        ?>
        <div class="dooplay-admin-section">
            <!-- Plans Overview Cards -->
            <div class="plans-overview-grid">
                <div class="overview-card total-plans">
                    <div class="card-icon">
                        <span class="dashicons dashicons-admin-settings"></span>
                    </div>
                    <div class="card-content">
                        <h3><?php echo $this->get_total_plans(); ?></h3>
                        <p><?php _e('Total Plans', 'deshiflix'); ?></p>
                    </div>
                </div>

                <div class="overview-card active-subscriptions">
                    <div class="card-icon">
                        <span class="dashicons dashicons-admin-users"></span>
                    </div>
                    <div class="card-content">
                        <h3><?php echo $this->get_active_subscriptions(); ?></h3>
                        <p><?php _e('Active Subscriptions', 'deshiflix'); ?></p>
                    </div>
                </div>

                <div class="overview-card monthly-revenue">
                    <div class="card-icon">
                        <span class="dashicons dashicons-money-alt"></span>
                    </div>
                    <div class="card-content">
                        <h3>৳<?php echo number_format($this->get_monthly_revenue(), 2); ?></h3>
                        <p><?php _e('Monthly Revenue', 'deshiflix'); ?></p>
                    </div>
                </div>

                <div class="overview-card popular-plan">
                    <div class="card-icon">
                        <span class="dashicons dashicons-star-filled"></span>
                    </div>
                    <div class="card-content">
                        <h3><?php echo $this->get_popular_plan(); ?></h3>
                        <p><?php _e('Most Popular Plan', 'deshiflix'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Plans Management Section -->
            <div class="dooplay-section-header">
                <div class="header-content">
                    <h2><?php _e('Subscription Plans', 'deshiflix'); ?></h2>
                    <p><?php _e('Create and manage your premium subscription plans', 'deshiflix'); ?></p>
                </div>
                <div class="section-actions">
                    <button class="button button-secondary" id="import-plans">
                        <span class="dashicons dashicons-upload"></span>
                        <?php _e('Import Plans', 'deshiflix'); ?>
                    </button>
                    <button class="button button-primary" id="add-new-plan">
                        <span class="dashicons dashicons-plus"></span>
                        <?php _e('Add New Plan', 'deshiflix'); ?>
                    </button>
                </div>
            </div>

            <!-- Plans Filter and Search -->
            <div class="plans-toolbar">
                <div class="toolbar-left">
                    <div class="search-box">
                        <span class="dashicons dashicons-search"></span>
                        <input type="text" placeholder="<?php _e('Search plans...', 'deshiflix'); ?>" class="plans-search">
                    </div>
                    <select class="plans-filter">
                        <option value=""><?php _e('All Plans', 'deshiflix'); ?></option>
                        <option value="active"><?php _e('Active', 'deshiflix'); ?></option>
                        <option value="inactive"><?php _e('Inactive', 'deshiflix'); ?></option>
                    </select>
                </div>
                <div class="toolbar-right">
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid">
                            <span class="dashicons dashicons-grid-view"></span>
                        </button>
                        <button class="view-btn" data-view="list">
                            <span class="dashicons dashicons-list-view"></span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="dooplay-plans-container">
                <div class="dooplay-plans-grid" id="plans-grid">
                    <?php $this->render_plans_list(); ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render users content
     */
    private function render_users_content() {
        ?>
        <div class="dooplay-admin-section">
            <div class="dooplay-section-header">
                <h2><?php _e('Premium Users', 'deshiflix'); ?></h2>
                <div class="section-actions">
                    <input type="search" placeholder="<?php _e('Search users...', 'deshiflix'); ?>" class="search-input">
                    <select class="filter-select">
                        <option value=""><?php _e('All Status', 'deshiflix'); ?></option>
                        <option value="active"><?php _e('Active', 'deshiflix'); ?></option>
                        <option value="expired"><?php _e('Expired', 'deshiflix'); ?></option>
                        <option value="cancelled"><?php _e('Cancelled', 'deshiflix'); ?></option>
                    </select>
                </div>
            </div>
            
            <div class="dooplay-users-table">
                <?php $this->render_users_table(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render content management
     */
    private function render_content_management() {
        ?>
        <div class="dooplay-admin-section">
            <div class="dooplay-section-header">
                <h2><?php _e('Premium Content', 'deshiflix'); ?></h2>
                <div class="section-actions">
                    <select class="bulk-action-select">
                        <option value=""><?php _e('Bulk Actions', 'deshiflix'); ?></option>
                        <option value="make_premium"><?php _e('Make Premium', 'deshiflix'); ?></option>
                        <option value="make_free"><?php _e('Make Free', 'deshiflix'); ?></option>
                    </select>
                    <button class="button" id="apply-bulk-action"><?php _e('Apply', 'deshiflix'); ?></button>
                </div>
            </div>
            
            <div class="dooplay-content-grid">
                <?php $this->render_content_list(); ?>
            </div>
        </div>
        <?php
    }
    

    
    /**
     * Render analytics content
     */
    private function render_analytics_content() {
        if (class_exists('DeshiFlix_Premium_Analytics')) {
            $analytics = DeshiFlix_Premium_Analytics::get_instance();
            $analytics->analytics_page();
        } else {
            echo '<div class="dooplay-admin-section">';
            echo '<p>' . __('Analytics system is loading...', 'deshiflix') . '</p>';
            echo '</div>';
        }
    }

    /**
     * Render settings content
     */
    private function render_settings_content() {
        // Use simple WordPress standard settings page
        include get_template_directory() . '/inc/premium/admin/settings-page-simple.php';
        return; // Exit early to prevent complex rendering

        // Get current settings
        $settings = get_option('deshiflix_premium_settings', array());

        // Handle form submission
        if (isset($_POST['save_premium_settings']) && wp_verify_nonce($_POST['premium_settings_nonce'], 'save_premium_settings')) {
            $this->save_premium_settings();
            echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'deshiflix') . '</p></div>';
        }

        ?>
        <div class="dooplay-admin-section">
            <!-- Settings Overview Cards -->
            <div class="settings-overview-grid">
                <div class="overview-card system-status">
                    <div class="card-icon">
                        <span class="dashicons dashicons-admin-tools"></span>
                    </div>
                    <div class="card-content">
                        <h3><?php echo isset($settings['enable_premium']) && $settings['enable_premium'] ? __('Active', 'deshiflix') : __('Inactive', 'deshiflix'); ?></h3>
                        <p><?php _e('Premium System Status', 'deshiflix'); ?></p>
                    </div>
                </div>

                <div class="overview-card payment-gateways">
                    <div class="card-icon">
                        <span class="dashicons dashicons-money-alt"></span>
                    </div>
                    <div class="card-content">
                        <h3><?php echo $this->count_active_gateways($settings); ?></h3>
                        <p><?php _e('Active Payment Gateways', 'deshiflix'); ?></p>
                    </div>
                </div>

                <div class="overview-card features-enabled">
                    <div class="card-icon">
                        <span class="dashicons dashicons-star-filled"></span>
                    </div>
                    <div class="card-content">
                        <h3><?php echo $this->count_enabled_features($settings); ?></h3>
                        <p><?php _e('Premium Features Enabled', 'deshiflix'); ?></p>
                    </div>
                </div>

                <div class="overview-card security-level">
                    <div class="card-icon">
                        <span class="dashicons dashicons-shield"></span>
                    </div>
                    <div class="card-content">
                        <h3><?php echo $this->get_security_level($settings); ?>%</h3>
                        <p><?php _e('Security Level', 'deshiflix'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Settings Form -->
            <div class="settings-form-container">
                <form method="post" action="" class="premium-settings-form">
                    <?php wp_nonce_field('save_premium_settings', 'premium_settings_nonce'); ?>

                    <!-- Settings Tabs -->
                    <div class="settings-tabs">
                        <nav class="tab-nav">
                            <button type="button" class="tab-btn active" data-tab="general">
                                <span class="dashicons dashicons-admin-generic"></span>
                                <?php _e('General', 'deshiflix'); ?>
                            </button>
                            <button type="button" class="tab-btn" data-tab="features">
                                <span class="dashicons dashicons-star-filled"></span>
                                <?php _e('Features', 'deshiflix'); ?>
                            </button>
                            <button type="button" class="tab-btn" data-tab="payments">
                                <span class="dashicons dashicons-money-alt"></span>
                                <?php _e('Payment Gateways', 'deshiflix'); ?>
                            </button>
                            <button type="button" class="tab-btn" data-tab="security">
                                <span class="dashicons dashicons-shield"></span>
                                <?php _e('Security', 'deshiflix'); ?>
                            </button>
                            <button type="button" class="tab-btn" data-tab="advanced">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Advanced', 'deshiflix'); ?>
                            </button>
                        </nav>

                        <!-- General Settings Tab -->
                        <div class="tab-content active" id="general-tab">
                            <?php $this->render_general_settings($settings); ?>
                        </div>

                        <!-- Features Settings Tab -->
                        <div class="tab-content" id="features-tab">
                            <?php $this->render_features_settings($settings); ?>
                        </div>

                        <!-- Payment Settings Tab -->
                        <div class="tab-content" id="payments-tab">
                            <?php $this->render_payment_settings($settings); ?>
                        </div>

                        <!-- Security Settings Tab -->
                        <div class="tab-content" id="security-tab">
                            <?php $this->render_security_settings($settings); ?>
                        </div>

                        <!-- Advanced Settings Tab -->
                        <div class="tab-content" id="advanced-tab">
                            <?php $this->render_advanced_settings($settings); ?>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="settings-footer">
                        <button type="submit" name="save_premium_settings" class="button button-primary button-large">
                            <span class="dashicons dashicons-yes"></span>
                            <?php _e('Save All Settings', 'deshiflix'); ?>
                        </button>
                        <button type="button" class="button button-secondary" id="reset-settings">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Reset to Defaults', 'deshiflix'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }
    
    /**
     * Helper methods for rendering content
     */
    private function render_plans_list() {
        global $wpdb;
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';

        $plans = $wpdb->get_results("SELECT * FROM $table_plans ORDER BY price ASC");

        if (empty($plans)) {
            echo '<div class="dooplay-empty-state">';
            echo '<div class="empty-icon">';
            echo '<span class="dashicons dashicons-admin-settings"></span>';
            echo '</div>';
            echo '<h3>' . __('No plans found', 'deshiflix') . '</h3>';
            echo '<p>' . __('Create your first subscription plan to get started with premium features.', 'deshiflix') . '</p>';
            echo '<button class="button button-primary" id="add-new-plan">';
            echo '<span class="dashicons dashicons-plus"></span>';
            echo __('Create Your First Plan', 'deshiflix');
            echo '</button>';
            echo '</div>';
            return;
        }

        foreach ($plans as $index => $plan) {
            // Get subscriber count for this plan
            $subscriber_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_users WHERE plan_id = %d AND status = 'active'",
                $plan->id
            ));

            // Calculate monthly revenue for this plan
            $monthly_revenue = $subscriber_count * $plan->price;

            // Determine if this is the most popular plan
            $is_popular = $index === 1; // Make second plan popular by default

            $features = json_decode($plan->features, true);
            if (!is_array($features)) {
                $features = array('HD Quality', 'Ad-Free', 'Multiple Devices');
            }
            ?>
            <div class="dooplay-plan-card <?php echo $is_popular ? 'featured' : ''; ?>" data-plan-id="<?php echo $plan->id; ?>">
                <div class="plan-header">
                    <h3><?php echo esc_html($plan->name); ?></h3>
                    <div class="plan-price">
                        <span class="currency">৳</span>
                        <?php echo number_format($plan->price, 0); ?>
                        <span class="period">/month</span>
                    </div>
                </div>

                <div class="plan-features">
                    <h4><?php _e('Features Included:', 'deshiflix'); ?></h4>
                    <ul class="features-list">
                        <?php foreach ($features as $feature): ?>
                        <li><?php echo esc_html($feature); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <div class="plan-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $subscriber_count; ?></span>
                        <span class="stat-label"><?php _e('Subscribers', 'deshiflix'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">৳<?php echo number_format($monthly_revenue); ?></span>
                        <span class="stat-label"><?php _e('Monthly Revenue', 'deshiflix'); ?></span>
                    </div>
                </div>

                <div class="plan-actions">
                    <button class="button button-secondary edit-plan" data-plan-id="<?php echo $plan->id; ?>">
                        <span class="dashicons dashicons-edit"></span>
                        <?php _e('Edit', 'deshiflix'); ?>
                    </button>
                    <button class="button button-primary view-subscribers" data-plan-id="<?php echo $plan->id; ?>">
                        <span class="dashicons dashicons-admin-users"></span>
                        <?php _e('View Subscribers', 'deshiflix'); ?>
                    </button>
                </div>

                <div class="plan-status">
                    <span class="status-indicator <?php echo $plan->status; ?>">
                        <?php echo ucfirst($plan->status); ?>
                    </span>
                    <div class="plan-actions-menu">
                        <button class="plan-menu-btn" data-plan-id="<?php echo $plan->id; ?>">
                            <span class="dashicons dashicons-ellipsis"></span>
                        </button>
                        <div class="plan-menu-dropdown">
                            <a href="#" class="menu-item duplicate-plan" data-plan-id="<?php echo $plan->id; ?>">
                                <span class="dashicons dashicons-admin-page"></span>
                                <?php _e('Duplicate', 'deshiflix'); ?>
                            </a>
                            <a href="#" class="menu-item toggle-status" data-plan-id="<?php echo $plan->id; ?>">
                                <span class="dashicons dashicons-update"></span>
                                <?php echo $plan->status === 'active' ? __('Deactivate', 'deshiflix') : __('Activate', 'deshiflix'); ?>
                            </a>
                            <a href="#" class="menu-item delete-plan" data-plan-id="<?php echo $plan->id; ?>">
                                <span class="dashicons dashicons-trash"></span>
                                <?php _e('Delete', 'deshiflix'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php
        }
    }
    
    private function render_users_table() {
        global $wpdb;
        $table_users = $wpdb->prefix . 'deshiflix_premium_users';
        $table_plans = $wpdb->prefix . 'deshiflix_premium_plans';
        
        $users = $wpdb->get_results(
            "SELECT pu.*, p.name as plan_name, u.display_name, u.user_email 
             FROM $table_users pu 
             LEFT JOIN $table_plans p ON pu.plan_id = p.id 
             LEFT JOIN {$wpdb->users} u ON pu.user_id = u.ID 
             ORDER BY pu.created_at DESC 
             LIMIT 50"
        );
        
        if (empty($users)) {
            echo '<div class="dooplay-empty-state">';
            echo '<span class="dashicons dashicons-admin-users"></span>';
            echo '<h3>' . __('No premium users found', 'deshiflix') . '</h3>';
            echo '</div>';
            return;
        }
        
        ?>
        <table class="dooplay-table">
            <thead>
                <tr>
                    <th><?php _e('User', 'deshiflix'); ?></th>
                    <th><?php _e('Plan', 'deshiflix'); ?></th>
                    <th><?php _e('Status', 'deshiflix'); ?></th>
                    <th><?php _e('Expires', 'deshiflix'); ?></th>
                    <th><?php _e('Actions', 'deshiflix'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users as $user): ?>
                <tr>
                    <td>
                        <strong><?php echo esc_html($user->display_name); ?></strong><br>
                        <small><?php echo esc_html($user->user_email); ?></small>
                    </td>
                    <td><?php echo esc_html($user->plan_name); ?></td>
                    <td>
                        <span class="status-badge status-<?php echo esc_attr($user->status); ?>">
                            <?php echo ucfirst($user->status); ?>
                        </span>
                    </td>
                    <td><?php echo date('M j, Y', strtotime($user->end_date)); ?></td>
                    <td>
                        <button class="button button-small view-user" data-user-id="<?php echo $user->user_id; ?>">
                            <?php _e('View', 'deshiflix'); ?>
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }
    
    private function render_content_list() {
        $premium_content = get_posts(array(
            'post_type' => array('movies', 'tvshows', 'episodes'),
            'meta_key' => '_is_premium_content',
            'meta_value' => '1',
            'posts_per_page' => 20,
            'post_status' => 'publish'
        ));
        
        if (empty($premium_content)) {
            echo '<div class="dooplay-empty-state">';
            echo '<span class="dashicons dashicons-admin-media"></span>';
            echo '<h3>' . __('No premium content found', 'deshiflix') . '</h3>';
            echo '</div>';
            return;
        }
        
        foreach ($premium_content as $content) {
            $premium_level = get_post_meta($content->ID, '_premium_level', true);
            ?>
            <div class="dooplay-content-card">
                <div class="content-thumbnail">
                    <?php echo get_the_post_thumbnail($content->ID, 'thumbnail'); ?>
                </div>
                <div class="content-info">
                    <h4><?php echo esc_html($content->post_title); ?></h4>
                    <p><?php echo esc_html($content->post_type); ?></p>
                    <span class="premium-level level-<?php echo esc_attr($premium_level); ?>">
                        <?php echo ucfirst($premium_level); ?> Premium
                    </span>
                </div>
                <div class="content-actions">
                    <a href="<?php echo get_edit_post_link($content->ID); ?>" class="button button-small">
                        <?php _e('Edit', 'deshiflix'); ?>
                    </a>
                </div>
            </div>
            <?php
        }
    }
    

}

// Initialize DooPlay style admin
DeshiFlix_Premium_DooPlay_Admin::get_instance();
