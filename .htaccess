
RewriteEngine On

# API Directory - Allow direct access
RewriteRule ^api/ - [L]

# API Files - Direct Access (Before WordPress Rules)
RewriteRule ^test-api\.php$ - [L]
RewriteRule ^series-api\.php$ - [L]
RewriteRule ^episode-api\.php$ - [L]
RewriteRule ^debug-metadata\.php$ - [L]
RewriteRule ^find-movies\.php$ - [L]
RewriteRule ^data-verification\.html$ - [L]

# API Routes for DeshiFlix Mobile App
RewriteRule ^wp-json/doomobile/v1/(.*)$ test_api.php [QSA,L]

# BEGIN WordPress
# The directives (lines) between "BEGIN WordPress" and "END WordPress" are
# dynamically generated, and should only be modified via WordPress filters.
# Any changes to the directives between these markers will be overwritten.
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
RewriteBase /deshiflix/
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /deshiflix/index.php [L]
</IfModule>

# END WordPress