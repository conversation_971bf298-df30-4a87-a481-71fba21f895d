<?php
/*
* Live TV Channel Checker - Check which channels are working
*/

// Security check
if (!defined('ABSPATH')) {
    exit;
}

// Check admin permissions
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

global $wpdb;
$table_channels = $wpdb->prefix . 'doo_livetv_channels';

// Handle AJAX check request
if (isset($_POST['action']) && $_POST['action'] === 'check_channels') {
    check_ajax_referer('livetv_channel_check', 'nonce');
    
    $channel_ids = isset($_POST['channel_ids']) ? array_map('intval', $_POST['channel_ids']) : array();
    
    if (empty($channel_ids)) {
        wp_send_json_error('No channels selected');
    }
    
    $results = array();
    
    foreach ($channel_ids as $channel_id) {
        $channel = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_channels WHERE id = %d",
            $channel_id
        ));
        
        if (!$channel) {
            continue;
        }
        
        $check_result = check_stream_status($channel->stream_url);
        $results[] = array(
            'id' => $channel_id,
            'name' => $channel->name,
            'url' => $channel->stream_url,
            'status' => $check_result['status'],
            'message' => $check_result['message'],
            'response_time' => $check_result['response_time']
        );
        
        // Update channel status based on check result
        if ($check_result['status'] === 'working') {
            $wpdb->update(
                $table_channels,
                array('status' => 'active'),
                array('id' => $channel_id),
                array('%s'),
                array('%d')
            );
        } else {
            $wpdb->update(
                $table_channels,
                array('status' => 'inactive'),
                array('id' => $channel_id),
                array('%s'),
                array('%d')
            );
        }
    }
    
    wp_send_json_success($results);
}

function check_stream_status($url) {
    $start_time = microtime(true);
    
    // Basic URL validation
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return array(
            'status' => 'invalid',
            'message' => 'Invalid URL format',
            'response_time' => 0
        );
    }
    
    // Check stream accessibility
    $response = wp_remote_head($url, array(
        'timeout' => 15,
        'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'headers' => array(
            'Accept' => '*/*',
            'Accept-Language' => 'en-US,en;q=0.9',
            'Cache-Control' => 'no-cache',
            'Pragma' => 'no-cache'
        )
    ));
    
    $end_time = microtime(true);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    if (is_wp_error($response)) {
        return array(
            'status' => 'error',
            'message' => 'Connection failed: ' . $response->get_error_message(),
            'response_time' => $response_time
        );
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $headers = wp_remote_retrieve_headers($response);
    
    // Check response code
    if ($response_code >= 200 && $response_code < 400) {
        $content_type = isset($headers['content-type']) ? $headers['content-type'] : '';
        
        // Check if it's a valid streaming content type
        $valid_types = array(
            'application/vnd.apple.mpegurl',
            'application/x-mpegurl',
            'video/mp4',
            'video/webm',
            'application/dash+xml'
        );
        
        $is_valid_stream = false;
        foreach ($valid_types as $type) {
            if (strpos($content_type, $type) !== false) {
                $is_valid_stream = true;
                break;
            }
        }
        
        // Also check URL extension
        if (!$is_valid_stream) {
            $url_lower = strtolower($url);
            if (strpos($url_lower, '.m3u8') !== false || 
                strpos($url_lower, '.mp4') !== false || 
                strpos($url_lower, '.webm') !== false) {
                $is_valid_stream = true;
            }
        }
        
        if ($is_valid_stream) {
            return array(
                'status' => 'working',
                'message' => 'Stream accessible (HTTP ' . $response_code . ')',
                'response_time' => $response_time
            );
        } else {
            return array(
                'status' => 'warning',
                'message' => 'Accessible but may not be a valid stream (HTTP ' . $response_code . ')',
                'response_time' => $response_time
            );
        }
    } else {
        return array(
            'status' => 'error',
            'message' => 'HTTP ' . $response_code . ' error',
            'response_time' => $response_time
        );
    }
}

// Get all channels
$channels = $wpdb->get_results("
    SELECT c.*, cat.name as category_name 
    FROM $table_channels c 
    LEFT JOIN {$wpdb->prefix}doo_livetv_categories cat ON c.category_id = cat.id 
    ORDER BY c.name ASC
");

$total_channels = count($channels);
$active_channels = count(array_filter($channels, function($ch) { return $ch->status === 'active'; }));
$inactive_channels = $total_channels - $active_channels;
?>

<div class="wrap">
    <h1>🔍 Channel Status Checker</h1>
    
    <div class="notice notice-info">
        <p><strong>Channel Status Overview:</strong> 
        Total: <?php echo $total_channels; ?> | 
        Active: <span style="color: green;"><?php echo $active_channels; ?></span> | 
        Inactive: <span style="color: red;"><?php echo $inactive_channels; ?></span>
        </p>
    </div>
    
    <div style="margin: 20px 0;">
        <button id="check-all-btn" class="button button-primary">🔄 Check All Channels</button>
        <button id="check-selected-btn" class="button">🔍 Check Selected</button>
        <button id="activate-working-btn" class="button button-secondary">✅ Activate Working</button>
        <button id="deactivate-broken-btn" class="button">❌ Deactivate Broken</button>
    </div>
    
    <div id="check-progress" style="display: none; margin: 20px 0;">
        <div style="background: #f0f0f0; border-radius: 4px; padding: 10px;">
            <div id="progress-bar" style="background: #007cba; height: 20px; border-radius: 4px; width: 0%; transition: width 0.3s;"></div>
            <div id="progress-text" style="text-align: center; margin-top: 5px;">Checking channels...</div>
        </div>
    </div>
    
    <form id="channels-form">
        <?php wp_nonce_field('livetv_channel_check', 'nonce'); ?>
        
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <td class="check-column">
                        <input type="checkbox" id="select-all">
                    </td>
                    <th>Channel</th>
                    <th>Stream URL</th>
                    <th>Category</th>
                    <th>Current Status</th>
                    <th>Last Check</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($channels)): ?>
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px;">
                            <p>No channels found. <a href="<?php echo admin_url('admin.php?page=doo-livetv-import'); ?>">Import some channels</a> first.</p>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($channels as $channel): ?>
                        <tr id="channel-<?php echo $channel->id; ?>" data-channel-id="<?php echo $channel->id; ?>">
                            <th class="check-column">
                                <input type="checkbox" name="channel_ids[]" value="<?php echo $channel->id; ?>">
                            </th>
                            <td>
                                <strong><?php echo esc_html($channel->name); ?></strong>
                                <div class="row-actions">
                                    <span class="edit">
                                        <a href="<?php echo admin_url('admin.php?page=doo-livetv-add&edit=' . $channel->id); ?>">Edit</a> |
                                    </span>
                                    <span class="view">
                                        <a href="<?php echo home_url('/live-tv/?channel=' . $channel->slug); ?>" target="_blank">View</a>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <code style="font-size: 11px;">
                                    <?php echo esc_html(strlen($channel->stream_url) > 60 ? substr($channel->stream_url, 0, 60) . '...' : $channel->stream_url); ?>
                                </code>
                            </td>
                            <td><?php echo esc_html($channel->category_name ?: 'Uncategorized'); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $channel->status; ?>">
                                    <?php echo $channel->status === 'active' ? '✅ Active' : '❌ Inactive'; ?>
                                </span>
                            </td>
                            <td>
                                <span class="last-check">Never</span>
                            </td>
                            <td>
                                <button type="button" class="button button-small check-single" data-channel-id="<?php echo $channel->id; ?>">
                                    🔍 Check
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </form>
</div>

<style>
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.check-result {
    margin-top: 5px;
    padding: 5px;
    border-radius: 3px;
    font-size: 12px;
}

.result-working {
    background: #d4edda;
    color: #155724;
}

.result-error {
    background: #f8d7da;
    color: #721c24;
}

.result-warning {
    background: #fff3cd;
    color: #856404;
}

.result-invalid {
    background: #f8d7da;
    color: #721c24;
}
</style>

<script>
jQuery(document).ready(function($) {
    let isChecking = false;
    
    // Select all checkbox
    $('#select-all').change(function() {
        $('input[name="channel_ids[]"]').prop('checked', this.checked);
    });
    
    // Check all channels
    $('#check-all-btn').click(function() {
        if (isChecking) return;
        
        $('input[name="channel_ids[]"]').prop('checked', true);
        checkSelectedChannels();
    });
    
    // Check selected channels
    $('#check-selected-btn').click(function() {
        if (isChecking) return;
        checkSelectedChannels();
    });
    
    // Check single channel
    $('.check-single').click(function() {
        if (isChecking) return;
        
        const channelId = $(this).data('channel-id');
        checkChannels([channelId]);
    });
    
    function checkSelectedChannels() {
        const selectedIds = [];
        $('input[name="channel_ids[]"]:checked').each(function() {
            selectedIds.push(parseInt($(this).val()));
        });
        
        if (selectedIds.length === 0) {
            alert('Please select at least one channel to check.');
            return;
        }
        
        checkChannels(selectedIds);
    }
    
    function checkChannels(channelIds) {
        isChecking = true;
        
        // Show progress
        $('#check-progress').show();
        $('#progress-bar').css('width', '0%');
        $('#progress-text').text('Starting check...');
        
        // Disable buttons
        $('.button').prop('disabled', true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'doo_livetv_check_channels',
                channel_ids: channelIds,
                nonce: $('input[name="nonce"]').val()
            },
            success: function(response) {
                if (response.success) {
                    displayResults(response.data);
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                alert('AJAX error occurred');
            },
            complete: function() {
                isChecking = false;
                $('.button').prop('disabled', false);
                $('#check-progress').hide();
            }
        });
    }
    
    function displayResults(results) {
        results.forEach(function(result) {
            const row = $('#channel-' + result.id);
            const statusCell = row.find('.status-badge');
            const lastCheckCell = row.find('.last-check');
            
            // Update status
            statusCell.removeClass('status-active status-inactive');
            if (result.status === 'working') {
                statusCell.addClass('status-active').text('✅ Active');
            } else {
                statusCell.addClass('status-inactive').text('❌ Inactive');
            }
            
            // Update last check
            lastCheckCell.text('Just now');
            
            // Add result message
            let existingResult = row.find('.check-result');
            if (existingResult.length) {
                existingResult.remove();
            }
            
            const resultClass = 'result-' + result.status;
            const resultHtml = `<div class="check-result ${resultClass}">
                ${result.message} (${result.response_time}ms)
            </div>`;
            
            row.find('td:nth-child(6)').append(resultHtml);
        });
        
        // Update overview
        location.reload();
    }
});
</script>
