/**
 * Advanced Focus Management System for Android TV
 * Provides intelligent focus navigation and management
 */

(function($) {
    'use strict';

    var FocusManager = {
        focusMap: new Map(),
        focusHistory: [],
        currentFocusGroup: 'main',
        focusGroups: {
            main: {
                elements: [],
                defaultFocus: 0,
                wrap: true,
                remember: true
            },
            player: {
                elements: [],
                defaultFocus: 0,
                wrap: false,
                remember: false
            },
            settings: {
                elements: [],
                defaultFocus: 0,
                wrap: true,
                remember: false
            },
            search: {
                elements: [],
                defaultFocus: 0,
                wrap: false,
                remember: false
            }
        },
        
        init: function() {
            this.setupFocusGroups();
            this.bindEvents();
            this.startFocusTracking();
        },
        
        setupFocusGroups: function() {
            this.scanForFocusableElements();
            this.buildFocusMap();
            this.setInitialFocus();
        },
        
        scanForFocusableElements: function() {
            var self = this;
            
            // Main content elements
            this.focusGroups.main.elements = $('.item a, .page-link, .category-tab, .btn:not(.control-btn)').get();
            
            // Player controls
            this.focusGroups.player.elements = $('.control-btn, .quality-option').get();
            
            // Settings panel
            this.focusGroups.settings.elements = $('#tv-settings .tv-focusable').get();
            
            // Search form
            this.focusGroups.search.elements = $('.search-form .tv-focusable').get();
            
            // Add focus group attributes
            Object.keys(this.focusGroups).forEach(function(groupName) {
                self.focusGroups[groupName].elements.forEach(function(element, index) {
                    $(element).attr('data-focus-group', groupName)
                             .attr('data-focus-index', index);
                });
            });
        },
        
        buildFocusMap: function() {
            var self = this;
            
            Object.keys(this.focusGroups).forEach(function(groupName) {
                var group = self.focusGroups[groupName];
                var map = [];
                
                group.elements.forEach(function(element, index) {
                    var rect = element.getBoundingClientRect();
                    var neighbors = self.findNeighbors(element, group.elements);
                    
                    map[index] = {
                        element: element,
                        rect: rect,
                        neighbors: neighbors,
                        group: groupName
                    };
                });
                
                self.focusMap.set(groupName, map);
            });
        },
        
        findNeighbors: function(currentElement, elements) {
            var currentRect = currentElement.getBoundingClientRect();
            var neighbors = { up: null, down: null, left: null, right: null };
            
            elements.forEach(function(element) {
                if (element === currentElement) return;
                
                var rect = element.getBoundingClientRect();
                var direction = null;
                
                // Determine direction
                if (rect.bottom <= currentRect.top && 
                    Math.abs(rect.left - currentRect.left) < currentRect.width) {
                    direction = 'up';
                } else if (rect.top >= currentRect.bottom && 
                          Math.abs(rect.left - currentRect.left) < currentRect.width) {
                    direction = 'down';
                } else if (rect.right <= currentRect.left && 
                          Math.abs(rect.top - currentRect.top) < currentRect.height) {
                    direction = 'left';
                } else if (rect.left >= currentRect.right && 
                          Math.abs(rect.top - currentRect.top) < currentRect.height) {
                    direction = 'right';
                }
                
                if (direction) {
                    var distance = Math.sqrt(
                        Math.pow(rect.left - currentRect.left, 2) + 
                        Math.pow(rect.top - currentRect.top, 2)
                    );
                    
                    if (!neighbors[direction] || distance < neighbors[direction].distance) {
                        neighbors[direction] = {
                            element: element,
                            distance: distance
                        };
                    }
                }
            });
            
            return neighbors;
        },
        
        setInitialFocus: function() {
            var group = this.focusGroups[this.currentFocusGroup];
            if (group.elements.length > 0) {
                this.setFocus(this.currentFocusGroup, group.defaultFocus);
            }
        },
        
        setFocus: function(groupName, index) {
            var group = this.focusGroups[groupName];
            if (!group || index < 0 || index >= group.elements.length) return false;
            
            // Remove previous focus
            $('.tv-focused').removeClass('tv-focused');
            
            // Set new focus
            var element = $(group.elements[index]);
            element.addClass('tv-focused');
            
            // Update current group and index
            this.currentFocusGroup = groupName;
            group.currentIndex = index;
            
            // Add to history
            this.addToFocusHistory(groupName, index);
            
            // Scroll into view
            this.scrollToElement(element[0]);
            
            // Trigger focus event
            this.triggerFocusEvent(element[0]);
            
            return true;
        },
        
        navigate: function(direction) {
            var group = this.focusGroups[this.currentFocusGroup];
            if (!group || group.elements.length === 0) return false;
            
            var currentIndex = group.currentIndex || 0;
            var map = this.focusMap.get(this.currentFocusGroup);
            var currentItem = map[currentIndex];
            
            if (!currentItem) return false;
            
            var neighbor = currentItem.neighbors[direction];
            
            if (neighbor) {
                // Find index of neighbor element
                var neighborIndex = group.elements.indexOf(neighbor.element);
                if (neighborIndex !== -1) {
                    return this.setFocus(this.currentFocusGroup, neighborIndex);
                }
            } else if (group.wrap) {
                // Handle wrapping
                return this.handleWrapping(direction, currentIndex, group);
            } else {
                // Try to navigate to adjacent focus group
                return this.navigateToAdjacentGroup(direction);
            }
            
            return false;
        },
        
        handleWrapping: function(direction, currentIndex, group) {
            var newIndex = currentIndex;
            
            switch(direction) {
                case 'up':
                    newIndex = group.elements.length - 1;
                    break;
                case 'down':
                    newIndex = 0;
                    break;
                case 'left':
                    newIndex = currentIndex > 0 ? currentIndex - 1 : group.elements.length - 1;
                    break;
                case 'right':
                    newIndex = currentIndex < group.elements.length - 1 ? currentIndex + 1 : 0;
                    break;
            }
            
            return this.setFocus(this.currentFocusGroup, newIndex);
        },
        
        navigateToAdjacentGroup: function(direction) {
            // Define group relationships
            var groupRelations = {
                main: {
                    up: 'search',
                    down: 'player'
                },
                search: {
                    down: 'main'
                },
                player: {
                    up: 'main'
                }
            };
            
            var currentGroup = this.currentFocusGroup;
            var relations = groupRelations[currentGroup];
            
            if (relations && relations[direction]) {
                var targetGroup = relations[direction];
                var group = this.focusGroups[targetGroup];
                
                if (group.elements.length > 0) {
                    var targetIndex = group.remember && group.lastIndex !== undefined ? 
                                    group.lastIndex : group.defaultFocus;
                    return this.setFocus(targetGroup, targetIndex);
                }
            }
            
            return false;
        },
        
        switchToGroup: function(groupName, index) {
            if (!this.focusGroups[groupName]) return false;
            
            // Remember current position if group supports it
            var currentGroup = this.focusGroups[this.currentFocusGroup];
            if (currentGroup.remember) {
                currentGroup.lastIndex = currentGroup.currentIndex;
            }
            
            // Switch to new group
            var targetIndex = index !== undefined ? index : 
                            this.focusGroups[groupName].defaultFocus;
            
            return this.setFocus(groupName, targetIndex);
        },
        
        addToFocusHistory: function(groupName, index) {
            this.focusHistory.push({
                group: groupName,
                index: index,
                timestamp: Date.now()
            });
            
            // Keep only last 50 items
            if (this.focusHistory.length > 50) {
                this.focusHistory = this.focusHistory.slice(-50);
            }
        },
        
        goBack: function() {
            if (this.focusHistory.length > 1) {
                // Remove current position
                this.focusHistory.pop();
                
                // Get previous position
                var previous = this.focusHistory[this.focusHistory.length - 1];
                if (previous) {
                    return this.setFocus(previous.group, previous.index);
                }
            }
            
            return false;
        },
        
        scrollToElement: function(element) {
            if (!element || !element.scrollIntoView) return;
            
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
            });
        },
        
        triggerFocusEvent: function(element) {
            // Trigger custom focus event
            $(element).trigger('tv-focus');
            
            // Play focus sound if enabled
            if (window.TVSettings && window.TVSettings.settings.focusSound) {
                this.playFocusSound();
            }
        },
        
        playFocusSound: function() {
            // Create a subtle focus sound
            try {
                var audioContext = new (window.AudioContext || window.webkitAudioContext)();
                var oscillator = audioContext.createOscillator();
                var gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            } catch (e) {
                // Ignore audio errors
            }
        },
        
        bindEvents: function() {
            var self = this;
            
            // Handle focus group changes
            $(document).on('tv-focus-group-change', function(e, groupName, index) {
                self.switchToGroup(groupName, index);
            });
            
            // Handle element visibility changes
            $(window).on('resize scroll', function() {
                // Debounce the rebuild
                clearTimeout(self.rebuildTimeout);
                self.rebuildTimeout = setTimeout(function() {
                    self.rebuildFocusMap();
                }, 250);
            });
            
            // Handle dynamic content changes
            $(document).on('DOMNodeInserted DOMNodeRemoved', function() {
                clearTimeout(self.scanTimeout);
                self.scanTimeout = setTimeout(function() {
                    self.scanForFocusableElements();
                    self.buildFocusMap();
                }, 100);
            });
        },
        
        rebuildFocusMap: function() {
            this.buildFocusMap();
        },
        
        startFocusTracking: function() {
            var self = this;
            
            // Track focus changes every 100ms
            setInterval(function() {
                var focused = $('.tv-focused');
                if (focused.length === 0 && self.focusGroups[self.currentFocusGroup].elements.length > 0) {
                    // Restore focus if lost
                    self.setInitialFocus();
                }
            }, 100);
        },
        
        // Public API methods
        getCurrentFocus: function() {
            var group = this.focusGroups[this.currentFocusGroup];
            return {
                group: this.currentFocusGroup,
                index: group.currentIndex || 0,
                element: group.elements[group.currentIndex || 0]
            };
        },
        
        getFocusableElements: function(groupName) {
            return this.focusGroups[groupName] ? this.focusGroups[groupName].elements : [];
        },
        
        refresh: function() {
            this.setupFocusGroups();
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if (window.AndroidTV && window.AndroidTV.isAndroidTV) {
            FocusManager.init();
        }
    });

    // Make FocusManager globally available
    window.FocusManager = FocusManager;

})(jQuery);
