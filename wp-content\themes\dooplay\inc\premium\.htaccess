# DeshiFlix Premium System - Apache Configuration
# Prevent direct access to PHP files

<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# Allow access to specific files that need to be accessible
<Files "debug-premium.php">
    Order Allow,Deny
    Allow from all
</Files>

# Enable output buffering for PHP
php_flag output_buffering On
php_value output_buffering 4096

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Prevent access to sensitive files
<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.sql">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.bak">
    Order Deny,Allow
    Deny from all
</Files>
