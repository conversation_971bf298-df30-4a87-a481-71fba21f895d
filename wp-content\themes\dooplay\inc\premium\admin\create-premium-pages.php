<?php
/**
 * Create Premium System Pages
 * 
 * @package DeshiFlix Premium
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025
 */

if (!defined('ABSPATH')) {
    exit;
}

class DeshiFlix_Premium_Pages {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_init', array($this, 'maybe_create_pages'));
        add_action('admin_menu', array($this, 'add_pages_menu'));
    }
    
    /**
     * Add pages management menu
     */
    public function add_pages_menu() {
        add_submenu_page(
            'deshiflix-premium',
            'Premium Pages',
            'Premium Pages',
            'manage_options',
            'premium-pages',
            array($this, 'pages_management_page')
        );
    }
    
    /**
     * Pages management page
     */
    public function pages_management_page() {
        if (isset($_POST['create_pages'])) {
            $this->create_all_premium_pages();
            echo '<div class="notice notice-success"><p>Premium pages created successfully!</p></div>';
        }
        
        $pages_status = $this->check_pages_status();
        ?>
        <div class="wrap">
            <h1>Premium System Pages</h1>
            
            <div class="premium-pages-status">
                <h2>Pages Status</h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Page</th>
                            <th>Status</th>
                            <th>URL</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pages_status as $page_key => $page_info): ?>
                        <tr>
                            <td><strong><?php echo esc_html($page_info['title']); ?></strong></td>
                            <td>
                                <?php if ($page_info['exists']): ?>
                                    <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
                                    <span style="color: green;">Created</span>
                                <?php else: ?>
                                    <span class="dashicons dashicons-dismiss" style="color: red;"></span>
                                    <span style="color: red;">Missing</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($page_info['exists']): ?>
                                    <a href="<?php echo esc_url($page_info['url']); ?>" target="_blank">
                                        <?php echo esc_html($page_info['slug']); ?>
                                    </a>
                                <?php else: ?>
                                    <code><?php echo esc_html($page_info['slug']); ?></code>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($page_info['exists']): ?>
                                    <a href="<?php echo admin_url('post.php?post=' . $page_info['id'] . '&action=edit'); ?>" class="button button-small">
                                        Edit
                                    </a>
                                <?php else: ?>
                                    <button class="button button-small" onclick="createSinglePage('<?php echo $page_key; ?>')">
                                        Create
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="premium-pages-actions">
                <h2>Bulk Actions</h2>
                <form method="post">
                    <p>
                        <input type="submit" name="create_pages" class="button button-primary" 
                               value="Create All Missing Pages" 
                               onclick="return confirm('This will create all missing premium pages. Continue?')">
                    </p>
                    <p class="description">
                        This will create all necessary pages for the premium system including plans, dashboard, login, and registration pages.
                    </p>
                </form>
            </div>
        </div>
        
        <script>
        function createSinglePage(pageKey) {
            if (confirm('Create this page?')) {
                // AJAX call to create single page
                jQuery.post(ajaxurl, {
                    action: 'create_premium_page',
                    page_key: pageKey,
                    nonce: '<?php echo wp_create_nonce('create_premium_page'); ?>'
                }, function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Error creating page: ' + response.data);
                    }
                });
            }
        }
        </script>
        <?php
    }
    
    /**
     * Check if pages should be created automatically
     */
    public function maybe_create_pages() {
        $pages_created = get_option('deshiflix_premium_pages_created', false);
        
        if (!$pages_created) {
            $this->create_all_premium_pages();
            update_option('deshiflix_premium_pages_created', true);
        }
    }
    
    /**
     * Check status of all premium pages
     */
    private function check_pages_status() {
        $pages = $this->get_premium_pages_config();
        $status = array();
        
        foreach ($pages as $page_key => $page_config) {
            $page = get_page_by_path($page_config['slug']);
            
            $status[$page_key] = array(
                'title' => $page_config['title'],
                'slug' => $page_config['slug'],
                'exists' => !empty($page),
                'id' => $page ? $page->ID : null,
                'url' => $page ? get_permalink($page->ID) : home_url('/' . $page_config['slug'] . '/')
            );
        }
        
        return $status;
    }
    
    /**
     * Get premium pages configuration
     */
    private function get_premium_pages_config() {
        return array(
            'premium_plans' => array(
                'title' => 'Premium Plans',
                'slug' => 'premium-plans',
                'content' => '[premium_plans]',
                'template' => 'page-premium-plans.php'
            ),
            'premium_dashboard' => array(
                'title' => 'Premium Dashboard',
                'slug' => 'premium-dashboard',
                'content' => '[premium_dashboard]',
                'template' => 'page-premium-dashboard.php'
            ),
            'premium_login' => array(
                'title' => 'Login',
                'slug' => 'login',
                'content' => '[premium_login_form]',
                'template' => 'page-login.php'
            ),
            'premium_register' => array(
                'title' => 'Register',
                'slug' => 'register',
                'content' => '[premium_register_form]',
                'template' => 'page-register.php'
            ),
            'premium_account' => array(
                'title' => 'My Account',
                'slug' => 'my-account',
                'content' => '[premium_account_page]',
                'template' => 'page-account.php'
            ),
            'premium_referrals' => array(
                'title' => 'Referral Program',
                'slug' => 'referral-program',
                'content' => '[referral_dashboard]',
                'template' => 'page-referrals.php'
            ),
            'premium_help' => array(
                'title' => 'Help & Support',
                'slug' => 'help-support',
                'content' => $this->get_help_content(),
                'template' => 'page-help.php'
            ),
            'premium_terms' => array(
                'title' => 'Terms of Service',
                'slug' => 'terms-of-service',
                'content' => $this->get_terms_content(),
                'template' => 'page-terms.php'
            ),
            'premium_privacy' => array(
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'content' => $this->get_privacy_content(),
                'template' => 'page-privacy.php'
            )
        );
    }
    
    /**
     * Create all premium pages
     */
    public function create_all_premium_pages() {
        $pages = $this->get_premium_pages_config();
        
        foreach ($pages as $page_key => $page_config) {
            $this->create_page($page_config);
        }
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create a single page
     */
    private function create_page($config) {
        // Check if page already exists
        $existing_page = get_page_by_path($config['slug']);
        if ($existing_page) {
            return $existing_page->ID;
        }
        
        $page_data = array(
            'post_title' => $config['title'],
            'post_content' => $config['content'],
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => $config['slug'],
            'post_author' => 1,
            'comment_status' => 'closed',
            'ping_status' => 'closed'
        );
        
        $page_id = wp_insert_post($page_data);
        
        if ($page_id && !is_wp_error($page_id)) {
            // Set page template if specified
            if (isset($config['template'])) {
                update_post_meta($page_id, '_wp_page_template', $config['template']);
            }
            
            // Set as premium page
            update_post_meta($page_id, '_is_premium_page', true);
            
            return $page_id;
        }
        
        return false;
    }
    
    /**
     * Get help content
     */
    private function get_help_content() {
        return '
        <h2>Frequently Asked Questions</h2>
        
        <h3>How do I subscribe to premium?</h3>
        <p>Visit our <a href="/premium-plans/">Premium Plans</a> page and choose the plan that suits you best. You can pay using bKash, Nagad, Rocket, or credit/debit cards.</p>
        
        <h3>What payment methods do you accept?</h3>
        <p>We accept:</p>
        <ul>
            <li>bKash</li>
            <li>Nagad</li>
            <li>Rocket</li>
            <li>Credit/Debit Cards (Visa, MasterCard)</li>
            <li>Mobile Banking</li>
        </ul>
        
        <h3>Can I cancel my subscription?</h3>
        <p>Yes, you can cancel your subscription at any time from your <a href="/premium-dashboard/">Premium Dashboard</a>. Your access will continue until the end of your billing period.</p>
        
        <h3>How many devices can I use?</h3>
        <p>The number of devices depends on your plan:</p>
        <ul>
            <li>Basic Plan: 2 devices</li>
            <li>Standard Plan: 4 devices</li>
            <li>Premium Plan: 6 devices</li>
        </ul>
        
        <h3>Can I download content for offline viewing?</h3>
        <p>Yes, premium subscribers can download movies and shows for offline viewing. Download limits vary by plan.</p>
        
        <h3>Need more help?</h3>
        <p>Contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a> or call +880-1234-567890.</p>
        ';
    }
    
    /**
     * Get terms content
     */
    private function get_terms_content() {
        return '
        <h2>Terms of Service</h2>
        <p><em>Last updated: ' . date('F j, Y') . '</em></p>
        
        <h3>1. Acceptance of Terms</h3>
        <p>By accessing and using DeshiFlix, you accept and agree to be bound by the terms and provision of this agreement.</p>
        
        <h3>2. Subscription Services</h3>
        <p>DeshiFlix offers subscription-based access to premium content. Subscription fees are charged in advance and are non-refundable.</p>
        
        <h3>3. User Accounts</h3>
        <p>You are responsible for maintaining the confidentiality of your account and password. You agree to accept responsibility for all activities that occur under your account.</p>
        
        <h3>4. Content Usage</h3>
        <p>Content is provided for personal, non-commercial use only. You may not redistribute, broadcast, or publicly perform any content.</p>
        
        <h3>5. Payment Terms</h3>
        <p>Subscription fees are billed in advance. We accept various payment methods including mobile banking and credit cards.</p>
        
        <h3>6. Cancellation</h3>
        <p>You may cancel your subscription at any time. Cancellation will take effect at the end of your current billing period.</p>
        
        <h3>7. Prohibited Uses</h3>
        <p>You may not use our service for any illegal or unauthorized purpose. You must not violate any laws in your jurisdiction.</p>
        
        <h3>8. Contact Information</h3>
        <p>Questions about the Terms of Service should be sent to <NAME_EMAIL>.</p>
        ';
    }
    
    /**
     * Get privacy content
     */
    private function get_privacy_content() {
        return '
        <h2>Privacy Policy</h2>
        <p><em>Last updated: ' . date('F j, Y') . '</em></p>
        
        <h3>1. Information We Collect</h3>
        <p>We collect information you provide directly to us, such as when you create an account, subscribe to our service, or contact us for support.</p>
        
        <h3>2. How We Use Your Information</h3>
        <p>We use the information we collect to:</p>
        <ul>
            <li>Provide, maintain, and improve our services</li>
            <li>Process transactions and send related information</li>
            <li>Send technical notices and support messages</li>
            <li>Communicate with you about products, services, and events</li>
        </ul>
        
        <h3>3. Information Sharing</h3>
        <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>
        
        <h3>4. Data Security</h3>
        <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
        
        <h3>5. Cookies</h3>
        <p>We use cookies to enhance your experience, gather general visitor information, and track visits to our website.</p>
        
        <h3>6. Your Rights</h3>
        <p>You have the right to access, update, or delete your personal information. You can do this through your account settings or by contacting us.</p>
        
        <h3>7. Changes to This Policy</h3>
        <p>We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page.</p>
        
        <h3>8. Contact Us</h3>
        <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.</p>
        ';
    }
}

// Initialize premium pages
DeshiFlix_Premium_Pages::get_instance();
