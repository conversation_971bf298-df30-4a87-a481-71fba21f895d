"use strict";(self.webpackChunkpayment_gateway_bkash_for_wc=self.webpackChunkpayment_gateway_bkash_for_wc||[]).push([[524],{37:(A,e,t)=>{t.d(e,{A:()=>c});var n,r=t(6540);function a(A){var e,t=A.field,n=A.id,a=A.handleChange,o=A.value,c=void 0===o?"":o,l=A.section_id,s=A.allSettings;return!(null!=t&&t.show_if&&!t.show_if.map((function(A){var t,n,r,a;return u(A,null!==(n=e=null==s||null===(t=s[l])||void 0===t?void 0:t[A.key])&&void 0!==n&&n.value?null===(r=e)||void 0===r?void 0:r.value:null===(a=e)||void 0===a?void 0:a.default)})).every((function(A){return!0===A})))&&(c=""===c?null==t?void 0:t.default:c,r.createElement(r.Fragment,null,r.createElement("p",{className:"label"},null==t?void 0:t.title),function(A){var e=A.type;switch(e){case"text":case"password":return r.createElement("input",{type:e,className:"widefat",value:c,onChange:function(A){return a(A.target.value,l,n)}});case"checkbox":return r.createElement(r.Fragment,null,r.createElement("input",{type:"checkbox",className:"widefat",id:n,value:c,onChange:function(A){return a(A.target.value,l,n)}}),r.createElement("label",{htmlFor:n},A.title));case"select":var t=Object.entries(A.options);return r.createElement(r.Fragment,null,r.createElement("select",{className:"widefat",value:c,onChange:function(A){return a(A.target.value,l,n)}},t.map((function(A,e){return r.createElement("option",{key:e,value:A[0]},A[1])}))));case"textarea":return r.createElement(r.Fragment,null,r.createElement("textarea",{id:n,cols:"30",rows:"10",className:"widefat",onChange:function(A){return a(A.target.value,l,n)},value:c||(null==A?void 0:A.default)}));default:return""}}(t),t.description?r.createElement("p",{className:"help-text"},t.description):""))}function u(A,e){return"equal"===(null==A?void 0:A.condition)&&e===A.value}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var o=a;const c=o;var l,s;(l="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(l.register(a,"Fields","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/fields.js"),l.register(u,"is_matched","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/fields.js"),l.register(o,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/fields.js")),(s="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&s(A)},775:(A,e,t)=>{t.d(e,{A:()=>p});var n,r=t(6540),a=t(6645),u=t(9508),o=t.n(u),c=t(3546),l=t(5898),s=t(6474),d=t(4040);function i(A){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},i(A)}function f(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(A);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,n)}return t}function m(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?f(Object(t),!0).forEach((function(e){v(A,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(t)):f(Object(t)).forEach((function(e){Object.defineProperty(A,e,Object.getOwnPropertyDescriptor(t,e))}))}return A}function v(A,e,t){return(e=function(A){var e=function(A){if("object"!=i(A)||!A)return A;var e=A[Symbol.toPrimitive];if(void 0!==e){var t=e.call(A,"string");if("object"!=i(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(A)}(A);return"symbol"==i(e)?e:e+""}(e))in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}function b(A,e){return function(A){if(Array.isArray(A))return A}(A)||function(A,e){var t=null==A?null:"undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(null!=t){var n,r,a,u,o=[],c=!0,l=!1;try{if(a=(t=t.call(A)).next,0===e){if(Object(t)!==t)return;c=!1}else for(;!(c=(n=a.call(t)).done)&&(o.push(n.value),o.length!==e);c=!0);}catch(A){l=!0,r=A}finally{try{if(!c&&null!=t.return&&(u=t.return(),Object(u)!==u))return}finally{if(l)throw r}}return o}}(A,e)||function(A,e){if(A){if("string"==typeof A)return y(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?y(A,e):void 0}}(A,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}function X(A){var e,t=A.afterComplete,n=b((0,r.useState)(""),2),u=n[0],i=n[1],f=b((0,r.useState)(""),2),y=f[0],X=f[1],z=b((0,r.useState)(""),2),p=z[0],h=z[1],O=b((0,r.useState)(""),2),L=O[0],j=O[1],P=b((0,r.useState)({}),2),w=P[0],Z=P[1],H=b((0,r.useState)(!1),2),k=H[0],M=H[1],g=b((0,r.useState)({}),2),W=g[0],N=g[1],x=b((0,r.useState)(!1),2),q=x[0],I=x[1],T=b((0,r.useState)(!1),2),V=T[0],E=T[1],C=b((0,r.useState)({getToken:!1,createPayment:!1,executePayment:!1,queryPayment:!1,searchPayment:!1,duplicateTransaction:!1,exceedPinLimit:!1,refundPayment:!1,refundStatusApi:!1}),2),D=C[0],G=C[1],J=function(A){G(m(m({},D),{},v({},A,!0)))},R=function(A){h(A.data.trxID),J("executePayment")},Y=function(A){A&&(M(!0),J("createPayment"))},Q=function(A){if(A){J("searchPayment");var e=d.n.v1.createPayment+"?amount="+L;s.oR.warn("Duplicate Transaction Test",{position:"bottom-center",autoClose:!1,hideProgressBar:!0,closeOnClick:!0,pauseOnHover:!1,draggable:!1}),o()({path:e}).then((function(A){Z(A.data),N(A.data),i(A.data.paymentID),l.A.initBkash(A.data.merchantInvoiceNumber,A.data.amount,A.data,K)})).catch((function(A){}))}},K=function(A){var e;A&&(i((function(A){e=d.n.v1.executePayment+A})),o()({path:e}).then((function(A){})).catch((function(A){E(!0),J("duplicateTransaction"),s.oR.dismiss(),F()})))},F=function(){s.oR.warn("Exceed Pin Limit. Please enter wrong pin.",{position:"bottom-center",autoClose:!1,hideProgressBar:!0,closeOnClick:!0,pauseOnHover:!1,draggable:!1}),o()({path:d.n.v1.createPayment}).then((function(A){Z(A.data),l.A.initBkash(A.data.merchantInvoiceNumber,A.data.amount,A.data,B,!0)})).catch((function(A){}))},B=function(A){A&&(s.oR.dismiss(),J("exceedPinLimit"),I(!0))},S=function(A){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(q){var n=d.n.v1.docRefundPayment+y+"?trx_id="+p+"&amount="+L+"&title="+A;return e?(n=d.n.v1.docRefundPayment+y+"?trx_id="+p+"&title="+A,r.createElement(c.A,{path:n,callback:function(){s.oR.success((0,a.__)("Doc Generation Done.","dc-bkash")),t()}})):r.createElement(c.A,{path:n,callback:function(){J("refundPayment")}})}};return r.createElement("div",{className:"generator-container-area",id:"doc-details"},r.createElement("h2",null,(0,a.__)("API Request/Response","dc-bkash")),r.createElement(c.A,{path:d.n.v1.getToken,callback:function(){J("getToken")}}),D.getToken&&r.createElement(c.A,{path:d.n.v1.createPayment,callback:function(A){Z(A.data),i(A.data.paymentID),X(A.data.paymentID),j(A.data.amount),l.A.initBkash(A.data.merchantInvoiceNumber,A.data.amount,A.data,Y)}}),D.createPayment&&function(){if(k){var A=d.n.v1.executePayment+u;return r.createElement("div",null,r.createElement(c.A,{path:A,callback:R}))}}(),D.executePayment&&(e=d.n.v1.queryPayment+u,r.createElement("div",null,r.createElement(c.A,{path:e,callback:function(){J("queryPayment")}}))),D.queryPayment&&function(){if(p){var A=d.n.v1.docSearchPayment+p;return r.createElement(c.A,{path:A,callback:Q})}}(),function(){if(V)return r.createElement("div",null,r.createElement("h3",null,(0,a.__)("Error Message Implimentation","dc-bkash")),r.createElement("p",{className:"strong"},(0,a.__)("Case #1","dc-bkash")),r.createElement("p",{className:"strong"},(0,a.__)("Invoice Number: ","dc-bkash")," ",W.merchantInvoiceNumber),r.createElement("p",{className:"strong"},(0,a.__)("Time of Transaction: ","dc-bkash"),W.createTime),r.createElement("p",{className:"strong"},(0,a.__)("Screenshot","dc-bkash")),r.createElement("img",{className:"img-full",src:"data:image/png;base64,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",alt:"error-screenshot"}))}(),function(){if(q)return r.createElement("div",null,r.createElement("p",{className:"strong"},(0,a.__)("Case #2","dc-bkash")),r.createElement("p",{className:"strong"},(0,a.__)("Invoice Number: ","dc-bkash")," ",w.merchantInvoiceNumber),r.createElement("p",{className:"strong"},(0,a.__)("Time of Transaction: ","dc-bkash"),w.createTime),r.createElement("p",{className:"strong"},(0,a.__)("Screenshot","dc-bkash")),r.createElement("img",{className:"img-full",src:"data:image/png;base64,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",alt:"error-screenshot"}))}(),D.exceedPinLimit&&S("Refund API"),D.refundPayment&&S("Refund Status API",!0))}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),("undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default.signature:function(A){return A})(X,"useState{[ paymentID, setPaymentID ]('')}\nuseState{[ firstPaymentID, setFirstPaymentID ]('')}\nuseState{[ transactionID, setTransactionID ]('')}\nuseState{[ amount, setAmount ]('')}\nuseState{[ createPaymentData, setCreatePaymentData ]({})}\nuseState{[ validatePin, setValidatePin ](false)}\nuseState{[ duplicateTransactionData, setDuplicateTransactionData ]({})}\nuseState{[ exceedPinLimit, setExceedPinLimit ](false)}\nuseState{[ duplicateTransactionExecuteFailed, setDuplicateTransactionExecuteFailed ](false)}\nuseState{[ docSteps, setDocSteps ]({\n\t\tgetToken: false,\n\t\tcreatePayment: false,\n\t\texecutePayment: false,\n\t\tqueryPayment: false,\n\t\tsearchPayment: false,\n\t\tduplicateTransaction: false,\n\t\texceedPinLimit: false,\n\t\trefundPayment: false,\n\t\trefundStatusApi: false\n\t})}");var z=X;const p=z;var h,O;(h="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(h.register(X,"DocDataContainer","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/Doc/doc-container.js"),h.register(z,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/Doc/doc-container.js")),(O="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&O(A)},1224:(A,e,t)=>{t.d(e,{A:()=>s});var n,r=t(6540),a=t(6645),u=t(8012),o=t(4625);A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var c=function(){return r.createElement("div",{className:"dokan_admin_settings_container"},r.createElement("div",{className:"title-section"},r.createElement("h2",null,(0,a.__)("Refund","dc-bkash"))),r.createElement("div",{className:"generic-container refund-container"},"1"===dc_bkash_admin.all_credentials_filled?r.createElement(u.A,null):r.createElement("p",null,(0,a.__)("Before refund, you must have to add API keys in ","dc-bkash"),r.createElement(o.N_,{to:"/settings"},"Settings"),".")))},l=c;const s=l;var d,i;(d="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(d.register(c,"RefundContainer","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/refund-container.js"),d.register(l,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/refund-container.js")),(i="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&i(A)},3546:(A,e,t)=>{t.d(e,{A:()=>m});var n,r=t(6540),a=t(6645),u=t(9508),o=t.n(u),c=t(851),l=t(5359);function s(A,e){return function(A){if(Array.isArray(A))return A}(A)||function(A,e){var t=null==A?null:"undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(null!=t){var n,r,a,u,o=[],c=!0,l=!1;try{if(a=(t=t.call(A)).next,0===e){if(Object(t)!==t)return;c=!1}else for(;!(c=(n=a.call(t)).done)&&(o.push(n.value),o.length!==e);c=!0);}catch(A){l=!0,r=A}finally{try{if(!c&&null!=t.return&&(u=t.return(),Object(u)!==u))return}finally{if(l)throw r}}return o}}(A,e)||function(A,e){if(A){if("string"==typeof A)return d(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?d(A,e):void 0}}(A,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}function i(A){var e=A.path,t=A.callback,n=void 0!==t&&t,u=s((0,r.useState)(!0),2),d=u[0],i=u[1],f=s((0,r.useState)(""),2),m=f[0],v=f[1],b=s((0,r.useState)({}),2),y=b[0],X=b[1],z=s((0,r.useState)({}),2),p=z[0],h=z[1],O=s((0,r.useState)(""),2),L=O[0],j=O[1];return(0,r.useEffect)((function(){o()({path:e}).then((function(A){i(!1),v(A.title),X(A.data),h(A.request_params),j(A.request_url),n&&n(A)})).catch((function(A){}))}),[]),r.createElement("div",{className:"grant-token-container"},d&&r.createElement(c.A,null),!d&&r.createElement(r.Fragment,null,r.createElement("p",{className:"strong"},(0,a.__)("API Title: ","dc-bkash"),m),r.createElement("p",{className:"strong"},(0,a.__)("API URL: ","dc-bkash"),r.createElement("a",{href:L,target:"_blank"},L)),r.createElement("p",{className:"strong"},"Request Body:"),r.createElement("pre",null,(0,l.I)(p)),r.createElement("p",{className:"strong"},"API Response:"),r.createElement("pre",null,(0,l.I)(y))))}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),("undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default.signature:function(A){return A})(i,"useState{[ loading, setLoading ](true)}\nuseState{[ apiTitle, setApiTitle ]('')}\nuseState{[ responseData, setResponseData ]({})}\nuseState{[ requestParamsData, setRequestParamsData ]({})}\nuseState{[ requestUrl, setRequestUrl ]('')}\nuseEffect{}");var f=i;const m=f;var v,b;(v="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(v.register(i,"ApiResponse","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/bKash/api-response.js"),v.register(f,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/bKash/api-response.js")),(b="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&b(A)},4001:(A,e,t)=>{t.d(e,{A:()=>X});var n,r=t(6540),a=t(6645),u=t(6399),o=t(9508),c=t.n(o),l=t(6347),s=t(4625),d=t(6474),i=t(4040);function f(A){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},f(A)}function m(A,e){return function(A){if(Array.isArray(A))return A}(A)||function(A,e){var t=null==A?null:"undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(null!=t){var n,r,a,u,o=[],c=!0,l=!1;try{if(a=(t=t.call(A)).next,0===e){if(Object(t)!==t)return;c=!1}else for(;!(c=(n=a.call(t)).done)&&(o.push(n.value),o.length!==e);c=!0);}catch(A){l=!0,r=A}finally{try{if(!c&&null!=t.return&&(u=t.return(),Object(u)!==u))return}finally{if(l)throw r}}return o}}(A,e)||function(A,e){if(A){if("string"==typeof A)return v(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?v(A,e):void 0}}(A,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A);var b=function(){var A=m((0,r.useState)(!1),2),e=A[0],t=A[1],n=m((0,r.useState)(""),2),o=n[0],v=n[1],b=m((0,r.useState)({}),2),y=b[0],X=b[1],z=(0,l.zy)().search;return(0,r.useEffect)((function(){v(z.replace("?trx_id=",""))}),[o]),r.createElement("div",{className:"dokan_admin_settings_container"},r.createElement("div",{className:"title-section"},r.createElement("h2",null,(0,a.__)("Search Transaction ","dc-bkash"))),r.createElement("div",{className:"generic-container search-transaction-container"},"1"===dc_bkash_admin.all_credentials_filled?r.createElement(r.Fragment,null,r.createElement("div",{className:"search-transaction-container__form"},r.createElement("div",{className:"form-group"},r.createElement("label",null,(0,a.__)("Transaction ID","dc-bkash")),r.createElement("input",{type:"text",className:"form-control",value:o,onChange:function(A){return v(A.target.value)}})),r.createElement(u.A,{type:"submit",isBusy:e,disabled:e||""===o,className:"dc_bkash_save_btn",isPrimary:!0,onClick:function(){return function(){t(!0),X({});var A=i.n.v1.searchTransaction+o;c()({path:A}).then((function(A){t(!1),v(""),X(A)})).catch((function(A){t(!1),"object"!==f(A.message)?d.oR.error(A.data.status+" : "+A.message):d.oR.error(A.message.errorCode+" : "+A.message.errorMessage)}))}()}},e?(0,a.__)("Searching","dc-bkash"):(0,a.__)("Search","dc-bkash"))),0===Object.keys(y).length?"":r.createElement("div",{className:"transaction-deatils"},r.createElement("h3",null,(0,a.__)("Transaction Details","dc-bkash")),r.createElement("table",{className:"table table-bordered border-primary transactions"},r.createElement("thead",null,r.createElement("tr",null,r.createElement("th",{scope:"col"},(0,a.__)("Title","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Data","dc-bkash")))),r.createElement("tbody",null,Object.entries(y).map((function(A,e){var t=m(A,2),n=t[0],a=t[1];return r.createElement("tr",{key:e},r.createElement("td",null,n),r.createElement("td",null,a))})))))):r.createElement("p",null,(0,a.__)("Before search transaction, you must have to add API keys in ","dc-bkash"),r.createElement(s.N_,{to:"/settings"},"Settings"),".")))};("undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default.signature:function(A){return A})(b,"useState{[ isSubmitted, setIsSubmitted ](false)}\nuseState{[ transactionID, setTransactionID ]('')}\nuseState{[ transactionData, setTransactionData ]({})}\nuseLocation{{ search }}\nuseEffect{}",(function(){return[l.zy]}));var y=b;const X=y;var z,p;(z="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(z.register(b,"SearchTransaction","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/search-transaction.js"),z.register(y,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/search-transaction.js")),(p="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&p(A)},4040:(A,e,t)=>{var n;t.d(e,{n:()=>o}),A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var r,a,u="/dc-bkash/v1",o={v1:{settings:u+"/settings",transactions:u+"/transactions",transactionSearch:u+"/transactions/?search=",getToken:u+"/payment/get-token",createPayment:u+"/payment/create-payment",queryPayment:u+"/payment/query-payment/",executePayment:u+"/payment/execute-payment/",docSearchPayment:u+"/payment/search-payment/",docRefundPayment:u+"/payment/refund-payment/",refund:u+"/transactions/refund",searchTransaction:u+"/payment/search-transaction/",upgrade:u+"/upgrade"}};(r="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(r.register(u,"v1","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/constants.js"),r.register(o,"API","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/constants.js")),(a="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&a(A)},4161:(A,e,t)=>{t.d(e,{A:()=>O});var n,r=t(6540),a=t(6645),u=t(6399),o=t(9508),c=t.n(o),l=t(37),s=t(6119),d=t(6474),i=t(4040);function f(A){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},f(A)}function m(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(A);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,n)}return t}function v(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?m(Object(t),!0).forEach((function(e){b(A,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(t)):m(Object(t)).forEach((function(e){Object.defineProperty(A,e,Object.getOwnPropertyDescriptor(t,e))}))}return A}function b(A,e,t){return(e=function(A){var e=function(A){if("object"!=f(A)||!A)return A;var e=A[Symbol.toPrimitive];if(void 0!==e){var t=e.call(A,"string");if("object"!=f(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(A)}(A);return"symbol"==f(e)?e:e+""}(e))in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}function y(A,e){return function(A){if(Array.isArray(A))return A}(A)||function(A,e){var t=null==A?null:"undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(null!=t){var n,r,a,u,o=[],c=!0,l=!1;try{if(a=(t=t.call(A)).next,0===e){if(Object(t)!==t)return;c=!1}else for(;!(c=(n=a.call(t)).done)&&(o.push(n.value),o.length!==e);c=!0);}catch(A){l=!0,r=A}finally{try{if(!c&&null!=t.return&&(u=t.return(),Object(u)!==u))return}finally{if(l)throw r}}return o}}(A,e)||function(A,e){if(A){if("string"==typeof A)return X(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?X(A,e):void 0}}(A,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function X(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A);var z="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default.signature:function(A){return A};function p(){var A=y((0,r.useState)({sections:{},fields:{gateway:{},dokan_integration:{}}}),2),e=A[0],t=A[1],n=y((0,r.useState)({}),2),o=n[0],f=n[1],m=y((0,r.useState)([]),2),X=m[0],z=m[1],p=y((0,r.useState)(!0),2),h=p[0],O=p[1],L=y((0,r.useState)(""),2),j=L[0],P=L[1],w=y((0,r.useState)(!1),2),Z=w[0],H=w[1],k=function(A,n,r){var a,u;t(v(v({},e),{},{fields:v(v({},e.fields),{},b({},n,v(v({},null===(a=e.fields)||void 0===a?void 0:a[n]),{},b({},r,v(v({},null===(u=e.fields)||void 0===u||null===(u=u[n])||void 0===u?void 0:u[r]),{},{value:A})))))})),f(b({},n,v(v({},null==o?void 0:o[n]),{},b({},r,{value:A}))))},M=function(A){var e={};Object.keys(A.fields).map((function(t,n){Object.keys(null==A?void 0:A.fields[t]).map((function(n,r){var a,u,o;e=v(v({},e),{},b({},t,v(v({},null===(a=e)||void 0===a?void 0:a[t]),{},b({},n,{value:(null==A||null===(u=A.fields[t])||void 0===u||null===(u=u[n])||void 0===u?void 0:u.value)||(null==A||null===(o=A.fields[t])||void 0===o||null===(o=o[n])||void 0===o?void 0:o.default)}))))}))})),f(e)};return(0,r.useEffect)((function(){O(!0),c()({path:i.n.v1.settings}).then((function(A){O(!1),t(A),z(A.sections),P("gateway"),M(A)})).catch((function(A){O(!1),d.oR.error(A.data.status+" : "+A.message)}))}),[]),h?r.createElement(s.A,null):r.createElement("div",{className:"dokan_admin_settings_container"},r.createElement("h2",null,(0,a.__)("Settings","dc-bkash")),r.createElement("div",{className:"dokan_admin_settings_area"},r.createElement("div",{className:"admin_settings_sections"},r.createElement("ul",{className:"dokan_admin_settings"},Object.keys(X).map((function(A,e){return r.createElement("li",{key:A,onClick:function(){return P(A)},className:j===A?"active":""},X[A].title)})))),r.createElement("div",{className:"admin_settings_fields"},Object.keys(e.fields).map((function(A,n){if(A===j)return r.createElement("div",{key:n,className:"single_settings_container"},r.createElement("p",{className:"section_title"},X[A].title),Object.keys(null==e?void 0:e.fields[A]).map((function(t,n){var a;return r.createElement("div",{key:n,className:"single_settings_field"},r.createElement(l.A,{field:null==e?void 0:e.fields[A][t],section_id:A,id:t,handleChange:k,value:null==e||null===(a=e.fields[A][t])||void 0===a?void 0:a.value,allSettings:null==e?void 0:e.fields}))})),r.createElement(u.A,{type:"submit",isBusy:Z,disabled:Z,className:"dc_bkash_save_btn",isPrimary:!0,onClick:function(){return H(!0),void c()({path:i.n.v1.settings,method:"POST",data:{data:o}}).then((function(A){H(!1),t(A),z(A.sections),M(A),d.oR.success((0,a.__)("Saved Successfully!","dc-bkash"))})).catch((function(A){H(!1),d.oR.error(A.data.status+" : "+A.message)}))}},Z?(0,a.__)("Saving","dc-bkash"):(0,a.__)("Save","dc-bkash")))})))))}d.oR.configure({position:"top-right",autoClose:5e3,closeOnClick:!1,pauseOnHover:!1,draggable:!1,closeButton:!1,style:{top:"3em"}}),z(p,"useState{[ settings, setSettings ]({\n\t\tsections: {},\n\t\tfields: {\n\t\t\tgateway: {},\n\t\t\tdokan_integration: {}\n\t\t}\n\t})}\nuseState{[ settingsOption, setSettingsOption ]({})}\nuseState{[ sections, setSections ]([])}\nuseState{[ isFetching, setIsFetching ](true)}\nuseState{[ currentTab, setCurrentTab ]('')}\nuseState{[ isSubmitted, setIsSubmitted ](false)}\nuseEffect{}");var h=p;const O=h;var L,j;(L="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(L.register(p,"Settings","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/settings.js"),L.register(h,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/settings.js")),(j="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&j(A)},4967:(A,e,t)=>{t.d(e,{A:()=>y});var n,r=t(6540),a=t(4625),u=t(6347),o=t(4161),c=t(9053),l=t(7805),s=t(1224),d=t(4001);function i(){return i=Object.assign?Object.assign.bind():function(A){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)({}).hasOwnProperty.call(t,n)&&(A[n]=t[n])}return A},i.apply(null,arguments)}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var f=[{path:"/",component:l.A,exact:!0},{path:"/search-transaction",component:d.A},{path:"/refund",component:s.A},{path:"/settings",component:o.A},{path:"/generate-doc",component:c.A}];function m(){return r.createElement(r.Fragment,null,r.createElement(a.I9,null,r.createElement(u.dO,null,f.map((function(A,e){return r.createElement(v,i({key:e},A))})))))}function v(A){return A.exact?r.createElement(u.qh,{path:A.path,exact:!0,component:A.component}):r.createElement(u.qh,{path:A.path,component:A.component})}var b=m;const y=b;var X,z;(X="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(X.register(f,"routes","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/router/index.js"),X.register(m,"Routerview","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/router/index.js"),X.register(v,"RenderRoute","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/router/index.js"),X.register(b,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/router/index.js")),(z="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&z(A)},5359:(A,e,t)=>{var n,r,a;function u(A){return JSON.stringify(A,void 0,4)}t.d(e,{I:()=>u}),A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature,(r="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&r.register(u,"beautifyJson","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/utils/helper.js"),(a="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&a(A)},5898:(A,e,t)=>{t.d(e,{A:()=>i});var n,r=t(4692),a=t.n(r),u=t(6645),o=t(6474),c=t(8465),l=t.n(c);A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var s={init:function(){this.loadScript()},loadScript:function(){var A=this;window.$=a().noConflict(!0),a().getScript(dc_bkash_admin.script_url,(function(){A.create_bkash_button()}))},create_bkash_button:function(){var A=document.createElement("button");A.id="bKash_button",A.className="btn btn-danger",A.setAttribute("disabled","disabled"),A.style.display="none",document.body.appendChild(A)},initBkash:function(A,e){var t=this,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],c=arguments.length>4&&void 0!==arguments[4]&&arguments[4],l=o.oR.info((0,u.__)("bKash Processing...","dc-bkash"),{position:"bottom-right",autoClose:!1,closeOnClick:!1,pauseOnHover:!1,draggable:!1,closeButton:!1}),s={amount:e,intent:"sale",currency:"BDT",merchantInvoiceNumber:A};bKash.init({paymentMode:"checkout",paymentRequest:s,createRequest:function(){n?bKash.create().onSuccess(n):bKash.create().onError()},executeRequestOnAuthorization:function(){if(r&&!c)return bKash.execute().onError(),o.oR.dismiss(l),r(!0);o.oR.dismiss(),bKash.execute().onError()},onClose:function(){if(bKash.create().onError(),o.oR.dismiss(l),t.showAlert((0,u.__)("Opps..."),(0,u.__)("Payment Cancelled!")),c&&r)return r(!0,n)}}),bKash.reconfigure({paymentRequest:s}),a()("#bKash_button").removeAttr("disabled"),a()("#bKash_button").click()},showAlert:function(A,e){l().fire({icon:"error",title:A,text:e,confirmButtonText:"OK"}).then((function(A){}))}},d=s;const i=d;var f,m;(f="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(f.register(s,"dcBkash","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/utils/bkash.js"),f.register(d,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/utils/bkash.js")),(m="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&m(A)},6119:(A,e,t)=>{t.d(e,{A:()=>c});var n,r=t(6540),a=t(6645);A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var u=function(){return r.createElement("div",{className:"loader"},r.createElement("img",{src:"https://res.cloudinary.com/d-coders/image/upload/v1592201998/wp-plugins/bkash.gif",alt:"bkash-loader"}),r.createElement("p",null,(0,a.__)("Loading...","dc-bkash")))},o=u;const c=o;var l,s;(l="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(l.register(u,"Loader","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/loader.js"),l.register(o,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/loader.js")),(s="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&s(A)},6393:(A,e,t)=>{var n,r=t(6540),a=t(9627),u=t(9546),o=t(9796),c=t(4625);A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var l,s,d=document.getElementById("dc-bkash-app");a.render(r.createElement(c.Kd,null,r.createElement(u.A,null)),d),(0,o.A)("dc-bkash"),(l="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&l.register(d,"mountNode","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/index.js"),(s="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&s(A)},7805:(A,e,t)=>{t.d(e,{A:()=>X});var n,r=t(6540),a=t(6645),u=t(9508),o=t.n(u),c=t(6474),l=t(9764),s=t.n(l),d=t(4625),i=t(6119),f=t(4040);function m(A,e){return function(A){if(Array.isArray(A))return A}(A)||function(A,e){var t=null==A?null:"undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(null!=t){var n,r,a,u,o=[],c=!0,l=!1;try{if(a=(t=t.call(A)).next,0===e){if(Object(t)!==t)return;c=!1}else for(;!(c=(n=a.call(t)).done)&&(o.push(n.value),o.length!==e);c=!0);}catch(A){l=!0,r=A}finally{try{if(!c&&null!=t.return&&(u=t.return(),Object(u)!==u))return}finally{if(l)throw r}}return o}}(A,e)||function(A,e){if(A){if("string"==typeof A)return v(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?v(A,e):void 0}}(A,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A);var b=function(){var A=m((0,r.useState)(!0),2),e=A[0],t=A[1],n=m((0,r.useState)([]),2),u=n[0],l=n[1],v=m((0,r.useState)(1),2),b=v[0],y=v[1],X=m((0,r.useState)(1),2),z=X[0],p=X[1],h=m((0,r.useState)(!1),2),O=h[0],L=h[1],j=(0,r.useRef)(null),P=function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=f.n.v1.transactions+"?per_page=20&page="+A;w(e)},w=function(A){o()({path:A,parse:!1}).then((function(A){t(!1),A.json().then((function(A){l(A)})),y(A.headers.get("X-WP-TotalPages"))})).catch((function(A){t(!1),c.oR.error(A.data.status+" : "+A.message)}))};if((0,r.useEffect)((function(){t(!0),P()}),[]),e)return r.createElement(i.A,null);return r.createElement("div",{className:"dokan_admin_settings_container"},r.createElement("div",{className:"title-section"},r.createElement("h2",null,(0,a.__)("Transactions","dc-bkash")),r.createElement("div",{className:"search-transaction"},r.createElement("input",{type:"text",className:"widefat",name:"search",id:"search",placeholder:(0,a.__)("Search","dc-bkash"),onChange:function(A){""!==A.target.value?(O||setTimeout((function(){var A=j.current.value;L(!1),function(A){var e=f.n.v1.transactionSearch+A;w(e)}(A)}),1e3),L(!0)):P()},ref:j}))),r.createElement("div",{className:"all-transactions"},r.createElement("table",{className:"table table-bordered border-primary transactions"},r.createElement("thead",null,r.createElement("tr",null,r.createElement("th",{scope:"col"},(0,a.__)("Order Number","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Amount","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Payment ID","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Trx ID","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Invoice No","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Trx Status","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Verification Status","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Payment Time","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Refund","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Refund Amount","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Refund Charge","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Refund ID","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Refund Reason","dc-bkash")),r.createElement("th",{scope:"col"},(0,a.__)("Action","dc-bkash")))),r.createElement("tbody",null,u.map((function(A,e){var t,n,u;return r.createElement("tr",{key:e},r.createElement("td",null,r.createElement("a",{href:A.order_url},A.order_number)),r.createElement("td",null,"1"===A.refund_status?r.createElement("del",null,A.amount):A.amount),r.createElement("td",null,A.payment_id),r.createElement("td",null,r.createElement(d.k2,{to:{pathname:"/search-transaction",search:"trx_id="+A.trx_id}},A.trx_id)),r.createElement("td",null,A.invoice_number),r.createElement("td",null,A.transaction_status),r.createElement("td",null,"1"===A.verification_status?r.createElement("p",{className:"success-label"},"Verified"):r.createElement("p",{className:"error-label"},"Pending")),r.createElement("td",null,A.created_at),r.createElement("td",null,"1"===A.refund_status?(0,a.__)("Refunded","dc-bkash"):""),r.createElement("td",null,"0"!==A.refund_amount?A.refund_amount:""),r.createElement("td",null,null!==(t=A.refund_charge)&&void 0!==t?t:""),r.createElement("td",null,r.createElement(d.k2,{to:{pathname:"/search-transaction",search:"trx_id="+A.refund_id}},A.refund_id)),r.createElement("td",null,A.refund_reason),r.createElement("td",null,(n=A.verification_status,u=A.payment_id,"1"===n?r.createElement("div",{className:"verify-btn-container verified"}):r.createElement("div",{className:"verify-btn-container"},r.createElement("button",{className:"btn verify-btn",onClick:function(A){return function(A,e){A.target.parentNode.classList.add("processing"),o()({path:f.n.v1.queryPayment+e}).then((function(e){A.target.parentNode.classList.remove("processing"),c.oR.success((0,a.__)("Successfully verified!","dc-bkash")),P(z)})).catch((function(e){A.target.parentNode.classList.remove("processing"),c.oR.error((0,a.__)("Problem in verification!","dc-bkash"))}))}(A,u)}},(0,a.__)("Verify","dc-bkash"))))))})))),r.createElement("div",{id:"react-paginate"},r.createElement(s(),{previousLabel:"Previous",nextLabel:"Next",breakLabel:"...",breakClassName:"break-me",pageCount:b,marginPagesDisplayed:2,pageRangeDisplayed:2,containerClassName:"pagination",activeClassName:"active",onPageChange:function(A){var e=A.selected+1;p(e),P(e)}}))))};("undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default.signature:function(A){return A})(b,"useState{[ isFetching, setIsFetching ](true)}\nuseState{[ transactions, setTransactions ]([])}\nuseState{[ pageCount, setPageCount ](1)}\nuseState{[ currentPage, setCurrentPage ](1)}\nuseState{[ awaitingSearch, setAwaitingSearch ](false)}\nuseRef{searchInput}\nuseEffect{}");var y=b;const X=y;var z,p;(z="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(z.register(b,"Transactions","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/transactions.js"),z.register(y,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/transactions.js")),(p="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&p(A)},8012:(A,e,t)=>{t.d(e,{A:()=>b});var n,r=t(6540),a=t(5818),u=t(6645),o=t(6399),c=t(9508),l=t.n(c),s=t(4040),d=t(6474);function i(A,e){return function(A){if(Array.isArray(A))return A}(A)||function(A,e){var t=null==A?null:"undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(null!=t){var n,r,a,u,o=[],c=!0,l=!1;try{if(a=(t=t.call(A)).next,0===e){if(Object(t)!==t)return;c=!1}else for(;!(c=(n=a.call(t)).done)&&(o.push(n.value),o.length!==e);c=!0);}catch(A){l=!0,r=A}finally{try{if(!c&&null!=t.return&&(u=t.return(),Object(u)!==u))return}finally{if(l)throw r}}return o}}(A,e)||function(A,e){if(A){if("string"==typeof A)return f(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?f(A,e):void 0}}(A,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A);var m=function(){var A,e,t=i((0,r.useState)(!1),2),n=t[0],c=t[1],f=i((0,r.useState)(""),2),m=f[0],v=f[1],b=i((0,r.useState)(0),2),y=b[0],X=b[1],z=i((0,r.useState)(""),2),p=z[0],h=z[1],O=i((0,r.useState)(!0),2),L=O[0],j=O[1],P=i((0,r.useState)(!1),2),w=P[0],Z=P[1],H=i((0,r.useState)({trx_id:"",amount:0}),2),k=H[0],M=H[1];return r.createElement(r.Fragment,null,r.createElement("div",{className:"refund-container__form"},r.createElement("div",{className:"search-order"},r.createElement("div",{className:"form-group"},r.createElement("label",null,(0,u.__)("Search Order ID","dc-bkash")),r.createElement(a.A,{cacheOptions:!0,defaultOptions:!0,value:k,getOptionLabel:function(A){return A.order_number},getOptionValue:function(A){return A.id},loadOptions:function(A){var e=s.n.v1.transactionSearch+A;return l()({path:e,parse:!1}).then((function(A){return A.json()}))},onChange:function(A){M(A),v(null==A?void 0:A.trx_id),X(null==A?void 0:A.amount)}}),r.createElement("span",{className:"help"},(0,u.__)("You may type your order number or transaction ID here.","dc-bkash")),"1"===k.refund_status?r.createElement(r.Fragment,null,r.createElement("span",{className:"help warning"},(0,u.__)("This order is refunded once. Refund amount was ".concat(k.refund_amount),"dc-bkash")),r.createElement("span",{className:"help warning"},(0,u.__)("A merchant can do refund only once for a transaction, it can be a full refund or partial amount refund.","dc-bkash"))):"")),r.createElement("div",{className:"form-group"},r.createElement("label",null,(0,u.__)("Trx ID","dc-bkash")),r.createElement("input",{type:"text",className:"form-control",defaultValue:m,readOnly:!0})),r.createElement("div",{className:"form-group"},r.createElement("label",null,(0,u.__)("Amount","dc-bkash")),r.createElement("input",{type:"number",value:y,step:.01,className:"form-control ".concat(w?"danger":""),onChange:function(A){return e=parseFloat(A.target.value),t=parseFloat(null==k?void 0:k.amount),Z(1>e||e>t),void X(e);var e,t},readOnly:null!==(A="1"===k.refund_status)&&void 0!==A&&A}),r.createElement("span",{className:"help"},(0,u.__)("You can only put value only between 1 to ".concat(null==k?void 0:k.amount),"dc-bkash"))),r.createElement("div",{className:"form-group"},r.createElement("label",null,(0,u.__)("Reason","dc-bkash")),r.createElement("input",{type:"text",value:p,className:"form-control",onChange:function(A){return h(A.target.value)},readOnly:null!==(e="1"===k.refund_status)&&void 0!==e&&e})),r.createElement("div",{className:"form-group"},r.createElement("label",null,r.createElement("input",{name:"isGoing",type:"checkbox",checked:L,onChange:function(A){return j(!L)}}),(0,u.__)("Create refund on WooCommerce?","dc-bkash"))),r.createElement(o.A,{type:"submit",isBusy:n,disabled:!y||w||n||"1"===k.refund_status,className:"dc_bkash_save_btn",isPrimary:!0,onClick:function(){return c(!0),void l()({path:s.n.v1.refund,method:"POST",data:{order_number:k.order_number,amount:y,wc_create_refund:L,refund_reason:p}}).then((function(A){c(!1),M({trx_id:"",amount:0}),v(""),h(""),X(0),j(!0),d.oR.success((0,u.__)("Refund Successfully!","dc-bkash"))})).catch((function(A){c(!1),d.oR.error(A.data.status+" : "+A.message)}))}},n?(0,u.__)("Submitting","dc-bkash"):(0,u.__)("Submit","dc-bkash"))))};("undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default.signature:function(A){return A})(m,"useState{[ isSubmitted, setIsSubmitted ](false)}\nuseState{[ refundTrxId, setRefundTrxId ]('')}\nuseState{[ refundAmount, setRefundAmount ](0)}\nuseState{[ refundReason, setRefundReason ]('')}\nuseState{[ wcCreateRefund, setWcCreateRefund ](true)}\nuseState{[ invalidAmount, setInvalidAmount ](false)}\nuseState{[ selectedValue, setSelectedValue ]({ trx_id: '', amount: 0 })}");var v=m;const b=v;var y,X;(y="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(y.register(m,"Refund","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/bKash/refund.js"),y.register(v,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/bKash/refund.js")),(X="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&X(A)},8741:(A,e,t)=>{t.d(e,{A:()=>c});var n,r=t(6540);function a(){return r.createElement("div",{className:"bkash_header_container"},r.createElement("div",{className:"header_logo"},r.createElement("img",{src:u(),alt:""})))}function u(){return window.dc_bkash_admin.asset_url+"/images/bkash_logo.png"}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var o=a;const c=o;var l,s;(l="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(l.register(a,"Header","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/Header.js"),l.register(u,"getLogo","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/Header.js"),l.register(o,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/components/Header.js")),(s="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&s(A)},9053:(A,e,t)=>{t.d(e,{A:()=>f});var n,r=t(6540),a=t(6645),u=t(6399),o=t(775),c=t(5898);function l(A,e){return function(A){if(Array.isArray(A))return A}(A)||function(A,e){var t=null==A?null:"undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(null!=t){var n,r,a,u,o=[],c=!0,l=!1;try{if(a=(t=t.call(A)).next,0===e){if(Object(t)!==t)return;c=!1}else for(;!(c=(n=a.call(t)).done)&&(o.push(n.value),o.length!==e);c=!0);}catch(A){l=!0,r=A}finally{try{if(!c&&null!=t.return&&(u=t.return(),Object(u)!==u))return}finally{if(l)throw r}}return o}}(A,e)||function(A,e){if(A){if("string"==typeof A)return s(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?s(A,e):void 0}}(A,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}function d(){var A=l((0,r.useState)(!1),2),e=A[0],t=A[1],n=l((0,r.useState)(!1),2),s=n[0],d=n[1],i=l((0,r.useState)(!1),2),f=i[0],m=i[1];return(0,r.useEffect)((function(){"undefined"==typeof bKash&&c.A.init()}),[]),r.createElement("div",{className:"dokan_admin_settings_container"},r.createElement("h2",null,(0,a.__)("Generate Doc","dc-bkash")),r.createElement("div",{className:"generate_help_text_container"},r.createElement("h4",null,(0,a.__)("You may generate API Request/Response doc from here.","dc-bkash")),"1"===dc_bkash_admin.all_credentials_filled?r.createElement("div",{className:"generate-content-actions"},r.createElement("p",null,(0,a.__)("In case, if you need sandbox mobile number and OTP then you may use the below number.","dc-bkash")),r.createElement("div",{className:"sandbox_number_details"},r.createElement("p",null,r.createElement("span",null,"bKash Number")," : 01770618575"),r.createElement("p",null,r.createElement("span",null,"OTP")," : 123456"),r.createElement("p",null,r.createElement("span",null,"PIN")," : 12121")),r.createElement(u.A,{type:"submit",isBusy:e,disabled:e,className:"dc_bkash_save_btn",isPrimary:!0,onClick:function(){m(!0),t(!0)}},e?(0,a.__)("Generating","dc-bkash"):(0,a.__)("Generate","dc-bkash")),r.createElement(u.A,{type:"submit",className:"dc_bkash_save_btn",isPrimary:!0,disabled:!s,onClick:function(){window.print()}},(0,a.__)("Download","dc-bkash"))):r.createElement("div",{className:"generate-content-actions"},r.createElement("p",null,(0,a.__)("Before generate the doc, you must have to add sandbox keys in settings.","dc-bkash")))),!!f&&r.createElement(o.A,{afterComplete:function(){t(!1),d(!0)}}))}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),("undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default.signature:function(A){return A})(d,"useState{[ isGenerating, setIsGenerating ](false)}\nuseState{[ isDownloadable, setIsDownloadable ](false)}\nuseState{[ visibleDocContainer, setVisibleDocContainer ](false)}\nuseEffect{}");var i=d;const f=i;var m,v;(m="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(m.register(d,"GenerateDoc","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/generatedoc.js"),m.register(i,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/Pages/generatedoc.js")),(v="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&v(A)},9508:A=>{A.exports=wp.apiFetch},9546:(A,e,t)=>{t.d(e,{A:()=>s});var n,r=t(6540),a=t(3472),u=t(8741),o=t(4967);function c(){return r.createElement(r.Fragment,null,r.createElement(u.A,null),r.createElement("div",{className:"wrap"},r.createElement(o.A,null)))}A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var l=(0,a.K)(c);const s=l;var d,i;(d="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(d.register(c,"App","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/App.js"),d.register(l,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/App.js")),(i="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&i(A)},9796:(A,e,t)=>{var n;function r(A){var e=jQuery,t=e("#toplevel_page_"+A),n=window.location.href,r=n.substr(n.indexOf("admin.php"));t.on("click","a",(function(){var A=e(this);e("ul.wp-submenu li",t).removeClass("current"),A.hasClass("wp-has-submenu")?e("li.wp-first-item",t).addClass("current"):A.parents("li").addClass("current")})),e("ul.wp-submenu a",t).each((function(A,t){e(t).attr("href")!==r||e(t).parent().addClass("current")}))}t.d(e,{A:()=>u}),A=t.hmd(A),(n="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.enterModule:void 0)&&n(A),"undefined"!=typeof reactHotLoaderGlobal&&reactHotLoaderGlobal.default.signature;var a=r;const u=a;var o,c;(o="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.default:void 0)&&(o.register(r,"menuFix","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/utils/admin-menu-fix.js"),o.register(a,"default","/home/<USER>/work/bKash-woocommerce/bKash-woocommerce/assets/src/admin/utils/admin-menu-fix.js")),(c="undefined"!=typeof reactHotLoaderGlobal?reactHotLoaderGlobal.leaveModule:void 0)&&c(A)}},A=>{A.O(0,[96],(()=>A(A.s=6393))),A.O()}]);